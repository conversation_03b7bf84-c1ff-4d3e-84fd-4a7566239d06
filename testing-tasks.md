# Lista zadań do dokończenia testów w projekcie family-task-shadcn

## Status obecny

✅ Testy API - przechodzą  
✅ Testy date utils - przechodzą  
🔄 Testy komponentów - wymagają naprawy  
❌ Brakujące testy komponentów i hooków  
❌ Konfiguracja pokrycia kodu

## 1. **Naprawa pozostałych testów komponentów**

### 1.1 LoadingSpinner - pozostałe naprawy

**Plik:** `__tests__/components/ui/LoadingSpinner.test.tsx`

**Problemy do naprawy:**

- Testy kolorów sprawdzają nieprawidłowe klasy CSS
- Testy dostępności oczekują nieistniejących elementów SVG
- Testy struktury DOM nie pasują do rzeczywistej implementacji

**Konkretne kroki:**

```typescript
// Napraw testy kolorów - zmień z text-* na border-*
expect(spinner).toHaveClass('border-purple-600', 'border-t-transparent'); // zamiast text-primary
expect(spinner).toHaveClass('border-white', 'border-t-transparent'); // zamiast text-white
expect(spinner).toHaveClass('border-gray-600', 'border-t-transparent'); // zamiast text-muted-foreground

// Napraw test struktury DOM
expect(spinner).toHaveClass('animate-spin', 'rounded-full', 'border-2', 'custom-class');

// Usuń testy SVG (komponent nie używa SVG)
// Usuń testy aria-hidden dla ikon
```

### 1.2 ThemeToggle - kompletna naprawa

**Plik:** `__tests__/components/theme/ThemeToggle.test.tsx`

**Problemy:**

- Komponent może nie istnieć lub ma inną strukturę
- Brak właściwych atrybutów ARIA
- Funkcjonalność przełączania nie działa

**Konkretne kroki:**

1. Sprawdź czy komponent ThemeToggle istnieje w `src/components/theme/`
2. Jeśli nie istnieje, utwórz go lub znajdź alternatywną implementację
3. Dostosuj testy do rzeczywistej implementacji
4. Dodaj mocki dla next-themes jeśli potrzebne

### 1.3 FamilySwitcher - naprawa mocków

**Plik:** `__tests__/components/family/FamilySwitcher.test.tsx`

**Problemy:**

- Nieprawidłowe mockowanie useRouter z next/navigation
- Brak hooka useFamily

**Konkretne kroki:**

```typescript
// Napraw mock useRouter
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    // ... inne metody
  }),
}));

// Sprawdź czy hook useFamily istnieje, jeśli nie - utwórz mock
jest.mock('@/hooks/useFamily', () => ({
  useFamily: jest.fn(),
}));
```

## 2. **Dokończenie pisania testów dla nietestowanych komponentów**

### 2.1 Komponenty UI wymagające testów

**Pliki do utworzenia:**

- `__tests__/components/ui/Button.test.tsx`
- `__tests__/components/ui/Card.test.tsx`
- `__tests__/components/ui/Input.test.tsx`
- `__tests__/components/ui/Select.test.tsx`
- `__tests__/components/ui/Dialog.test.tsx`
- `__tests__/components/ui/Badge.test.tsx`

### 2.2 Komponenty zadań wymagające testów

**Pliki do utworzenia:**

- `__tests__/components/tasks/TaskForm.test.tsx`
- `__tests__/components/tasks/TaskList.test.tsx`
- `__tests__/components/tasks/TaskFilters.test.tsx`
- `__tests__/components/tasks/TaskStats.test.tsx`

### 2.3 Komponenty rodziny wymagające testów

**Pliki do utworzenia:**

- `__tests__/components/family/FamilyForm.test.tsx`
- `__tests__/components/family/MembersList.test.tsx`
- `__tests__/components/family/InviteForm.test.tsx`

### 2.4 Hooki wymagające testów

**Pliki do utworzenia:**

- `__tests__/hooks/useFamily.test.ts`
- `__tests__/hooks/useTasks.test.ts`
- `__tests__/hooks/useLocalStorage.test.ts`

**Przykładowa struktura testu hooka:**

```typescript
import { renderHook, act } from '@testing-library/react';
import { useFamily } from '@/hooks/useFamily';

describe('useFamily', () => {
  it('powinien zwrócić listę rodzin', () => {
    // test implementation
  });
});
```

## 3. **Konfiguracja pokrycia kodu**

### 3.1 Edycja konfiguracji Jest

**Plik:** `jest.config.js`

**Zmiany do dodania:**

```javascript
module.exports = {
  // ... istniejąca konfiguracja
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/index.{ts,tsx}',
    '!src/app/**/layout.tsx',
    '!src/app/**/page.tsx',
    '!src/app/**/loading.tsx',
    '!src/app/**/error.tsx',
    '!src/app/**/not-found.tsx',
    '!src/lib/prisma.ts',
    '!src/lib/auth.ts',
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html', 'json'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};
```

## 4. **Dodanie skryptów npm**

### 4.1 Edycja package.json

**Plik:** `package.json`

**Skrypty do dodania w sekcji "scripts":**

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:coverage:watch": "jest --coverage --watch",
    "test:ci": "jest --coverage --watchAll=false --passWithNoTests",
    "test:update-snapshots": "jest --updateSnapshot"
  }
}
```

## 5. **Finalne sprawdzenie**

### 5.1 Uruchomienie wszystkich testów

**Komendy do wykonania w kolejności:**

```bash
# 1. Uruchom wszystkie testy bez pokrycia
npm test

# 2. Uruchom testy z pokryciem kodu
npm run test:coverage

# 3. Sprawdź konkretne grupy testów
npm test -- __tests__/api/
npm test -- __tests__/components/
npm test -- __tests__/hooks/
npm test -- __tests__/lib/

# 4. Uruchom testy w trybie CI
npm run test:ci
```

### 5.2 Sprawdzenie raportów pokrycia

**Pliki do sprawdzenia:**

- `coverage/lcov-report/index.html` - raport HTML
- `coverage/lcov.info` - raport LCOV
- Terminal output - podsumowanie pokrycia

**Oczekiwane rezultaty:**

- Wszystkie testy przechodzą (0 failed)
- Pokrycie kodu ≥ 70% dla linii, funkcji, gałęzi i instrukcji
- Brak błędów w konsoli
- Raport HTML dostępny w przeglądarce

### 5.3 Weryfikacja jakości testów

**Kryteria do sprawdzenia:**

- Testy używają polskich nazw zgodnie z preferencjami użytkownika
- Zastosowany wzorzec AAA (Arrange-Act-Assert)
- Testy obejmują scenariusze błędów i edge cases
- Mocki są prawidłowo skonfigurowane
- Testy są niezależne i deterministyczne

### 5.4 Dokumentacja

**Plik do utworzenia/aktualizacji:** `README.md`

**Sekcja do dodania:**

````markdown
## Testowanie

### Uruchamianie testów

```bash
npm test                    # Wszystkie testy
npm run test:watch         # Tryb watch
npm run test:coverage      # Z pokryciem kodu
```
````

### Pokrycie kodu

Projekt utrzymuje minimum 70% pokrycia kodu dla:

- Linii kodu
- Funkcji
- Gałęzi
- Instrukcji

Raport pokrycia dostępny w `coverage/lcov-report/index.html`

```

## Podsumowanie kolejności wykonania:

1. **Najpierw** - napraw pozostałe testy komponentów (LoadingSpinner, ThemeToggle, FamilySwitcher)
2. **Następnie** - napisz testy dla najważniejszych komponentów (UI, zadania, rodzina)
3. **Potem** - napisz testy dla hooków
4. **Dalej** - skonfiguruj pokrycie kodu w Jest
5. **Następnie** - dodaj skrypty npm
6. **Na końcu** - przeprowadź finalne sprawdzenie i dokumentację

Każdy krok powinien być weryfikowany przez uruchomienie testów przed przejściem do następnego.
```
