import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,

  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  environment: process.env.NODE_ENV,

  beforeSend(event, hint) {
    // Filter out database connection errors in development
    if (process.env.NODE_ENV === 'development' && event.exception) {
      const error = event.exception.values?.[0];
      if (
        error?.type === 'PrismaClientKnownRequestError' ||
        error?.type === 'PrismaClientInitializationError'
      ) {
        return null;
      }
    }

    // Add additional context for server errors
    if (event.exception) {
      event.tags = {
        ...event.tags,
        server: true,
      };
    }

    return event;
  },

  // Enhanced context for server-side
  initialScope: {
    tags: {
      component: 'server',
    },
  },
});
