import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,

  // Capture Replay for 10% of all sessions,
  // plus for 100% of sessions with an error
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1.0,

  environment: process.env.NODE_ENV,

  beforeSend(event, hint) {
    // Filter out non-critical errors
    if (event.exception) {
      const error = event.exception.values?.[0];

      // Filter out AbortError (common when users navigate away)
      if (error?.type === 'AbortError') {
        return null;
      }

      // Filter out network errors that aren't actionable
      if (error?.type === 'NetworkError' && error?.value?.includes('fetch')) {
        return null;
      }

      // Filter out script loading errors from browser extensions
      if (
        error?.value?.includes('chrome-extension://') ||
        error?.value?.includes('moz-extension://')
      ) {
        return null;
      }
    }

    // Filter out certain fingerprints
    if (event.fingerprint) {
      const fingerprintStr = event.fingerprint.join('');
      if (fingerprintStr.includes('Non-Error promise rejection captured')) {
        return null;
      }
    }

    return event;
  },

  beforeSendTransaction(event) {
    // Don't send transactions for static assets
    if (event.transaction?.includes('/static/') || event.transaction?.includes('/_next/')) {
      return null;
    }

    return event;
  },

  // Additional configuration
  integrations: [
    Sentry.replayIntegration({
      // Mask all text content, only record clicks and navigations
      maskAllText: true,
      blockAllMedia: true,
    }),
  ],

  // Enhanced context
  initialScope: {
    tags: {
      component: 'client',
    },
  },
});
