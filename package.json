{"name": "family-tasks", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "prepare": "husky", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__", "test:integration": "jest --testPathPattern=integration", "test:e2e": "playwright test", "analyze": "ANALYZE=true npm run build", "lighthouse": "lhci autorun", "seed": "tsx prisma/seed.ts"}, "dependencies": {"@clerk/nextjs": "^6.23.0", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^9.32.0", "@tanstack/react-query": "^5.81.2", "@types/nodemailer": "^6.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "lucide-react": "^0.523.0", "next": "15.3.4", "next-intl": "^4.3.1", "next-themes": "^0.4.6", "nodemailer": "^7.0.3", "prisma": "^6.10.1", "react": "^19.0.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "recharts": "^3.0.0", "sonner": "^2.0.5", "svix": "^1.68.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@lhci/cli": "^0.15.1", "@next/bundle-analyzer": "^15.3.4", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/supertest": "^6.0.3", "eslint": "^9", "eslint-config-next": "15.3.4", "husky": "^9.1.7", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.2", "jest-environment-node": "^30.0.2", "jest-mock-extended": "^4.0.0", "lint-staged": "^16.1.2", "node-mocks-http": "^1.17.2", "playwright": "^1.53.1", "prettier": "^3.6.1", "supertest": "^7.1.1", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}