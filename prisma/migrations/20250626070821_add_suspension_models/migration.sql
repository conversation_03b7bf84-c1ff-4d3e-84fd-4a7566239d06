/*
  Warnings:

  - Added the required column `createdById` to the `task_suspensions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `title` to the `task_suspensions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `task_suspensions` table without a default value. This is not possible if the table is not empty.
  - Made the column `endDate` on table `task_suspensions` required. This step will fail if there are existing NULL values in that column.

*/
-- CreateTable
CREATE TABLE "suspended_tasks" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "suspensionId" TEXT NOT NULL,
    "taskId" TEXT NOT NULL,
    CONSTRAINT "suspended_tasks_suspensionId_fkey" FOREIGN KEY ("suspensionId") REFERENCES "task_suspensions" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "suspended_tasks_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "tasks" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "suspended_members" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "suspensionId" TEXT NOT NULL,
    "memberId" TEXT NOT NULL,
    CONSTRAINT "suspended_members_suspensionId_fkey" FOREIGN KEY ("suspensionId") REFERENCES "task_suspensions" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "suspended_members_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES "family_members" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_task_suspensions" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "familyId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "reason" TEXT NOT NULL,
    "startDate" DATETIME NOT NULL,
    "endDate" DATETIME NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "createdById" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "task_suspensions_familyId_fkey" FOREIGN KEY ("familyId") REFERENCES "families" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "task_suspensions_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "family_members" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_task_suspensions" ("createdAt", "endDate", "familyId", "id", "reason", "startDate") SELECT "createdAt", "endDate", "familyId", "id", "reason", "startDate" FROM "task_suspensions";
DROP TABLE "task_suspensions";
ALTER TABLE "new_task_suspensions" RENAME TO "task_suspensions";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "suspended_tasks_suspensionId_taskId_key" ON "suspended_tasks"("suspensionId", "taskId");

-- CreateIndex
CREATE UNIQUE INDEX "suspended_members_suspensionId_memberId_key" ON "suspended_members"("suspensionId", "memberId");
