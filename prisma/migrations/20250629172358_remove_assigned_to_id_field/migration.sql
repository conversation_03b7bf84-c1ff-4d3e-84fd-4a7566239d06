/*
  Warnings:

  - You are about to drop the column `assignedToId` on the `tasks` table. All the data in the column will be lost.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_tasks" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "familyId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "points" INTEGER NOT NULL DEFAULT 1,
    "weight" TEXT NOT NULL DEFAULT 'NORMAL',
    "frequency" TEXT NOT NULL DEFAULT 'ONCE',
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "dueDate" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "assignedBy" TEXT NOT NULL,
    "assignedAt" DATETIME,
    "startDate" DATETIME,
    "endDate" DATETIME,
    "allDay" BOOLEAN NOT NULL DEFAULT true,
    "recurrenceRule" TEXT,
    "color" TEXT,
    "googleEventId" TEXT,
    "appleEventId" TEXT,
    "recurrenceEnd" DATETIME,
    CONSTRAINT "tasks_assignedBy_fkey" FOREIGN KEY ("assignedBy") REFERENCES "family_members" ("id") ON DELETE RESTRICT ON UPDATE CASCADE,
    CONSTRAINT "tasks_familyId_fkey" FOREIGN KEY ("familyId") REFERENCES "families" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_tasks" ("allDay", "appleEventId", "assignedAt", "assignedBy", "color", "createdAt", "description", "dueDate", "endDate", "familyId", "frequency", "googleEventId", "id", "points", "recurrenceEnd", "recurrenceRule", "startDate", "status", "title", "updatedAt", "weight") SELECT "allDay", "appleEventId", "assignedAt", "assignedBy", "color", "createdAt", "description", "dueDate", "endDate", "familyId", "frequency", "googleEventId", "id", "points", "recurrenceEnd", "recurrenceRule", "startDate", "status", "title", "updatedAt", "weight" FROM "tasks";
DROP TABLE "tasks";
ALTER TABLE "new_tasks" RENAME TO "tasks";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
