-- CreateTable
CREATE TABLE "calendar_settings" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "familyId" TEXT NOT NULL,
    "defaultView" TEXT NOT NULL DEFAULT 'MONTH',
    "startWeekOn" TEXT NOT NULL DEFAULT 'MONDAY',
    "timeFormat" TEXT NOT NULL DEFAULT 'HOUR_24',
    "showWeekends" BOOLEAN NOT NULL DEFAULT true,
    "showCompleted" BOOLEAN NOT NULL DEFAULT false,
    "colorScheme" TEXT NOT NULL DEFAULT 'default',
    "googleCalendarEnabled" BOOLEAN NOT NULL DEFAULT false,
    "appleCalendarEnabled" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "calendar_settings_familyId_fkey" FOREIGN KEY ("familyId") REFERENCES "families" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_tasks" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "familyId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "points" INTEGER NOT NULL DEFAULT 1,
    "weight" TEXT NOT NULL DEFAULT 'NORMAL',
    "frequency" TEXT NOT NULL DEFAULT 'ONCE',
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "dueDate" DATETIME,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "assignedToId" TEXT,
    "assignedBy" TEXT NOT NULL,
    "assignedAt" DATETIME,
    "startDate" DATETIME,
    "endDate" DATETIME,
    "allDay" BOOLEAN NOT NULL DEFAULT true,
    "recurrenceRule" TEXT,
    "color" TEXT,
    "googleEventId" TEXT,
    "appleEventId" TEXT,
    CONSTRAINT "tasks_familyId_fkey" FOREIGN KEY ("familyId") REFERENCES "families" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "tasks_assignedToId_fkey" FOREIGN KEY ("assignedToId") REFERENCES "family_members" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "tasks_assignedBy_fkey" FOREIGN KEY ("assignedBy") REFERENCES "family_members" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_tasks" ("assignedAt", "assignedBy", "assignedToId", "createdAt", "description", "dueDate", "familyId", "frequency", "id", "points", "status", "title", "updatedAt", "weight") SELECT "assignedAt", "assignedBy", "assignedToId", "createdAt", "description", "dueDate", "familyId", "frequency", "id", "points", "status", "title", "updatedAt", "weight" FROM "tasks";
DROP TABLE "tasks";
ALTER TABLE "new_tasks" RENAME TO "tasks";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "calendar_settings_familyId_key" ON "calendar_settings"("familyId");
