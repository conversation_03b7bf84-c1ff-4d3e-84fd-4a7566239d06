generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

enum TaskWeight {
  LIGHT
  NORMAL
  HEAVY
  CRITICAL
}

enum TaskFrequency {
  ONCE
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
  CUSTOM
}

enum TaskStatus {
  ACTIVE
  COMPLETED
  SUSPENDED
  ARCHIVED
}

enum TaskAssignmentType {
  ANY
  ALL
  SPECIFIC
}

enum CompletionStatus {
  PENDING
  APPROVED
  REJECTED
}

enum FamilyRole {
  OWNER
  PARENT
  GUARDIAN
  CHILD
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

enum CalendarView {
  MONTH
  WEEK
  DAY
  AGENDA
}

enum WeekDay {
  MONDAY
  SUNDAY
}

enum TimeFormat {
  HOUR_12
  HOUR_24
}

enum StatsPeriod {
  DAILY
  WEEKLY
  MONTHLY
  YEARLY
}

model User {
  id                 String              @id @default(cuid())
  clerkId            String              @unique
  email              String              @unique
  firstName          String?
  lastName           String?
  preferredLanguage  String              @default("pl")
  timezone           String              @default("Europe/Warsaw")
  dateFormat         String              @default("dd.MM.yyyy")
  timeFormat         String              @default("24h")
  // Email notification preferences
  emailNotificationsEnabled Boolean         @default(true)
  notifyTaskAssigned        Boolean         @default(true)
  notifyTaskCompleted       Boolean         @default(true)
  notifyTaskVerified        Boolean         @default(true)
  notifyTaskReminder        Boolean         @default(true)
  notifyFamilyInvitations   Boolean         @default(true)
  // Privacy preferences
  profileVisibility         String          @default("family") // 'public' | 'family' | 'private'
  showEmail                 Boolean         @default(false)
  showActivity              Boolean         @default(true)
  showStats                 Boolean         @default(true)
  allowInvitations          Boolean         @default(true)
  dataSharing               Boolean         @default(true)
  analyticsOptOut           Boolean         @default(false)
  marketingEmails           Boolean         @default(false)
  // Security/notification preferences (app-level only)
  notifySuspiciousActivity  Boolean         @default(true)
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  activityLogs       ActivityLog[]
  emailNotifications EmailNotification[]
  familyMembers      FamilyMember[]
  userPreferences    UserPreferences?

  @@map("users")
}

// Zaawansowane preferencje użytkownika - dla przyszłych rozszerzeń
model UserPreferences {
  id                    String   @id @default(cuid())
  userId                String   @unique
  // Interface preferences
  compactMode           Boolean  @default(false)
  showAvatars           Boolean  @default(true)
  animations            Boolean  @default(true)
  // Advanced notifications
  digestFrequency       String   @default("daily") // 'daily' | 'weekly' | 'monthly' | 'disabled'
  quietHours            Json?    // { start: "22:00", end: "08:00" }
  weekendNotifications  Boolean  @default(true)
  // Mobile preferences  
  pushNotifications     Boolean  @default(true)
  vibration             Boolean  @default(true)
  soundEnabled          Boolean  @default(true)
  // Export and backup
  autoBackup            Boolean  @default(false)
  backupFrequency       String   @default("weekly") // 'daily' | 'weekly' | 'monthly'
  // Beta features
  betaFeatures          Boolean  @default(false)
  experimentalUI        Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  user                  User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model Family {
  id                 String              @id @default(cuid())
  name               String
  description        String?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  activityLogs       ActivityLog[]
  calendarSettings   CalendarSettings?
  emailNotifications EmailNotification[]
  invitations        FamilyInvitation[]
  members            FamilyMember[]
  familyStats        FamilyStats[]
  suspensions        TaskSuspension[]
  tasks              Task[]

  @@map("families")
}

model FamilyMember {
  id                                       String            @id @default(cuid())
  userId                                   String
  familyId                                 String
  role                                     FamilyRole        @default(CHILD)
  isAdult                                  Boolean           @default(false)
  points                                   Int               @default(0)
  joinedAt                                 DateTime          @default(now())
  lastActive                               DateTime?
  activityLogs                             ActivityLog[]
  family                                   Family            @relation(fields: [familyId], references: [id], onDelete: Cascade)
  user                                     User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  memberStats                              MemberStats[]
  memberSuspensions                        SuspendedMember[] @relation("MemberSuspensions")
  taskAssignments                          TaskAssignment[]
  verifiedTasks                            TaskCompletion[]  @relation("VerifiedTasks")
  completedTasks                           TaskCompletion[]  @relation("CompletedTasks")
  createdSuspensions                       TaskSuspension[]  @relation("CreatedSuspensions")
  createdTasks                             Task[]            @relation("CreatedTasks")

  @@unique([userId, familyId])
  @@map("family_members")
}

model Task {
  id                                                String           @id @default(cuid())
  familyId                                          String
  title                                             String
  description                                       String?
  points                                            Int              @default(1)
  weight                                            TaskWeight       @default(NORMAL)
  frequency                                         TaskFrequency    @default(ONCE)
  status                                            TaskStatus       @default(ACTIVE)
  // Nowe semantycznie jasne pola czasowe
  startDateTime                                     DateTime?        // Początek zadania/pierwszego wystąpienia
  endDateTime                                       DateTime?        // Koniec pojedynczego wystąpienia
  cycleEndDate                                      DateTime?        // Koniec cyklu dla zadań cyklicznych (tylko data)
  // Pozostałe pola czasowe
  createdAt                                         DateTime         @default(now())
  updatedAt                                         DateTime         @updatedAt
  assignedBy                                        String
  assignedAt                                        DateTime?
  allDay                                            Boolean          @default(true)
  recurrenceRule                                    String?
  color                                             String?
  googleEventId                                     String?
  appleEventId                                      String?
  // Przestarzałe pola - zachowane dla kompatybilności podczas migracji
  dueDate                                           DateTime?        // @deprecated Użyj startDateTime
  startDate                                         DateTime?        // @deprecated Użyj startDateTime
  endDate                                           DateTime?        // @deprecated Użyj endDateTime lub cycleEndDate
  recurrenceEnd                                     DateTime?        // @deprecated Użyj cycleEndDate
  suspensions                                       SuspendedTask[]
  assignments                                       TaskAssignment[]
  completions                                       TaskCompletion[]
  reminders                                         TaskReminder[]
  assignedByUser                                    FamilyMember     @relation("CreatedTasks", fields: [assignedBy], references: [id])
  family                                            Family           @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@map("tasks")
}

model TaskAssignment {
  id             String             @id @default(cuid())
  taskId         String
  memberId       String
  assignedAt     DateTime           @default(now())
  assignedBy     String
  assignmentType TaskAssignmentType @default(ANY)
  member         FamilyMember       @relation(fields: [memberId], references: [id], onDelete: Cascade)
  task           Task               @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@unique([taskId, memberId])
  @@map("task_assignments")
}

model FamilyInvitation {
  id        String           @id @default(cuid())
  familyId  String
  email     String
  role      FamilyRole       @default(CHILD)
  isAdult   Boolean          @default(false)
  status    InvitationStatus @default(PENDING)
  token     String           @unique
  expiresAt DateTime
  createdAt DateTime         @default(now())
  family    Family           @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@unique([familyId, email])
  @@map("family_invitations")
}

model TaskCompletion {
  id            String           @id @default(cuid())
  taskId        String
  completedById String
  completedAt   DateTime         @default(now())
  verifiedById  String?
  verifiedAt    DateTime?
  status        CompletionStatus @default(PENDING)
  notes         String?
  pointsAwarded Int              @default(0)
  verifiedBy    FamilyMember?    @relation("VerifiedTasks", fields: [verifiedById], references: [id])
  completedBy   FamilyMember     @relation("CompletedTasks", fields: [completedById], references: [id])
  task          Task             @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("task_completions")
}

model TaskReminder {
  id       String    @id @default(cuid())
  taskId   String
  remindAt DateTime
  sent     Boolean   @default(false)
  sentAt   DateTime?
  task     Task      @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("task_reminders")
}

model TaskSuspension {
  id               String            @id @default(cuid())
  familyId         String
  title            String
  description      String?
  reason           String
  startDate        DateTime
  endDate          DateTime
  status           String            @default("ACTIVE")
  createdById      String
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  suspendedMembers SuspendedMember[]
  suspendedTasks   SuspendedTask[]
  createdBy        FamilyMember      @relation("CreatedSuspensions", fields: [createdById], references: [id])
  family           Family            @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@map("task_suspensions")
}

model SuspendedTask {
  id           String         @id @default(cuid())
  suspensionId String
  taskId       String
  task         Task           @relation(fields: [taskId], references: [id], onDelete: Cascade)
  suspension   TaskSuspension @relation(fields: [suspensionId], references: [id], onDelete: Cascade)

  @@unique([suspensionId, taskId])
  @@map("suspended_tasks")
}

model SuspendedMember {
  id           String         @id @default(cuid())
  suspensionId String
  memberId     String
  member       FamilyMember   @relation("MemberSuspensions", fields: [memberId], references: [id], onDelete: Cascade)
  suspension   TaskSuspension @relation(fields: [suspensionId], references: [id], onDelete: Cascade)

  @@unique([suspensionId, memberId])
  @@map("suspended_members")
}

model CalendarSettings {
  id                    String       @id @default(cuid())
  familyId              String       @unique
  defaultView           CalendarView @default(MONTH)
  startWeekOn           WeekDay      @default(MONDAY)
  timeFormat            TimeFormat   @default(HOUR_24)
  showWeekends          Boolean      @default(true)
  showCompleted         Boolean      @default(false)
  colorScheme           String       @default("default")
  googleCalendarEnabled Boolean      @default(false)
  appleCalendarEnabled  Boolean      @default(false)
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @updatedAt
  family                Family       @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@map("calendar_settings")
}

model ActivityLog {
  id         String        @id @default(cuid())
  familyId   String
  userId     String?
  memberId   String?
  action     String
  entityType String
  entityId   String?
  metadata   Json?
  timestamp  DateTime      @default(now())
  member     FamilyMember? @relation(fields: [memberId], references: [id])
  user       User?         @relation(fields: [userId], references: [id])
  family     Family        @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@index([familyId, timestamp])
  @@index([familyId, action, timestamp])
  @@map("activity_logs")
}

model FamilyStats {
  id                   String      @id @default(cuid())
  familyId             String
  date                 DateTime
  period               StatsPeriod @default(DAILY)
  tasksCreated         Int         @default(0)
  tasksCompleted       Int         @default(0)
  tasksVerified        Int         @default(0)
  completionRate       Float       @default(0)
  totalPointsAwarded   Int         @default(0)
  averagePointsPerTask Float       @default(0)
  activeMembersCount   Int         @default(0)
  createdAt            DateTime    @default(now())
  updatedAt            DateTime    @updatedAt
  family               Family      @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@unique([familyId, date, period])
  @@map("family_stats")
}

model MemberStats {
  id              String       @id @default(cuid())
  memberId        String
  date            DateTime
  period          StatsPeriod  @default(DAILY)
  tasksAssigned   Int          @default(0)
  tasksCompleted  Int          @default(0)
  tasksOnTime     Int          @default(0)
  tasksLate       Int          @default(0)
  completionRate  Float        @default(0)
  pointsEarned    Int          @default(0)
  pointsFromTasks Int          @default(0)
  bonusPoints     Int          @default(0)
  activeDays      Int          @default(0)
  streakDays      Int          @default(0)
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  member          FamilyMember @relation(fields: [memberId], references: [id], onDelete: Cascade)

  @@unique([memberId, date, period])
  @@map("member_stats")
}

model EmailNotification {
  id        String    @id @default(cuid())
  familyId  String
  userId    String
  type      String
  subject   String
  content   String
  sentAt    DateTime?
  createdAt DateTime  @default(now())
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  family    Family    @relation(fields: [familyId], references: [id], onDelete: Cascade)

  @@index([familyId, type, createdAt])
  @@map("email_notifications")
}
