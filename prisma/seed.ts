import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Rozpoczynam seeding bazy danych...');

  try {
    // <PERSON><PERSON><PERSON><PERSON> istniej<PERSON>ce dane (opcjonalnie)
    await cleanDatabase();

    // Pobierz aktualnego użytkownika
    const currentUser = await getCurrentUser();

    // Stwórz przykładowych użytkowników
    const sampleUsers = await createSampleUsers();

    // Stwórz przykładowe rodziny
    const families = await createSampleFamilies(currentUser, sampleUsers);

    // Stwórz przykładowe zadania
    await createSampleTasks(families);

    console.log('✅ Seeding zakończony pomyślnie!');
  } catch (error) {
    console.error('❌ Błąd podczas seeding:', error);
    throw error;
  }
}

async function cleanDatabase() {
  console.log('🧹 Czyszczenie bazy danych...');

  // Usuń w odpowiedniej kolejności ze względu na relacje
  await prisma.taskCompletion.deleteMany();
  await prisma.taskReminder.deleteMany();
  await prisma.suspendedTask.deleteMany();
  await prisma.suspendedMember.deleteMany();
  await prisma.taskSuspension.deleteMany();
  await prisma.task.deleteMany();
  await prisma.familyInvitation.deleteMany();
  await prisma.familyMember.deleteMany();
  await prisma.family.deleteMany();
  // Nie usuwamy użytkowników z Clerk, tylko lokalne dodatkowe
  await prisma.user.deleteMany({
    where: {
      clerkId: {
        startsWith: 'sample_',
      },
    },
  });
}

async function getCurrentUser() {
  console.log('👤 Pobieram aktualnego użytkownika...');

  // Pobierz pierwszego użytkownika z bazy danych (aktualnie zalogowany)
  let user = await prisma.user.findFirst({
    where: {
      NOT: {
        clerkId: {
          startsWith: 'sample_',
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  if (!user) {
    console.log(
      '⚠️ Nie znaleziono żadnego użytkownika. Tworzę przykładowego użytkownika dla demonstracji...'
    );

    // Stwórz przykładowego użytkownika jako właściciela pierwszej rodziny
    user = await prisma.user.create({
      data: {
        clerkId: 'demo_jan_kowalski',
        email: '<EMAIL>',
        firstName: 'Jan',
        lastName: 'Kowalski',
        preferredLanguage: 'pl',
      },
    });

    console.log('✅ Stworzono demonstracyjnego użytkownika: <EMAIL>');
  } else {
    console.log(`📧 Znalazłem użytkownika: ${user.email}`);
  }

  return user;
}

async function createSampleUsers() {
  console.log('👥 Tworzę przykładowych użytkowników...');

  const users = await Promise.all([
    // Rodzina Nowaków
    prisma.user.create({
      data: {
        clerkId: 'sample_piotr_nowak',
        email: '<EMAIL>',
        firstName: 'Piotr',
        lastName: 'Nowak',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_katarzyna_nowak',
        email: '<EMAIL>',
        firstName: 'Katarzyna',
        lastName: 'Nowak',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_jakub_nowak',
        email: '<EMAIL>',
        firstName: 'Jakub',
        lastName: 'Nowak',
        preferredLanguage: 'pl',
      },
    }),

    // Rodzina Wiśniewskich
    prisma.user.create({
      data: {
        clerkId: 'sample_tomasz_wisniewski',
        email: '<EMAIL>',
        firstName: 'Tomasz',
        lastName: 'Wiśniewski',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_magdalena_wisniewska',
        email: '<EMAIL>',
        firstName: 'Magdalena',
        lastName: 'Wiśniewska',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_karolina_wisniewska',
        email: '<EMAIL>',
        firstName: 'Karolina',
        lastName: 'Wiśniewska',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_mateusz_wisniewski',
        email: '<EMAIL>',
        firstName: 'Mateusz',
        lastName: 'Wiśniewski',
        preferredLanguage: 'pl',
      },
    }),

    // Rodzina Dudków
    prisma.user.create({
      data: {
        clerkId: 'sample_andrzej_dudek',
        email: '<EMAIL>',
        firstName: 'Andrzej',
        lastName: 'Dudek',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_ewa_dudek',
        email: '<EMAIL>',
        firstName: 'Ewa',
        lastName: 'Dudek',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_patrycja_dudek',
        email: '<EMAIL>',
        firstName: 'Patrycja',
        lastName: 'Dudek',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_kamil_dudek',
        email: '<EMAIL>',
        firstName: 'Kamil',
        lastName: 'Dudek',
        preferredLanguage: 'pl',
      },
    }),

    // Dodatkowi członkowie rodziny Kowalskich (dla aktualnego użytkownika)
    prisma.user.create({
      data: {
        clerkId: 'sample_anna_kowalska',
        email: '<EMAIL>',
        firstName: 'Anna',
        lastName: 'Kowalska',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_michal_kowalski',
        email: '<EMAIL>',
        firstName: 'Michał',
        lastName: 'Kowalski',
        preferredLanguage: 'pl',
      },
    }),
    prisma.user.create({
      data: {
        clerkId: 'sample_zosia_kowalska',
        email: '<EMAIL>',
        firstName: 'Zosia',
        lastName: 'Kowalska',
        preferredLanguage: 'pl',
      },
    }),
  ]);

  console.log(`✅ Stworzono ${users.length} przykładowych użytkowników`);
  return users;
}

async function createSampleFamilies(currentUser: any, sampleUsers: any[]) {
  console.log('👨‍👩‍👧‍👦 Tworzę przykładowe rodziny...');

  // Organizuj użytkowników według rodzin
  const [
    piotrNowak,
    katarzynaNowak,
    jakubNowak,
    tomaszWisniewski,
    magdalenaWisniewska,
    karolinaWisniewska,
    mateuszWisniewski,
    andrzejDudek,
    ewaDudek,
    patrycjaDudek,
    kamilDudek,
    annaKowalska,
    michalKowalski,
    zosiaKowalska,
  ] = sampleUsers;

  // 1. Rodzina Kowalskich (z aktualnym użytkownikiem jako OWNER)
  const familyKowalski = await prisma.family.create({
    data: {
      name: 'Rodzina Kowalskich',
      description:
        'Nasza kochana rodzina, która organizuje życie codzienne przez wspólne zadania i obowiązki.',
      members: {
        create: [
          {
            userId: currentUser.id,
            role: 'OWNER',
            isAdult: true,
            points: 150,
          },
          {
            userId: annaKowalska.id,
            role: 'PARENT',
            isAdult: true,
            points: 120,
          },
          {
            userId: michalKowalski.id,
            role: 'CHILD',
            isAdult: false,
            points: 85,
          },
          {
            userId: zosiaKowalska.id,
            role: 'CHILD',
            isAdult: false,
            points: 75,
          },
        ],
      },
    },
  });

  // 2. Rodzina Nowaków
  const familyNowak = await prisma.family.create({
    data: {
      name: 'Rodzina Nowaków',
      description:
        'Aktywna rodzina, która lubi spędzać czas razem i wspólnie planować obowiązki domowe.',
      members: {
        create: [
          {
            userId: piotrNowak.id,
            role: 'OWNER',
            isAdult: true,
            points: 200,
          },
          {
            userId: katarzynaNowak.id,
            role: 'PARENT',
            isAdult: true,
            points: 180,
          },
          {
            userId: jakubNowak.id,
            role: 'CHILD',
            isAdult: false,
            points: 95,
          },
        ],
      },
    },
  });

  // 3. Rodzina Wiśniewskich
  const familyWisniewski = await prisma.family.create({
    data: {
      name: 'Rodzina Wiśniewskich',
      description:
        'Duża, wielopokoleniowa rodzina, gdzie każdy ma swoje obowiązki dostosowane do wieku.',
      members: {
        create: [
          {
            userId: tomaszWisniewski.id,
            role: 'OWNER',
            isAdult: true,
            points: 170,
          },
          {
            userId: magdalenaWisniewska.id,
            role: 'PARENT',
            isAdult: true,
            points: 160,
          },
          {
            userId: karolinaWisniewska.id,
            role: 'GUARDIAN',
            isAdult: true,
            points: 110,
          },
          {
            userId: mateuszWisniewski.id,
            role: 'CHILD',
            isAdult: false,
            points: 80,
          },
        ],
      },
    },
  });

  // 4. Rodzina Dudków
  const familyDudek = await prisma.family.create({
    data: {
      name: 'Rodzina Dudków',
      description:
        'Zorganizowana rodzina, która stawia na współpracę i wzajemną pomoc w codziennych obowiązkach.',
      members: {
        create: [
          {
            userId: andrzejDudek.id,
            role: 'OWNER',
            isAdult: true,
            points: 190,
          },
          {
            userId: ewaDudek.id,
            role: 'PARENT',
            isAdult: true,
            points: 175,
          },
          {
            userId: patrycjaDudek.id,
            role: 'CHILD',
            isAdult: false,
            points: 100,
          },
          {
            userId: kamilDudek.id,
            role: 'CHILD',
            isAdult: false,
            points: 60,
          },
        ],
      },
    },
  });

  console.log('✅ Stworzono 4 przykładowe rodziny:');
  console.log(
    `- ${familyKowalski.name} (właściciel: ${currentUser.firstName} ${currentUser.lastName})`
  );
  console.log(`- ${familyNowak.name}`);
  console.log(`- ${familyWisniewski.name}`);
  console.log(`- ${familyDudek.name}`);

  return [familyKowalski, familyNowak, familyWisniewski, familyDudek];
}

async function createSampleTasks(families: any[]) {
  console.log('📋 Tworzę przykładowe zadania na czerwiec-lipiec 2025...');

  const taskTemplates = [
    // Kuchnia i gotowanie
    {
      title: 'Przygotuj śniadanie',
      description: 'Przygotuj śniadanie dla całej rodziny',
      points: 3,
      weight: 'NORMAL',
    },
    {
      title: 'Umyj naczynia po obiedzie',
      description: 'Umyj wszystkie naczynia i sztućce',
      points: 3,
      weight: 'NORMAL',
    },
    {
      title: 'Ugotuj obiad',
      description: 'Przygotuj obiad dla rodziny',
      points: 5,
      weight: 'HEAVY',
    },
    {
      title: 'Posprzątaj kuchnię',
      description: 'Wyczyść blaty, zmyj zlewozmywak',
      points: 3,
      weight: 'NORMAL',
    },
    {
      title: 'Zrób zakupy spożywcze',
      description: 'Kup produkty według listy zakupów',
      points: 5,
      weight: 'HEAVY',
    },

    // Sprzątanie
    {
      title: 'Odkurz salon',
      description: 'Odkurz cały salon i uporządkuj',
      points: 3,
      weight: 'NORMAL',
    },
    {
      title: 'Umyj łazienkę',
      description: 'Wyczyść wannę, umywalkę i lustro',
      points: 5,
      weight: 'HEAVY',
    },
    {
      title: 'Pościel łóżka',
      description: 'Zmień pościel i uporządkuj sypialnię',
      points: 1,
      weight: 'LIGHT',
    },
    {
      title: 'Wyprasuj ubrania',
      description: 'Wyprasuj ubrania z kosza',
      points: 3,
      weight: 'NORMAL',
    },
    {
      title: 'Wynieś śmieci',
      description: 'Wynieś śmieci do kontenera',
      points: 1,
      weight: 'LIGHT',
    },

    // Opieka nad dziećmi
    {
      title: 'Pomóż z pracą domową',
      description: 'Pomóż dziecku z lekcjami',
      points: 5,
      weight: 'HEAVY',
    },
    {
      title: 'Zabierz dzieci do szkoły',
      description: 'Odprowadź dzieci do szkoły',
      points: 1,
      weight: 'LIGHT',
    },
    {
      title: 'Przygotuj lunch dla dziecka',
      description: 'Spakuj lunch do szkoły',
      points: 1,
      weight: 'LIGHT',
    },
    {
      title: 'Kąpiel dziecka',
      description: 'Wykąp dziecko przed snem',
      points: 3,
      weight: 'NORMAL',
    },

    // Pranie
    {
      title: 'Włóż pranie do pralki',
      description: 'Przygotuj i włóż pranie',
      points: 1,
      weight: 'LIGHT',
    },
    { title: 'Rozwieś pranie', description: 'Rozwieś wyprane ubrania', points: 1, weight: 'LIGHT' },
    {
      title: 'Składanie czystego prania',
      description: 'Posortuj i złóż czyste ubrania',
      points: 3,
      weight: 'NORMAL',
    },

    // Ogród i zewnątrz
    {
      title: 'Podlej rośliny',
      description: 'Podlej wszystkie rośliny w domu i na balkonie',
      points: 1,
      weight: 'LIGHT',
    },
    {
      title: 'Wyczyść samochód',
      description: 'Umyj samochód z zewnątrz i wewnątrz',
      points: 10,
      weight: 'CRITICAL',
    },
    {
      title: 'Posprzątaj garaż',
      description: 'Uporządkuj garaż i narzędzia',
      points: 5,
      weight: 'HEAVY',
    },

    // Zwierzęta
    { title: 'Wyprowadź psa', description: 'Idź z psem na spacer', points: 1, weight: 'LIGHT' },
    {
      title: 'Nakarm zwierzęta',
      description: 'Nakarm i napoj zwierzęta domowe',
      points: 1,
      weight: 'LIGHT',
    },
    {
      title: 'Wyczyść kuwetę kota',
      description: 'Wyczyść i uzupełnij żwirek',
      points: 3,
      weight: 'NORMAL',
    },

    // Inne
    {
      title: 'Zapłać rachunki',
      description: 'Opłać wszystkie bieżące rachunki',
      points: 3,
      weight: 'NORMAL',
    },
    {
      title: 'Uporządkuj biurko',
      description: 'Posprzątaj miejsce pracy',
      points: 1,
      weight: 'LIGHT',
    },
    {
      title: 'Backup danych komputera',
      description: 'Zrób kopię zapasową ważnych plików',
      points: 3,
      weight: 'NORMAL',
    },
  ];

  const frequencies = ['ONCE', 'DAILY', 'WEEKLY', 'MONTHLY'];
  const statuses = ['ACTIVE'];

  // Dates for June and July 2025
  const startDate = new Date('2025-06-01');
  const endDate = new Date('2025-07-31');

  for (const family of families) {
    console.log(`📝 Tworzę zadania dla rodziny: ${family.name}`);

    // Get family members
    const familyMembers = await prisma.familyMember.findMany({
      where: { familyId: family.id },
      include: { user: true },
    });

    // Generate 20-50 tasks per family
    const taskCount = Math.floor(Math.random() * 31) + 20; // 20-50

    for (let i = 0; i < taskCount; i++) {
      const template = taskTemplates[Math.floor(Math.random() * taskTemplates.length)];
      const assignee = familyMembers[Math.floor(Math.random() * familyMembers.length)];
      const frequency = frequencies[Math.floor(Math.random() * frequencies.length)];
      const status = statuses[0]; // Always ACTIVE

      // Random date between June 1 and July 31, 2025
      const randomDate = new Date(
        startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
      );

      // Due date is 1-7 days after creation
      const dueDate = new Date(
        randomDate.getTime() + (Math.random() * 7 + 1) * 24 * 60 * 60 * 1000
      );

      try {
        const task = await prisma.task.create({
          data: {
            title: template.title,
            description: template.description,
            points: template.points,
            weight: template.weight as any,
            frequency: frequency as any,
            status: status as any,
            familyId: family.id,
            assignedBy: familyMembers[0].id, // First member (owner) creates tasks
            assignedAt: randomDate,
            dueDate: dueDate,
            createdAt: randomDate,
            updatedAt: randomDate,
          },
        });

        // Utworz nowe przypisanie używając nowego systemu
        await prisma.taskAssignment.create({
          data: {
            taskId: task.id,
            memberId: assignee.id,
            assignedBy: familyMembers[0].id,
            assignedAt: randomDate,
          },
        });
      } catch (error) {
        console.error(`Błąd przy tworzeniu zadania: ${template.title}`, error);
      }
    }

    console.log(`✅ Stworzono ${taskCount} zadań dla rodziny ${family.name}`);
  }

  const totalTasks = await prisma.task.count();
  console.log(`✅ Łącznie stworzono ${totalTasks} zadań dla wszystkich rodzin`);
}

main()
  .catch(e => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
