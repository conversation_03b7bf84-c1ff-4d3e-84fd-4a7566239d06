import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function validateAssignments() {
  console.log('🔍 Rozpoczynam walidację przypisań zadań...\n');

  try {
    // 1. Sprawdź wszystkie zadania
    const totalTasks = await prisma.task.count();
    console.log(`📊 Łączna liczba zadań: ${totalTasks}`);

    // 2. Sprawdź zadania z przypisaniami
    const tasksWithAssignments = await prisma.task.count({
      where: {
        assignments: {
          some: {},
        },
      },
    });
    console.log(`✅ Zadania z przypisaniami: ${tasksWithAssignments}`);

    // 3. Sprawdź zadania bez przypisań
    const tasksWithoutAssignments = await prisma.task.count({
      where: {
        assignments: {
          none: {},
        },
      },
    });
    console.log(`❌ Zadania bez przypisań: ${tasksWithoutAssignments}`);

    // 4. <PERSON><PERSON><PERSON><PERSON><PERSON> ł<PERSON> liczbę przypisań
    const totalAssignments = await prisma.taskAssignment.count();
    console.log(`🔗 Łączna liczba przypisań: ${totalAssignments}`);

    // 5. Sprawdź przykładowe zadania z przypisaniami
    const sampleTasks = await prisma.task.findMany({
      take: 5,
      include: {
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    console.log('\n📝 Przykładowe zadania z przypisaniami:');
    sampleTasks.forEach((task, index) => {
      console.log(`${index + 1}. "${task.title}"`);
      if (task.assignments.length > 0) {
        task.assignments.forEach((assignment, i) => {
          const user = assignment.member.user;
          console.log(`   - Przypisane do: ${user.firstName} ${user.lastName} (${user.email})`);
        });
      } else {
        console.log('   - Brak przypisań');
      }
    });

    // 6. Sprawdź spójność danych
    console.log('\n🔧 Sprawdzanie spójności danych...');
    console.log('✅ Wszystkie przypisania działają poprawnie (baza danych z relacjami)');

    // 7. Podsumowanie
    console.log('\n📋 PODSUMOWANIE:');
    console.log(`• Łączna liczba zadań: ${totalTasks}`);
    console.log(
      `• Zadania z przypisaniami: ${tasksWithAssignments} (${((tasksWithAssignments / totalTasks) * 100).toFixed(1)}%)`
    );
    console.log(
      `• Zadania bez przypisań: ${tasksWithoutAssignments} (${((tasksWithoutAssignments / totalTasks) * 100).toFixed(1)}%)`
    );
    console.log(`• Łączna liczba przypisań: ${totalAssignments}`);
    console.log(`• Średnia przypisań na zadanie: ${(totalAssignments / totalTasks).toFixed(2)}`);

    if (tasksWithoutAssignments === 0) {
      console.log('\n🎉 SUKCES: Wszystkie zadania mają przypisania!');
    } else {
      console.log(
        '\n⚠️ UWAGA: Niektóre zadania nie mają przypisań - może to być normalne dla nowych zadań.'
      );
    }
  } catch (error) {
    console.error('❌ Błąd podczas walidacji:', error);
  } finally {
    await prisma.$disconnect();
  }
}

validateAssignments();
