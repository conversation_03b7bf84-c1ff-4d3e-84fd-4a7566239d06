# 🔄 Zadania Cykliczne - Dokumentacja

## Przegląd

System zadań cyklicznych w aplikacji umożliwia tworzenie zadań powtarzających się w regularnych interwałach. Zadania są prezentowane w kalendarzu jako seria markerów zamiast ciągłych pasków, co zapewnia lepszą czytelność i rozróżnienie między dniami aktywnymi a przerwami.

## 🎯 Kluczowe Funkcjonalności

### ✅ Zaimplementowane

- **Tworzenie zadań cyklicznych** z prostymi interwałami (codziennie, co 2 dni, tygodniowo, etc.)
- **Wizualna prezentacja** jako seria kolorowych markerów w kalendarzu
- **System kolorów** - różne kolory dla różnych typów cykliczności
- **Tooltips** z pełnymi informacjami o zadaniu i jego wzorcu
- **<PERSON><PERSON><PERSON><PERSON> zada<PERSON>** z opcjami: "tylko to wystąpienie" vs "całą serię"
- **Cache system** dla optymalizacji wydajności
- **Zaawansowane RRULE** dla niestandardowych wzorców

## 🎨 Design System

### Kolory Cykliczności

| Częstotliwość  | Kolor           | Kod       | Zastosowanie       |
| -------------- | --------------- | --------- | ------------------ |
| Codziennie     | 🟢 Zielony      | `#10b981` | Zadania dzienne    |
| Tygodniowo     | 🔵 Niebieski    | `#3b82f6` | Zadania tygodniowe |
| Miesięcznie    | 🟣 Fioletowy    | `#8b5cf6` | Zadania miesięczne |
| Rocznie        | 🟠 Pomarańczowy | `#f59e0b` | Zadania roczne     |
| Niestandardowe | 🟦 Indygo       | `#6366f1` | Zadania z RRULE    |

### Ikony

- **Clock** - zadania codzienne
- **Calendar** - zadania tygodniowe/miesięczne
- **RotateCcw** - zadania miesięczne (rotacja)
- **Repeat** - zadania niestandardowe

## 🏗️ Architektura

### Model Danych

Wykorzystuje istniejący model `Task` z polami:

```prisma
model Task {
  frequency         TaskFrequency    @default(ONCE)    // DAILY, WEEKLY, MONTHLY, YEARLY, CUSTOM
  recurrenceRule    String?          // RRULE format
  startDate         DateTime?        // Data rozpoczęcia
  endDate           DateTime?        // Data zakończenia
  recurrenceEnd     DateTime?        // Koniec cyklu
  // ... pozostałe pola
}
```

### Kluczowe Komponenty

#### 1. **RecurringTaskMarker**

```typescript
<RecurringTaskMarker
  task={calendarTask}
  date={currentDate}
  size="md"
  showTitle={true}
  onClick={handleTaskClick}
/>
```

**Funkcje:**

- Wyświetla zadanie cykliczne jako marker
- Różne rozmiary (sm, md, lg)
- Tooltip z informacjami o cykliczności
- Obsługa kliknięć

#### 2. **RecurringPatternIndicator**

```typescript
<RecurringPatternIndicator
  frequency="DAILY"
  interval={2}
  showLabel={true}
/>
```

**Funkcje:**

- Wizualne przedstawienie wzorca cykliczności
- Kompaktowy i szczegółowy wariant
- Wsparcie dla wszystkich typów częstotliwości

#### 3. **EditRecurringTaskDialog**

```typescript
<EditRecurringTaskDialog
  isOpen={true}
  task={recurringTask}
  onEditOccurrence={handleEditOccurrence}
  onEditSeries={handleEditSeries}
  onClose={handleClose}
/>
```

**Funkcje:**

- Dialog wyboru: "tylko wystąpienie" vs "cała seria"
- Ostrzeżenia o wpływie na przyszłe wystąpienia
- Intuicyjny UX

### Utility Functions

#### 1. **recurring-tasks.ts**

```typescript
// Generowanie dat wystąpień
generateRecurringDates(options, startDate, endDate): TaskOccurrence[]

// Sprawdzanie aktywności w danym dniu
isTaskActiveOnDate(options, date): boolean

// Następne wystąpienie
getNextTaskOccurrence(options, afterDate): Date | null
```

#### 2. **calendar-helpers.ts**

```typescript
// Konwersja Task → CalendarTask
taskToCalendarTask(task): CalendarTask

// Łączenie jednorazowych i cyklicznych
combineTasksForCalendar(oneTimeTasks, recurringTasks, range): CalendarTask[]

// Filtrowanie zadań dla dnia
getTasksForDay(tasks, day): CalendarTask[]
```

#### 3. **recurring-tasks-cache.ts**

```typescript
// Cache z TTL dla różnych częstotliwości
RecurringTasksCache.getOccurrences(taskId, options, range): TaskOccurrence[]

// Invalidacja cache
RecurringTasksCache.invalidateTask(taskId): void

// Preloading popularnych zakresów
RecurringTasksCache.preloadCache(tasks, ranges): Promise<void>
```

### Service Layer

#### **RecurringTaskService**

```typescript
class RecurringTaskService {
  // CRUD operations
  static createRecurringTask(data, prisma): Promise<Task>;
  static updateRecurringTask(taskId, data, prisma, updateFutureOnly): Promise<Task>;
  static deleteRecurringTask(taskId, prisma, deleteFutureOnly): Promise<void>;

  // Pobieranie z wystąpieniami
  static getRecurringTasksForFamily(familyId, range, prisma): Promise<CalendarTask[]>;

  // Statystyki
  static getRecurringTaskStats(taskId, prisma): Promise<RecurringTaskStats>;

  // Wyjątki w serii
  static createRecurrenceException(taskId, date, prisma): Promise<void>;
}
```

## 🎛️ Użycie

### Tworzenie Zadania Cyklicznego

```typescript
// 1. W TaskForm
const formData = {
  title: 'Codzienne sprzątanie',
  frequency: 'DAILY',
  rruleInterval: 3, // co 3 dni
  startDate: new Date(),
  endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 dni
};

// 2. System automatycznie generuje RRULE: "FREQ=DAILY;INTERVAL=3"
```

### Wyświetlanie w Kalendarzu

```typescript
// 1. Pobierz zadania dla zakresu
const tasks = await RecurringTaskService.getRecurringTasksForFamily(
  familyId,
  startDate,
  endDate,
  prisma
);

// 2. Renderuj w MobileCalendarView
tasks.map(task => {
  if (task.isRecurring) {
    return <RecurringTaskMarker task={task} date={day} />;
  }
  return <RegularTaskCard task={task} />;
});
```

### Edycja Zadania Cyklicznego

```typescript
// 1. Kliknięcie w marker otwiera dialog
<EditRecurringTaskDialog
  task={clickedTask}
  onEditOccurrence={async (task) => {
    // Tworzy nowe zadanie jednorazowe dla tego dnia
    await createTask({
      ...task,
      frequency: 'ONCE'
    });
  }}
  onEditSeries={async (originalTaskId) => {
    // Otwiera formularz edycji oryginalnego zadania
    openTaskForm(originalTaskId);
  }}
/>
```

## 🚀 Performance

### Cache System

- **TTL** zależny od częstotliwości:
  - Dzienne: 7.5-15 min
  - Tygodniowe: 30 min
  - Miesięczne: 1h
  - Roczne: 2h

- **Inteligentne** cache'owanie:
  - Tylko sensowne zakresy (≤365 dni)
  - Cleanup expired entries
  - Hit rate tracking

### Optymalizacje

1. **Lazy generation** - wystąpienia generowane on-demand
2. **Range-based queries** - tylko potrzebne zakresy
3. **Preloading** - popularne zakresy w tle
4. **Debounced updates** - grupowanie zmian

## 🧪 Testowanie

### Testy Jednostkowe

```bash
npm test recurring-tasks.test.ts
```

**Coverage:**

- ✅ Generowanie dat dla wszystkich częstotliwości
- ✅ Interwały i zakresy dat
- ✅ Edge cases (invalid dates, zero intervals)
- ✅ RRULE parsing i generowanie
- ✅ Cache behavior i TTL

### Testy Integracyjne

```bash
npm test recurring-tasks-integration.test.tsx
```

**Coverage:**

- ✅ TaskForm z cyklicznością
- ✅ RecurringTaskMarker rendering
- ✅ EditRecurringTaskDialog workflow
- ✅ Calendar integration
- ✅ Service layer operations

## 📖 Przykłady Użycia

### 1. Zadanie Codzienne

```typescript
const dailyTask = {
  title: 'Mycie zębów',
  frequency: 'DAILY',
  interval: 1,
  startDate: new Date(),
  color: '#10b981',
};
// RRULE: "FREQ=DAILY"
```

### 2. Zadanie co 3 Dni

```typescript
const everyThreeDays = {
  title: 'Podlewanie roślin',
  frequency: 'DAILY',
  interval: 3,
  startDate: new Date(),
  color: '#3b82f6',
};
// RRULE: "FREQ=DAILY;INTERVAL=3"
```

### 3. Zadanie Tygodniowe

```typescript
const weeklyTask = {
  title: 'Zakupy spożywcze',
  frequency: 'WEEKLY',
  interval: 1,
  startDate: new Date(), // Poniedziałek
  color: '#8b5cf6',
};
// RRULE: "FREQ=WEEKLY"
```

### 4. Zadanie Zaawansowane (RRULE)

```typescript
const complexTask = {
  title: 'Spotkanie zespołu',
  frequency: 'CUSTOM',
  recurrenceRule: 'FREQ=WEEKLY;BYDAY=MO,WE,FR',
  startDate: new Date(),
  color: '#6366f1',
};
```

## 🔧 Konfiguracja

### Environment Variables

```env
# Cache settings
RECURRING_TASKS_CACHE_TTL=1800000  # 30 min default
RECURRING_TASKS_MAX_RANGE_DAYS=365  # Max range to cache
RECURRING_TASKS_MAX_CACHE_ENTRIES=100  # Max cache entries
```

### Feature Flags

```typescript
const features = {
  enableRecurringTasks: true,
  enableAdvancedRRULE: true,
  enableCaching: true,
  enablePreloading: false, // Experimental
};
```

## 🐛 Debugging

### Cache Debug Info

```typescript
const debugInfo = RecurringTasksCache.getDebugInfo();
console.log(`Hit rate: ${debugInfo.stats.hitRate}%`);
console.log(`Entries: ${debugInfo.entries.length}`);
```

### Task Generation Debug

```typescript
const options = { startDate, frequency: 'DAILY', interval: 2 };
const occurrences = generateRecurringDates(options, start, end);
console.log(`Generated ${occurrences.length} occurrences`);
```

## 🚨 Znane Ograniczenia

1. **Timezone handling** - obecnie używa lokalnej strefy czasowej
2. **RRULE complexity** - wsparcie dla podstawowych wzorców
3. **Historical data** - ograniczone wsparcie dla przeszłych zmian
4. **Performance** - duże zakresy dat (>1 rok) mogą być wolne

## 🔮 Przyszłe Rozszerzenia

- [ ] **Time zones** - pełne wsparcie dla stref czasowych
- [ ] **RRULE editor** - wizualny edytor zaawansowanych reguł
- [ ] **Conflict detection** - wykrywanie nakładających się zadań
- [ ] **Smart scheduling** - automatyczne sugerowanie terminów
- [ ] **Bulk operations** - masowe operacje na seriach zadań
- [ ] **History tracking** - śledzenie zmian w zadaniach cyklicznych

---

**Ostatnia aktualizacja:** 2025-07-01  
**Wersja:** 1.0.0  
**Autor:** Claude Code Implementation
