# Przewodnik mapowania kolorów - Shadcn UI Theme

## Konfiguracja tematu

- **accentColor**: `violet`
- **grayColor**: `sand`
- **panelBackground**: `solid`
- **radius**: `large`

## Mapowanie standardowych kolorów Tailwind → Shadcn Theme

### Purple/Violet (Główny kolor akcentowy)

```
bg-purple-50    → bg-violet-2
bg-purple-100   → bg-violet-3
bg-purple-200   → bg-violet-4
bg-purple-300   → bg-violet-5
bg-purple-400   → bg-violet-6
bg-purple-500   → bg-violet-7
bg-purple-600   → bg-violet-8  (główny accent)
bg-purple-700   → bg-violet-9
bg-purple-800   → bg-violet-10
bg-purple-900   → bg-violet-11

text-purple-50   → text-violet-2
text-purple-100  → text-violet-3
text-purple-200  → text-violet-4
text-purple-300  → text-violet-5
text-purple-400  → text-violet-6
text-purple-500  → text-violet-7
text-purple-600  → text-violet-8
text-purple-700  → text-violet-9
text-purple-800  → text-violet-10
text-purple-900  → text-violet-11

border-purple-*  → border-violet-*
```

### Green (Success/Positive)

```
bg-green-50     → bg-green-2
bg-green-100    → bg-green-3
bg-green-500    → bg-green-8
bg-green-600    → bg-green-9  (semantic success)
text-green-600  → text-green-11 lub color="green"
```

### Red (Destructive/Error)

```
bg-red-50       → bg-red-2
bg-red-100      → bg-red-3
bg-red-500      → bg-red-8
bg-red-600      → bg-red-9    (semantic destructive)
text-red-600    → text-red-11 lub color="red"
```

### Blue (Informational)

```
bg-blue-50      → bg-blue-2
bg-blue-100     → bg-blue-3
bg-blue-500     → bg-blue-8
bg-blue-600     → bg-blue-9   (semantic info)
text-blue-500   → text-blue-11 lub color="blue"
```

### Orange/Yellow (Warning)

```
bg-orange-100   → bg-amber-3
bg-orange-500   → bg-amber-8
bg-orange-600   → bg-amber-9  (semantic warning)
bg-yellow-100   → bg-amber-3
text-orange-600 → text-amber-11 lub color="amber"
```

### Gray/Neutral (Sand theme)

```
bg-gray-50      → bg-sand-2
bg-gray-100     → bg-sand-3
bg-gray-200     → bg-sand-4
bg-gray-300     → bg-sand-5
bg-gray-400     → bg-sand-6
bg-gray-500     → bg-sand-7
bg-gray-600     → bg-sand-8
bg-gray-700     → bg-sand-9
bg-gray-800     → bg-sand-10
bg-gray-900     → bg-sand-11
```

## Shadcn UI Components Color Props

### Podstawowe komponenty z color prop

```jsx
// Zamiast bg-purple-* użyj:
<Button color="violet">...</Button>
<Badge color="violet">...</Badge>
<Text color="violet">...</Text>

// Semantic colors:
<Button color="green">Success</Button>     // zamiast bg-green-*
<Button color="red">Error</Button>         // zamiast bg-red-*
<Button color="amber">Warning</Button>     // zamiast bg-orange-*/bg-yellow-*
<Button color="blue">Info</Button>         // zamiast bg-blue-*
```

### Gradients → Shadcn patterns

```jsx
// Zamiast: bg-gradient-to-r from-purple-50 to-purple-100
<Box style={{
  background: 'linear-gradient(to right, var(--violet-2), var(--violet-3))'
}}>

// Lub lepiej - użyj Shadcn Card/Box z odpowiednim color
<Card variant="surface">
```

## Hover states

```
hover:bg-purple-700 → hover:bg-violet-9
hover:bg-blue-50    → hover:bg-blue-2
hover:bg-green-100  → hover:bg-green-3
```

## Focus states

```
focus:bg-blue-100   → focus:bg-blue-3
focus:ring-blue-500 → focus:ring-violet-8
```

## Przykłady transformacji

### Przed (Tailwind):

```jsx
<div className="bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200">
  <span className="text-green-600">Success</span>
  <button className="bg-blue-500 text-white hover:bg-blue-600">Info Button</button>
</div>
```

### Po (Shadcn Theme):

```jsx
<Card color="violet" variant="surface">
  <Text color="green">Success</Text>
  <Button color="blue">Info Button</Button>
</Card>
```

## Zalecenia

1. **Preferuj Shadcn komponenty** z color prop zamiast Tailwind bg-\* classes
2. **Używaj semantic colors** (green=success, red=error, amber=warning, blue=info)
3. **Violet jest głównym accent color** - używaj dla primary actions
4. **Sand jest głównym gray color** - używaj dla neutral elements
5. **Gradients** zamień na pojedyncze kolory lub shadcn patterns
6. **Testuj dark mode** - shadcn automatycznie zarządza dark variants
