## Raport z Analizy Projektu: FamilyTasks

### 1. <PERSON>st<PERSON>p

Niniejszy raport przedstawia szczegółową analizę struktury i funkcjonalności komponentów w projekcie FamilyTasks. Celem analizy było zidentyfikowanie kluczowych komponentów, zrozumienie ich zastosowania oraz sposobu, w jaki współpracują ze sobą w ramach aplikacji.

### 2. Struktura Projektu (Komponenty)

Projekt FamilyTasks jest zbudowany w oparciu o komponentową architekturę, z wyraźnym podziałem na moduły funkcjonalne. Komponenty UI są wydzielone do osobnego katalogu, co zapewnia spójność wizualną i ułatwia zarządzanie.

Główne kategorie komponentów zlokalizowane w `src/components`:

- `auth`: Komponenty związane z uwierzytelnianiem i profilem użytkownika.
- `calendar`: Komponenty do zarządzania i wyświetlania kalendarza zadań.
- `family`: Komponenty do zarządzania rodzinami i ich członkami.
- `history`: Komponenty do wyświetlania historii zadań.
- `i18n`: Komponenty do obsługi internacjonalizacji.
- `landing`: Komponenty dla stron lądowania (landing pages).
- `layout`: Komponenty do definiowania układów stron (layoutów).
- `mobile`: Komponenty zoptymalizowane pod kątem urządzeń mobilnych.
- `pricing`: Komponenty do prezentacji planów cenowych.
- `stats`: Komponenty do wyświetlania statystyk i wykresów.
- `suspensions`: Komponenty do zarządzania zawieszeniami zadań.
- `tasks`: Komponenty do zarządzania zadaniami.
- `theme`: Komponenty do zarządzania motywem aplikacji.
- `ui`: Podstawowe komponenty interfejsu użytkownika (Shadcn UI).

### 3. Szczegółowa Analiza Komponentów

Poniżej przedstawiono szczegółową analizę kluczowych komponentów, pogrupowanych według ich funkcjonalności.

#### 3.1. Komponenty Uwierzytelniania (`src/components/auth`)

- **`AuthGuard.tsx`**:
  - **Funkcjonalność**: Chroni trasy aplikacji, przekierowując niezalogowanych użytkowników na stronę logowania. Wyświetla spinner ładowania podczas sprawdzania statusu uwierzytelnienia.
  - **Zastosowanie**: Zapewnia, że tylko uwierzytelnieni użytkownicy mają dostęp do chronionych części aplikacji.
  - **Użycie**: Otacza chronione ścieżki lub komponenty.
- **`SignInButton.tsx`**:
  - **Funkcjonalność**: Wyświetla przycisk "Zaloguj się" lub "Wyloguj się" w zależności od statusu uwierzytelnienia użytkownika. Wykorzystuje komponenty z biblioteki `@clerk/nextjs`.
  - **Zastosowanie**: Umożliwia użytkownikom łatwe logowanie i wylogowywanie się.
  - **Użycie**: W nagłówku, stopce lub na stronach uwierzytelniania.
- **`UserProfile.tsx`**:
  - **Funkcjonalność**: Wyświetla przycisk profilu użytkownika (z Clerk) oraz opcjonalnie imię, nazwisko i adres e-mail użytkownika.
  - **Zastosowanie**: Zapewnia szybki dostęp do ustawień profilu użytkownika.
  - **Użycie**: W nagłówku, pasku bocznym, menu mobilnym.

#### 3.2. Komponenty Kalendarza (`src/components/calendar`)

- **`CalendarDashboard.tsx`**:
  - **Funkcjonalność**: Główny komponent pulpitu kalendarza. Zarządza stanem kalendarza, obsługuje interakcje użytkownika (kliknięcia, przeciąganie zadań), koordynuje otwieranie modali (ustawienia, szczegóły zadania, tworzenie/edycja zadania).
  - **Zastosowanie**: Centralny punkt widoku kalendarza.
  - **Użycie**: Prawdopodobnie główny komponent renderowany na stronie kalendarza.
- **`CalendarFilters.tsx`**:
  - **Funkcjonalność**: Umożliwia filtrowanie zadań w kalendarzu według członka rodziny, statusu zadania, kategorii zadania oraz opcji wyświetlania ukończonych zadań. Posiada wersję kompaktową.
  - **Zastosowanie**: Ułatwia użytkownikowi przeglądanie zadań w kalendarzu.
  - **Użycie**: W panelu bocznym kalendarza lub jako część interfejsu filtrowania.
- **`CalendarNavigation.tsx`**:
  - **Funkcjonalność**: Komponent nawigacyjny kalendarza. Umożliwia przechodzenie między datami, zmianę widoku kalendarza (miesiąc, tydzień, dzień, agenda) oraz wyświetlanie aktualnej daty. Opcjonalnie zawiera przyciski do tworzenia zadań i ustawień.
  - **Zastosowanie**: Nawigacja w kalendarzu.
  - **Użycie**: W `CalendarDashboard.tsx` lub `MobileCalendarView.tsx`.
- **`CalendarSettingsModal.tsx`**:
  - **Funkcjonalność**: Modal do konfiguracji ustawień kalendarza (domyślny widok, pierwszy dzień tygodnia, format czasu, pokazywanie weekendów/ukończonych zadań, schemat kolorów, integracje).
  - **Zastosowanie**: Personalizacja widoku kalendarza.
  - **Użycie**: Wywoływany z `CalendarDashboard.tsx` lub przycisku ustawień.
- **`CalendarView.tsx`**:
  - **Funkcjonalność**: Warstwa kompatybilności, która przekazuje wszystkie propsy do `MobileCalendarView.tsx`.
  - **Zastosowanie**: Utrzymanie spójności API, podczas gdy rzeczywiste renderowanie odbywa się w komponencie mobilnym.
  - **Użycie**: W `CalendarDashboard.tsx`.
- **`MobileCalendarView.tsx`**:
  - **Funkcjonalność**: Główny komponent renderujący widoki kalendarza (Miesiąc, Tydzień, Dzień, Agenda) zoptymalizowany pod kątem urządzeń mobilnych, w tym obsługę gestów przeciągania. Wyświetla zadania, umożliwia interakcję i wybór slotów.
  - **Zastosowanie**: Renderowanie kalendarza na urządzeniach mobilnych.
  - **Użycie**: W `CalendarView.tsx`.
- **`TaskDetailsModal.tsx`**:
  - **Funkcjonalność**: Modal do wyświetlania szczegółowych informacji o zadaniu z kalendarza. Umożliwia edycję, usunięcie i oznaczenie zadania jako ukończone.
  - **Zastosowanie**: Prezentacja szczegółów zadania.
  - **Użycie**: Wywoływany z `CalendarDashboard.tsx` po kliknięciu zadania.

#### 3.3. Komponenty Rodziny (`src/components/family`)

- **`ChangeMemberRoleDialog.tsx`**:
  - **Funkcjonalność**: Modal do zmiany roli członka rodziny i statusu "dorosły". Waliduje dane, wysyła żądanie PATCH do API, wyświetla ostrzeżenia.
  - **Zastosowanie**: Zarządzanie uprawnieniami członków rodziny.
  - **Użycie**: Prawdopodobnie z `FamilyMembersList.tsx` lub `FamilyDashboard.tsx`.
- **`CreateFamilyDialog.tsx`**:
  - **Funkcjonalność**: Modal do tworzenia nowej rodziny (nazwa, opis). Waliduje dane, wysyła żądanie POST do API.
  - **Zastosowanie**: Inicjowanie nowej grupy rodzinnej.
  - **Użycie**: Z panelu nawigacyjnego lub strony głównej.
- **`DeleteMemberDialog.tsx`**:
  - **Funkcjonalność**: Modal do potwierdzenia i wykonania usunięcia członka rodziny. Informuje o konsekwencjach.
  - **Zastosowanie**: Bezpieczne usuwanie członków rodziny.
  - **Użycie**: Prawdopodobnie z `FamilyMembersList.tsx` lub `FamilyDashboard.tsx`.
- **`FamilyDashboard.tsx`**:
  - **Funkcjonalność**: Główny komponent pulpitu nawigacyjnego dla rodziny. Wyświetla kluczowe statystyki (punkty, zadania, członkowie, postęp, zawieszenia), listę moich zadań, ranking rodziny i ostatnią aktywność.
  - **Zastosowanie**: Centralny punkt informacyjny dla członków rodziny.
  - **Użycie**: Główny komponent renderowany na stronie rodziny (`/families/[familyId]`).
- **`FamilyLayout.tsx`**:
  - **Funkcjonalność**: Komponent układu dla stron związanych z rodziną. Tworzy dwukolumnowy układ z paskiem bocznym (sidebar) i główną zawartością. Wyświetla informacje o rodzinie i bieżącym użytkowniku, nawigację.
  - **Zastosowanie**: Spójny interfejs użytkownika dla stron rodziny.
  - **Użycie**: Otacza wszystkie strony w `src/app/(dashboard)/families/[familyId]`.
- **`FamilyMembersList.tsx`**:
  - **Funkcjonalność**: Wyświetla listę członków rodziny z ich szczegółami (rola, punkty, zadania) i opcjami zarządzania (zmiana roli, usunięcie).
  - **Zastosowanie**: Przeglądanie i zarządzanie członkami rodziny.
  - **Użycie**: Na stronie zarządzania członkami rodziny (`/families/[familyId]/members`).
- **`FamilySwitcher.tsx`**:
  - **Funkcjonalność**: Umożliwia przełączanie się między różnymi rodzinami, do których należy użytkownik. Wyświetla listę rodzin z ich statystykami.
  - **Zastosowanie**: Szybka nawigacja między rodzinami.
  - **Użycie**: W nagłówku aplikacji lub w pasku bocznym.
- **`InviteMemberDialog.tsx`**:
  - **Funkcjonalność**: Modal do wysyłania zaproszeń do nowych członków rodziny (e-mail, rola, status dorosły). Waliduje dane, wysyła żądanie POST do API.
  - **Zastosowanie**: Rozszerzanie rodziny o nowych członków.
  - **Użycie**: Na stronie zarządzania członkami rodziny.
- **`SentInvitationsList.tsx`**:
  - **Funkcjonalność**: Wyświetla listę wysłanych zaproszeń do rodziny. Monitoruje status zaproszeń, umożliwia ponowne wysyłanie i anulowanie.
  - **Zastosowanie**: Monitorowanie i zarządzanie wysłanymi zaproszeniami.
  - **Użycie**: Na stronie zarządzania członkami rodziny lub dedykowanej stronie zaproszeń.
- **`UserInvitationsDialog.tsx`**:
  - **Funkcjonalność**: Modal wyświetlający zaproszenia do rodzin, które użytkownik otrzymał. Umożliwia akceptację lub odrzucenie zaproszenia.
  - **Zastosowanie**: Zarządzanie otrzymanymi zaproszeniami.
  - **Użycie**: W nagłówku aplikacji lub w profilu użytkownika.

#### 3.4. Komponenty Historii (`src/components/history`)

- **`TaskHistory.tsx`**:
  - **Funkcjonalność**: Wyświetla historię ukończonych zadań. Umożliwia wyszukiwanie, filtrowanie według kategorii i sortowanie. Prezentuje szczegóły zadań i podsumowanie statystyk.
  - **Zastosowanie**: Przeglądanie i analizowanie historii wykonanych zadań.
  - **Użycie**: Na stronie historii zadań (`/families/[familyId]/history`).

#### 3.5. Komponenty Internacjonalizacji (`src/components/i18n`)

- **`LanguageSwitcher.tsx`**:
  - **Funkcjonalność**: Umożliwia użytkownikowi przełączanie języka interfejsu aplikacji. Dynamicznie zmienia ścieżkę URL.
  - **Zastosowanie**: Internacjonalizacja aplikacji.
  - **Użycie**: W nagłówku, stopce lub ustawieniach użytkownika.
- **`LocalizedText.tsx`**:
  - **Funkcjonalność**: Komponent do wyświetlania przetłumaczonego tekstu. Wykorzystuje `next-intl` do pobierania tłumaczeń. Zawiera pomocnicze komponenty dla nagłówków, paragrafów i przycisków.
  - **Zastosowanie**: Podstawowy komponent do obsługi internacjonalizacji tekstu w całej aplikacji.
  - **Użycie**: W większości komponentów UI wyświetlających tekst.

#### 3.6. Komponenty Stron Lądowania (`src/components/landing`)

- **`FeaturesSection.tsx`**:
  - **Funkcjonalność**: Prezentuje kluczowe i dodatkowe funkcje aplikacji w atrakcyjny wizualnie sposób. Zawiera wezwanie do działania.
  - **Zastosowanie**: Przekonanie potencjalnych użytkowników o wartości aplikacji.
  - **Użycie**: Na stronie głównej (landing page).
- **`HeroSection.tsx`**:
  - **Funkcjonalność**: Główny element na górze strony lądowania. Prezentuje główny nagłówek, opis, przyciski CTA i wizualną demonstrację działania aplikacji.
  - **Zastosowanie**: Przyciągnięcie uwagi użytkownika i szybkie przekazanie wartości aplikacji.
  - **Użycie**: Na stronie głównej (landing page).
- **`HowItWorksSection.tsx`**:
  - **Funkcjonalność**: Wyjaśnia, jak działa aplikacja, dzieląc proces na cztery proste kroki. Zawiera wezwanie do działania i wskaźniki zaufania.
  - **Zastosowanie**: Edukacja potencjalnych użytkowników o prostocie korzystania z aplikacji.
  - **Użycie**: Na stronie głównej (landing page).

#### 3.7. Komponenty Układu (`src/components/layout`)

- **`DashboardLayout.tsx`**:
  - **Funkcjonalność**: Definiuje główny układ dla stron panelu nawigacyjnego (dashboardu), obsługując widoki desktopowe (sidebar + główna treść) i mobilne (nagłówek mobilny + dolny pasek nawigacyjny). Eksportuje pomocnicze komponenty do strukturyzowania treści w dashboardzie.
  - **Zastosowanie**: Spójny interfejs użytkownika dla sekcji dashboardu.
  - **Użycie**: Otacza wszystkie strony w `src/app/(dashboard)`.
- **`Header.tsx`**:
  - **Funkcjonalność**: Nagłówek aplikacji. Wyświetla logo, nawigację (zmienną w zależności od kontekstu), przełącznik rodzin i profil użytkownika/przyciski uwierzytelniania. Obsługuje menu mobilne.
  - **Zastosowanie**: Globalna nawigacja i dostęp do kluczowych funkcji.
  - **Użycie**: W głównym komponencie układu aplikacji lub w `DashboardLayout.tsx`.
- **`Navigation.tsx`**:
  - **Funkcjonalność**: Renderuje różne typy nawigacji (landing page, dashboard) w trzech wariantach (desktop, mobile, sidebar). Podświetla aktywne linki. Eksportuje `BreadcrumbNavigation`.
  - **Zastosowanie**: Elastyczne i spójne rozwiązania nawigacyjne.
  - **Użycie**: W `Header.tsx` i `Sidebar.tsx`.
- **`Sidebar.tsx`**:
  - **Funkcjonalność**: Pasek boczny dla widoku desktopowego. Wyświetla logo, profil użytkownika, przełącznik rodzin, nawigację i linki do ustawień/wylogowania. Umożliwia zwijanie/rozwijanie.
  - **Zastosowanie**: Stały dostęp do nawigacji i informacji w dashboardzie.
  - **Użycie**: W `DashboardLayout.tsx`.

#### 3.8. Komponenty Mobilne (`src/components/mobile`)

- **`MobileHeader.tsx`**:
  - **Funkcjonalność**: Nagłówek aplikacji dla urządzeń mobilnych. Wyświetla tytuł/logo, przycisk "Wstecz" lub menu hamburgera, opcjonalne akcje i awatar użytkownika. Otwiera boczne menu nawigacyjne.
  - **Zastosowanie**: Intuicyjna nawigacja na małych ekranach.
  - **Użycie**: W `DashboardLayout.tsx`.
- **`MobileTabBar.tsx`**:
  - **Funkcjonalność**: Dolny pasek nawigacyjny dla urządzeń mobilnych. Wyświetla ikony i nazwy głównych sekcji aplikacji, podświetla aktywną zakładkę.
  - **Zastosowanie**: Szybki i intuicyjny dostęp do najważniejszych sekcji na urządzeniach mobilnych.
  - **Użycie**: W `DashboardLayout.tsx`.
- **`MobileTaskForm.tsx`**:
  - **Funkcjonalność**: Formularz do tworzenia nowego zadania, zoptymalizowany pod kątem urządzeń mobilnych. Umożliwia wprowadzenie szczegółów zadania, przypisanie do członków i ustawienie terminu.
  - **Zastosowanie**: Szybkie tworzenie zadań na urządzeniach mobilnych.
  - **Użycie**: W kontekście tworzenia zadań, np. z kalendarza mobilnego.
- **`SwipeableCard.tsx`**:
  - **Funkcjonalność**: Opakowuje zawartość i dodaje funkcjonalność przesuwania (swipe) w celu ujawnienia ukrytych akcji (np. usuń, edytuj).
  - **Zastosowanie**: Implementacja interakcji "swipe-to-reveal-actions" w listach.
  - **Użycie**: W listach zadań, członków rodziny lub innych elementach wymagających szybkich akcji kontekstowych.

#### 3.9. Komponenty Cennika (`src/components/pricing`)

- **`FeatureComparison.tsx`**:
  - **Funkcjonalność**: Wyświetla tabelę porównawczą funkcji dostępnych w różnych planach cenowych (Podstawowy, Rodzinny, Premium).
  - **Zastosowanie**: Pomoc w wyborze odpowiedniego planu cenowego.
  - **Użycie**: Na stronie z cennikiem.
- **`PricingCards.tsx`**:
  - **Funkcjonalność**: Wyświetla karty z planami cenowymi aplikacji. Prezentuje nazwę, cenę, opis, listę funkcji i przyciski CTA.
  - **Zastosowanie**: Prezentacja oferty cenowej aplikacji.
  - **Użycie**: Na stronie z cennikiem.

#### 3.10. Komponenty Statystyk (`src/components/stats`)

- **`ActivityTimeline.tsx`**:
  - **Funkcjonalność**: Wyświetla oś czasu aktywności w rodzinie (np. ukończone zadania, dołączenie członka). Obsługuje paginację.
  - **Zastosowanie**: Monitorowanie i przeglądanie zdarzeń w rodzinie.
  - **Użycie**: Na stronie dashboardu rodziny lub dedykowanej stronie aktywności.
- **`CategoryBreakdownChart.tsx`**:
  - **Funkcjonalność**: Wyświetla wykres kołowy przedstawiający podział zadań według kategorii.
  - **Zastosowanie**: Wizualizacja typów zadań najczęściej wykonywanych w rodzinie.
  - **Użycie**: Na stronie statystyk rodziny.
- **`CompletionRateChart.tsx`**:
  - **Funkcjonalność**: Wyświetla wykres wskaźnika ukończenia zadań w czasie (liniowy lub słupkowy). Prezentuje trend i podsumowanie statystyk.
  - **Zastosowanie**: Analiza efektywności rodziny w wykonywaniu zadań.
  - **Użycie**: Na stronie statystyk rodziny.
- **`MemberPerformanceChart.tsx`**:
  - **Funkcjonalność**: Wyświetla wykres radarowy do porównywania wydajności członków rodziny w różnych kategoriach.
  - **Zastosowanie**: Wizualna ocena i porównanie wydajności członków.
  - **Użycie**: Na stronie statystyk rodziny.
- **`PointsLeaderboard.tsx`**:
  - **Funkcjonalność**: Wyświetla ranking członków rodziny na podstawie zdobytych punktów lub wskaźnika ukończenia zadań.
  - **Zastosowanie**: Motywowanie członków rodziny poprzez rywalizację.
  - **Użycie**: Na stronie statystyk rodziny.
- **`StatsDashboard.tsx`**:
  - **Funkcjonalność**: Główny komponent pulpitu statystyk. Pobiera i wyświetla ogólne statystyki, trendy, integruje inne komponenty statystyk.
  - **Zastosowanie**: Kompleksowe centrum analityczne dla rodziny.
  - **Użycie**: Główny komponent renderowany na stronie statystyk rodziny.
- **`TaskTrendsChart.tsx`**:
  - **Funkcjonalność**: Wyświetla wykres liniowy trendów zadań (ukończone, przypisane, przeterminowane).
  - **Zastosowanie**: Analiza dynamiki zadań w rodzinie.
  - **Użycie**: Na stronie statystyk rodziny.

#### 3.11. Komponenty Zawieszeń (`src/components/suspensions`)

- **`CreateSuspensionDialog.tsx`**:
  - **Funkcjonalność**: Modal do tworzenia nowego zawieszenia dla zadań lub członków rodziny (tytuł, opis, powód, zakres dat, wybrane zadania/członkowie).
  - **Zastosowanie**: Tymczasowe wyłączanie zadań lub członków z systemu.
  - **Użycie**: Na stronie zarządzania zawieszeniami.
- **`SuspensionCard.tsx`**:
  - **Funkcjonalność**: Wyświetla szczegóły pojedynczego zawieszenia (tytuł, opis, powód, status, zakres dat, zawieszone zadania/członkowie). Zawiera menu akcji.
  - **Zastosowanie**: Prezentacja i zarządzanie pojedynczymi zawieszeniami.
  - **Użycie**: W `SuspensionList.tsx`.
- **`SuspensionFilters.tsx`**:
  - **Funkcjonalność**: Umożliwia filtrowanie i wyszukiwanie zawieszeń według statusu, powodu, zakresu dat i opcji uwzględniania wygasłych.
  - **Zastosowanie**: Efektywne zarządzanie i przeglądanie listy zawieszeń.
  - **Użycie**: W `SuspensionList.tsx`.
- **`SuspensionList.tsx`**:
  - **Funkcjonalność**: Wyświetla listę zawieszeń zadań. Zawiera nagłówek ze statystykami, przycisk "Nowe zawieszenie", filtry i listę kart zawieszeń.
  - **Zastosowanie**: Kompleksowe zarządzanie zawieszeniami zadań.
  - **Użycie**: Główny komponent renderowany na stronie zarządzania zawieszeniami.

#### 3.12. Komponenty Zadań (`src/components/tasks`)

- **`TaskForm/index.tsx`**:
  - **Funkcjonalność**: Główny komponent formularza do tworzenia i edycji zadań. Dzieli formularz na sekcje (podstawowe info, właściwości, data/czas, cykliczność, przypisanie, podgląd). Obsługuje mutacje zadań i podstawową walidację.
  - **Zastosowanie**: Kompleksowe zarządzanie zadaniami.
  - **Użycie**: W `TaskDialog.tsx` i `TaskMobileForm.tsx`.
- **`DeleteTaskDialog.tsx`**:
  - **Funkcjonalność**: Modal do potwierdzenia i wykonania usunięcia zadania. Informuje o konsekwencjach.
  - **Zastosowanie**: Bezpieczne usuwanie zadań.
  - **Użycie**: W `TaskDetailsModal.tsx`, `TaskCard.tsx` lub `TaskList.tsx`.
- **`TaskCard.tsx`**:
  - **Funkcjonalność**: Wyświetla pojedynczą kartę zadania. Prezentuje tytuł, przypisanych członków, termin, punkty, status (z ikonami i stylami). Obsługuje oznaczanie jako ukończone, weryfikację, edycję i usunięcie.
  - **Zastosowanie**: Prezentacja zadań w listach i na dashboardach.
  - **Użycie**: W `TaskList.tsx`, `FamilyDashboard.tsx` i `MobileCalendarView.tsx`.
- **`TaskDialog.tsx`**:
  - **Funkcjonalność**: Modal, który służy jako kontener dla `TaskForm`. Umożliwia tworzenie lub edycję zadań w oknie modalnym.
  - **Zastosowanie**: Spójny interfejs użytkownika do zarządzania zadaniami.
  - **Użycie**: W `TaskList.tsx` i `CalendarDashboard.tsx`.
- **`TaskList.tsx`**:
  - **Funkcjonalność**: Wyświetla listę zadań dla danej rodziny. Zawiera nagłówek z podsumowaniem, pasek postępu, filtry (status, członek), sortowanie i listę kart zadań.
  - **Zastosowanie**: Przeglądanie, filtrowanie i zarządzanie zadaniami.
  - **Użycie**: Główny komponent renderowany na stronie zadań.
- **`TasksDashboard.tsx`**:
  - **Funkcjonalność**: Główny komponent pulpitu nawigacyjnego dla zadań. Wyświetla przegląd statystyk zadań, wyróżnia "Zadania wymagające uwagi" i zawiera sekcję "Szybkie akcje".
  - **Zastosowanie**: Centralne miejsce do zarządzania i monitorowania zadań.
  - **Użycie**: Główny komponent renderowany na stronie dashboardu zadań.

#### 3.13. Komponenty Motywu (`src/components/theme`)

- **`ThemeToggle.tsx`**:
  - **Funkcjonalność**: Umożliwia użytkownikowi przełączanie motywu aplikacji (jasny, ciemny, systemowy). Zapisuje preferencje w `localStorage`. Eksportuje hook `useTheme`.
  - **Zastosowanie**: Personalizacja wyglądu aplikacji.
  - **Użycie**: W nagłówku lub ustawieniach użytkownika.

#### 3.14. Komponenty UI (`src/components/ui`)

- **Funkcjonalność**: Zestaw podstawowych, wielokrotnego użytku komponentów interfejsu użytkownika (np. przyciski, pola tekstowe, karty, dialogi, menu rozwijane, awatary, badge, kalendarz, spinner ładowania, paski postępu, tooltipy). Są to komponenty "headless" stylizowane za pomocą Tailwind CSS.
- **Zastosowanie**: Zapewnienie spójnego wyglądu i zachowania w całej aplikacji, przyspieszenie rozwoju UI.
- **Użycie**: Wykorzystywane przez niemal wszystkie inne komponenty w projekcie.

### 4. Podsumowanie i Wnioski

Projekt FamilyTasks charakteryzuje się dobrze zorganizowaną strukturą komponentową, co sprzyja modularności, ponownemu użyciu kodu i łatwości utrzymania. Wyraźny podział na moduły funkcjonalne (uwierzytelnianie, kalendarz, rodzina, zadania, statystyki itp.) ułatwia nawigację po kodzie i zrozumienie poszczególnych części systemu.

Zastosowanie biblioteki Shadcn UI dla podstawowych komponentów interfejsu użytkownika zapewnia spójny i nowoczesny wygląd, jednocześnie pozwalając na dużą elastyczność w stylizacji za pomocą Tailwind CSS.

Aplikacja jest responsywna, z dedykowanymi komponentami i układami dla urządzeń mobilnych, co świadczy o dbałości o doświadczenie użytkownika na różnych platformach.

Widać również, że aplikacja jest przygotowana na internacjonalizację, co jest kluczowe dla globalnego zasięgu.

Ogólnie rzecz biorąc, projekt jest solidnie zbudowany, z przemyślaną architekturą komponentową, która powinna ułatwić dalszy rozwój i rozbudowę funkcjonalności.
