# Etap 10: Testy i optymalizacja

## Cel etapu

Implementacja komprehensywnej strategii testowania, optymalizacja wydajności aplikacji, przygotowanie do produkcji oraz konfiguracja monitoringu i analytics zgodnie z KPI z PRD.

## Oszacowany czas

**16-20 godzin**

## Priorytet

🔴 **KRYTYCZNY** - Zapewnia jakość i niezawodność

---

## Zadania do wykonania

### 10.1 Konfiguracja środowiska testowego

**Czas: 3h | Priorytet: Wysoki**

- [x] **10.1.1** Setup testing frameworks

  ```bash
  # Unit & Integration testing
  npm install -D jest @testing-library/react @testing-library/jest-dom
  npm install -D @testing-library/user-event @types/jest

  # E2E testing
  npm install -D playwright @playwright/test

  # Database testing
  npm install -D jest-environment-node
  ```

- [x] **10.1.2** Jest configuration

  ```javascript
  // jest.config.js
  const nextJest = require('next/jest');

  const createJestConfig = nextJest({
    dir: './',
  });

  const customJestConfig = {
    setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
    testEnvironment: 'jest-environment-jsdom',
    testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
    moduleNameMapping: {
      '^@/(.*)$': '<rootDir>/src/$1',
    },
    collectCoverageFrom: [
      'src/**/*.{js,jsx,ts,tsx}',
      '!src/**/*.d.ts',
      '!src/**/*.stories.{js,jsx,ts,tsx}',
      '!src/app/api/**/*.ts', // Exclude API routes from coverage
    ],
    coverageThreshold: {
      global: {
        branches: 70,
        functions: 70,
        lines: 70,
        statements: 70,
      },
    },
  };

  module.exports = createJestConfig(customJestConfig);
  ```

- [x] **10.1.3** Playwright E2E setup

  ```typescript
  // playwright.config.ts
  import { defineConfig, devices } from '@playwright/test';

  export default defineConfig({
    testDir: './e2e',
    fullyParallel: true,
    forbidOnly: !!process.env.CI,
    retries: process.env.CI ? 2 : 0,
    workers: process.env.CI ? 1 : undefined,
    reporter: 'html',
    use: {
      baseURL: process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000',
      trace: 'on-first-retry',
      screenshot: 'only-on-failure',
    },
    projects: [
      {
        name: 'chromium',
        use: { ...devices['Desktop Chrome'] },
      },
      {
        name: 'firefox',
        use: { ...devices['Desktop Firefox'] },
      },
      {
        name: 'webkit',
        use: { ...devices['Desktop Safari'] },
      },
      {
        name: 'Mobile Chrome',
        use: { ...devices['Pixel 5'] },
      },
    ],
    webServer: {
      command: 'npm run dev',
      url: 'http://localhost:3000',
      reuseExistingServer: !process.env.CI,
    },
  });
  ```

### 10.2 Unit Tests - Core Logic

**Czas: 4h | Priorytet: Wysoki**

- [x] **10.2.1** Service layer tests

  ```typescript
  // __tests__/services/task.service.test.ts
  import { TaskService } from '@/lib/services/task.service';
  import { prismaMock } from '@/lib/test-utils/prisma-mock';

  describe('TaskService', () => {
    describe('createTask', () => {
      it('should create task with valid data', async () => {
        const taskData = {
          title: 'Test Task',
          familyId: 'family-1',
          assignedById: 'user-1',
        };

        const mockTask = { id: 'task-1', ...taskData };
        prismaMock.task.create.mockResolvedValue(mockTask);

        const result = await TaskService.createTask('family-1', taskData);

        expect(result).toEqual(mockTask);
        expect(prismaMock.task.create).toHaveBeenCalledWith({
          data: expect.objectContaining(taskData),
        });
      });

      it('should throw error for invalid family access', async () => {
        prismaMock.familyMember.findFirst.mockResolvedValue(null);

        await expect(TaskService.createTask('invalid-family', {})).rejects.toThrow('Access denied');
      });
    });
  });
  ```

- [x] **10.2.2** Utility functions tests

  ```typescript
  // __tests__/lib/utils/date.test.ts
  import { formatDate, getWeekBounds, calculateTaskPoints } from '@/lib/utils';

  describe('Date utilities', () => {
    it('should format date correctly for Polish locale', () => {
      const date = new Date('2024-06-25');
      expect(formatDate(date, 'pl')).toBe('25.06.2024');
    });

    it('should calculate week bounds correctly', () => {
      const monday = new Date('2024-06-24'); // Monday
      const bounds = getWeekBounds(monday, 'MONDAY');

      expect(bounds.start.getDay()).toBe(1); // Monday
      expect(bounds.end.getDay()).toBe(0); // Sunday
    });
  });

  describe('Points calculation', () => {
    it('should calculate base points correctly', () => {
      const lightTask = { weight: 'LIGHT', points: 1 };
      expect(calculateTaskPoints(lightTask)).toBe(1);

      const heavyTask = { weight: 'HEAVY', points: 5 };
      expect(calculateTaskPoints(heavyTask)).toBe(5);
    });
  });
  ```

- [x] **10.2.3** Validation schema tests

  ```typescript
  // __tests__/lib/validations/task.test.ts
  import { createTaskSchema, completeTaskSchema } from '@/lib/validations/task';

  describe('Task validation schemas', () => {
    describe('createTaskSchema', () => {
      it('should validate valid task data', () => {
        const validData = {
          title: 'Clean room',
          description: 'Clean and organize bedroom',
          points: 5,
          weight: 'NORMAL',
        };

        expect(() => createTaskSchema.parse(validData)).not.toThrow();
      });

      it('should reject empty title', () => {
        const invalidData = { title: '', points: 1 };

        expect(() => createTaskSchema.parse(invalidData)).toThrow();
      });
    });
  });
  ```

### 10.3 Component Tests - UI Layer

**Czas: 3h | Priorytet: Wysoki**

- [x] **10.3.1** Task components tests

  ```typescript
  // __tests__/components/tasks/TaskCard.test.tsx
  import { render, screen, fireEvent } from '@testing-library/react';
  import { TaskCard } from '@/components/tasks/TaskCard';
  import { mockTask, mockMember } from '@/lib/test-utils/mocks';

  describe('TaskCard', () => {
    const defaultProps = {
      task: mockTask,
      currentMember: mockMember,
      onComplete: jest.fn(),
      onEdit: jest.fn(),
      onDelete: jest.fn()
    };

    it('should render task information correctly', () => {
      render(<TaskCard {...defaultProps} />);

      expect(screen.getByText(mockTask.title)).toBeInTheDocument();
      expect(screen.getByText(`Przypisane: ${mockTask.assignedTo.name}`)).toBeInTheDocument();
      expect(screen.getByText(`${mockTask.points} punktów`)).toBeInTheDocument();
    });

    it('should call onComplete when complete button clicked', async () => {
      const user = userEvent.setup();
      render(<TaskCard {...defaultProps} />);

      const completeButton = screen.getByRole('button', { name: /wykonane/i });
      await user.click(completeButton);

      expect(defaultProps.onComplete).toHaveBeenCalledTimes(1);
    });

    it('should show loading state during completion', () => {
      render(<TaskCard {...defaultProps} task={{ ...mockTask, isCompleting: true }} />);

      expect(screen.getByRole('button', { name: /wykonywanie/i })).toBeDisabled();
    });
  });
  ```

- [x] **10.3.2** Form components tests

  ```typescript
  // __tests__/components/forms/CreateTaskDialog.test.tsx
  import { render, screen, fireEvent, waitFor } from '@testing-library/react';
  import { CreateTaskDialog } from '@/components/forms/CreateTaskDialog';

  describe('CreateTaskDialog', () => {
    it('should validate form and show errors', async () => {
      const user = userEvent.setup();
      render(<CreateTaskDialog isOpen onClose={jest.fn()} familyId="family-1" />);

      const submitButton = screen.getByRole('button', { name: /utwórz zadanie/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Nazwa zadania jest wymagana')).toBeInTheDocument();
      });
    });

    it('should submit valid form data', async () => {
      const user = userEvent.setup();
      const onSubmit = jest.fn();

      render(
        <CreateTaskDialog
          isOpen
          onClose={jest.fn()}
          onSubmit={onSubmit}
          familyId="family-1"
        />
      );

      await user.type(screen.getByLabelText(/nazwa zadania/i), 'Test Task');
      await user.selectOptions(screen.getByLabelText(/waga/i), 'NORMAL');
      await user.click(screen.getByRole('button', { name: /utwórz/i }));

      await waitFor(() => {
        expect(onSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            title: 'Test Task',
            weight: 'NORMAL'
          })
        );
      });
    });
  });
  ```

### 10.4 Integration Tests - API Routes

**Czas: 3h | Priorytet: Średni**

- [x] **10.4.1** API route tests

  ```typescript
  // __tests__/api/families/[familyId]/tasks.test.ts
  import { createMocks } from 'node-mocks-http';
  import handler from '@/app/api/families/[familyId]/tasks/route';
  import { getServerSession } from '@/lib/auth';

  jest.mock('@/lib/auth');
  jest.mock('@/lib/prisma');

  describe('/api/families/[familyId]/tasks', () => {
    beforeEach(() => {
      (getServerSession as jest.Mock).mockResolvedValue({
        user: { id: 'user-1' },
      });
    });

    describe('GET', () => {
      it('should return family tasks for authorized user', async () => {
        const { req, res } = createMocks({
          method: 'GET',
          query: { familyId: 'family-1' },
        });

        prismaMock.task.findMany.mockResolvedValue([mockTask]);

        await handler(req, res);

        expect(res._getStatusCode()).toBe(200);
        const data = JSON.parse(res._getData());
        expect(data.tasks).toHaveLength(1);
      });

      it('should return 403 for unauthorized access', async () => {
        (getServerSession as jest.Mock).mockResolvedValue(null);

        const { req, res } = createMocks({
          method: 'GET',
          query: { familyId: 'family-1' },
        });

        await handler(req, res);

        expect(res._getStatusCode()).toBe(403);
      });
    });

    describe('POST', () => {
      it('should create task with valid data', async () => {
        const { req, res } = createMocks({
          method: 'POST',
          query: { familyId: 'family-1' },
          body: {
            title: 'New Task',
            weight: 'NORMAL',
          },
        });

        prismaMock.task.create.mockResolvedValue(mockTask);

        await handler(req, res);

        expect(res._getStatusCode()).toBe(201);
        expect(prismaMock.task.create).toHaveBeenCalled();
      });
    });
  });
  ```

### 10.5 E2E Tests - Critical User Flows

**Czas: 4h | Priorytet: Wysoki**

- [x] **10.5.1** Authentication flow

  ```typescript
  // e2e/auth.spec.ts
  import { test, expect } from '@playwright/test';

  test.describe('Authentication Flow', () => {
    test('should allow user to sign up and create family', async ({ page }) => {
      await page.goto('/');

      // Sign up process
      await page.click('text=Zarejestruj się');
      await page.fill('[name="email"]', '<EMAIL>');
      await page.fill('[name="password"]', 'password123');
      await page.click('button[type="submit"]');

      // Should redirect to onboarding
      await expect(page).toHaveURL('/onboarding');

      // Create family
      await page.fill('[name="familyName"]', 'Rodzina Testowa');
      await page.click('text=Utwórz rodzinę');

      // Should redirect to dashboard
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('h1')).toContainText('Kokpit');
    });

    test('should persist session after page reload', async ({ page }) => {
      // Login first
      await loginAsUser(page, '<EMAIL>');

      await page.reload();

      // Should still be logged in
      await expect(page).toHaveURL('/dashboard');
      await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
    });
  });
  ```

- [x] **10.5.2** Task management flow

  ```typescript
  // e2e/tasks.spec.ts
  import { test, expect } from '@playwright/test';

  test.describe('Task Management', () => {
    test.beforeEach(async ({ page }) => {
      await loginAsUser(page, '<EMAIL>');
      await page.goto('/dashboard');
    });

    test('should create, assign and complete task', async ({ page }) => {
      // Create task
      await page.click('text=Dodaj zadanie');
      await page.fill('[name="title"]', 'Pozmywaj naczynia');
      await page.selectOption('[name="assignedTo"]', 'child-1');
      await page.selectOption('[name="weight"]', 'NORMAL');
      await page.click('button[type="submit"]');

      // Verify task appears in list
      await expect(page.locator('.task-card')).toContainText('Pozmywaj naczynia');

      // Complete task as child
      await switchUserContext(page, '<EMAIL>');
      await page.goto('/dashboard');

      await page.click('.task-card button[aria-label="Oznacz jako wykonane"]');
      await page.fill('[name="notes"]', 'Wszystko czyste!');
      await page.click('text=Potwierdź');

      // Verify completion
      await expect(page.locator('.task-card')).toHaveClass(/completed/);

      // Verify task as parent
      await switchUserContext(page, '<EMAIL>');
      await page.goto('/dashboard');

      await page.click('text=Do weryfikacji');
      await expect(page.locator('.task-completion')).toContainText('Wszystko czyste!');
    });

    test('should handle task drag and drop in calendar', async ({ page }) => {
      await page.goto('/calendar');

      // Create task for today
      await createTask(page, 'Test Task', new Date());

      // Drag to tomorrow
      const taskElement = page.locator('.calendar-task').first();
      const tomorrowCell = page.locator('.calendar-day[data-date="tomorrow"]');

      await taskElement.dragTo(tomorrowCell);

      // Verify task moved
      await expect(tomorrowCell.locator('.calendar-task')).toContainText('Test Task');
    });
  });
  ```

- [x] **10.5.3** Family management flow

  ```typescript
  // e2e/family.spec.ts
  test.describe('Family Management', () => {
    test('should invite member and handle acceptance', async ({ browser }) => {
      const context = await browser.newContext();
      const parentPage = await context.newPage();

      await loginAsUser(parentPage, '<EMAIL>');
      await parentPage.goto('/family');

      // Send invitation
      await parentPage.click('text=Zaproś członka');
      await parentPage.fill('[name="email"]', '<EMAIL>');
      await parentPage.selectOption('[name="role"]', 'CHILD');
      await parentPage.click('button[type="submit"]');

      await expect(parentPage.locator('.invitation-pending')).toContainText(
        '<EMAIL>'
      );

      // Simulate email click (new context for new user)
      const memberContext = await browser.newContext();
      const memberPage = await memberContext.newPage();

      // Get invitation token from database or mock
      const invitationToken = await getInvitationToken('<EMAIL>');
      await memberPage.goto(`/invitations/${invitationToken}`);

      // Accept invitation
      await memberPage.click('text=Akceptuj zaproszenie');

      // Should be redirected to family dashboard
      await expect(memberPage).toHaveURL('/dashboard');

      // Verify member appears in family list
      await parentPage.reload();
      await expect(parentPage.locator('.family-member')).toContainText('<EMAIL>');
    });
  });
  ```

### 10.6 Performance Testing i Optimization

**Czas: 2h | Priorytet: Średni**

- [x] **10.6.1** Lighthouse CI setup

  ```yaml
  # .github/workflows/lighthouse.yml
  name: Lighthouse CI
  on:
    pull_request:
      branches: [main]
    push:
      branches: [main]

  jobs:
    lighthouse:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v3
        - uses: actions/setup-node@v3
          with:
            node-version: 18
            cache: 'npm'

        - run: npm ci
        - run: npm run build
        - run: npm run start &

        - name: Wait for server
          run: npx wait-on http://localhost:3000

        - name: Run Lighthouse CI
          run: npx lhci autorun
          env:
            LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
  ```

- [x] **10.6.2** Bundle analyzer setup

  ```javascript
  // next.config.js rozszerzenie
  const withBundleAnalyzer = require('@next/bundle-analyzer')({
    enabled: process.env.ANALYZE === 'true',
  });

  module.exports = withBundleAnalyzer({
    // ... existing config
    webpack: (config, { isServer }) => {
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
        };
      }
      return config;
    },
  });
  ```

- [x] **10.6.3** Performance monitoring setup

  ```typescript
  // lib/monitoring/performance.ts
  export function trackWebVitals(metric: any) {
    const { name, value, id } = metric;

    // Send to analytics service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', name, {
        value: Math.round(name === 'CLS' ? value * 1000 : value),
        event_label: id,
        non_interaction: true,
      });
    }

    // Log performance issues
    if (name === 'LCP' && value > 2500) {
      console.warn('Poor LCP detected:', value);
    }

    if (name === 'FID' && value > 100) {
      console.warn('Poor FID detected:', value);
    }
  }
  ```

### 10.7 Error Monitoring i Analytics

**Czas: 1h | Priorytet: Średni**

- [x] **10.7.1** Sentry setup dla error tracking

  ```bash
  npm install @sentry/nextjs
  ```

  ```javascript
  // sentry.client.config.js
  import * as Sentry from '@sentry/nextjs';

  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    tracesSampleRate: 0.1,
    environment: process.env.NODE_ENV,
    beforeSend(event) {
      // Filter out non-critical errors
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (error?.type === 'AbortError') {
          return null;
        }
      }
      return event;
    },
  });
  ```

- [x] **10.7.2** Analytics tracking (zgodnie z KPI z PRD)

  ```typescript
  // lib/analytics/events.ts
  export interface AnalyticsEvent {
    name: string;
    properties: Record<string, any>;
  }

  export function trackEvent(event: AnalyticsEvent) {
    // Track metrics from PRD
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', event.name, event.properties);
    }
  }

  // Specific events for KPI tracking
  export const analyticsEvents = {
    familyCreated: (familyId: string) =>
      trackEvent({
        name: 'family_created',
        properties: { family_id: familyId },
      }),

    taskCompleted: (taskId: string, points: number) =>
      trackEvent({
        name: 'task_completed',
        properties: { task_id: taskId, points },
      }),

    memberInvited: (familyId: string, role: string) =>
      trackEvent({
        name: 'member_invited',
        properties: { family_id: familyId, role },
      }),

    // KPI: Współczynnik ukończenia zadań
    completionRateCalculated: (familyId: string, rate: number) =>
      trackEvent({
        name: 'completion_rate_calculated',
        properties: { family_id: familyId, rate },
      }),
  };
  ```

---

## Kryteria akceptacji

### ✅ Test Coverage

- [ ] Unit tests coverage > 70%
- [ ] Critical paths mają 100% coverage
- [ ] All API routes are tested
- [ ] Database operations are tested
- [ ] Utility functions are tested

### ✅ E2E Coverage

- [ ] Authentication flow działa
- [ ] Task management flow działa
- [ ] Family management flow działa
- [ ] Payment flow działa (jeśli zaimplementowany)
- [ ] Mobile responsive flows działają

### ✅ Performance (zgodnie z PRD KPI)

- [ ] Czas ładowania aplikacji < 3 sekund
- [ ] First Contentful Paint < 1.5s
- [ ] Largest Contentful Paint < 2.5s
- [ ] Cumulative Layout Shift < 0.1
- [ ] Responsywność UI < 100ms

### ✅ Quality Assurance

- [ ] Zero critical security vulnerabilities
- [ ] Accessibility score > 90%
- [ ] Cross-browser compatibility
- [ ] Mobile device compatibility
- [ ] Error monitoring is active

### ✅ Monitoring & Analytics

- [ ] All KPI metrics are tracked
- [ ] Error monitoring captures issues
- [ ] Performance monitoring active
- [ ] User behavior analytics working

---

## Zależności

**Wymaga ukończenia:**

- Wszystkich poprzednich etapów (1-9)

## Blokuje

**Brak** - To jest etap finalizujący

---

## KPI Tracking Implementation (z PRD)

### 📊 Metryki użytkowania

```typescript
// lib/analytics/usage-metrics.ts
export class UsageMetrics {
  static async trackActiveFamilies() {
    const weeklyActive = await prisma.family.count({
      where: {
        members: {
          some: {
            lastActive: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            },
          },
        },
      },
    });

    trackEvent({
      name: 'weekly_active_families',
      properties: { count: weeklyActive },
    });
  }

  static async trackAverageTasksPerFamily() {
    const stats = await prisma.family.aggregate({
      _avg: { _count: { tasks: true } },
    });

    trackEvent({
      name: 'avg_tasks_per_family',
      properties: { average: stats._avg },
    });
  }
}
```

### 📊 Metryki zaangażowania

```typescript
// lib/analytics/engagement-metrics.ts
export class EngagementMetrics {
  static async trackDailyActiveUsers() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const activeUsers = await prisma.user.count({
      where: {
        familyMembers: {
          some: {
            lastActive: { gte: today },
          },
        },
      },
    });

    trackEvent({
      name: 'daily_active_users',
      properties: { count: activeUsers, date: today.toISOString() },
    });
  }

  static async trackTaskCompletionRate(familyId: string) {
    const total = await prisma.task.count({
      where: { familyId, status: 'ACTIVE' },
    });

    const completed = await prisma.taskCompletion.count({
      where: {
        task: { familyId },
        status: 'APPROVED',
        completedAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
      },
    });

    const rate = total > 0 ? (completed / total) * 100 : 0;

    trackEvent({
      name: 'task_completion_rate',
      properties: { family_id: familyId, rate, total, completed },
    });

    return rate;
  }
}
```

---

## Testing Strategy Matrix

### 🧪 Test Types by Priority

| Component         | Unit      | Integration | E2E         | Performance |
| ----------------- | --------- | ----------- | ----------- | ----------- |
| **Auth**          | ✅ High   | ✅ High     | ✅ Critical | ⚠️ Medium   |
| **Tasks**         | ✅ High   | ✅ High     | ✅ Critical | ✅ High     |
| **Family**        | ✅ High   | ✅ High     | ✅ High     | ⚠️ Medium   |
| **Calendar**      | ✅ Medium | ⚠️ Medium   | ✅ High     | ✅ High     |
| **Stats**         | ✅ High   | ⚠️ Medium   | ⚠️ Medium   | ✅ High     |
| **UI Components** | ✅ High   | ❌ Low      | ⚠️ Medium   | ✅ High     |

### 🔄 CI/CD Pipeline Tests

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:coverage

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm run test:integration

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm run build
      - run: npx playwright install
      - run: npm run test:e2e

  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm ci
      - run: npm run build
      - run: npm run test:lighthouse
```

---

## Notatki implementacyjne

### Test Data Management

```typescript
// lib/test-utils/factories.ts
export const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  ...overrides,
});

export const createMockFamily = (overrides = {}) => ({
  id: 'family-1',
  name: 'Test Family',
  createdAt: new Date(),
  ...overrides,
});

export const createMockTask = (overrides = {}) => ({
  id: 'task-1',
  title: 'Test Task',
  familyId: 'family-1',
  status: 'ACTIVE',
  points: 3,
  weight: 'NORMAL',
  ...overrides,
});
```

### Performance Budgets

```javascript
// lighthouse.config.js
module.exports = {
  ci: {
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.9 }],
        'first-contentful-paint': ['error', { maxNumericValue: 1500 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
      },
    },
  },
};
```

### Error Boundaries Testing

```typescript
// components/ErrorBoundary.test.tsx
import { render, screen } from '@testing-library/react';
import { ErrorBoundary } from '@/components/ErrorBoundary';

const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('ErrorBoundary', () => {
  it('should catch and display errors', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText(/Coś poszło nie tak/)).toBeInTheDocument();

    consoleSpy.mockRestore();
  });
});
```

### Load Testing (opcjonalne)

```typescript
// scripts/load-test.js
import { check } from 'k6';
import http from 'k6/http';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Steady state
    { duration: '2m', target: 0 }, // Ramp down
  ],
};

export default function () {
  const response = http.get('https://your-app.com/api/families/1/tasks');

  check(response, {
    'status is 200': r => r.status === 200,
    'response time < 500ms': r => r.timings.duration < 500,
  });
}
```
