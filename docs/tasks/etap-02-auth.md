# Etap 2: Uwierzytelnianie i autoryzacja

## Cel etapu

Implementacja systemu uwierzytelniania z Clerk, konfiguracja bazy danych z Prisma oraz stworzenie systemu ról i uprawnień z Row-Level Security.

## Oszacowany czas

**12-16 godzin**

## Priorytet

🔴 **KRYTYCZNY** - Fundament bezpieczeństwa aplikacji

---

## Zadania do wykonania

### 2.1 Integracja z Clerk (Authentication Provider)

**Czas: 4h | Priorytet: Wysoki**

- [x] **2.1.1** Konfiguracja konta Clerk
  - Utworzenie konta na Clerk.com
  - Utworzenie nowej aplikacji "FamilyTasks"
  - Konfiguracja domen (localhost, production)
  - Pobranie kluczy API

- [x] **2.1.2** Instalacja i konfiguracja Clerk SDK

  ```bash
  npm install @clerk/nextjs
  ```
  - Konfiguracja middleware w `middleware.ts`
  - Wrapped App Router w `ClerkProvider`
  - Konfiguracja protected routes

- [x] **2.1.3** Konfiguracja stron uwierzytelniania
  - Utworzenie `/sign-in` i `/sign-up` routes
  - Dostosowanie UI do brand guidelines
  - Konfiguracja przekierowań po logowaniu
  - Implementacja wylogowywania

- [x] **2.1.4** Custom User Profile
  - Rozszerzenie domyślnego profilu użytkownika
  - Dodanie pól: firstName, lastName, preferredLanguage
  - Synchronizacja z lokalną bazą danych

### 2.2 Konfiguracja bazy danych (Prisma + PostgreSQL)

**Czas: 3h | Priorytet: Wysoki**

- [x] **2.2.1** Instalacja Prisma

  ```bash
  npm install prisma @prisma/client
  npm install -D prisma
  ```
  - Inicjalizacja: `npx prisma init`
  - Konfiguracja connection string

- [x] **2.2.2** Konfiguracja bazy danych
  - **Development**: SQLite dla szybkiego prototypowania
  - **Production**: PostgreSQL (Supabase/Neon)
  - Konfiguracja zmiennych środowiskowych

- [x] **2.2.3** Definicja podstawowych modeli

  ```prisma
  model User {
    id        String   @id @default(cuid())
    clerkId   String   @unique
    email     String   @unique
    firstName String?
    lastName  String?
    preferredLanguage String @default("pl")
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    // Relations
    familyMembers FamilyMember[]

    @@map("users")
  }

  model Family {
    id          String   @id @default(cuid())
    name        String
    description String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    members FamilyMember[]
    tasks   Task[]

    @@map("families")
  }

  model FamilyMember {
    id       String     @id @default(cuid())
    userId   String
    familyId String
    role     FamilyRole @default(CHILD)
    isAdult  Boolean    @default(false)
    points   Int        @default(0)
    joinedAt DateTime   @default(now())

    // Relations
    user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
    family Family @relation(fields: [familyId], references: [id], onDelete: Cascade)

    @@unique([userId, familyId])
    @@map("family_members")
  }

  enum FamilyRole {
    OWNER
    PARENT
    GUARDIAN
    CHILD
  }
  ```

### 2.3 System ról i uprawnień

**Czas: 3h | Priorytet: Wysoki**

- [x] **2.3.1** Implementacja typu ról
  - Definicja enum FamilyRole
  - Implementacja funkcji sprawdzających uprawnienia
  - Utility functions dla ról

- [x] **2.3.2** Middleware autoryzacji

  ```typescript
  // lib/auth.ts
  export async function checkFamilyPermission(
    userId: string,
    familyId: string,
    requiredRole: FamilyRole[]
  ): Promise<boolean>;

  export async function getFamilyMemberWithRole(
    userId: string,
    familyId: string
  ): Promise<FamilyMember | null>;
  ```

- [x] **2.3.3** HOC i hooks dla autoryzacji
  ```typescript
  // hooks/useAuth.ts
  export function useAuth();
  export function useFamilyRole(familyId: string);
  export function useCanManageTasks(familyId: string);
  ```

### 2.4 Row-Level Security (RLS)

**Czas: 3h | Priorytet: Średni**

- [ ] **2.4.1** Konfiguracja RLS w bazie danych
  - Włączenie RLS dla wrażliwych tabel
  - Definicja policies dla każdej tabeli
  - Konfiguracja funkcji bezpieczeństwa

- [ ] **2.4.2** Implementacja middleware RLS

  ```typescript
  // lib/rls.ts
  export function createAuthenticatedPrisma(userId: string);
  export function withFamilyAccess(familyId: string, userId: string);
  ```

- [ ] **2.4.3** API routes z security checks
  - Wrapper dla API endpoints
  - Automatyczna weryfikacja uprawnień
  - Error handling dla unauthorized access

### 2.5 API Routes dla uwierzytelniania

**Czas: 2h | Priorytet: Średni**

- [x] **2.5.1** Webhook dla synchronizacji Clerk

  ```typescript
  // app/api/webhooks/clerk/route.ts
  export async function POST(request: Request);
  ```
  - Synchronizacja user creation/update/delete
  - Walidacja webhook signature

- [x] **2.5.2** Profile API endpoints

  ```typescript
  // app/api/user/profile/route.ts
  export async function GET(); // Get user profile
  export async function PATCH(); // Update user profile
  ```

- [x] **2.5.3** Session management
  - Implementacja session handling
  - Refresh token logic
  - Session cleanup

### 2.6 Komponenty UI dla uwierzytelniania

**Czas: 3h | Priorytet: Średni**

- [x] **2.6.1** Layout komponenty

  ```typescript
  // components/auth/AuthGuard.tsx
  // components/auth/SignInButton.tsx
  // components/auth/UserProfile.tsx
  ```

- [x] **2.6.2** Custom sign-in/sign-up pages
  - Dostosowanie do designu aplikacji
  - Obsługa błędów
  - Loading states
  - Responsywność

- [x] **2.6.3** Navigation z uwierzytelnianiem
  - Conditional rendering dla zalogowanych
  - User menu w header
  - Sign out functionality

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [x] Użytkownik może się zarejestrować i zalogować
- [x] Profile użytkownika synchronizuje się z bazą danych
- [x] System ról działa poprawnie
- [x] Unauthorized users nie mają dostępu do protected routes
- [x] Webhook Clerk synchronizuje dane poprawnie

### ✅ Techniczne

- [ ] RLS policies działają poprawnie
- [x] API endpoints są zabezpieczone
- [ ] Baza danych jest zoptymalizowana (indexy)
- [x] Error handling jest kompletny
- [x] TypeScript types są kompletne

### ✅ Bezpieczeństwo

- [x] Wszystkie secrets są w zmiennych środowiskowych
- [x] API routes wymagają autoryzacji
- [x] Role-based access control działa
- [x] Webhook signatures są weryfikowane
- [x] Session management jest bezpieczny

### ✅ UX/UI

- [x] Sign-in/sign-up są intuicyjne
- [x] Loading states są wyświetlane
- [x] Error messages są user-friendly
- [x] UI jest responsywne na wszystkich urządzeniach

---

## Zależności

**Wymaga ukończenia:**

- Etap 1: Fundament i konfiguracja

## Blokuje

- Etap 3: Moduł rodziny
- Etap 4: Moduł zadań
- Wszystkie pozostałe etapy funkcjonalne

---

## Bezpieczeństwo - Lista kontrolna

### 🔐 Clerk Configuration

- [ ] Proper domain configuration
- [ ] API keys are stored securely
- [ ] Webhook endpoints are protected
- [ ] CORS is configured correctly

### 🔐 Database Security

- [ ] Connection strings nie zawierają credentials w plain text
- [ ] RLS policies są aktywne
- [ ] Sensitive data jest encrypted
- [ ] Backup strategy jest zdefiniowana

### 🔐 API Security

- [ ] Rate limiting jest włączony
- [ ] Input validation na wszystkich endpoints
- [ ] CSRF protection
- [ ] Proper error handling (no data leaks)

---

## Notatki implementacyjne

### Development vs Production

- **Development**: SQLite + podstawowa konfiguracja Clerk
- **Production**: PostgreSQL + pełna konfiguracja security

### Best Practices

- Używaj server components gdzie to możliwe
- Zaimplementuj proper error boundaries
- Wszystkie API calls powinny być type-safe
- Dokumentuj wszystkie role permissions

### Testing Strategy

- Unit tests dla auth utilities
- Integration tests dla API endpoints
- E2E tests dla auth flow
- Security testing dla permissions
