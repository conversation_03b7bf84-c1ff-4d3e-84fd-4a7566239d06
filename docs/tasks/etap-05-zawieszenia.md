# Etap 5: <PERSON><PERSON><PERSON> zadań

## Cel etapu

Implementacja systemu czasowego zawieszania zadań w określonych okolicznościach (wakacje, choroba, wyjazdy) z możliwością zarządzania aktywными zawieszeniami.

## Oszacowany czas

**8-10 godzin**

## Priorytet

🟡 **ŚREDNI** - Funkcja usprawniająca zarządzanie

---

## Zadania do wykonania

### 5.1 Modele danych dla zawieszeń

**Czas: 2h | Priorytet: Wysoki**

- [x] **5.1.1** Model TaskSuspension

  ```prisma
  model TaskSuspension {
    id          String            @id @default(cuid())
    familyId    String
    title       String
    description String?
    reason      SuspensionReason
    startDate   DateTime
    endDate     DateTime
    status      SuspensionStatus  @default(ACTIVE)
    createdById String
    createdAt   DateTime          @default(now())
    updatedAt   DateTime          @updatedAt

    // Relations
    family      Family            @relation(fields: [familyId], references: [id], onDelete: Cascade)
    createdBy   FamilyMember      @relation("CreatedSuspensions", fields: [createdById], references: [id])

    // Suspended items
    suspendedTasks    SuspendedTask[]
    suspendedMembers  SuspendedMember[]

    @@map("task_suspensions")
  }

  enum SuspensionReason {
    HOLIDAY_VACATION  // Święta/urlop
    TRAVEL           // Wyjazd
    ILLNESS          // Choroba
    SCHOOL_BREAK     // Przerwa szkolna
    MAINTENANCE      // Prace remontowe
    OTHER            // Inny powód
  }

  enum SuspensionStatus {
    ACTIVE           // Aktywne zawieszenie
    ENDED            // Zakończone
    CANCELLED        // Anulowane
  }
  ```

- [x] **5.1.2** Model SuspendedTask

  ```prisma
  model SuspendedTask {
    id           String         @id @default(cuid())
    suspensionId String
    taskId       String

    // Relations
    suspension   TaskSuspension @relation(fields: [suspensionId], references: [id], onDelete: Cascade)
    task         Task           @relation(fields: [taskId], references: [id], onDelete: Cascade)

    @@unique([suspensionId, taskId])
    @@map("suspended_tasks")
  }
  ```

- [x] **5.1.3** Model SuspendedMember

  ```prisma
  model SuspendedMember {
    id           String         @id @default(cuid())
    suspensionId String
    memberId     String

    // Relations
    suspension   TaskSuspension @relation(fields: [suspensionId], references: [id], onDelete: Cascade)
    member       FamilyMember   @relation("MemberSuspensions", fields: [memberId], references: [id], onDelete: Cascade)

    @@unique([suspensionId, memberId])
    @@map("suspended_members")
  }
  ```

### 5.2 API Routes dla zawieszeń

**Czas: 2h | Priorytet: Wysoki**

- [x] **5.2.1** CRUD operacje dla zawieszeń

  ```typescript
  // app/api/families/[familyId]/suspensions/route.ts
  export async function GET(); // Lista zawieszeń rodziny
  export async function POST(); // Tworzenie nowego zawieszenia

  // app/api/families/[familyId]/suspensions/[suspensionId]/route.ts
  export async function GET(); // Szczegóły zawieszenia
  export async function PATCH(); // Edycja zawieszenia
  export async function DELETE(); // Usunięcie zawieszenia
  ```

- [x] **5.2.2** API zarządzania elementami zawieszenia

  ```typescript
  // app/api/families/[familyId]/suspensions/[suspensionId]/tasks/route.ts
  export async function POST(); // Dodanie zadań do zawieszenia
  export async function DELETE(); // Usunięcie zadań z zawieszenia

  // app/api/families/[familyId]/suspensions/[suspensionId]/members/route.ts
  export async function POST(); // Dodanie członków do zawieszenia
  export async function DELETE(); // Usunięcie członków z zawieszenia
  ```

- [x] **5.2.3** API aktywacji/dezaktywacji
  ```typescript
  // app/api/families/[familyId]/suspensions/[suspensionId]/activate/route.ts
  export async function POST(); // Aktywacja zawieszenia
  export async function DELETE(); // Dezaktywacja zawieszenia
  ```

### 5.3 Business Logic dla zawieszeń

**Czas: 2h | Priorytet: Wysoki**

- [x] **5.3.1** SuspensionService

  ```typescript
  // lib/services/suspension.service.ts
  export class SuspensionService {
    // CRUD operations
    static async createSuspension(
      familyId: string,
      data: CreateSuspensionData
    ): Promise<TaskSuspension>;
    static async updateSuspension(
      suspensionId: string,
      data: UpdateSuspensionData
    ): Promise<TaskSuspension>;
    static async deleteSuspension(suspensionId: string): Promise<void>;

    // Suspension management
    static async addTasksToSuspension(suspensionId: string, taskIds: string[]): Promise<void>;
    static async removeTasksFromSuspension(suspensionId: string, taskIds: string[]): Promise<void>;
    static async addMembersToSuspension(suspensionId: string, memberIds: string[]): Promise<void>;
    static async removeMembersFromSuspension(
      suspensionId: string,
      memberIds: string[]
    ): Promise<void>;

    // Activation logic
    static async activateSuspension(suspensionId: string): Promise<TaskSuspension>;
    static async deactivateSuspension(suspensionId: string): Promise<TaskSuspension>;

    // Automated management
    static async processExpiredSuspensions(): Promise<void>;
    static async getActiveSuspensionsForTask(taskId: string): Promise<TaskSuspension[]>;
    static async getActiveSuspensionsForMember(memberId: string): Promise<TaskSuspension[]>;
  }
  ```

- [x] **5.3.2** Task integration logic

  ```typescript
  // lib/services/task.service.ts (rozszerzenie)
  export class TaskService {
    // Sprawdzanie zawieszenia przed akcjami
    static async isTaskSuspended(taskId: string): Promise<boolean>;
    static async isMemberSuspended(memberId: string): Promise<boolean>;

    // Filtrowanie zadań z uwzględnieniem zawieszeń
    static async getTasksWithSuspensions(familyId: string, filters: TaskFilters): Promise<Task[]>;

    // Modyfikacja punktów dla zawieszonych zadań
    static async adjustPointsForSuspension(
      taskId: string,
      suspensionPeriod: DateRange
    ): Promise<void>;
  }
  ```

- [x] **5.3.3** Validatory
  ```typescript
  // lib/validations/suspension.ts
  export const createSuspensionSchema = z
    .object({
      title: z.string().min(1).max(200),
      description: z.string().max(1000).optional(),
      reason: z.enum([
        'HOLIDAY_VACATION',
        'TRAVEL',
        'ILLNESS',
        'SCHOOL_BREAK',
        'MAINTENANCE',
        'OTHER',
      ]),
      startDate: z.date(),
      endDate: z.date(),
      taskIds: z.array(z.string()).optional(),
      memberIds: z.array(z.string()).optional(),
    })
    .refine(data => data.endDate > data.startDate, {
      message: 'Data końca musi być późniejsza niż data rozpoczęcia',
      path: ['endDate'],
    });
  ```

### 5.4 Automated Suspension Management

**Czas: 1h | Priorytet: Średni**

- [x] **5.4.1** Cron job dla zawieszeń

  ```typescript
  // app/api/cron/suspensions/route.ts
  export async function GET() {
    // Automatyczne kończenie wygasłych zawieszeń
    // Aktywacja zawieszeń które mają się rozpocząć
    // Przypomnienia o zbliżających się zakończeniach
  }
  ```

- [x] **5.4.2** Middleware integracja
  ```typescript
  // lib/middleware/suspension.middleware.ts
  export async function checkSuspensions(req: Request, taskId?: string, memberId?: string) {
    // Sprawdzanie aktywnych zawieszeń przed wykonaniem akcji
    // Blokowanie akcji jeśli element jest zawieszony
    // Informowanie o powodzie zawieszenia
  }
  ```

### 5.5 Komponenty UI - Lista zawieszeń

**Czas: 2h | Priorytet: Średni**

- [x] **5.5.1** SuspensionList component

  ```typescript
  // components/suspensions/SuspensionList.tsx
  interface SuspensionListProps {
    familyId: string;
    status?: SuspensionStatus[];
    showExpired?: boolean;
  }
  ```
  - Lista aktywnych zawieszeń
  - Filtry według statusu i powodu
  - Informacje o zawieszonych zadaniach/członkach
  - Action buttons (edycja, anulowanie, przedłużenie)

- [x] **5.5.2** SuspensionCard component

  ```typescript
  // components/suspensions/SuspensionCard.tsx
  interface SuspensionCardProps {
    suspension: TaskSuspension & {
      suspendedTasks: Task[];
      suspendedMembers: FamilyMember[];
    };
    onEdit: () => void;
    onCancel: () => void;
    onExtend: () => void;
  }
  ```
  - Wizualna karta z informacjami o zawieszeniu
  - Timeline pokazujący okres zawieszenia
  - Lista zawieszonych elementów
  - Status badge z kolorami

- [x] **5.5.3** Suspension filters
  ```typescript
  // components/suspensions/SuspensionFilters.tsx
  // - Filtr po statusie (Active, Ended, Cancelled)
  // - Filtr po powodzie zawieszenia
  // - Filtr po dacie (Current, Upcoming, Past)
  // - Search po tytule/opisie
  ```

### 5.6 Komponenty UI - Formularze

**Czas: 2h | Priorytet: Średni**

- [x] **5.6.1** CreateSuspensionDialog

  ```typescript
  // components/suspensions/CreateSuspensionDialog.tsx
  // - Formularz z wszystkimi polami
  // - Date range picker
  // - Multi-select dla zadań i członków
  // - Podgląd wpływu na zadania
  // - Presets dla typowych sytuacji (weekend, wakacje)
  ```

- [x] **5.6.2** EditSuspensionDialog

  ```typescript
  // components/suspensions/EditSuspensionDialog.tsx
  // - Edycja wszystkich pól zawieszenia
  // - Możliwość dodania/usunięcia zadań/członków
  // - Przedłużenie lub skrócenie okresu
  // - Ostrzeżenia o wpływie na zadania
  ```

- [x] **5.6.3** Quick suspension actions
  ```typescript
  // components/suspensions/QuickSuspensionActions.tsx
  // - Szybkie akcje z dropdown
  // - "Zawieś wszystkie zadania na tydzień"
  // - "Zawieś zadania dla konkretnego członka"
  // - "Zawieś wszystkie zadania domowe"
  ```

### 5.7 Integracja z modułem zadań

**Czas: 1h | Priorytet: Wysoki**

- [x] **5.7.1** Task UI modifications

  ```typescript
  // components/tasks/TaskCard.tsx (modyfikacja)
  // - Wskaźnik zawieszenia na karcie zadania
  // - Tooltip z informacją o zawieszeniu
  // - Wyłączenie akcji dla zawieszonych zadań
  ```

- [x] **5.7.2** Task list integration

  ```typescript
  // components/tasks/TaskList.tsx (modyfikacja)
  // - Filtr "Pokaż zawieszone zadania"
  // - Wizualne odróżnienie zawieszonych zadań
  // - Bulk actions dla zawieszeń
  ```

- [x] **5.7.3** Dashboard integration
  ```typescript
  // components/dashboard/TasksDashboard.tsx (modyfikacja)
  // - Sekcja aktywnych zawieszeń
  // - Informacja o zawieszonych zadaniach
  // - Quick actions dla zarządzania zawieszeniami
  ```

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [x] Rodzice/opiekunowie mogą tworzyć zawieszenia zadań
- [x] Można zawieszać pojedyncze zadania lub całe kategorie
- [x] Można zawieszać zadania dla konkretnych członków
- [x] Zawieszenia automatycznie się kończą po upływie terminu
- [x] Zawieszone zadania nie generują przypomnień
- [x] Możliwość przedłużenia lub wcześniejszego zakończenia zawieszeń

### ✅ Techniczne

- [x] API endpoints są zabezpieczone odpowiednimi permissions
- [x] Walidacja dat i logiki biznesowej
- [x] Cron job przetwarza zawieszenia automatycznie
- [x] Database queries są zoptymalizowane
- [x] Proper error handling dla edge cases

### ✅ UX/UI

- [x] Interface jest intuicyjny i łatwy w użyciu
- [x] Zawieszone zadania są wizualnie odróżnione
- [x] Loading states i error messages są czytelne
- [x] Responsive design na wszystkich urządzeniach
- [x] Formularz zawieszeń jest user-friendly

### ✅ Integracja

- [x] Moduł zadań respektuje zawieszenia
- [x] Dashboard pokazuje aktualne zawieszenia
- [x] Statystyki uwzględniają okresy zawieszeń
- [x] Email notifications informują o zawieszeniach

---

## Zależności

**Wymaga ukończenia:**

- Etap 1: Fundament i konfiguracja
- Etap 2: Uwierzytelnianie i autoryzacja
- Etap 3: Moduł rodziny
- Etap 4: Moduł zadań

## Blokuje

**Opcjonalnie wpływa na:**

- Etap 6: Moduł statystyk (należy uwzględnić zawieszenia)
- Etap 7: Moduł kalendarza (wizualizacja zawieszeń)

---

## User Stories

### 👤 Jako rodzic

- Chcę móc zawiesić wszystkie zadania domowe na okres wakacji
- Chcę móc zawiesić zadania dla chorego dziecka
- Chcę widzieć jakie zawieszenia są aktywne
- Chcę móc przedłużyć zawieszenie jeśli to konieczne
- Chcę otrzymać przypomnienie przed końcem zawieszenia

### 👤 Jako dziecko

- Chcę wiedzieć dlaczego moje zadania są zawieszone
- Chcę widzieć kiedy zawieszenie się skończy
- Chcę móc zobaczyć historię zawieszeń
- Chcę otrzymać powiadomienie gdy zadania wracają

### 👤 Jako administrator rodziny

- Chcę móc łatwo zarządzać wszystkimi zawieszeniami
- Chcę widzieć wpływ zawieszeń na statystyki rodziny
- Chcę móc anulować zawieszenia w razie potrzeby

---

## Przykłady użycia

### 🏖️ Wakacje letnie

```typescript
{
  title: "Wakacje letnie 2024",
  reason: "HOLIDAY_VACATION",
  startDate: "2024-07-01",
  endDate: "2024-08-31",
  description: "Zawieszenie zadań domowych na okres wakacji",
  taskIds: ["wszystkie-zadania-domowe"],
  memberIds: ["dzieci"]
}
```

### 🤒 Choroba członka rodziny

```typescript
{
  title: "Choroba - Anna",
  reason: "ILLNESS",
  startDate: "2024-06-20",
  endDate: "2024-06-27",
  description: "Zawieszenie zadań ze względu na chorobę",
  memberIds: ["anna-id"]
}
```

### ✈️ Wyjazd służbowy

```typescript
{
  title: "Wyjazd służbowy taty",
  reason: "TRAVEL",
  startDate: "2024-07-15",
  endDate: "2024-07-20",
  description: "Reorganizacja zadań podczas nieobecności",
  taskIds: ["zadania-wymagajace-taty"]
}
```

---

## Notatki implementacyjne

### Business Rules

- Zawieszenia nie mogą się nakładać dla tego samego zadania
- Tylko rodzice/opiekunowie mogą tworzyć zawieszenia
- Zawieszenia automatycznie się kończą o północy w dniu końcowym
- Zawieszone zadania nie naliczają kar za opóźnienia

### Database Optimizations

- Index na (familyId, status, startDate, endDate)
- Soft delete dla zawieszeń (audit trail)
- Cascade delete dla suspended tasks/members

### Monitoring & Analytics

- Track suspension usage patterns
- Monitor impact on task completion rates
- Alert gdy za dużo zawieszeń w rodzinie

### Future Enhancements

- Automatic suspension suggestions (holidays, school breaks)
- Integration with calendar apps
- Smart suspension based on weather/events
- Suspension templates dla częstych sytuacji
