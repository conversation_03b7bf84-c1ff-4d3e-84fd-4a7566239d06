# Etap 1: Fundament i konfiguracja

## Cel etapu

Przygotowanie środowiska deweloperskiego i konfiguracja podstawowych technologii dla aplikacji FamilyTasks.

## O<PERSON><PERSON><PERSON>y czas

**8-12 godzin**

## Priorytet

🔴 **KRYTYCZNY** - Blokuje wszystkie inne etapy

---

## Zadania do wykonania

### 1.1 Konfiguracja Next.js 15+ z TypeScript

**Czas: 2h | Priorytet: Wysoki**

- [ ] **1.1.1** Inicjalizacja projektu Next.js 15+
  - Wykonanie: `npx create-next-app@latest family-tasks --typescript --tailwind --app`
  - Weryfikacja wersji Node.js (≥18.17.0)
  - Konfiguracja `next.config.js` z podstawowymi ustawieniami

- [ ] **1.1.2** Konfiguracja TypeScript
  - Przegląd i dostosowanie `tsconfig.json`
  - Konfiguracja ścieżek absolutnych (@/)
  - Włączenie strict mode dla TypeScript
  - Konfiguracja ESLint dla TypeScript

- [ ] **1.1.3** Konfiguracja podstawowych skryptów
  ```json
  {
    "scripts": {
      "dev": "next dev",
      "build": "next build",
      "start": "next start",
      "lint": "next lint",
      "type-check": "tsc --noEmit"
    }
  }
  ```

### 1.2 Instalacja i konfiguracja TailwindCSS 4 + Shadcn UI

**Czas: 3h | Priorytet: Wysoki**

- [ ] **1.2.1** Aktualizacja TailwindCSS do wersji 4
  - Instalacja najnowszej wersji Tailwind
  - Konfiguracja `tailwind.config.ts`
  - Przygotowanie custom design tokens

- [ ] **1.2.2** Instalacja Shadcn UI
  - Wykonanie: `npx shadcn-ui@latest init`
  - Konfiguracja komponentów bazowych
  - Dostosowanie kolorystyki do "family-purple"
  - Instalacja podstawowych komponentów: Button, Card, Input, Dialog

- [ ] **1.2.3** Konfiguracja kolorystyki marki
  ```css
  :root {
    --family-purple: #8b5cf6;
    --family-purple-light: #a78bfa;
    --family-purple-dark: #7c3aed;
    /* Pozostałe kolory zgodnie z prototypami */
  }
  ```

### 1.3 Struktura folderów i architektura projektu

**Czas: 2h | Priorytet: Wysoki**

- [ ] **1.3.1** Utworzenie struktury folderów

  ```
  src/
  ├── app/                    # App Router (Next.js 13+)
  │   ├── (auth)/            # Grupa tras uwierzytelniania
  │   ├── (dashboard)/       # Grupa tras aplikacji
  │   ├── api/               # API routes
  │   └── globals.css
  ├── components/            # Komponenty React
  │   ├── ui/               # Komponenty UI (Shadcn)
  │   ├── forms/            # Formularze
  │   ├── layout/           # Komponenty layoutu
  │   └── features/         # Komponenty specyficzne dla funkcji
  ├── lib/                  # Utilities i konfiguracje
  │   ├── utils.ts          # Funkcje pomocnicze
  │   ├── validations.ts    # Schematy walidacji
  │   └── constants.ts      # Stałe aplikacji
  ├── hooks/                # Custom hooks
  ├── stores/               # Stan aplikacji (Zustand/Redux)
  ├── types/                # Definicje typów TypeScript
  └── styles/               # Dodatkowe style
  ```

- [ ] **1.3.2** Konfiguracja aliasów dla importów
  - Dodanie aliasów do `tsconfig.json`
  - Konfiguracja VSCode dla intellisense

### 1.4 Konfiguracja zmiennych środowiskowych

**Czas: 1h | Priorytet: Średni**

- [ ] **1.4.1** Utworzenie plików środowiskowych
  - `.env.local` (development)
  - `.env.example` (szablon)
  - Dodanie do `.gitignore`

- [ ] **1.4.2** Definicja podstawowych zmiennych

  ```env
  # Database
  DATABASE_URL=""

  # Authentication (Clerk)
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=""
  CLERK_SECRET_KEY=""

  # App Configuration
  NEXT_PUBLIC_APP_URL="http://localhost:3000"
  ```

### 1.5 Konfiguracja narzędzi deweloperskich

**Czas: 2h | Priorytet: Średni**

- [ ] **1.5.1** Prettier
  - Instalacja i konfiguracja Prettier
  - Utworzenie `.prettierrc`
  - Integracja z ESLint

- [ ] **1.5.2** Husky i lint-staged
  - Konfiguracja pre-commit hooks
  - Automatyczne formatowanie przed commit

- [ ] **1.5.3** VSCode settings
  - Utworzenie `.vscode/settings.json`
  - Konfiguracja extensions recommendations

### 1.6 Testy podstawowej konfiguracji

**Czas: 1h | Priorytet: Średni**

- [ ] **1.6.1** Uruchomienie serwera deweloperskiego
  - Test `npm run dev`
  - Weryfikacja działania hot reload

- [ ] **1.6.2** Test kompilacji
  - Test `npm run build`
  - Weryfikacja braku błędów TypeScript

- [ ] **1.6.3** Test komponentów Shadcn
  - Utworzenie testowej strony z podstawowymi komponentami
  - Weryfikacja stylowania

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [ ] Aplikacja uruchamia się bez błędów na `localhost:3000`
- [ ] TypeScript kompiluje się bez błędów
- [ ] Komponenty Shadcn UI działają poprawnie
- [ ] Struktura folderów jest zgodna z planem

### ✅ Techniczne

- [ ] Wszystkie zależności są w najnowszych stabilnych wersjach
- [ ] Linting i formatowanie działają automatycznie
- [ ] Import aliasy funkcjonują poprawnie
- [ ] Konfiguracja środowiskowa jest zabezpieczona

### ✅ Jakościowe

- [ ] Kod jest sformatowany zgodnie z ustalonym stylem
- [ ] Dokumentacja konfiguracji jest kompletna
- [ ] Nie ma błędów konsoli w przeglądarce

---

## Zależności

**Brak** - To jest etap inicjalny

## Blokuje

- Etap 2: Uwierzytelnianie i autoryzacja
- Etap 3: Moduł rodziny
- Wszystkie pozostałe etapy

---

## Notatki implementacyjne

- Użyj najnowszych stabilnych wersji wszystkich narzędzi
- Skonfiguruj development i production environment od początku
- Zadbaj o security best practices już na etapie konfiguracji
- Dokumentuj wszystkie custom konfiguracje
