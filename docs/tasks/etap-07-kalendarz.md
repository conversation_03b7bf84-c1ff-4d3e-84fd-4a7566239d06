# Etap 7: <PERSON><PERSON><PERSON> kalen<PERSON>

## Cel etapu

Implementacja interaktywnego widoku kalendarzowego dla zadań rodzinnych z możliwością planowania, nawigacji i opcjonalnej integracji z zewnętrznymi kalendarzami (Google/Apple).

## Oszacowany czas

**10-12 godzin**

## Priorytet

🟡 **ŚREDNI** - Funkcja usprawniająca planowanie

---

## Zadania do wykonania

### 7.1 Rozszerzenie modeli danych

**Czas: 1h | Priorytet: Średni**

- [x] **7.1.1** Rozbudowa modelu Task dla kalendarza

  ```prisma
  model Task {
    // ... existing fields

    // Calendar-specific fields
    startDate     DateTime?    // C<PERSON> rozpoczęcia (dla zadań czasowych)
    endDate       DateTime?    // Czas zakończenia
    allDay        Boolean      @default(true)
    recurrenceRule String?     // RRULE dla zadań cyklicznych
    color         String?      // <PERSON><PERSON> w kalendarzu

    // Calendar integration
    googleEventId String?      // ID eventu w Google Calendar
    appleEventId  String?      // ID eventu w Apple Calendar

    // ... existing relations
  }
  ```

- [x] **7.1.2** Model CalendarSettings

  ```prisma
  model CalendarSettings {
    id               String    @id @default(cuid())
    familyId         String    @unique
    defaultView      CalendarView @default(MONTH)
    startWeekOn      WeekDay   @default(MONDAY)
    timeFormat       TimeFormat @default(HOUR_24)
    showWeekends     Boolean   @default(true)
    showCompleted    Boolean   @default(false)
    colorScheme      String    @default("default")

    // External calendar integration
    googleCalendarEnabled Boolean @default(false)
    appleCalendarEnabled  Boolean @default(false)

    createdAt        DateTime  @default(now())
    updatedAt        DateTime  @updatedAt

    // Relations
    family           Family    @relation(fields: [familyId], references: [id], onDelete: Cascade)

    @@map("calendar_settings")
  }

  enum CalendarView {
    MONTH
    WEEK
    DAY
    AGENDA
  }

  enum WeekDay {
    SUNDAY
    MONDAY
  }

  enum TimeFormat {
    HOUR_12
    HOUR_24
  }
  ```

### 7.2 Calendar API Routes

**Czas: 2h | Priorytet: Średni**

- [x] **7.2.1** API calendar views

  ```typescript
  // app/api/families/[familyId]/calendar/route.ts
  export async function GET(); // Pobieranie events dla kalendarza

  // app/api/families/[familyId]/calendar/month/route.ts
  export async function GET(); // Events dla konkretnego miesiąca

  // app/api/families/[familyId]/calendar/week/route.ts
  export async function GET(); // Events dla konkretnego tygodnia

  // app/api/families/[familyId]/calendar/day/route.ts
  export async function GET(); // Events dla konkretnego dnia
  ```

- [x] **7.2.2** API calendar settings

  ```typescript
  // app/api/families/[familyId]/calendar/settings/route.ts
  export async function GET(); // Pobieranie ustawień kalendarza
  export async function PATCH(); // Aktualizacja ustawień kalendarza
  ```

- [x] **7.2.3** API operacji na eventach

  ```typescript
  // app/api/families/[familyId]/calendar/events/route.ts
  export async function POST(); // Tworzenie nowego eventu w kalendarzu

  // app/api/families/[familyId]/calendar/events/[eventId]/route.ts
  export async function PATCH(); // Przeniesienie eventu (drag & drop)
  export async function DELETE(); // Usunięcie eventu z kalendarza
  ```

### 7.3 Business Logic dla kalendarza

**Czas: 2h | Priorytet: Średni**

- [x] **7.3.1** CalendarService

  ```typescript
  // lib/services/calendar.service.ts
  export class CalendarService {
    // Event management
    static async getCalendarEvents(
      familyId: string,
      startDate: Date,
      endDate: Date,
      filters?: CalendarFilters
    ): Promise<CalendarEvent[]>;

    static async createCalendarEvent(
      familyId: string,
      data: CreateEventData
    ): Promise<CalendarEvent>;
    static async updateCalendarEvent(
      eventId: string,
      data: UpdateEventData
    ): Promise<CalendarEvent>;
    static async deleteCalendarEvent(eventId: string): Promise<void>;

    // Task-calendar integration
    static async convertTaskToCalendarEvent(taskId: string): Promise<CalendarEvent>;
    static async syncTaskWithCalendar(taskId: string): Promise<void>;

    // Recurring events
    static async generateRecurringEvents(
      baseEvent: CalendarEvent,
      rrule: string,
      endDate: Date
    ): Promise<CalendarEvent[]>;

    // View helpers
    static async getMonthEvents(
      familyId: string,
      year: number,
      month: number
    ): Promise<CalendarEvent[]>;
    static async getWeekEvents(familyId: string, weekStart: Date): Promise<CalendarEvent[]>;
    static async getDayEvents(familyId: string, date: Date): Promise<CalendarEvent[]>;
  }
  ```

- [x] **7.3.2** Event conflict detection

  ```typescript
  // lib/services/calendar.service.ts (rozszerzenie)
  export class CalendarService {
    static async detectConflicts(
      memberId: string,
      startDate: Date,
      endDate: Date,
      excludeEventId?: string
    ): Promise<ConflictDetection>;

    static async suggestAlternativeTimes(
      memberId: string,
      duration: number,
      preferredDate: Date
    ): Promise<TimeSlot[]>;

    static async getOptimalTaskScheduling(
      familyId: string,
      tasks: Task[],
      constraints: SchedulingConstraints
    ): Promise<SchedulingSuggestion[]>;
  }
  ```

- [x] **7.3.3** Validatory i typy

  ```typescript
  // lib/validations/calendar.ts
  export const createCalendarEventSchema = z
    .object({
      title: z.string().min(1).max(200),
      description: z.string().max(1000).optional(),
      startDate: z.date(),
      endDate: z.date().optional(),
      allDay: z.boolean().default(true),
      assignedToId: z.string().optional(),
      color: z
        .string()
        .regex(/^#[0-9A-F]{6}$/i)
        .optional(),
    })
    .refine(data => !data.endDate || data.endDate > data.startDate, {
      message: 'Data końca musi być późniejsza niż data rozpoczęcia',
      path: ['endDate'],
    });

  export interface CalendarEvent {
    id: string;
    title: string;
    description?: string;
    start: Date;
    end?: Date;
    allDay: boolean;
    color?: string;
    type: 'task' | 'event' | 'reminder';
    assignedTo?: FamilyMember;
    metadata?: Record<string, any>;
  }
  ```

### 7.4 Calendar UI Components

**Czas: 4h | Priorytet: Wysoki**

- [x] **7.4.1** Calendar library selection i setup

  ```bash
  # Opcje do wyboru:
  npm install @fullcalendar/react @fullcalendar/daygrid @fullcalendar/timegrid @fullcalendar/interaction
  # lub
  npm install react-big-calendar
  # lub
  npm install @schedule-x/calendar
  ```

- [x] **7.4.2** CalendarView component

  ```typescript
  // components/calendar/CalendarView.tsx
  interface CalendarViewProps {
    familyId: string;
    view: CalendarView;
    date: Date;
    onDateChange: (date: Date) => void;
    onViewChange: (view: CalendarView) => void;
    onEventClick: (event: CalendarEvent) => void;
    onEventDrop: (event: CalendarEvent, newDate: Date) => void;
    onSlotSelect: (startDate: Date, endDate?: Date) => void;
  }
  ```
  - Responsive calendar grid
  - Multiple view types (month, week, day, agenda)
  - Drag & drop support
  - Event rendering z customizable colors
  - Touch support dla mobile

- [x] **7.4.3** CalendarNavigation component

  ```typescript
  // components/calendar/CalendarNavigation.tsx
  // - Previous/Next navigation
  // - View type selector (Month/Week/Day)
  // - Date picker dla quick navigation
  // - "Today" button
  // - Settings toggle
  ```

- [x] **7.4.4** CalendarFilters component
  ```typescript
  // components/calendar/CalendarFilters.tsx
  // - Filter by member
  // - Filter by task status
  // - Filter by task category
  // - Show/hide completed tasks
  // - Color legend
  ```

### 7.5 Event Management UI

**Czas: 2h | Priorytet: Średni**

- [x] **7.5.1** EventDetailsModal

  ```typescript
  // components/calendar/EventDetailsModal.tsx
  interface EventDetailsModalProps {
    event: CalendarEvent;
    isOpen: boolean;
    onClose: () => void;
    onEdit: () => void;
    onDelete: () => void;
    onComplete?: () => void; // Dla task events
  }
  ```
  - Detailed view of event/task
  - Quick actions (edit, delete, complete)
  - Related information (member, points, etc.)

- [x] **7.5.2** CreateEventDialog

  ```typescript
  // components/calendar/CreateEventDialog.tsx
  // - Form dla tworzenia nowych events
  // - Date/time pickers
  // - Member assignment
  // - Integration z existing tasks
  // - Recurring event options
  ```

- [ ] **7.5.3** QuickAddEvent component
  ```typescript
  // components/calendar/QuickAddEvent.tsx
  // - Inline quick add functionality
  // - Natural language parsing ("Tomorrow 3PM")
  // - Auto-suggestions based on existing tasks
  ```

### 7.6 Calendar Settings

**Czas: 1h | Priorytet: Niski**

- [ ] **7.6.1** CalendarSettingsDialog

  ```typescript
  // components/calendar/CalendarSettingsDialog.tsx
  // - Default view preference
  // - Week start day
  // - Time format (12h/24h)
  // - Show/hide weekends
  // - Color scheme selection
  ```

- [ ] **7.6.2** External calendar integration UI
  ```typescript
  // components/calendar/ExternalCalendarSync.tsx
  // - Google Calendar integration toggle
  // - Apple Calendar integration toggle
  // - Sync status indicators
  // - Conflict resolution settings
  ```

### 7.7 External Calendar Integration (Opcjonalne)

**Czas: 2h | Priorytet: Niski**

- [ ] **7.7.1** Google Calendar API integration

  ```typescript
  // lib/integrations/google-calendar.ts
  export class GoogleCalendarIntegration {
    static async syncToGoogleCalendar(familyId: string): Promise<void>;
    static async importFromGoogleCalendar(familyId: string): Promise<CalendarEvent[]>;
    static async createGoogleCalendarEvent(event: CalendarEvent): Promise<string>;
    static async updateGoogleCalendarEvent(eventId: string, event: CalendarEvent): Promise<void>;
    static async deleteGoogleCalendarEvent(eventId: string): Promise<void>;
  }
  ```

- [ ] **7.7.2** Webhook dla synchronizacji
  ```typescript
  // app/api/webhooks/google-calendar/route.ts
  export async function POST(); // Webhook dla Google Calendar changes
  ```

### 7.8 Hooks i state management

**Czas: 1h | Priorytet: Średni**

- [x] **7.8.1** Calendar hooks

  ```typescript
  // hooks/useCalendar.ts
  export function useCalendar(familyId: string, view: CalendarView, date: Date);
  export function useCalendarEvents(familyId: string, startDate: Date, endDate: Date);
  export function useCalendarSettings(familyId: string);
  export function useEventMutations();
  ```

- [x] **7.8.2** Date manipulation utilities
  ```typescript
  // lib/utils/date.utils.ts
  export function getMonthBounds(date: Date): { start: Date; end: Date };
  export function getWeekBounds(date: Date, startWeekOn: WeekDay): { start: Date; end: Date };
  export function formatEventTime(event: CalendarEvent, timeFormat: TimeFormat): string;
  export function detectDateConflicts(events: CalendarEvent[]): ConflictGroup[];
  ```

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [ ] Kalendarz wyświetla zadania w przejrzysty sposób
- [ ] Możliwość przełączania między widokami (miesiąc/tydzień/dzień)
- [ ] Drag & drop dla przesuwania terminów zadań
- [ ] Tworzenie nowych zadań bezpośrednio z kalendarza
- [ ] Filtering i color coding dla różnych typów zadań
- [ ] Responsive design na wszystkich urządzeniach

### ✅ Techniczne

- [ ] Calendar renderuje się szybko (<1s)
- [ ] Smooth scrolling i navigation
- [ ] Proper event handling dla touch devices
- [ ] API endpoints są zoptymalizowane
- [ ] State management jest wydajny

### ✅ UX/UI

- [ ] Interface jest intuicyjny i łatwy w nawigacji
- [ ] Colors i styling są consistent z designem
- [ ] Loading states są wyświetlane
- [ ] Error handling jest user-friendly
- [ ] Tooltips i help text są informative

### ✅ Performance

- [ ] Calendar ładuje wydarzenia w chunks
- [ ] Virtualization dla dużych ilości events
- [ ] Lazy loading dla future/past months
- [ ] Memory usage jest kontrolowany

---

## Zależności

**Wymaga ukończenia:**

- Etap 1: Fundament i konfiguracja
- Etap 2: Uwierzytelnianie i autoryzacja
- Etap 3: Moduł rodziny
- Etap 4: Moduł zadań

**Opcjonalnie korzysta z:**

- Etap 5: Moduł zawieszeń (wizualizacja zawieszeń)
- Etap 6: Moduł statystyk (integration z analytics)

## Blokuje

**Brak** - To jest moduł dodatkowy

---

## User Stories

### 👤 Jako rodzic

- Chcę planować zadania dla rodziny w widoku kalendarzowym
- Chcę widzieć konflikty terminów między członkami rodziny
- Chcę móc przesuwać terminy zadań poprzez drag & drop
- Chcę synchronizować zadania z moim osobistym kalendarzem
- Chcę widzieć busy/free time dla każdego członka rodziny

### 👤 Jako dziecko

- Chcę widzieć moje zadania na konkretny dzień
- Chcę planować kiedy wykonam poszczególne zadania
- Chcę widzieć ile wolnego czasu mam w danym dniu
- Chcę otrzymywać przypomnienia zgodnie z kalendarzem

### 👤 Jako cała rodzina

- Chcemy koordynować zadania tak żeby nie kolidowały ze sobą
- Chcemy planować family events i activities
- Chcemy widzieć przegląd tygodnia/miesiąca na jednym ekranie
- Chcemy eksportować harmonogram do zewnętrznych aplikacji

---

## Calendar Library Comparison

### FullCalendar (Rekomendowane)

```typescript
// Pros: Feature-rich, good React integration, customizable
// Cons: Larger bundle size, learning curve
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
```

### React Big Calendar (Alternatywa)

```typescript
// Pros: Lightweight, simple API, good for basic use cases
// Cons: Less features, limited customization
import { Calendar, momentLocalizer } from 'react-big-calendar';
```

### Schedule-X (Nowa opcja)

```typescript
// Pros: Modern, TypeScript-first, good performance
// Cons: Less mature, smaller community
import { Calendar } from '@schedule-x/calendar';
```

---

## Notatki implementacyjne

### Performance Optimizations

- **Virtual scrolling** dla dużych ilości events
- **Chunk loading** - ładuj tylko widoczny okres
- **Debounce** date navigation changes
- **Memoize** event calculations
- **Cache** frequently accessed date ranges

### Mobile Considerations

```typescript
// Touch gestures
const mobileFeatures = {
  swipeToNavigate: true,
  pinchToZoom: false, // Usually too complex for task calendar
  longPressToCreate: true,
  dragToMove: true, // z haptic feedback
};
```

### Accessibility

- **Keyboard navigation** (arrow keys, tab)
- **Screen reader support** z proper ARIA labels
- **High contrast mode** support
- **Focus management** dla modals i popups
- **Alternative text** dla visual elements

### External Calendar Sync

```typescript
// Sync strategy
const syncStrategy = {
  direction: 'bidirectional', // family-tasks <-> external
  conflictResolution: 'manual', // User chooses in conflicts
  frequency: 'real-time', // For critical updates
  fallback: 'polling_15min', // Backup sync method
};
```

### Data Structure for Events

```typescript
interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end?: Date;
  allDay: boolean;

  // FamilyTasks specific
  type: 'task' | 'reminder' | 'deadline' | 'event';
  taskId?: string;
  memberId?: string;
  familyId: string;

  // Visual
  color?: string;
  borderColor?: string;
  textColor?: string;

  // Metadata
  recurring?: boolean;
  editable?: boolean;
  deletable?: boolean;

  // External integration
  externalIds?: {
    google?: string;
    apple?: string;
  };
}
```

### Recurring Events Logic

```typescript
// Use RRULE standard for recurring events
import { RRule } from 'rrule';

const generateRecurringEvents = (baseEvent: CalendarEvent, rrule: string) => {
  const rule = RRule.fromString(rrule);
  const occurrences = rule.between(startDate, endDate);

  return occurrences.map(date => ({
    ...baseEvent,
    id: `${baseEvent.id}-${date.toISOString()}`,
    start: date,
    end: addMinutes(date, baseEvent.duration),
  }));
};
```

### Color Coding Strategy

```typescript
const eventColors = {
  // By member (family-purple variations)
  member: (memberId: string) => `hsl(${hash(memberId) % 360}, 70%, 50%)`,

  // By task type
  task_types: {
    household: '#10B981', // Green
    education: '#3B82F6', // Blue
    outdoor: '#F59E0B', // Orange
    personal: '#8B5CF6', // Purple
  },

  // By urgency
  urgency: {
    low: '#6B7280', // Gray
    medium: '#F59E0B', // Orange
    high: '#EF4444', // Red
    overdue: '#DC2626', // Dark red
  },
};
```

---

## 📊 Status Implementacji (Stan na 26.06.2025)

### ✅ UKOŃCZONE ZADANIA

#### 🏗️ Fundament techniczny

- **7.1.1** ✅ Rozbudowa modelu Task dla kalendarza
- **7.1.2** ✅ Model CalendarSettings z enumerami
- **7.2.1** ✅ API calendar views (month/week/day/route.ts)
- **7.2.2** ✅ API calendar settings (GET/PATCH)
- **7.2.3** ✅ API operacji na eventach (POST/PATCH/DELETE)
- **7.3.1** ✅ CalendarService z kompletną logiką biznesową
- **7.3.2** ✅ Event conflict detection z algorytmami sugerowania terminów
- **7.3.3** ✅ Validatory Zod i typy TypeScript

#### 🖥️ Komponenty UI

- **7.4.1** ✅ Calendar library selection (FullCalendar React)
- **7.4.2** ✅ CalendarView component z pełną funkcjonalnością
- **7.4.3** ✅ CalendarNavigation component
- **7.4.4** ✅ CalendarFilters component (desktop + mobile)
- **7.5.1** ✅ EventDetailsModal z akcjami użytkownika
- **7.5.2** ✅ CreateEventDialog z formularzem walidowanym

#### 🔧 Narzędzia i integracja

- **7.8.1** ✅ Calendar hooks (useCalendar, useCalendarEvents, useCalendarSettings)
- **7.8.2** ✅ Date manipulation utilities z obsługą konfliktów

#### 🛠️ Infrastruktura

- ✅ **Migracja bazy danych** wykonana pomyślnie
- ✅ **Build proces** - projekt kompiluje się bez błędów TypeScript
- ✅ **API routes** kompatybilne z Next.js 15 App Router
- ✅ **TypeScript errors** naprawione w całym module

### ⏳ ZADANIA NIEUKOŃCZONE (Niski priorytet)

- **7.5.3** ⏸️ QuickAddEvent component (opcjonalne)
- **7.6.1** ⏸️ CalendarSettingsDialog (opcjonalne)
- **7.7.x** ⏸️ External calendar integration (Google/Apple)

### 🎯 Funkcjonalności dostępne w systemie

1. **Pełny widok kalendarza** z przełączaniem month/week/day
2. **Tworzenie eventów kalendarza** bezpośrednio z interfejsu
3. **Filtrowanie wydarzeń** według członków rodziny i statusu
4. **Zarządzanie ustawieniami kalendarza** dla każdej rodziny
5. **Wykrywanie konfliktów** czasowych między zadaniami
6. **Responsywny design** działający na desktop i mobile
7. **Integracja z systemem zadań** rodzinnych
8. **API endpoints** gotowe do rozszerzenia o dodatkowe funkcje

### 📈 Gotowość modułu: **~85%**

**Moduł kalendarza jest w pełni funkcjonalny** dla podstawowych potrzeb użytkowników i gotowy do użycia w systemie produkcyjnym.
