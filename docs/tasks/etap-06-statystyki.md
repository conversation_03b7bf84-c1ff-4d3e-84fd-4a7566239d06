# Etap 6: <PERSON><PERSON><PERSON> statystyk i historii

## Cel etapu

Implementacja systemu zbierania, analizy i wizualizacji danych o aktywności rodziny - statystyki wykonania zadań, historia aktywności, raporty i dashboard z wykresami.

## Oszacowany czas

**12-14 godzin**

## Priorytet

🟡 **ŚREDNI** - Funkcja analityczna i motywacyjna

---

## Zadania do wykonania

### 6.1 Modele danych dla statystyk

**Czas: 2h | Priorytet: Wysoki**

- [x] **6.1.1** Model ActivityLog

  ```prisma
  model ActivityLog {
    id        String       @id @default(cuid())
    familyId  String
    userId    String?
    memberId  String?
    action    ActivityAction
    entityType String      // "task", "family", "member"
    entityId  String?
    metadata  Json?        // Dodatkowe dane specyficzne dla akcji
    timestamp DateTime     @default(now())

    // Relations
    family    Family       @relation(fields: [familyId], references: [id], onDelete: Cascade)
    user      User?        @relation(fields: [userId], references: [id])
    member    FamilyMember? @relation(fields: [memberId], references: [id])

    @@index([familyId, timestamp])
    @@index([familyId, action, timestamp])
    @@map("activity_logs")
  }

  enum ActivityAction {
    TASK_CREATED
    TASK_COMPLETED
    TASK_VERIFIED
    TASK_ASSIGNED
    TASK_SUSPENDED
    MEMBER_JOINED
    MEMBER_LEFT
    POINTS_AWARDED
    FAMILY_CREATED
  }
  ```

- [x] **6.1.2** Model FamilyStats (agregowane dane)

  ```prisma
  model FamilyStats {
    id                String   @id @default(cuid())
    familyId          String
    date              DateTime // Data dla której są statystyki
    period            StatsPeriod @default(DAILY)

    // Task statistics
    tasksCreated      Int      @default(0)
    tasksCompleted    Int      @default(0)
    tasksVerified     Int      @default(0)
    completionRate    Float    @default(0)

    // Points statistics
    totalPointsAwarded Int     @default(0)
    averagePointsPerTask Float @default(0)

    // Member activity
    activeMembersCount Int     @default(0)

    // Metadata
    createdAt         DateTime @default(now())
    updatedAt         DateTime @updatedAt

    // Relations
    family            Family   @relation(fields: [familyId], references: [id], onDelete: Cascade)

    @@unique([familyId, date, period])
    @@map("family_stats")
  }

  enum StatsPeriod {
    DAILY
    WEEKLY
    MONTHLY
    YEARLY
  }
  ```

- [x] **6.1.3** Model MemberStats

  ```prisma
  model MemberStats {
    id                String       @id @default(cuid())
    memberId          String
    date              DateTime
    period            StatsPeriod  @default(DAILY)

    // Task performance
    tasksAssigned     Int          @default(0)
    tasksCompleted    Int          @default(0)
    tasksOnTime       Int          @default(0)
    tasksLate         Int          @default(0)
    completionRate    Float        @default(0)

    // Points
    pointsEarned      Int          @default(0)
    pointsFromTasks   Int          @default(0)
    bonusPoints       Int          @default(0)

    // Activity
    activeDays        Int          @default(0)
    streakDays        Int          @default(0)

    createdAt         DateTime     @default(now())
    updatedAt         DateTime     @updatedAt

    // Relations
    member            FamilyMember @relation(fields: [memberId], references: [id], onDelete: Cascade)

    @@unique([memberId, date, period])
    @@map("member_stats")
  }
  ```

### 6.2 Serwisy dla zbierania statystyk

**Czas: 3h | Priorytet: Wysoki**

- [x] **6.2.1** ActivityLogService

  ```typescript
  // lib/services/activity.service.ts
  export class ActivityLogService {
    static async logActivity(data: {
      familyId: string;
      userId?: string;
      memberId?: string;
      action: ActivityAction;
      entityType: string;
      entityId?: string;
      metadata?: Record<string, any>;
    }): Promise<ActivityLog>;

    static async getActivityHistory(
      familyId: string,
      filters: ActivityFilters
    ): Promise<ActivityLog[]>;

    static async getRecentActivity(familyId: string, limit: number = 10): Promise<ActivityLog[]>;

    static async getMemberActivity(memberId: string, dateRange: DateRange): Promise<ActivityLog[]>;
  }
  ```

- [x] **6.2.2** StatsService

  ```typescript
  // lib/services/stats.service.ts
  export class StatsService {
    // Family statistics
    static async generateFamilyStats(
      familyId: string,
      date: Date,
      period: StatsPeriod
    ): Promise<FamilyStats>;
    static async getFamilyStats(
      familyId: string,
      dateRange: DateRange,
      period: StatsPeriod
    ): Promise<FamilyStats[]>;
    static async getFamilyCompletionRate(familyId: string, dateRange: DateRange): Promise<number>;

    // Member statistics
    static async generateMemberStats(
      memberId: string,
      date: Date,
      period: StatsPeriod
    ): Promise<MemberStats>;
    static async getMemberStats(
      memberId: string,
      dateRange: DateRange,
      period: StatsPeriod
    ): Promise<MemberStats[]>;
    static async getMemberRanking(
      familyId: string,
      metric: 'points' | 'completion_rate',
      period: StatsPeriod
    ): Promise<MemberRanking[]>;

    // Advanced analytics
    static async getTaskCategoryStats(
      familyId: string,
      dateRange: DateRange
    ): Promise<CategoryStats[]>;
    static async getProductivityTrends(
      familyId: string,
      dateRange: DateRange
    ): Promise<TrendData[]>;
    static async generateInsights(familyId: string): Promise<FamilyInsights>;
  }
  ```

- [x] **6.2.3** ReportsService
  ```typescript
  // lib/services/reports.service.ts
  export class ReportsService {
    static async generateWeeklyReport(familyId: string, weekStart: Date): Promise<WeeklyReport>;
    static async generateMonthlyReport(familyId: string, month: Date): Promise<MonthlyReport>;
    static async exportStatsToCSV(familyId: string, dateRange: DateRange): Promise<Buffer>;
    static async exportActivityToCSV(familyId: string, dateRange: DateRange): Promise<Buffer>;
  }
  ```

### 6.3 API Routes dla statystyk

**Czas: 2h | Priorytet: Średni**

- [x] **6.3.1** API statystyk rodziny

  ```typescript
  // app/api/families/[familyId]/stats/route.ts
  export async function GET(); // Statystyki rodziny z filtrami

  // app/api/families/[familyId]/stats/overview/route.ts
  export async function GET(); // Przegląd statystyk (dashboard)

  // app/api/families/[familyId]/stats/trends/route.ts
  export async function GET(); // Dane do wykresów trendów
  ```

- [x] **6.3.2** API statystyk członków

  ```typescript
  // app/api/families/[familyId]/members/[memberId]/stats/route.ts
  export async function GET(); // Statystyki konkretnego członka

  // app/api/families/[familyId]/stats/leaderboard/route.ts
  export async function GET(); // Ranking członków rodziny
  ```

- [x] **6.3.3** API raportów i eksportu

  ```typescript
  // app/api/families/[familyId]/reports/weekly/route.ts
  export async function GET(); // Raport tygodniowy

  // app/api/families/[familyId]/reports/monthly/route.ts
  export async function GET(); // Raport miesięczny

  // app/api/families/[familyId]/export/stats/route.ts
  export async function GET(); // Eksport danych do CSV/PDF
  ```

### 6.4 Automated Stats Generation

**Czas: 1h | Priorytet: Średni**

- [x] **6.4.1** Cron job dla statystyk

  ```typescript
  // app/api/cron/stats/route.ts
  export async function GET() {
    // Generowanie dziennych statystyk
    // Agregacja tygodniowych i miesięcznych
    // Cleanup starych danych aktywności
  }
  ```

- [x] **6.4.2** Real-time stats updates
  ```typescript
  // lib/hooks/stats.hooks.ts
  export function useStatsUpdater() {
    // Hook do aktualizacji statystyk w real-time
    // Integracja z task completion events
    // Invalidation cache dla statystyk
  }
  ```

### 6.5 Komponenty UI - Dashboard statystyk

**Czas: 3h | Priorytet: Średni**

- [x] **6.5.1** StatsDashboard component

  ```typescript
  // components/stats/StatsDashboard.tsx
  interface StatsDashboardProps {
    familyId: string;
    period: StatsPeriod;
    dateRange?: DateRange;
  }
  ```
  - Główny dashboard z kluczowymi metrykami
  - Completion rate w procentach
  - Total points earned
  - Active members count
  - Quick insights i achievements

- [x] **6.5.2** CompletionRateChart

  ```typescript
  // components/stats/CompletionRateChart.tsx
  // - Wykres słupkowy completion rate over time
  // - Możliwość zmiany okresów (daily/weekly/monthly)
  // - Hover tooltips z dodatkowymi informacjami
  ```

- [x] **6.5.3** PointsLeaderboard

  ```typescript
  // components/stats/PointsLeaderboard.tsx
  // - Ranking członków rodziny
  // - Points earned w wybranym okresie
  // - Progress bars i avatary
  // - Badges dla achievements
  ```

- [x] **6.5.4** ActivityTimeline
  ```typescript
  // components/stats/ActivityTimeline.tsx
  // - Timeline recent activity
  // - Ikony dla różnych typów akcji
  // - Relative timestamps
  // - Infinite scroll lub pagination
  ```

### 6.6 Komponenty UI - Wykresy i visualizacje

**Czas: 3h | Priorytet: Średni**

- [x] **6.6.1** Biblioteka wykresów

  ```bash
  npm install recharts
  # lub
  npm install @tremor/react
  ```
  - Wybór i konfiguracja biblioteki wykresów
  - Konfiguracja theme zgodnie z designem
  - Responsive charts

- [ ] **6.6.2** TaskTrendsChart

  ```typescript
  // components/stats/TaskTrendsChart.tsx
  // - Line chart showing task completion trends
  // - Multiple series (completed, assigned, overdue)
  // - Zoom i pan functionality
  // - Export chart as image
  ```

- [ ] **6.6.3** CategoryBreakdownChart

  ```typescript
  // components/stats/CategoryBreakdownChart.tsx
  // - Pie chart lub donut chart
  // - Breakdown tasks by category/type
  // - Interactive legends
  // - Drill-down capability
  ```

- [ ] **6.6.4** MemberPerformanceChart
  ```typescript
  // components/stats/MemberPerformanceChart.tsx
  // - Radar chart dla member performance
  // - Multiple metrics (completion rate, points, consistency)
  // - Comparison między członkami
  ```

### 6.7 Historii i archiwizacja

**Czas: 1h | Priorytet: Niski**

- [ ] **6.7.1** TaskHistory component

  ```typescript
  // components/history/TaskHistory.tsx
  // - Lista completed tasks z filtrami
  // - Search i sortowanie
  // - Export do różnych formatów
  // - Pagination dla performance
  ```

- [x] **6.7.2** Data archival service
  ```typescript
  // lib/services/archival.service.ts
  export class ArchivalService {
    static async archiveOldActivityLogs(olderThan: Date): Promise<number>;
    static async cleanupExpiredStats(olderThan: Date): Promise<number>;
    static async exportHistoricalData(familyId: string, dateRange: DateRange): Promise<Buffer>;
  }
  ```

### 6.8 Hooks i state management

**Czas: 1h | Priorytet: Średni**

- [x] **6.8.1** Stats hooks

  ```typescript
  // hooks/useStats.ts
  export function useFamilyStats(familyId: string, period: StatsPeriod, dateRange?: DateRange);
  export function useMemberStats(memberId: string, period: StatsPeriod);
  export function useActivityLog(familyId: string, filters?: ActivityFilters);
  export function useLeaderboard(familyId: string, metric: string, period: StatsPeriod);
  ```

- [x] **6.8.2** Real-time updates
  ```typescript
  // hooks/useRealtimeStats.ts
  export function useRealtimeStats(familyId: string) {
    // WebSocket connection dla real-time updates
    // Auto-refresh critical stats
    // Optimistic updates
  }
  ```

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [ ] System automatycznie zbiera dane o aktywności
- [ ] Generowane są statystyki dzienne, tygodniowe i miesięczne
- [ ] Dashboard wyświetla kluczowe metryki w przejrzysty sposób
- [ ] Wykresy są interaktywne i responsywne
- [ ] Możliwość eksportu danych do CSV/PDF
- [ ] Historia aktywności jest przeszukiwalna

### ✅ Techniczne

- [ ] Cron jobs generują statystyki automatycznie
- [ ] Database queries są zoptymalizowane (indexy)
- [ ] Large datasets są paginated
- [ ] Cache strategia dla frequently accessed stats
- [ ] Error handling dla chart rendering

### ✅ UX/UI

- [ ] Dashboard ładuje się szybko (<2s)
- [ ] Wykresy są czytelne i dobrze opisane
- [ ] Color scheme jest consistent z designem
- [ ] Mobile-friendly charts i tables
- [ ] Loading states dla długich operacji

### ✅ Performance

- [ ] Stats queries wykonują się w <500ms
- [ ] Charts renderują się płynnie
- [ ] Infinite scroll działa bez lagów
- [ ] Memory usage jest kontrolowany

---

## Zależności

**Wymaga ukończenia:**

- Etap 1: Fundament i konfiguracja
- Etap 2: Uwierzytelnianie i autoryzacja
- Etap 3: Moduł rodziny
- Etap 4: Moduł zadań
- Etap 5: Moduł zawieszeń (opcjonalnie)

## Blokuje

**Brak** - To jest moduł dodatkowy

---

## Metryki i KPI (z PRD)

### 📊 Metryki użytkowania

- **Liczba aktywnych rodzin**: tracking weekly/monthly active families
- **Średnia liczba zadań per rodzina**: zadania created vs completed
- **Współczynnik ukończenia zadań**: percentage completed tasks
- **Średnia liczba członków per rodzina**: family size analytics

### 📊 Metryki zaangażowania

- **Dzienny/tygodniowy współczynnik aktywności**: active users tracking
- **Średni czas spędzany w aplikacji**: session duration
- **Współczynnik powracających użytkowników**: retention rates
- **Liczba interakcji**: task operations, verifications

### 📊 Analytics Events

```typescript
// Przykłady event tracking
interface AnalyticsEvent {
  name: string;
  properties: Record<string, any>;
  timestamp: Date;
}

// Events to track:
// - task_completed
// - family_created
// - member_invited
// - points_earned
// - feature_used
```

---

## User Stories

### 👤 Jako rodzic

- Chcę widzieć jak aktywne są dzieci w wykonywaniu zadań
- Chcę śledzić postępy całej rodziny w czasie
- Chcę otrzymywać insights o productivity patterns
- Chcę móc eksportować dane do analizy zewnętrznej
- Chcę widzieć trends i patterns w wykonywaniu zadań

### 👤 Jako dziecko

- Chcę widzieć moje postępy i achievements
- Chcę porównywać się z innymi członkami rodziny
- Chcę widzieć historię moich wykonanych zadań
- Chcę śledzić swoje punkty w czasie

### 👤 Jako administrator rodziny

- Chcę analizować effectiveness of task system
- Chcę identyfikować członków requiring more support
- Chcę optimalizować task distribution
- Chcę mierzyć ROI z używania aplikacji

---

## Charts Library Comparison

### Recharts (Rekomendowane)

```typescript
// Pros: Lightweight, React-native, good documentation
// Cons: Limited advanced features
npm install recharts
```

### Tremor React (Alternatywa)

```typescript
// Pros: Beautiful by default, pre-styled components
// Cons: Less customizable, larger bundle
npm install @tremor/react
```

### Chart.js + react-chartjs-2

```typescript
// Pros: Very powerful, lots of chart types
// Cons: Heavier, imperative API
npm install chart.js react-chartjs-2
```

---

## Notatki implementacyjne

### Performance Considerations

- **Aggregate data** daily to avoid real-time calculations
- **Index heavily queried** columns (familyId, date, period)
- **Use materialized views** for complex analytics queries
- **Implement caching** for frequently accessed stats
- **Paginate large datasets** (>1000 records)

### Data Retention Policy

```typescript
// Suggested retention periods
const RETENTION_POLICY = {
  activity_logs: '2 years',
  daily_stats: '1 year',
  weekly_stats: '3 years',
  monthly_stats: 'indefinite',
  yearly_stats: 'indefinite',
};
```

### Real-time vs Batch Processing

- **Real-time**: Critical metrics (points, completion status)
- **Batch**: Historical analysis, trends, aggregations
- **Hybrid**: Recent activity + cached historical data

### Analytics Privacy

- **Anonymize exported data** for external analysis
- **Allow users to opt-out** of detailed tracking
- **GDPR compliance** for data export/deletion
- **Family-level privacy settings**

### Mobile Optimizations

- **Responsive charts** that work on small screens
- **Touch-friendly interactions** (zoom, pan)
- **Simplified mobile dashboards** with key metrics only
- **Offline viewing** of cached stats
