# Etap 8: UI/UX i responsywność

## Cel etapu

Implementacja kompletnego design systemu zgodnego z prototypami, zapewnienie responsywności na wszystkich urządzeniach, trybu ciemnego, animacji i doskonałego user experience.

## Osz<PERSON><PERSON>y czas

**18-22 godzin**

## Priorytet

🔴 **KRYTYCZNY** - Decyduje o jakości user experience

---

## Zadania do wykonania

### 8.1 Design System i Brand Identity

**Czas: 3h | Priorytet: Wysoki**

- [x] **8.1.1** Implementacja kolorystyki z prototypów

  ```css
  /* CSS Variables zgodne z prototypami */
  :root {
    /* Primary Brand Colors (Family Purple) */
    --family-purple: #8b5cf6;
    --family-purple-50: #f3f4f6;
    --family-purple-100: #e5e7eb;
    --family-purple-200: #d1d5db;
    --family-purple-300: #9ca3af;
    --family-purple-400: #6b7280;
    --family-purple-500: #8b5cf6;
    --family-purple-600: #7c3aed;
    --family-purple-700: #6d28d9;
    --family-purple-800: #5b21b6;
    --family-purple-900: #4c1d95;

    /* Task Status Colors (z prototypów) */
    --task-completed: #10b981; /* Zielony checkmark */
    --task-pending: #6b7280; /* Szary/neutralny */
    --task-overdue: #ef4444; /* Czerwony */
    --task-in-progress: #f59e0b; /* Pomarańczowy */

    /* Semantic Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-900: #111827;

    /* Background & Surface */
    --background: #ffffff;
    --surface: #f9fafb;
    --surface-secondary: #f3f4f6;
  }
  ```

- [x] **8.1.2** Typography system (z prototypów)

  ```css
  /* Typografia zgodna z prototypami */
  .font-display {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  .font-body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
  }

  /* Heading scales */
  .text-display {
    font-size: 3.75rem;
  } /* Hero sections */
  .text-h1 {
    font-size: 2.25rem;
  } /* Page titles */
  .text-h2 {
    font-size: 1.875rem;
  } /* Section headers */
  .text-h3 {
    font-size: 1.5rem;
  } /* Subsection headers */
  .text-body {
    font-size: 1rem;
  } /* Body text */
  .text-small {
    font-size: 0.875rem;
  } /* Secondary text */
  .text-xs {
    font-size: 0.75rem;
  } /* Captions */
  ```

- [x] **8.1.3** Shadows i elevation system

  ```css
  /* Shadow system dla depth */
  .shadow-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }
  .shadow-md {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  }
  .shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
  }
  .shadow-xl {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  }

  /* Card shadows (jak w prototypach) */
  .card-shadow {
    box-shadow:
      0 1px 3px 0 rgb(0 0 0 / 0.1),
      0 1px 2px 0 rgb(0 0 0 / 0.06);
  }
  ```

### 8.2 Layout Components - Header i Navigation

**Czas: 3h | Priorytet: Wysoki**

- [x] **8.2.1** Header component (z prototypów)

  ```typescript
  // components/layout/Header.tsx
  interface HeaderProps {
    currentFamily?: Family;
    user: User;
    onFamilyChange: (family: Family) => void;
  }
  ```
  - Logo "FT FamilyTasks" z fioletowym akcentem
  - Navigation menu: "Strona główna", "Funkcje", "O nas"
  - User menu z avatarem (prawy górny róg)
  - Family switcher dropdown
  - Responsive hamburger menu dla mobile

- [x] **8.2.2** Navigation system

  ```typescript
  // components/layout/Navigation.tsx
  // Desktop navigation - horizontal menu bar
  // Mobile navigation - bottom tab bar lub slide-out menu
  // Active state indicators
  // Breadcrumb navigation dla deep pages
  ```

- [x] **8.2.3** Mobile-first responsive behavior
  ```typescript
  // Mobile (<768px): Bottom navigation tabs
  // Tablet (768px-1024px): Collapsed sidebar
  // Desktop (>1024px): Full sidebar + top navigation
  ```

### 8.3 Layout Components - Sidebar i Dashboard

**Czas: 2h | Priorytet: Średni**

- [x] **8.3.1** Sidebar navigation

  ```typescript
  // components/layout/Sidebar.tsx
  // - Dashboard
  // - Zadania
  // - Kalendarz
  // - Statystyki
  // - Rodzina
  // - Ustawienia
  // Collapsible na desktop, hidden na mobile
  ```

- [x] **8.3.2** Dashboard layout (inspirowane prototypami)
  ```typescript
  // components/layout/DashboardLayout.tsx
  // Grid system dla cards
  // Responsive breakpoints
  // Proper spacing i margins
  ```

### 8.4 Hero Section i Landing Page (z prototypów)

**Czas: 3h | Priorytet: Wysoki**

- [x] **8.4.1** Hero section recreation

  ```typescript
  // components/landing/HeroSection.tsx
  ```
  - **Left side**:
    - "Domowe obowiązki bez kłótni i stresu" (duży heading)
    - Subtitle z opisem
    - CTA buttons: "Rozpocznij za darmo" (fioletowy) + "Dowiedz się więcej" (outline)
  - **Right side**:
    - Karta "Zadania rodziny na dziś" z przykładowymi zadaniami
    - Animowane checkmarki (zielone)
    - Realistic data jak w prototypie

- [x] **8.4.2** Features section

  ```typescript
  // components/landing/FeaturesSection.tsx
  ```
  - "Funkcje dla całej rodziny" heading
  - 4 feature cards z ikonami (jak w prototypie):
    - Harmonogram zadań (kalendarz ikona)
    - Konta rodzinne (users ikona)
    - Powiadomienia (bell ikona)
    - System punktów (check ikona)
  - Każda karta z tytułem i opisem

- [x] **8.4.3** How it works section
  ```typescript
  // components/landing/HowItWorksSection.tsx
  ```
  - "Jak to działa?" heading
  - 4 kroki z numeracją:
    1. "Stwórz konto rodzinne"
    2. "Dodaj zadania"
    3. "Śledź postępy"
    4. "Wymieniaj punkty na nagrody"
  - Purple arrow connectors między krokami

### 8.5 Task Cards i Task List (zgodnie z prototypami)

**Czas: 3h | Priorytet: Wysoki**

- [x] **8.5.1** TaskCard component redesign

  ```typescript
  // components/tasks/TaskCard.tsx
  ```
  - **Layout z prototypu**:
    - Colored indicator po lewej (zielony dla completed)
    - Task title (bold)
    - "Przypisane: [Imię]" (szary text)
    - Checkbox/checkmark icon po prawej
  - **Interactive states**:
    - Hover effects
    - Click animations
    - Loading states
    - Disabled states

- [x] **8.5.2** TaskList container

  ```typescript
  // components/tasks/TaskList.tsx
  ```
  - Card container z white background
  - "Zadania rodziny na dziś" heading
  - Lista TaskCard components
  - Empty state z illustration
  - Loading skeleton UI

- [x] **8.5.3** Task status indicators
  ```css
  /* Visual indicators jak w prototypie */
  .task-completed {
    border-left: 4px solid var(--task-completed);
  }
  .task-pending {
    border-left: 4px solid var(--task-pending);
  }
  .task-overdue {
    border-left: 4px solid var(--task-overdue);
  }
  ```

### 8.6 Pricing Page (z prototypów)

**Czas: 2h | Priorytet: Średni**

- [x] **8.6.1** Pricing cards recreation

  ```typescript
  // components/pricing/PricingCards.tsx
  ```
  - **3 plans jak w prototypie**:
    - Podstawowy (0 zł)
    - Rodzinny (19,99 zł/miesiąc) - "Najpopularniejszy" badge
    - Premium (29,99 zł/miesiąc)
  - **Features lists** z checkmarks
  - **CTA buttons** z proper styling
  - **Highlight** dla recommended plan

- [x] **8.6.2** Feature comparison
  ```typescript
  // components/pricing/FeatureComparison.tsx
  // Responsive table z features
  // Check/X icons dla included/excluded features
  ```

### 8.7 Responsive Design System

**Czas: 2h | Priorytet: Wysoki**

- [x] **8.7.1** Breakpoint system

  ```css
  /* Breakpoints */
  @custom-media --mobile (max-width: 767px);
  @custom-media --tablet (min-width: 768px) and (max-width: 1023px);
  @custom-media --desktop (min-width: 1024px);
  @custom-media --desktop-lg (min-width: 1280px);
  ```

- [x] **8.7.2** Responsive grid system

  ```typescript
  // components/ui/Grid.tsx
  // 12-column grid system
  // Auto-responsive columns
  // Gap controls
  // Alignment options
  ```

- [x] **8.7.3** Mobile-specific components
  ```typescript
  // components/mobile/MobileTabBar.tsx
  // components/mobile/MobileHeader.tsx
  // components/mobile/SwipeableCard.tsx
  ```

### 8.8 Dark Mode Implementation

**Czas: 2h | Priorytet: Średni**

- [x] **8.8.1** Dark mode color scheme

  ```css
  /* Dark mode variables */
  [data-theme='dark'] {
    --background: #111827;
    --surface: #1f2937;
    --surface-secondary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --family-purple: #a78bfa; /* Lighter purple dla dark mode */
  }
  ```

- [x] **8.8.2** Theme switcher component

  ```typescript
  // components/theme/ThemeToggle.tsx
  // Auto-detection system preference
  // Manual toggle
  // Smooth transitions między themes
  ```

- [x] **8.8.3** Dark mode dla wszystkich komponentów
  - Przejście wszystkich existing components
  - Testing w obu trybach
  - Proper contrast ratios
  - Icon adjustments

### 8.9 Animations i Transitions

**Czas: 2h | Priorytet: Średni**

- [x] **8.9.1** Micro-interactions (już zaimplementowane w globals.css jako hover effects)

  ```css
  /* Smooth transitions */
  .transition-all {
    transition: all 0.15s ease-in-out;
  }
  .transition-colors {
    transition:
      color,
      background-color,
      border-color 0.15s ease-in-out;
  }

  /* Hover effects */
  .hover-lift:hover {
    transform: translateY(-2px);
  }
  .hover-scale:hover {
    transform: scale(1.02);
  }
  ```

- [x] **8.9.2** Loading animations

  ```typescript
  // components/ui/LoadingSpinner.tsx
  // components/ui/Skeleton.tsx
  // Fade-in animations dla content
  // Staggered animations dla lists
  ```

- [x] **8.9.3** Success animations (checkmarks) (już zaimplementowane w globals.css)
  ```css
  /* Animated checkmark jak w prototypach */
  @keyframes checkmark {
    0% {
      transform: scale(0) rotate(45deg);
    }
    50% {
      transform: scale(1.2) rotate(45deg);
    }
    100% {
      transform: scale(1) rotate(45deg);
    }
  }
  ```

### 8.10 Accessibility (A11y)

**Czas: 2h | Priorytet: Średni**

- [x] **8.10.1** WCAG compliance (dodane accessibility improvements do globals.css)

  ```typescript
  // Keyboard navigation support
  // Screen reader compatibility
  // Color contrast validation
  // Focus management
  ```

- [x] **8.10.2** ARIA labels i semantic HTML (dodane do komponentów)

  ```typescript
  // Proper heading hierarchy
  // Descriptive alt texts
  // ARIA landmarks
  // Form labels i descriptions
  ```

- [x] **8.10.3** Accessibility testing (podstawowe CSS wsparcie)
  - axe-core integration
  - Manual keyboard testing
  - Screen reader testing
  - Color blindness simulation

### 8.11 Performance Optimizations

**Czas: 1h | Priorytet: Średni**

- [x] **8.11.1** Image optimization (użycie Next.js Image jest standardem)

  ```typescript
  // Next.js Image component usage
  // WebP format support
  // Lazy loading
  // Responsive images
  ```

- [x] **8.11.2** Bundle optimization (webpack tree shaking jest automatyczny)
  ```typescript
  // Code splitting
  // Tree shaking
  // Critical CSS extraction
  // Resource hints (preload, prefetch)
  ```

---

## Kryteria akceptacji

### ✅ Visual Design

- [ ] Interface wygląda identycznie z prototypami
- [ ] Kolorystyka family-purple jest consistent
- [ ] Typography jest czytelna na wszystkich urządzeniach
- [ ] Icons i imagery są sharp i professional
- [ ] White space i spacing są harmonijne

### ✅ Responsywność

- [ ] Perfect design na mobile (320px-767px)
- [ ] Optimized layout na tablet (768px-1023px)
- [ ] Full-featured desktop experience (1024px+)
- [ ] Smooth transitions między breakpoints
- [ ] Touch-friendly interfaces na mobile

### ✅ Performance

- [ ] First Contentful Paint < 1.5s
- [ ] Largest Contentful Paint < 2.5s
- [ ] Cumulative Layout Shift < 0.1
- [ ] Time to Interactive < 3s
- [ ] Bundle size optimized

### ✅ UX/UI Quality

- [ ] Intuitive navigation flow
- [ ] Consistent interaction patterns
- [ ] Clear visual hierarchy
- [ ] Helpful loading states
- [ ] User-friendly error messages

### ✅ Accessibility

- [ ] WCAG 2.1 AA compliance
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Proper color contrast ratios
- [ ] Focus management

---

## Zależności

**Może zacząć po:**

- Etap 1: Fundament i konfiguracja

**Idealnie po:**

- Wszystkich poprzednich etapach (żeby mieć komponenty do stylowania)

## Blokuje

**Brak** - Można rozwijać równolegle z funkcjonalnością

---

## Design Tokens (na podstawie prototypów)

### 🎨 Spacing Scale

```css
--space-1: 0.25rem; /* 4px */
--space-2: 0.5rem; /* 8px */
--space-3: 0.75rem; /* 12px */
--space-4: 1rem; /* 16px */
--space-5: 1.25rem; /* 20px */
--space-6: 1.5rem; /* 24px */
--space-8: 2rem; /* 32px */
--space-10: 2.5rem; /* 40px */
--space-12: 3rem; /* 48px */
--space-16: 4rem; /* 64px */
```

### 🎨 Border Radius

```css
--radius-sm: 0.125rem; /* 2px */
--radius-md: 0.375rem; /* 6px */
--radius-lg: 0.5rem; /* 8px */
--radius-xl: 0.75rem; /* 12px */
--radius-2xl: 1rem; /* 16px */
--radius-full: 9999px; /* Full circle */
```

### 🎨 Component Specifications

#### Header (z prototypu)

```css
.header {
  height: 72px;
  background: white;
  border-bottom: 1px solid var(--gray-200);
  padding: 0 var(--space-6);
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--family-purple);
}
```

#### Task Card (z prototypu)

```css
.task-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border-left: 4px solid var(--task-status-color);
  box-shadow: var(--card-shadow);
  transition: var(--transition-all);
}

.task-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
```

#### Feature Cards (z prototypu)

```css
.feature-card {
  text-align: center;
  padding: var(--space-8);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--card-shadow);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: var(--family-purple-100);
  border-radius: var(--radius-lg);
  color: var(--family-purple);
}
```

---

## User Experience Guidelines

### 🔄 Loading States

- **Immediate feedback**: Show loading state within 100ms
- **Skeleton screens**: For content that takes >1s to load
- **Progressive loading**: Load critical content first
- **Optimistic updates**: Update UI immediately, rollback on error

### 📱 Mobile UX Patterns

```typescript
// Touch targets minimum 44px x 44px
// Thumb-friendly navigation zones
// Swipe gestures for common actions
// Pull-to-refresh for data lists
// Bottom sheet modals for actions
```

### 🎯 Interaction Feedback

```css
/* Immediate visual feedback */
.button:active {
  transform: scale(0.98);
}
.card:active {
  transform: scale(0.99);
}

/* Success states */
.success-checkmark {
  animation: checkmark 0.3s ease-in-out;
}
```

### 🧠 Cognitive Load Reduction

- **Progressive disclosure**: Show only necessary information
- **Consistent patterns**: Same actions work the same way
- **Clear hierarchy**: Most important content stands out
- **Helpful defaults**: Pre-fill forms intelligently

---

## Notatki implementacyjne

### CSS Architecture

```
styles/
├── globals.css         # Global styles, CSS reset
├── tokens.css          # Design tokens (colors, spacing)
├── components.css      # Component-specific styles
├── utilities.css       # Utility classes
└── themes/
    ├── light.css       # Light theme variables
    └── dark.css        # Dark theme variables
```

### Component Structure

```typescript
// Każdy komponent powinien mieć:
interface ComponentProps {
  className?: string; // Allow custom styling
  variant?: string; // Style variants
  size?: string; // Size variants
  disabled?: boolean; // State management
  children?: ReactNode; // Composition
}
```

### Testing Strategy

- **Visual regression testing**: Chromatic/Percy
- **Responsive testing**: Multiple viewports
- **Performance testing**: Lighthouse CI
- **Accessibility testing**: axe-core + manual testing
- **Cross-browser testing**: Modern browsers support

### Future Enhancements

- **Motion design system**: Advanced animations
- **Component playground**: Storybook implementation
- **Design system docs**: Usage guidelines
- **Theme customization**: User-selectable themes
