# Etap 4: <PERSON><PERSON><PERSON>

## Cel etapu

Implementacja kompletnego systemu zarządzania zadaniami rodzinnymi - tworz<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON>, we<PERSON><PERSON><PERSON><PERSON><PERSON> wykonania, system punktowy i interfejsy użytkownika zgodnie z prototypami.

## Oszacowany czas

**16-20 godzin**

## Priorytet

🔴 **KRYTYCZNY** - Główna funkcjonalność aplikacji

---

## Zadania do wykonania

### 4.1 Modele danych dla zadań

**Czas: 3h | Priorytet: Wysoki**

- [x] **4.1.1** Model Task

  ```prisma
  model Task {
    id          String      @id @default(cuid())
    familyId    String
    title       String
    description String?
    points      Int         @default(1)
    weight      TaskWeight  @default(NORMAL)
    frequency   TaskFrequency @default(ONCE)
    status      TaskStatus  @default(ACTIVE)
    dueDate     DateTime?
    createdAt   DateTime    @default(now())
    updatedAt   DateTime    @updatedAt

    // Assignment
    assignedToId String?
    assignedBy   String
    assignedAt   DateTime?

    // Completion tracking
    completions TaskCompletion[]

    // Relations
    family      Family      @relation(fields: [familyId], references: [id], onDelete: Cascade)
    assignedTo  FamilyMember? @relation("AssignedTasks", fields: [assignedToId], references: [id])
    assignedByUser FamilyMember @relation("CreatedTasks", fields: [assignedBy], references: [id])

    @@map("tasks")
  }

  enum TaskWeight {
    LIGHT    // 1 punkt
    NORMAL   // 3 punkty
    HEAVY    // 5 punktów
    CRITICAL // 10 punktów
  }

  enum TaskFrequency {
    ONCE        // Jednorazowe
    DAILY       // Codziennie
    WEEKLY      // Tygodniowo
    MONTHLY     // Miesięcznie
    CUSTOM      // Niestandardowe (z cron expression)
  }

  enum TaskStatus {
    ACTIVE      // Aktywne
    SUSPENDED   // Zawieszone
    COMPLETED   // Zakończone (dla jednorazowych)
    ARCHIVED    // Zarchiwizowane
  }
  ```

- [x] **4.1.2** Model TaskCompletion

  ```prisma
  model TaskCompletion {
    id            String            @id @default(cuid())
    taskId        String
    completedById String
    completedAt   DateTime          @default(now())
    verifiedById  String?
    verifiedAt    DateTime?
    status        CompletionStatus  @default(PENDING)
    notes         String?
    pointsAwarded Int              @default(0)

    // Relations
    task          Task             @relation(fields: [taskId], references: [id], onDelete: Cascade)
    completedBy   FamilyMember     @relation("CompletedTasks", fields: [completedById], references: [id])
    verifiedBy    FamilyMember?    @relation("VerifiedTasks", fields: [verifiedById], references: [id])

    @@map("task_completions")
  }

  enum CompletionStatus {
    PENDING     // Oczekuje weryfikacji
    APPROVED    // Zatwierdzono
    REJECTED    // Odrzucono
    AUTO_APPROVED // Automatycznie zatwierdzone
  }
  ```

- [x] **4.1.3** Model TaskReminder

  ```prisma
  model TaskReminder {
    id        String   @id @default(cuid())
    taskId    String
    remindAt  DateTime
    sent      Boolean  @default(false)
    sentAt    DateTime?

    // Relations
    task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)

    @@map("task_reminders")
  }
  ```

### 4.2 API Routes dla zadań

**Czas: 4h | Priorytet: Wysoki**

- [x] **4.2.1** CRUD operacje dla zadań

  ```typescript
  // app/api/families/[familyId]/tasks/route.ts
  export async function GET(); // Lista zadań rodziny z filtrami
  export async function POST(); // Tworzenie nowego zadania

  // app/api/families/[familyId]/tasks/[taskId]/route.ts
  export async function GET(); // Szczegóły zadania
  export async function PATCH(); // Edycja zadania
  export async function DELETE(); // Usunięcie zadania
  ```

- [x] **4.2.2** API wykonywania zadań

  ```typescript
  // app/api/families/[familyId]/tasks/[taskId]/complete/route.ts
  export async function POST(); // Oznaczenie jako wykonane
  export async function DELETE(); // Cofnięcie wykonania

  // app/api/families/[familyId]/tasks/[taskId]/verify/route.ts
  export async function POST(); // Weryfikacja wykonania
  export async function PATCH(); // Zmiana statusu weryfikacji
  ```

- [x] **4.2.3** API zarządzania przypisaniami

  ```typescript
  // app/api/families/[familyId]/tasks/[taskId]/assign/route.ts
  export async function POST(); // Przypisanie zadania
  export async function DELETE(); // Cofnięcie przypisania

  // app/api/families/[familyId]/tasks/bulk/route.ts
  export async function POST(); // Operacje bulk (assign, complete, delete)
  ```

### 4.3 Business Logic dla zadań

**Czas: 4h | Priorytet: Wysoki**

- [x] **4.3.1** TaskService

  ```typescript
  // lib/services/task.service.ts
  export class TaskService {
    // CRUD operations
    static async createTask(familyId: string, data: CreateTaskData): Promise<Task>;
    static async updateTask(taskId: string, data: UpdateTaskData): Promise<Task>;
    static async deleteTask(taskId: string): Promise<void>;

    // Assignment logic
    static async assignTask(taskId: string, memberId: string): Promise<Task>;
    static async unassignTask(taskId: string): Promise<Task>;

    // Completion logic
    static async completeTask(
      taskId: string,
      memberId: string,
      notes?: string
    ): Promise<TaskCompletion>;
    static async verifyCompletion(
      completionId: string,
      verifierId: string,
      approved: boolean
    ): Promise<TaskCompletion>;

    // Points calculation
    static async calculatePoints(task: Task): Promise<number>;
    static async awardPoints(completion: TaskCompletion): Promise<void>;

    // Recurring tasks
    static async generateRecurringTask(task: Task): Promise<Task | null>;
    static async processRecurringTasks(): Promise<void>;
  }
  ```

- [x] **4.3.2** PointsService

  ```typescript
  // lib/services/points.service.ts
  export class PointsService {
    static async calculateTaskPoints(task: Task, completion: TaskCompletion): Promise<number>;
    static async awardPoints(memberId: string, points: number, reason: string): Promise<void>;
    static async getMemberPoints(memberId: string, period?: DateRange): Promise<number>;
    static async getFamilyLeaderboard(
      familyId: string,
      period?: DateRange
    ): Promise<MemberPoints[]>;
  }
  ```

- [x] **4.3.3** Validatory i logika biznesowa

  ```typescript
  // lib/validations/task.ts
  export const createTaskSchema = z.object({
    title: z.string().min(1).max(200),
    description: z.string().max(1000).optional(),
    points: z.number().min(1).max(100),
    weight: z.enum(['LIGHT', 'NORMAL', 'HEAVY', 'CRITICAL']),
    frequency: z.enum(['ONCE', 'DAILY', 'WEEKLY', 'MONTHLY', 'CUSTOM']),
    dueDate: z.date().optional(),
    assignedToId: z.string().optional(),
  });

  export const completeTaskSchema = z.object({
    notes: z.string().max(500).optional(),
  });

  export const verifyTaskSchema = z.object({
    approved: z.boolean(),
    feedback: z.string().max(500).optional(),
  });
  ```

### 4.4 System przypomnień

**Czas: 2h | Priorytet: Średni**

- [ ] **4.4.1** Reminder service

  ```typescript
  // lib/services/reminder.service.ts
  export class ReminderService {
    static async scheduleReminder(taskId: string, remindAt: Date): Promise<TaskReminder>;
    static async cancelReminder(reminderId: string): Promise<void>;
    static async processReminders(): Promise<void>;
    static async sendTaskReminder(reminder: TaskReminder): Promise<void>;
  }
  ```

- [ ] **4.4.2** Cron job dla przypomnień
  ```typescript
  // app/api/cron/reminders/route.ts
  export async function GET(); // Cron endpoint do przetwarzania przypomnień
  ```

### 4.5 Komponenty UI - Lista zadań

**Czas: 4h | Priorytet: Wysoki**

- [x] **4.5.1** TaskList component (jak w prototypie)

  ```typescript
  // components/tasks/TaskList.tsx
  interface TaskListProps {
    familyId: string;
    memberId?: string; // Filtr dla konkretnego członka
    status?: TaskStatus[];
    showCompleted?: boolean;
  }
  ```
  - Implementacja layoutu z prototypu (karty zadań)
  - Kolorowe wskaźniki statusu (zielone checkmarki jak w prototypie)
  - Informacje: tytuł, przypisana osoba, termin, punkty
  - Action buttons (edycja, usunięcie, oznacz jako wykonane)

- [x] **4.5.2** TaskCard component

  ```typescript
  // components/tasks/TaskCard.tsx
  interface TaskCardProps {
    task: Task & { assignedTo?: FamilyMember };
    currentMember: FamilyMember;
    onComplete: () => void;
    onEdit: () => void;
    onDelete: () => void;
  }
  ```
  - Wizualne odzwierciedlenie prototypu
  - Kolorowe ikony według typu zadania
  - Status badges
  - Progressive disclosure dla szczegółów

- [ ] **4.5.3** Filters i sortowanie
  ```typescript
  // components/tasks/TaskFilters.tsx
  // - Filtr po statusie (Active, Completed, All)
  // - Filtr po członku rodziny
  // - Filtr po terminie (Today, This week, Overdue)
  // - Sortowanie (Due date, Points, Created, Alphabetical)
  ```

### 4.6 Komponenty UI - Formularze

**Czas: 3h | Priorytet: Wysoki**

- [x] **4.6.1** CreateTaskDialog/Form

  ```typescript
  // components/tasks/CreateTaskDialog.tsx
  // - Wszystkie pola z modelu Task
  // - Wybór członka do przypisania
  // - Date picker dla terminu
  // - Slider/wybór dla punktów i wagi
  // - Wyjaśnienia dla frequency types
  ```

- [ ] **4.6.2** EditTaskDialog

  ```typescript
  // components/tasks/EditTaskDialog.tsx
  // - Te same pola co CreateTask ale prefilled
  // - Możliwość zmiany przypisania
  // - Historia zmian (opcjonalnie)
  ```

- [ ] **4.6.3** CompleteTaskDialog

  ```typescript
  // components/tasks/CompleteTaskDialog.tsx
  // - Pole na notatki (opcjonalne)
  // - Potwierdzenie akcji
  // - Informacja o punktach do zdobycia
  ```

- [ ] **4.6.4** VerifyTaskDialog
  ```typescript
  // components/tasks/VerifyTaskDialog.tsx
  // - Approve/Reject buttons
  // - Pole na feedback
  // - Podgląd notatek wykonawcy
  // - Informacja o punktach
  ```

### 4.7 Dashboard i statystyki zadań

**Czas: 2h | Priorytet: Średni**

- [ ] **4.7.1** TasksDashboard

  ```typescript
  // components/tasks/TasksDashboard.tsx
  // - Podsumowanie aktywnych zadań
  // - Zadania wymagające weryfikacji
  // - Nadchodzące terminy
  // - Quick actions (Add task, Bulk complete)
  ```

- [ ] **4.7.2** TaskStats component
  ```typescript
  // components/tasks/TaskStats.tsx
  // - Completion rate dla rodziny
  // - Punkty zdobyte w okresie
  // - Most active members
  // - Wykres aktywności (opcjonalnie)
  ```

### 4.8 Hooks i state management

**Czas: 2h | Priorytet: Średni**

- [x] **4.8.1** Task hooks

  ```typescript
  // hooks/useTasks.ts
  export function useTasks(familyId: string, filters?: TaskFilters);
  export function useTask(taskId: string);
  export function useTaskMutations();
  export function useTaskCompletion(taskId: string);
  ```

- [ ] **4.8.2** React Query setup
  ```typescript
  // lib/queries/tasks.ts
  export const taskQueries = {
    all: () => ['tasks'] as const,
    family: (familyId: string) => [...taskQueries.all(), familyId] as const,
    task: (taskId: string) => [...taskQueries.all(), taskId] as const,
  };
  ```

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [ ] Rodzice/opiekunowie mogą tworzyć i przydzielać zadania
- [ ] Dzieci mogą oznaczać zadania jako wykonane
- [ ] System weryfikacji przez rodziców działa poprawnie
- [ ] Punkty są przyznawane automatycznie po weryfikacji
- [ ] Zadania cykliczne generują się automatycznie
- [ ] Filtry i sortowanie działają płynnie
- [ ] Przypomnienia są wysyłane na czas

### ✅ Techniczne

- [ ] Wszystkie API endpoints są zabezpieczone permissions
- [ ] Walidacja danych na frontend i backend
- [ ] Optimistic updates dla lepszego UX
- [ ] Error handling i rollback mechanizmy
- [ ] Database queries są zoptymalizowane
- [ ] TypeScript types są kompletne

### ✅ UX/UI

- [ ] Interface odzwierciedla prototypy (kolorystyka, layout)
- [ ] Animacje i przejścia są płynne
- [ ] Loading states są wyświetlane
- [ ] Error states są user-friendly
- [ ] Responsive design na wszystkich urządzeniach
- [ ] Accessibility jest zachowane

### ✅ Performance

- [ ] Lista zadań ładuje się szybko (<1s)
- [ ] Infinite scroll lub pagination dla dużych list
- [ ] Optimistic updates dla akcji użytkownika
- [ ] Cache invalidation działa poprawnie

---

## Zależności

**Wymaga ukończenia:**

- Etap 1: Fundament i konfiguracja
- Etap 2: Uwierzytelnianie i autoryzacja
- Etap 3: Moduł rodziny

## Blokuje

- Etap 5: Moduł zawieszeń
- Etap 6: Moduł statystyk i historii
- Etap 7: Moduł kalendarza

---

## User Stories

### 👤 Jako rodzic

- Chcę móc łatwo tworzyć zadania dla członków rodziny
- Chcę móc ustawić punkty i wagę zadań
- Chcę weryfikować wykonane zadania i dawać feedback
- Chcę widzieć postępy wszystkich w rodzinie
- Chcę móc planować zadania z wyprzedzeniem

### 👤 Jako dziecko

- Chcę widzieć wszystkie moje zadania w jednym miejscu
- Chcę łatwo oznaczać zadania jako wykonane
- Chcę widzieć ile punktów zdobędę za zadanie
- Chcę otrzymywać przypomnienia o terminach
- Chcę widzieć moje postępy i punkty

### 👤 Jako członek rodziny (wszyscy)

- Chcę widzieć zadania na dzisiaj w przejrzysty sposób
- Chcę móc filtrować zadania według różnych kryteriów
- Chcę otrzymywać notyfikacje o nowych zadaniach
- Chcę móc dodawać notatki do wykonanych zadań

---

## Design System - Zadania

### 🎨 Kolorystyka (z prototypów)

- **Completed tasks**: Zielony (#10B981) z checkmark
- **Pending tasks**: Szary/neutralny z icons
- **Overdue tasks**: Czerwony/pomarańczowy
- **Priority tasks**: Family-purple accent

### 🎨 Ikony zadań (z prototypów)

- **Sprzątanie**: 🧹 lub podobny
- **Gotowanie**: 🍳
- **Nauka**: 📚
- **Ogród**: 🌱
- **Zwierzęta**: 🐕
- **Inne**: ✓

### 🎨 Layout

- Karty zadań jak w prototypie "Zadania rodziny na dziś"
- Compact list view dla desktop
- Card-based view dla mobile
- Sticky header z filtrami

---

## Notatki implementacyjne

### Points Calculation

```typescript
const calculatePoints = (task: Task, completion: TaskCompletion): number => {
  const basePoints = {
    LIGHT: 1,
    NORMAL: 3,
    HEAVY: 5,
    CRITICAL: 10,
  }[task.weight];

  // Bonusy za terminowość, jakość wykonania itp.
  const bonuses = calculateBonuses(task, completion);

  return basePoints + bonuses;
};
```

### Recurring Tasks Logic

- Używaj cron expressions dla custom frequency
- Generate next occurrence po completion
- Handle timezone differences
- Archive completed recurring tasks po określonym czasie

### Performance Optimizations

- Virtual scrolling dla długich list
- Cache task data w localStorage
- Debounce filter changes
- Preload next page data

### Mobile-specific Features

- Swipe gestures (swipe to complete)
- Push notifications
- Offline mode z sync
- Quick actions shortcuts
