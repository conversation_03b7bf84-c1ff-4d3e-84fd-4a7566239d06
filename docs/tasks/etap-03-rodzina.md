# Etap 3: <PERSON><PERSON><PERSON> rod<PERSON>

## Cel etapu

Implementacja kompletnego systemu zarządzania rodzinami - tworzenie, zarząd<PERSON><PERSON> członkami, system zaproszeń i ról zgodnie z wymaganiami z PRD.

## O<PERSON><PERSON>wany czas

**14-18 godzin**

## Priorytet

🔴 **KRYTYCZNY** - Podstawa funkcjonalna aplikacji

---

## Zadania do wykonania

### 3.1 Rozszerzenie modeli danych

**Czas: 2h | Priorytet: Wysoki**

- [x] **3.1.1** Rozbudowa modelu Family

  ```prisma
  model Family {
    id          String   @id @default(cuid())
    name        String
    description String?
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt

    // Relations
    members     FamilyMember[]
    tasks       Task[]
    invitations FamilyInvitation[]
    suspensions TaskSuspension[]

    @@map("families")
  }
  ```

- [x] **3.1.2** Model zaproszeń

  ```prisma
  model FamilyInvitation {
    id        String            @id @default(cuid())
    familyId  String
    email     String
    role      FamilyRole        @default(CHILD)
    isAdult   Boolean           @default(false)
    status    InvitationStatus  @default(PENDING)
    token     String            @unique
    expiresAt DateTime
    createdAt DateTime          @default(now())

    // Relations
    family    Family            @relation(fields: [familyId], references: [id], onDelete: Cascade)

    @@unique([familyId, email])
    @@map("family_invitations")
  }

  enum InvitationStatus {
    PENDING
    ACCEPTED
    REJECTED
    EXPIRED
  }
  ```

- [x] **3.1.3** Rozbudowa FamilyMember

  ```prisma
  model FamilyMember {
    id          String   @id @default(cuid())
    userId      String
    familyId    String
    role        FamilyRole @default(CHILD)
    isAdult     Boolean    @default(false)
    points      Int        @default(0)
    joinedAt    DateTime   @default(now())
    lastActive  DateTime?

    // Relations
    user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
    family      Family     @relation(fields: [familyId], references: [id], onDelete: Cascade)
    assignedTasks Task[]   @relation("AssignedTasks")
    completedTasks TaskCompletion[]

    @@unique([userId, familyId])
    @@map("family_members")
  }
  ```

### 3.2 API Routes dla rodzin

**Czas: 4h | Priorytet: Wysoki**

- [x] **3.2.1** CRUD operacje dla rodzin

  ```typescript
  // app/api/families/route.ts
  export async function GET(); // Lista rodzin użytkownika
  export async function POST(); // Tworzenie nowej rodziny

  // app/api/families/[familyId]/route.ts
  export async function GET(); // Szczegóły rodziny
  export async function PATCH(); // Edycja rodziny
  export async function DELETE(); // Usunięcie rodziny (tylko owner)
  ```

- [x] **3.2.2** API zarządzania członkami

  ```typescript
  // app/api/families/[familyId]/members/route.ts
  export async function GET(); // Lista członków
  export async function POST(); // Zaproszenie nowego członka

  // app/api/families/[familyId]/members/[memberId]/route.ts
  export async function PATCH(); // Zmiana roli członka
  export async function DELETE(); // Usunięcie członka
  ```

- [x] **3.2.3** API systemu zaproszeń

  ```typescript
  // app/api/families/invitations/route.ts
  export async function GET(); // Lista zaproszeń użytkownika

  // app/api/families/invitations/[token]/route.ts
  export async function GET(); // Szczegóły zaproszenia
  export async function POST(); // Akceptacja zaproszenia
  export async function DELETE(); // Odrzucenie zaproszenia

  // app/api/families/[familyId]/invitations/route.ts
  export async function GET(); // Lista zaproszeń rodziny
  export async function DELETE(); // Anulowanie zaproszenia
  ```

### 3.3 Business Logic i Validations

**Czas: 3h | Priorytet: Wysoki**

- [x] **3.3.1** Serwisy rodzinne

  ```typescript
  // lib/services/family.service.ts
  export class FamilyService {
    static async createFamily(data: CreateFamilyData): Promise<Family>;
    static async inviteMember(
      familyId: string,
      invitation: InvitationData
    ): Promise<FamilyInvitation>;
    static async acceptInvitation(token: string, userId: string): Promise<FamilyMember>;
    static async changeMemberRole(
      familyId: string,
      memberId: string,
      newRole: FamilyRole
    ): Promise<FamilyMember>;
    static async removeMember(familyId: string, memberId: string): Promise<void>;
    static async leaveFamily(familyId: string, userId: string): Promise<void>;
  }
  ```

- [x] **3.3.2** Validatory Zod

  ```typescript
  // lib/validations/family.ts
  export const createFamilySchema = z.object({
    name: z.string().min(1).max(100),
    description: z.string().max(500).optional(),
  });

  export const inviteMemberSchema = z.object({
    email: z.string().email(),
    role: z.enum(['PARENT', 'GUARDIAN', 'CHILD']),
    isAdult: z.boolean().optional(),
  });

  export const changeMemberRoleSchema = z.object({
    role: z.enum(['OWNER', 'PARENT', 'GUARDIAN', 'CHILD']),
    isAdult: z.boolean().optional(),
  });
  ```

- [x] **3.3.3** Logika biznesowa
  - Walidacja uprawnień dla poszczególnych ról
  - Automatyczne generowanie tokenów zaproszeń
  - Obsługa wygaśnięcia zaproszeń
  - Logika opuszczania rodziny (transfer ownership)

### 3.4 Komponenty UI - Panel zarządzania rodziną

**Czas: 4h | Priorytet: Wysoki**

- [x] **3.4.1** Layout główny rodziny

  ```typescript
  // components/family/FamilyLayout.tsx
  interface FamilyLayoutProps {
    family: Family;
    currentMember: FamilyMember;
    children: React.ReactNode;
  }
  ```

- [x] **3.4.2** Dashboard rodziny

  ```typescript
  // components/family/FamilyDashboard.tsx
  // - Podstawowe statystyki
  // - Lista aktywnych zadań
  // - Ostatnia aktywność członków
  // - Szybkie akcje
  ```

- [x] **3.4.3** Lista członków rodziny

  ```typescript
  // components/family/FamilyMembersList.tsx
  // - Tabela/karty członków z avatarami
  // - Role badges
  // - Punkty i ostatnia aktywność
  // - Akcje dla każdego członka (edycja, usunięcie)
  ```

- [x] **3.4.4** Family Switcher
  ```typescript
  // components/family/FamilySwitcher.tsx
  // - Dropdown z listą rodzin użytkownika
  // - Szybkie przełączanie między rodzinami
  // - Wskaźnik aktywnej rodziny
  ```

### 3.5 Komponenty UI - Zarządzanie

**Czas: 3h | Priorytet: Średni**

- [x] **3.5.1** Dialog tworzenia rodziny

  ```typescript
  // components/family/CreateFamilyDialog.tsx
  // - Formularz z walidacją
  // - Loading states
  // - Error handling
  ```

- [x] **3.5.2** Dialog zapraszania członków

  ```typescript
  // components/family/InviteMemberDialog.tsx
  // - Formularz email + rola
  // - Walidacja email
  // - Podgląd uprawnień dla ról
  ```

- [x] **3.5.3** Dialog zmiany roli

  ```typescript
  // components/family/ChangeMemberRoleDialog.tsx
  // - Wybór nowej roli
  // - Ostrzeżenia o zmianie uprawnień
  // - Confirmation step
  ```

- [x] **3.5.4** Strona zaproszeń
  ```typescript
  // app/(dashboard)/invitations/page.tsx
  // - Lista pending invitations
  // - Akcje accept/reject
  // - Detale rodziny i roli
  ```

### 3.6 Email Templates i Notifications

**Czas: 2h | Priorytet: Średni**

- [x] **3.6.1** Email templates

  ```typescript
  // lib/email/templates/family-invitation.tsx
  // - HTML template dla zaproszeń
  // - Personalizacja z danymi rodziny
  // - Call-to-action buttons
  ```

- [x] **3.6.2** Email service
  ```typescript
  // lib/services/email.service.ts
  export class EmailService {
    static async sendFamilyInvitation(invitation: FamilyInvitation): Promise<void>;
    static async sendInvitationReminder(invitation: FamilyInvitation): Promise<void>;
    static async sendWelcomeToFamily(member: FamilyMember): Promise<void>;
  }
  ```

### 3.7 Hooks i State Management

**Czas: 2h | Priorytet: Średni**

- [x] **3.7.1** Custom hooks

  ```typescript
  // hooks/useFamily.ts
  export function useFamily(familyId?: string);
  export function useFamilyMembers(familyId: string);
  export function useFamilyInvitations(familyId?: string);
  export function useCurrentFamily();
  ```

- [ ] **3.7.2** Zustand stores (opcjonalne)
  ```typescript
  // stores/family.store.ts
  interface FamilyStore {
    currentFamily: Family | null;
    families: Family[];
    setCurrentFamily: (family: Family) => void;
    // ...
  }
  ```

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [x] Użytkownik może utworzyć nową rodzinę
- [x] Właściciel może zapraszać nowych członków przez email
- [x] Zaproszeni użytkownicy otrzymują email z linkiem
- [x] Użytkownicy mogą akceptować/odrzucać zaproszenia
- [x] System ról działa zgodnie z PRD (owner/parent/guardian/child)
- [x] Członkowie mogą opuszczać rodzinę
- [x] Właściciel może zmieniać role i usuwać członków

### ✅ Techniczne

- [x] Wszystkie API endpoints są zabezpieczone
- [x] Walidacja danych działa na frontend i backend
- [x] Email templates renderują się poprawnie
- [x] Database migrations działają bez błędów
- [x] TypeScript types są kompletne
- [x] Error handling jest komprehensywny

### ✅ UX/UI

- [x] Interfejs jest intuicyjny i zgodny z prototypami
- [x] Loading states są wyświetlane we wszystkich akcjach
- [x] Error messages są czytelne dla użytkownika
- [x] Responsive design na wszystkich urządzeniach
- [x] Accessibility guidelines są przestrzegane

### ✅ Bezpieczeństwo

- [x] RLS policies zabezpieczają dostęp do danych rodziny
- [x] Tokeny zaproszeń są bezpieczne i wygasają
- [x] Tylko autoryzowani użytkownicy mogą zarządzać rodziną
- [x] Email addresses są walidowane i sanitized

---

## Zależności

**Wymaga ukończenia:**

- Etap 1: Fundament i konfiguracja
- Etap 2: Uwierzytelnianie i autoryzacja

## Blokuje

- Etap 4: Moduł zadań
- Etap 5: Moduł zawieszeń
- Etap 6: Moduł statystyk i historii

---

## User Stories

### 👤 Jako nowy użytkownik

- Chcę móc utworzyć swoją pierwszą rodzinę
- Chcę móc zaprosić członków mojej rodziny
- Chcę otrzymać jasne instrukcje jak korzystać z aplikacji

### 👤 Jako właściciel rodziny

- Chcę móc zarządzać wszystkimi członkami
- Chcę móc zmieniać role i uprawnienia
- Chcę móc monitorować aktywność w rodzinie
- Chcę móc usunąć rodzinę jeśli jest niepotrzebna

### 👤 Jako członek rodziny

- Chcę móc akceptować zaproszenia
- Chcę widzieć jakie mam uprawnienia w rodzinie
- Chcę móc opuścić rodzinę jeśli zechcę
- Chcę widzieć innych członków rodziny

### 👤 Jako zaproszony użytkownik

- Chcę otrzymać czytelny email z zaproszeniem
- Chcę wiedzieć jaką rolę będę miał w rodzinie
- Chcę móc łatwo zaakceptować lub odrzucić zaproszenie

---

## Notatki implementacyjne

### Role Permissions Matrix

```
| Akcja               | Owner | Parent | Guardian | Child |
|---------------------|-------|--------|----------|-------|
| Usunięcie rodziny   | ✅    | ❌     | ❌       | ❌    |
| Zarządzanie członk. | ✅    | ✅     | ❌       | ❌    |
| Zapraszanie nowych  | ✅    | ✅     | ❌       | ❌    |
| Tworzenie zadań     | ✅    | ✅     | ✅       | ❌    |
| Weryfikacja zadań   | ✅    | ✅     | ✅       | ❌    |
| Wykonywanie zadań   | ✅    | ✅     | ✅       | ✅    |
```

### Email Configuration

- Skonfiguruj SMTP lub service provider (SendGrid, Resend)
- Templates powinny być brandowane
- Include unsubscribe links
- Track delivery status

### Performance Considerations

- Cache family data for current user
- Optimize queries z proper includes
- Pagination dla dużych rodzin
- Debounce search inputs

### Testing Priority

1. **Critical**: Role permissions
2. **High**: Invitation flow
3. **Medium**: Email delivery
4. **Low**: UI interactions
