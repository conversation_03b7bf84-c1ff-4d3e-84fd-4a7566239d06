# Etap 9: W<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (i18n)

## Cel etapu

Implementacja systemu wielojęzyczności z obsługą języka polskiego (domyślny), angielskiego oraz przygotowanie dla niemieckiego i hiszpańskiego. Automatyczna detekcja języka, personalizacja i pełne tłumaczenie interfejsu.

## Oszacowany czas

**8-10 godzin**

## Priorytet

🟢 **NISKI** - Funkcja rozszerzająca dostępność

---

## Zadania do wykonania

### 9.1 Konfiguracja systemu i18n

**Czas: 2h | Priorytet: Wysoki**

- [x] **9.1.1** Instalacja i konfiguracja next-intl

  ```bash
  npm install next-intl
  ```

- [x] **9.1.2** Konfiguracja middleware Next.js

  ```typescript
  // middleware.ts
  import createMiddleware from 'next-intl/middleware';
  import { locales, defaultLoc<PERSON> } from './lib/i18n/config';

  export default createMiddleware({
    locales,
    defaultLocale,
    localePrefix: 'as-needed', // Polish bez prefiksu
    localeDetection: true,
  });

  export const config = {
    matcher: ['/((?!api|_next|_vercel|.*\\..*).*)'],
  };
  ```

- [x] **9.1.3** Struktura folderów dla tłumaczeń

  ```
  messages/
  ├── pl.json                 # Polski (domyślny)
  ├── en.json                 # Angielski
  ├── de.json                 # Niemiecki (planowany)
  ├── es.json                 # Hiszpański (planowany)
  └── common/
      ├── navigation.json     # Nawigacja
      ├── forms.json          # Formularze
      ├── errors.json         # Błędy
      └── tasks.json          # Zadania
  ```

- [x] **9.1.4** Konfiguracja locale settings

  ```typescript
  // lib/i18n/config.ts
  export const locales = ['pl', 'en', 'de', 'es'] as const;
  export const defaultLocale = 'pl';

  export type Locale = (typeof locales)[number];

  export const localeConfig = {
    pl: {
      name: 'Polski',
      flag: '🇵🇱',
      dir: 'ltr',
      dateFormat: 'dd.MM.yyyy',
      timeFormat: 'HH:mm',
      currency: 'PLN',
    },
    en: {
      name: 'English',
      flag: '🇺🇸',
      dir: 'ltr',
      dateFormat: 'MM/dd/yyyy',
      timeFormat: 'h:mm a',
      currency: 'USD',
    },
    de: {
      name: 'Deutsch',
      flag: '🇩🇪',
      dir: 'ltr',
      dateFormat: 'dd.MM.yyyy',
      timeFormat: 'HH:mm',
      currency: 'EUR',
    },
    es: {
      name: 'Español',
      flag: '🇪🇸',
      dir: 'ltr',
      dateFormat: 'dd/MM/yyyy',
      timeFormat: 'HH:mm',
      currency: 'EUR',
    },
  };
  ```

### 9.2 Rozszerzenie modeli dla wielojęzyczności

**Czas: 1h | Priorytet: Średni**

- [x] **9.2.1** Aktualizacja User model

  ```prisma
  model User {
    // ... existing fields
    preferredLanguage String @default("pl")
    timezone         String @default("Europe/Warsaw")
    dateFormat       String @default("dd.MM.yyyy")
    timeFormat       String @default("24h")

    // ... existing relations
  }
  ```

- [x] **9.2.2** API dla language preferences
  ```typescript
  // app/api/user/preferences/language/route.ts
  export async function PATCH(); // Update user language preference
  export async function GET(); // Get current language settings
  ```

### 9.3 Tłumaczenia - Podstawowe interfejsy

**Czas: 2h | Priorytet: Wysoki**

- [x] **9.3.1** Navigation i layout

  ```json
  // messages/pl.json
  {
    "navigation": {
      "home": "Strona główna",
      "features": "Funkcje",
      "about": "O nas",
      "pricing": "Cennik",
      "dashboard": "Kokpit",
      "tasks": "Zadania",
      "calendar": "Kalendarz",
      "family": "Rodzina",
      "stats": "Statystyki",
      "settings": "Ustawienia"
    },
    "auth": {
      "signIn": "Zaloguj się",
      "signUp": "Zarejestruj się",
      "signOut": "Wyloguj się",
      "welcome": "Witamy",
      "welcomeBack": "Witamy ponownie"
    }
  }
  ```

- [x] **9.3.2** Tłumaczenia angielskie
  ```json
  // messages/en.json
  {
    "navigation": {
      "home": "Home",
      "features": "Features",
      "about": "About",
      "pricing": "Pricing",
      "dashboard": "Dashboard",
      "tasks": "Tasks",
      "calendar": "Calendar",
      "family": "Family",
      "stats": "Statistics",
      "settings": "Settings"
    },
    "auth": {
      "signIn": "Sign In",
      "signUp": "Sign Up",
      "signOut": "Sign Out",
      "welcome": "Welcome",
      "welcomeBack": "Welcome back"
    }
  }
  ```

### 9.4 Tłumaczenia - Moduł zadań

**Czas: 2h | Priorytet: Wysoki**

- [x] **9.4.1** Task-related translations

  ```json
  // messages/pl.json - sekcja tasks
  {
    "tasks": {
      "title": "Zadania",
      "todaysTasks": "Zadania na dzisiaj",
      "assignedTo": "Przypisane",
      "dueDate": "Termin",
      "points": "Punkty",
      "status": {
        "pending": "Oczekuje",
        "completed": "Wykonane",
        "overdue": "Opóźnione",
        "verified": "Zweryfikowane"
      },
      "actions": {
        "create": "Dodaj zadanie",
        "edit": "Edytuj",
        "delete": "Usuń",
        "complete": "Oznacz jako wykonane",
        "verify": "Zweryfikuj"
      },
      "form": {
        "taskName": "Nazwa zadania",
        "description": "Opis",
        "assignTo": "Przypisz do",
        "setDueDate": "Ustaw termin",
        "taskWeight": "Waga zadania",
        "frequency": "Częstotliwość"
      },
      "frequency": {
        "once": "Jednorazowo",
        "daily": "Codziennie",
        "weekly": "Tygodniowo",
        "monthly": "Miesięcznie"
      }
    }
  }
  ```

- [x] **9.4.2** Odpowiednie tłumaczenia angielskie
  ```json
  // messages/en.json - tasks section
  {
    "tasks": {
      "title": "Tasks",
      "todaysTasks": "Today's Tasks",
      "assignedTo": "Assigned to",
      "dueDate": "Due Date",
      "points": "Points",
      "status": {
        "pending": "Pending",
        "completed": "Completed",
        "overdue": "Overdue",
        "verified": "Verified"
      }
      // ... rest of translations
    }
  }
  ```

### 9.5 Tłumaczenia - Formularze i walidacja

**Czas: 1h | Priorytet: Średni**

- [x] **9.5.1** Form labels i placeholders
  ```json
  // messages/pl.json - forms section
  {
    "forms": {
      "common": {
        "save": "Zapisz",
        "cancel": "Anuluj",
        "submit": "Wyślij",
        "required": "Pole wymagane",
        "optional": "Opcjonalne"
      },
      "family": {
        "createFamily": "Utwórz rodzinę",
        "familyName": "Nazwa rodziny",
        "inviteMembers": "Zaproś członków",
        "memberEmail": "Adres e-mail",
        "memberRole": "Rola"
      }
    },
    "validation": {
      "required": "To pole jest wymagane",
      "email": "Nieprawidłowy adres e-mail",
      "minLength": "Minimalna długość: {min} znaków",
      "maxLength": "Maksymalna długość: {max} znaków"
    }
  }
  ```

### 9.6 Formatowanie dat i liczb

**Czas: 1h | Priorytet: Średni**

- [x] **9.6.1** Date formatting utilities

  ```typescript
  // lib/i18n/formatters.ts
  import { Locale } from './config';

  export function formatDate(date: Date, locale: Locale, format?: string): string {
    const localeConfig = getLocaleConfig(locale);
    return new Intl.DateTimeFormat(locale, {
      dateStyle: 'medium',
    }).format(date);
  }

  export function formatTime(date: Date, locale: Locale): string {
    const localeConfig = getLocaleConfig(locale);
    const use24Hour = localeConfig.timeFormat === 'HH:mm';

    return new Intl.DateTimeFormat(locale, {
      timeStyle: 'short',
      hour12: !use24Hour,
    }).format(date);
  }

  export function formatRelativeTime(date: Date, locale: Locale): string {
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
    const diff = Math.round((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
    return rtf.format(diff, 'day');
  }
  ```

- [x] **9.6.2** Number i currency formatting

  ```typescript
  // lib/i18n/formatters.ts (rozszerzenie)
  export function formatNumber(number: number, locale: Locale): string {
    return new Intl.NumberFormat(locale).format(number);
  }

  export function formatCurrency(amount: number, locale: Locale): string {
    const localeConfig = getLocaleConfig(locale);
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: localeConfig.currency,
    }).format(amount);
  }

  export function formatPoints(points: number, locale: Locale): string {
    const t = useTranslations('common');
    return `${formatNumber(points, locale)} ${t('points')}`;
  }
  ```

### 9.7 Komponenty UI dla i18n

**Czas: 1h | Priorytet: Średni**

- [x] **9.7.1** LanguageSwitcher component

  ```typescript
  // components/i18n/LanguageSwitcher.tsx
  interface LanguageSwitcherProps {
    variant?: 'dropdown' | 'tabs'
    showFlags?: boolean
    currentLocale: Locale
    onLocaleChange: (locale: Locale) => void
  }

  export function LanguageSwitcher({
    variant = 'dropdown',
    showFlags = true,
    currentLocale,
    onLocaleChange
  }: LanguageSwitcherProps) {
    const t = useTranslations('common');

    return (
      <Select value={currentLocale} onValueChange={onLocaleChange}>
        {locales.map(locale => (
          <SelectItem key={locale} value={locale}>
            {showFlags && localeConfig[locale].flag}
            {localeConfig[locale].name}
          </SelectItem>
        ))}
      </Select>
    );
  }
  ```

- [x] **9.7.2** LocalizedText wrapper component

  ```typescript
  // components/i18n/LocalizedText.tsx
  interface LocalizedTextProps {
    tKey: string
    values?: Record<string, string | number>
    fallback?: string
    as?: keyof JSX.IntrinsicElements
  }

  export function LocalizedText({
    tKey,
    values,
    fallback,
    as: Component = 'span'
  }: LocalizedTextProps) {
    const t = useTranslations();

    try {
      return <Component>{t(tKey, values)}</Component>;
    } catch {
      return <Component>{fallback || tKey}</Component>;
    }
  }
  ```

### 9.8 Tłumaczenia email i powiadomień

**Czas: 1h | Priorytet: Niski**

- [x] **9.8.1** Email templates internationalization

  ```typescript
  // lib/email/templates/i18n.ts
  export function getLocalizedEmailTemplate(
    templateName: string,
    locale: Locale,
    variables: Record<string, any>
  ): EmailTemplate {
    const templates = {
      familyInvitation: {
        pl: {
          subject: 'Zaproszenie do rodziny {familyName}',
          body: 'Zostałeś zaproszony do dołączenia do rodziny {familyName}...',
        },
        en: {
          subject: 'Family invitation to {familyName}',
          body: 'You have been invited to join the {familyName} family...',
        },
      },
    };

    return interpolateTemplate(templates[templateName][locale], variables);
  }
  ```

- [x] **9.8.2** Push notification localization

  ```typescript
  // lib/notifications/i18n.ts
  export function getLocalizedNotification(
    type: NotificationType,
    locale: Locale,
    data: NotificationData
  ): LocalizedNotification {
    const notifications = {
      taskCompleted: {
        pl: '{userName} wykonał zadanie "{taskName}"',
        en: '{userName} completed task "{taskName}"',
      },
      taskOverdue: {
        pl: 'Zadanie "{taskName}" jest opóźnione',
        en: 'Task "{taskName}" is overdue',
      },
    };

    return interpolateNotification(notifications[type][locale], data);
  }
  ```

---

## Kryteria akceptacji

### ✅ Funkcjonalne

- [x] Automatyczna detekcja języka z przeglądarki
- [x] Możliwość ręcznej zmiany języka
- [x] Wszystkie teksty interfejsu są przetłumaczone
- [x] Formatowanie dat zgodne z lokalną konwencją
- [x] Email i powiadomienia w wybranym języku
- [x] Ustawienia językowe zapisywane w profilu użytkownika

### ✅ Techniczne

- [x] Lazy loading translations dla performance
- [x] Proper fallback na domyślny język
- [x] TypeScript types dla translation keys
- [x] SEO-friendly URLs z locale prefix
- [x] Server-side rendering z proper locale detection

### ✅ UX/UI

- [x] Language switcher dostępny i intuicyjny
- [x] Smooth transitions między językami
- [x] Proper text direction support (przygotowanie dla RTL)
- [x] Consistent spacing niezależnie od długości tekstów
- [x] Responsive design z different text lengths

### ✅ Jakość tłumaczeń

- [x] Kontekstowo poprawne tłumaczenia
- [x] Consistent terminology across aplikacji
- [x] Proper pluralization rules
- [x] Cultural adaptation (not just translation)

---

## Zależności

**Może zacząć po:**

- Etap 1: Fundament i konfiguracja
- Etap 2: Uwierzytelnianie (do user preferences)

**Idealnie po:**

- Etap 8: UI/UX (żeby mieć wszystkie teksty do tłumaczenia)

## Blokuje

**Brak** - To jest funkcja dodatkowa

---

## Translation Strategy

### 🌍 Locale Priority (zgodnie z PRD)

1. **Polski** - Język domyślny, pełne wsparcie
2. **Angielski** - Drugi priorytet, pełne wsparcie
3. **Niemiecki** - Planowany, przygotowanie struktur
4. **Hiszpański** - Planowany, przygotowanie struktur

### 📝 Translation Keys Structure

```typescript
// Struktura hierarchiczna dla maintainability
interface TranslationKeys {
  common: {
    actions: Record<string, string>;
    navigation: Record<string, string>;
    status: Record<string, string>;
  };
  pages: {
    dashboard: Record<string, string>;
    tasks: Record<string, string>;
    family: Record<string, string>;
  };
  components: {
    forms: Record<string, string>;
    modals: Record<string, string>;
  };
  validation: Record<string, string>;
  errors: Record<string, string>;
}
```

### 🔄 Dynamic Content Translation

```typescript
// Dla user-generated content
interface TranslatableContent {
  taskNames: {
    // Sugestie tłumaczeń dla popularnych zadań
    'clean room': { pl: 'posprzątaj pokój'; en: 'clean room' };
    'wash dishes': { pl: 'pozmywaj naczynia'; en: 'wash dishes' };
    homework: { pl: 'odrabianie lekcji'; en: 'homework' };
  };
}
```

---

## Implementation Examples

### Hook Usage Pattern

```typescript
// components/tasks/TaskCard.tsx
export function TaskCard({ task }: TaskCardProps) {
  const t = useTranslations('tasks');
  const locale = useLocale();

  return (
    <div className="task-card">
      <h3>{task.title}</h3>
      <p>{t('assignedTo')}: {task.assignedTo.name}</p>
      <p>{t('dueDate')}: {formatDate(task.dueDate, locale)}</p>
      <span className="status">
        {t(`status.${task.status}`)}
      </span>
    </div>
  );
}
```

### Server Component Pattern

```typescript
// app/[locale]/dashboard/page.tsx
import { getTranslations } from 'next-intl/server';

export default async function DashboardPage({
  params: { locale }
}: {
  params: { locale: string }
}) {
  const t = await getTranslations('dashboard');

  return (
    <div>
      <h1>{t('title')}</h1>
      <TasksDashboard />
    </div>
  );
}
```

### Form Validation Pattern

```typescript
// lib/validations/task.ts
import { getTranslations } from 'next-intl/server';

export async function getTaskValidationSchema(locale: string) {
  const t = await getTranslations('validation');

  return z.object({
    title: z
      .string()
      .min(1, t('required'))
      .max(200, t('maxLength', { max: 200 })),
    description: z
      .string()
      .max(1000, t('maxLength', { max: 1000 }))
      .optional(),
  });
}
```

---

## Performance Considerations

### Bundle Optimization

```typescript
// Lazy loading per route
const translations = {
  dashboard: () => import('./messages/dashboard'),
  tasks: () => import('./messages/tasks'),
  family: () => import('./messages/family'),
};

// Only load needed translations
export async function getMessages(locale: string, namespace: string) {
  const loader = translations[namespace];
  if (!loader) return {};

  const messages = await loader();
  return messages[locale] || {};
}
```

### Caching Strategy

```typescript
// Cache translations w localStorage
const TRANSLATION_CACHE_KEY = 'familytasks_translations';
const CACHE_VERSION = '1.0.0';

export function cacheTranslations(locale: string, translations: any) {
  localStorage.setItem(
    TRANSLATION_CACHE_KEY,
    JSON.stringify({
      version: CACHE_VERSION,
      locale,
      translations,
      timestamp: Date.now(),
    })
  );
}
```

---

## Notatki implementacyjne

### SEO Considerations

- **Hreflang tags** dla different language versions
- **Sitemap generation** z locale-specific URLs
- **Meta tags translation** dla każdej strony
- **Structured data** w odpowiednim języku

### Testing Strategy

- **Translation coverage** - sprawdzanie czy wszystkie klucze mają tłumaczenia
- **Pseudo-localization** testing dla layout issues
- **Manual translation review** z native speakers
- **Automated testing** z different locales

### Future Enhancements

- **Professional translation service** integration
- **Community translations** platform
- **Context-aware translations** based on user behavior
- **A/B testing** dla different translation variants
