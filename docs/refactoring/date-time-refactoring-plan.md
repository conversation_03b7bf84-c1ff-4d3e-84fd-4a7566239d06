# Raport i Plan Refaktoryzacji Modelu Danych Task

**Data:** 2025-07-01
**Autor:** Gemini

## 1. Wprowadzenie i Diagnoza Problemu

W trakcie analizy błędu związanego z nieprawidłowym wyświetlaniem godzin w formularzu edycji zadań zidentyfikowano fundamentalny problem architektoniczny w sposobie przechowywania danych dotyczących daty i czasu w modelu `Task`.

Obecny model, mimo że funkcjonalny, jest niejednoznaczny i podatny na błędy. Pola `dueDate`, `startDate` i `endDate` są używane w sposób, który komplikuje logikę aplikacji, szczególnie w obsłudze zadań jednorazowych, całodniowych i cyklicznych. Ta niejednoznaczność prowadzi do nadmiernie skomplikowanego kodu, który jest trudny w utrzymaniu i podatny na błędy (np. związane z konwersją stref czasowych).

Celem tej refaktoryzacji jest ujednoznacznienie modelu danych, uproszczenie logiki aplikacji, zwiększenie jej niezawodności i przygotowanie fundamentów pod przyszły rozwój.

## 2. Analiza Obecnego Modelu Danych

**Aktualny schemat (`prisma.schema`):**

```prisma
model Task {
  // ...
  dueDate        DateTime?   // Używane jako data rozpoczęcia
  startDate      DateTime?   // Często duplikuje `dueDate`
  endDate        DateTime?   // Używane do dwóch celów!
  allDay         Boolean     // Rozróżnia zadania całodniowe od godzinowych
  frequency      TaskFrequency // ONCE, DAILY, WEEKLY etc.
  recurrenceRule String?     // Przechowuje regułę cyklu (RRULE)
  recurrenceEnd  DateTime?   // Potencjalnie data zakończenia cyklu
  // ...
}
```

**Główne zidentyfikowane problemy:**

1.  **Wieloznaczność Pola `endDate`:**
    - **Dla zadań godzinowych:** Przechowuje datę i **godzinę zakończenia** zadania.
    - **Dla zadań cyklicznych:** Powinno definiować **datę graniczną całego cyklu**, ale obecna struktura tego nie wymusza. Mieszanie tych dwóch koncepcji w jednym polu zmusza do pisania skomplikowanej logiki warunkowej.

2.  **Redundancja i Niejasność (`dueDate` vs `startDate`):**
    - Obecność obu pól jest myląca. `dueDate` jest de facto głównym polem, a `startDate` często je powiela, co prowadzi do ryzyka niespójności.

3.  **Komplikacje w Logice Biznesowej:**
    - Model danych zmusza do implementacji złożonej logiki w komponentach (np. `useTaskFormState`), która musi "odgadywać" znaczenie dat na podstawie innych pól (`allDay`, `frequency`).

## 3. Rekomendowane Zmiany w Modelu Danych

Proponuję uproszczenie i ujednoznacznienie modelu `Task` w `prisma.schema`, inspirując się standardem iCalendar (RFC 5545).

**Proponowany nowy model `Task`:**

```prisma
model Task {
  id              String        @id @default(cuid())
  // ... (inne pola bez zmian)

  // --- ZMIANY W POLACH DATY ---

  // Oznacza datę i czas rozpoczęcia PIERWSZEGO wystąpienia zadania.
  dtStart         DateTime

  // Używane TYLKO dla zadań, które mają określony czas trwania.
  // Dla zadań całodniowych (`allDay: true`), to pole będzie NULL.
  dtEnd           DateTime?

  // Pozostaje bez zmian - kluczowe.
  allDay          Boolean       @default(true)

  // Pola cykliczności
  frequency       TaskFrequency @default(ONCE)
  recurrenceRule  String?       // Przechowuje RRULE (wraz z datą `UNTIL`).

  // --- USUNIĘTE POLA ---
  // dueDate        DateTime?   // Zastąpione przez dtStart
  // startDate      DateTime?   // Zastąpione przez dtStart
  // endDate        DateTime?   // Zastąpione przez dtEnd
  // recurrenceEnd  DateTime?   // Informacja `UNTIL` będzie częścią recurrenceRule

  // ... (reszta pól)
}
```

## 4. Szczegółowy Plan Refaktoryzacji

### Faza 1: Migracja Schematu Bazy Danych (Backend)

1.  **Krytyczny Backup Danych:**
    - Przed rozpoczęciem jakichkolwiek zmian, należy wykonać pełny backup bazy danych.
    - Komenda: `cp prisma/dev.db backups/dev.db.backup-$(date +%Y%m%d%H%M%S)`

2.  **Modyfikacja `schema.prisma`:**
    - Otwórz plik `prisma/schema.prisma`.
    - W modelu `Task` usuń pola: `dueDate`, `startDate`, `endDate`, `recurrenceEnd`.
    - W modelu `Task` dodaj nowe pola: `dtStart` (typu `DateTime`) oraz `dtEnd` (typu `DateTime?`).

3.  **Wygenerowanie Nowej Migracji:**
    - Uruchom w terminalu komendę, aby Prisma wygenerowała plik migracji SQL.
    - Komenda: `npx prisma migrate dev --name refactor-task-datetime-fields`

4.  **Weryfikacja Pliku Migracji:**
    - Otwórz nowo wygenerowany plik w katalogu `prisma/migrations/...` i sprawdź, czy operacje `DROP COLUMN` i `ADD COLUMN` są zgodne z planem.

### Faza 2: Migracja Istniejących Danych (Backend)

1.  **Stworzenie Skryptu Migracyjnego:**
    - Utwórz nowy plik: `scripts/migrate-task-dates.ts`.
    - W skrypcie:
      - Zainicjuj Prisma Client.
      - Pobierz wszystkie zadania z bazy: `prisma.task.findMany()`.
      - Przejdź przez każde zadanie w pętli i wykonaj transformację:

        ```typescript
        const newDtStart = task.dueDate || task.startDate || new Date();
        let newDtEnd = null;
        if (!task.allDay && task.endDate) {
          newDtEnd = task.endDate;
        }

        let newRecurrenceRule = task.recurrenceRule;
        if (task.recurrenceEnd && task.recurrenceRule) {
          // Dodaj UNTIL do istniejącej reguły RRULE
          const untilDate = task.recurrenceEnd.toISOString().replace(/[-:.]/g, '');
          newRecurrenceRule += `;UNTIL=${untilDate}`;
        }

        await prisma.task.update({
          where: { id: task.id },
          data: {
            dtStart: newDtStart,
            dtEnd: newDtEnd,
            recurrenceRule: newRecurrenceRule,
          },
        });
        ```

      - Dodaj logowanie, aby monitorować postęp.

2.  **Uruchomienie Skryptu:**
    - Wykonaj skrypt za pomocą `ts-node` lub kompilując go do JS.
    - Komenda: `npx ts-node scripts/migrate-task-dates.ts`

### Faza 3: Aktualizacja Logiki Aplikacji (Backend i Frontend)

1.  **Globalne Wyszukiwanie i Zamiana:**
    - W całym projekcie wyszukaj wystąpienia usuniętych pól: `dueDate`, `startDate`, `endDate`, `recurrenceEnd`.

2.  **Backend (API Endpoints):**
    - Przejrzyj i zaktualizuj wszystkie endpointy API w `src/app/api/tasks/`.
    - Dostosuj logikę tworzenia (`POST`) i aktualizacji (`PUT`/`PATCH`) zadań, aby operowała na `dtStart` i `dtEnd`.
    - Upewnij się, że endpointy pobierające zadania (`GET`) zwracają nowe pola.

3.  **Frontend - Typy:**
    - Zaktualizuj definicje typów, zwłaszcza `TaskWithRelations` w `hooks/useTasks.ts`, aby odzwierciedlały nowy schemat.

4.  **Frontend - Logika Formularza (`useTaskFormState.ts`):**
    - To jest kluczowa i najbardziej złożona część.
    - Przepisz logikę w `useEffect` w `useTaskFormState.ts`, aby mapowała `dtStart` i `dtEnd` do stanu formularza.
    - Uprość logikę - nie będzie już potrzeby zgadywania znaczenia dat.
    - Zaktualizuj funkcję `buildRRule`, aby poprawnie obsługiwała datę `UNTIL` na podstawie danych z formularza.

5.  **Frontend - Komponenty:**
    - Dostosuj komponenty formularza (`DateTimeSection.tsx`, `RecurrenceSection.tsx`) do pracy z nowym, uproszczonym stanem.
    - Zaktualizuj komponenty wyświetlające zadania (w kalendarzu, na listach), aby korzystały z `dtStart` i `dtEnd`.

### Faza 4: Testowanie

1.  **Testy Jednostkowe i Integracyjne:**
    - Zaktualizuj istniejące testy, które mogły ulec awarii po zmianie modelu danych.

2.  **Testy E2E (End-to-End):**
    - Przejrzyj i zaktualizuj testy w `e2e/tasks.spec.ts`.
    - Dodaj nowe scenariusze testowe obejmujące:
      - Tworzenie/edycja zadania jednorazowego (godzinowego i całodniowego).
      - Tworzenie/edycja zadania cyklicznego z datą końcową (`UNTIL`).
      - Tworzenie/edycja zadania cyklicznego bez daty końcowej.
      - Poprawność wyświetlania dat i godzin w różnych widokach.

3.  **Testy Manualne:**
    - Dokładnie przetestuj całą ścieżkę użytkownika związaną z zadaniami, aby upewnić się, że wszystko działa zgodnie z oczekiwaniami. Zwróć szczególną uwagę na strefy czasowe.
