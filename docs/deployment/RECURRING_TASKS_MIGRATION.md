# 🚀 Strategia Migracji - Zadania Cykliczne

## Przegląd

Dokument opisuje strategię wdrożenia funkcjonalności zadań cyklicznych w istniejącej aplikacji. Migracja została zaprojektowana jako **backward compatible** - istniejące zadania pozostają nietknięte, a nowe funkcjonalności są dodawane stopniowo.

## 🎯 Cele Migracji

1. **Zero Downtime** - wdrożenie bez przerw w działaniu
2. **Backward Compatibility** - istniejące zadania działają bez zmian
3. **Stopniowe Wdrożenie** - możliwość włączania funkcji po etapach
4. **Rollback Ready** - możliwość cofnięcia w przypadku problemów
5. **Data Integrity** - zachowanie spójności danych

## 📊 Stan Przed Mi<PERSON>ją

### ✅ Co już istnieje (bez zmian)

- Model `Task` z polami `frequency`, `recurrenceRule`, `startDate`, `endDate`
- TaskForm z podstawową obsługą frequency
- RecurrenceSection dla zaawansowanych reguł
- MobileCalendarView z renderowaniem zadań

### 🆕 Co zostało dodane

- Nowe komponenty UI (RecurringTaskMarker, PatternIndicator)
- Utility functions (recurring-tasks.ts, calendar-helpers.ts)
- Cache system (recurring-tasks-cache.ts)
- Service layer (RecurringTaskService)
- Testy i dokumentacja

## 🗺️ Plan Migracji

### **ETAP 1: Przygotowanie (Pre-deployment)**

#### 1.1 Backup i Bezpieczeństwo

```bash
# Backup bazy danych
pg_dump family_tasks_prod > backup_pre_recurring_$(date +%Y%m%d).sql

# Backup kodu przed zmianami
git tag release/pre-recurring-tasks
git push origin release/pre-recurring-tasks
```

#### 1.2 Sprawdzenie Kompatybilności

```sql
-- Sprawdź istniejące zadania z frequency != 'ONCE'
SELECT id, title, frequency, recurrenceRule
FROM tasks
WHERE frequency != 'ONCE';

-- Sprawdź potencjalne konflikty
SELECT COUNT(*) as existing_recurring
FROM tasks
WHERE frequency IN ('DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY');
```

#### 1.3 Feature Flags

```typescript
// environment variables
ENABLE_RECURRING_TASKS = true;
ENABLE_RECURRING_UI = false; // Włączymy w etapie 2
ENABLE_RECURRING_CACHE = false; // Włączymy w etapie 3
ENABLE_RECURRING_ADVANCED = false; // Włączymy w etapie 4
```

### **ETAP 2: Backend Deployment**

#### 2.1 Aktualizacja Kodu

1. **Deploy nowych utility functions**
   - `recurring-tasks.ts`
   - `calendar-helpers.ts`
   - `RecurringTaskService`

2. **Backward compatible changes**

   ```typescript
   // W istniejących API endpoints
   if (ENABLE_RECURRING_TASKS) {
     // Nowa logika dla zadań cyklicznych
     return RecurringTaskService.getRecurringTasksForFamily(...)
   } else {
     // Stara logika (fallback)
     return existingTaskLogic(...)
   }
   ```

3. **Database migrations** (jeśli potrzebne)

   ```sql
   -- Dodanie indeksów dla optymalizacji
   CREATE INDEX CONCURRENTLY idx_tasks_frequency_active
   ON tasks(frequency, status)
   WHERE frequency != 'ONCE';

   CREATE INDEX CONCURRENTLY idx_tasks_family_recurring
   ON tasks(familyId, frequency, startDate)
   WHERE frequency != 'ONCE';
   ```

#### 2.2 Testowanie Backend

```bash
# Testy API z istniejącymi danymi
npm test -- --testPathPattern="recurring.*test"

# Performance testing
npm run test:load -- --endpoint="/api/families/*/tasks"
```

### **ETAP 3: Frontend Deployment**

#### 3.1 UI Components Release

1. **Włącz nowe komponenty**

   ```env
   ENABLE_RECURRING_UI=true
   ```

2. **Stopniowe rollout**

   ```typescript
   // Feature flag per family lub user
   const canUseRecurringTasks = await checkFeatureFlag('recurring-tasks-ui', familyId);
   ```

3. **A/B Testing**
   - 10% użytkowników - nowy UI
   - 90% użytkowników - stary UI
   - Monitoring UX metrics

#### 3.2 Monitoring

```typescript
// Analytics events
analytics.track('recurring_task_created', {
  frequency: task.frequency,
  interval: task.interval,
  familyId: task.familyId,
});

analytics.track('recurring_marker_clicked', {
  taskId: task.id,
  viewType: 'calendar',
});
```

### **ETAP 4: Cache Optimization**

#### 4.1 Włączenie Cache

```env
ENABLE_RECURRING_CACHE=true
RECURRING_TASKS_CACHE_TTL=1800000  # 30min
```

#### 4.2 Cache Warming

```typescript
// Pre-populate cache for active families
async function warmCache() {
  const activeFamilies = await getActiveFamilies();
  const commonRanges = [
    { start: startOfWeek(new Date()), end: endOfWeek(new Date()) },
    { start: startOfMonth(new Date()), end: endOfMonth(new Date()) },
  ];

  for (const family of activeFamilies) {
    const tasks = await getRecurringTasksForFamily(family.id);
    await RecurringTasksCache.preloadCache(tasks, commonRanges);
  }
}
```

### **ETAP 5: Advanced Features**

#### 5.1 Zaawansowane Funkcje

```env
ENABLE_RECURRING_ADVANCED=true
```

- Edycja serii vs wystąpienia
- Zaawansowane RRULE
- Statystyki zadań cyklicznych

#### 5.2 Performance Monitoring

```typescript
// Performance metrics
const performanceMonitor = {
  trackOccurrenceGeneration: (taskId, duration, count) => {
    metrics.histogram('recurring_task_generation_duration', duration, {
      task_id: taskId,
      occurrence_count: count,
    });
  },

  trackCacheHitRate: hitRate => {
    metrics.gauge('recurring_task_cache_hit_rate', hitRate);
  },
};
```

## 🔄 Strategia Rollback

### Quick Rollback (Emergency)

```bash
# 1. Disable feature flags
export ENABLE_RECURRING_TASKS=false
export ENABLE_RECURRING_UI=false

# 2. Restart services
pm2 restart all

# 3. Restore previous version if needed
git checkout release/pre-recurring-tasks
npm run build && npm run start
```

### Data Rollback

```sql
-- Rollback nowych zadań cyklicznych (jeśli potrzeba)
DELETE FROM tasks
WHERE frequency != 'ONCE'
AND created_at > '2025-07-01 12:00:00';

-- Przywróć backup
-- psql family_tasks_prod < backup_pre_recurring_20250701.sql
```

## 📊 Monitoring i Alerty

### Key Metrics

```typescript
const monitoringMetrics = {
  // Performance
  recurring_task_generation_time_ms: 'histogram',
  recurring_task_cache_hit_rate: 'gauge',
  calendar_render_time_ms: 'histogram',

  // Business
  recurring_tasks_created_total: 'counter',
  recurring_task_completions_total: 'counter',
  recurring_task_edits_total: 'counter',

  // Errors
  recurring_task_generation_errors_total: 'counter',
  recurring_task_cache_errors_total: 'counter',
};
```

### Alerty

```yaml
alerts:
  - name: RecurringTaskGenerationSlow
    condition: recurring_task_generation_time_ms.p95 > 1000
    action: slack_notification

  - name: RecurringTaskCacheLowHitRate
    condition: recurring_task_cache_hit_rate < 50
    action: email_notification

  - name: RecurringTaskErrors
    condition: recurring_task_generation_errors_total > 10/hour
    action: pager_duty
```

## 🧪 Testing Strategy

### Pre-deployment Testing

```bash
# 1. Unit tests
npm test -- recurring-tasks.test.ts

# 2. Integration tests
npm test -- recurring-tasks-integration.test.tsx

# 3. E2E tests
npm run e2e:recurring-tasks

# 4. Performance tests
npm run test:performance -- --scenario=recurring-tasks
```

### Post-deployment Verification

```bash
# Health check script
#!/bin/bash

echo "Checking recurring tasks functionality..."

# Test basic task creation
curl -X POST /api/families/test/tasks \
  -d '{"title":"Test recurring","frequency":"DAILY"}' \
  -H "Content-Type: application/json"

# Test calendar rendering
curl /api/families/test/calendar?start=2025-01-01&end=2025-01-31

# Test cache performance
curl /api/admin/recurring-tasks/cache/stats

echo "Health check completed"
```

## 📋 Migration Checklist

### Pre-deployment

- [ ] Backup bazy danych utworzony
- [ ] Git tag release/pre-recurring-tasks utworzony
- [ ] Feature flags skonfigurowane
- [ ] Monitoring i alerty skonfigurowane
- [ ] Performance baseline zapisany

### Deployment

- [ ] Backend deployed with flags OFF
- [ ] Database migrations wykonane
- [ ] Backend smoke tests passed
- [ ] Frontend deployed with flags OFF
- [ ] E2E tests passed

### Rollout

- [ ] ENABLE_RECURRING_TASKS=true
- [ ] Backend monitoring OK (30min)
- [ ] ENABLE_RECURRING_UI=true (10% users)
- [ ] UI monitoring OK (2h)
- [ ] ENABLE_RECURRING_UI=true (100% users)
- [ ] Full functionality monitoring OK (24h)

### Post-deployment

- [ ] Cache enabled and warmed
- [ ] Advanced features enabled
- [ ] Performance metrics collected
- [ ] User feedback collected
- [ ] Documentation updated

## 🎯 Success Criteria

### Technical

- [ ] Zero data loss during migration
- [ ] Page load times < 2s for calendar views
- [ ] Cache hit rate > 80%
- [ ] Error rate < 0.1%

### Business

- [ ] Existing tasks work unchanged
- [ ] New recurring tasks can be created
- [ ] Calendar shows recurring tasks correctly
- [ ] Users can edit recurring tasks

### User Experience

- [ ] No user-reported issues with existing functionality
- [ ] Positive feedback on new recurring tasks feature
- [ ] Calendar performance maintained or improved
- [ ] Intuitive UI for recurring task management

## 📞 Support Plan

### During Migration

- **Primary Contact:** Development Team Lead
- **Secondary Contact:** DevOps Engineer
- **Emergency Contact:** CTO

### Post-deployment (first 48h)

- On-call rotation for recurring tasks issues
- Monitoring dashboard watched 24/7
- User feedback channel prioritized
- Hotfix deployment process ready

---

**Przygotowane przez:** Claude Code Implementation  
**Data:** 2025-07-01  
**Status:** Ready for Review  
**Estimated Duration:** 3-5 dni roboczych
