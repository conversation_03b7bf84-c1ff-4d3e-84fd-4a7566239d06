⎿  Kompleksowy Plan Napraw Aplikacji FamilyTasks

     🎯 Problemy do Rozwiązania

     1. Problem z Wieloma Przypisaniami do Zadań

     - Obecny stan: Zadania przypisane do wielu osób tworzą duplikaty w kalendarzu
     - Rozwiązanie: Dodanie typu przypisania (ALL - wszyscy muszą w<PERSON>, ANY - jeden z przypisanych)

     2. Zadania Cykliczne w Kalendarzu

     - Problem: Wyświetlają się jako wydarzenia trwające od-do zamiast jako serie
     - Rozwiązanie: Implementacja generowania instancji zadań cyklicznych

     3. <PERSON><PERSON>y <PERSON>ń

     - Problem: Nie wszystkie zadania mają kolory + tylko 6 kolorów dostępnych
     - Rozwiązanie: Rozszerzenie palety do 12 kolorów + naprawy wyświetlania

     4. Dialog Potwierdzenia Usuwania

     - Problem: Brakuje dialoga potwierdzenia przy usuwaniu zadań
     - Rozwiązanie: Dialog już istnieje - sprawdzenie implementacji w UI

     5. Scrollowanie w Kalendarzu

     - Problem: Brak dostępu do wszystkich zadań - nie ma scrollowania
     - Rozwiązanie: Poprawki CSS i konfiguracji FullCalendar

     6. Mobile First & Responsywność

     - Problem: Kalendarz nieużyteczny na mobile, formularze w modalach
     - Rozwiązanie: Przeprojektowanie UI na mobile-first

     7. Powiadomienia E-mail

     - Problem: Brak systemu powiadomień e-mail
     - Rozwiązanie: Implementacja wysyłania e-maili z wykorzystaniem danych z .env.local

     📋 Szczegółowy Plan Implementacji

     Faza 1: Model Danych i Backend

     1. Rozszerzenie modelu TaskAssignment
       - Dodanie enum TaskAssignmentType { ALL, ANY }
       - Aktualizacja schematu Prisma
       - Migracja bazy danych
     2. Usprawnienie CalendarService
       - Implementacja logiki generowania zadań cyklicznych
       - Naprawy logiki wyświetlania kolorów
       - Optymalizacja zapytań dla zadań wielokrotnie przypisanych
     3. Rozszerzenie kolorów
       - Zwiększenie palety z 6 do 12 kolorów
       - Aktualizacja validacji i UI
     4. System Powiadomień E-mail
       - Implementacja EmailService z wykorzystaniem nodemailer
       - Konfiguracja SMTP z danymi z .env.local (MAIL_HOST, MAIL_PORT, MAIL_USER, MAIL_PASS)
       - Szablony e-maili dla różnych powiadomień
       - API endpoints dla wysyłania powiadomień

     Faza 2: Frontend - Kalendarz

     5. Naprawy CalendarView
       - Konfiguracja scrollowania w różnych widokach
       - Poprawki CSS dla lepszego wyświetlania
       - Optymalizacja dayMaxEvents i eventDisplay
     6. Logika zadań cyklicznych
       - Implementacja RRULE dla powtarzających się zadań
       - Wyświetlanie jako serie zamiast pojedynczych wydarzeń
     7. Mobile Calendar
       - Dedykowany komponent dla urządzeń mobilnych
       - Kompaktowy widok z gestami
       - Adaptacyjne menu i nawigacja

     Faza 3: UI/UX Mobile-First

     8. Formularze Mobile
       - Przekształcenie dialogów na pełnoekranowe widoki na mobile
       - Responsywne layouty
       - Touch-friendly controls
     9. Navigation & Layout
       - Dostosowanie nawigacji do mobile
       - Kompaktowe menu
       - Bottom tab bar dla mobile

     Faza 4: Konfiguracja Zadań i Powiadomienia

     10. Typ Przypisania
       - UI do wyboru typu przypisania (ALL/ANY)
       - Logika wykonywania zadań według typu
       - Wyświetlanie statusu w kalendarzu
     11. Dialog Usuwania
       - Sprawdzenie i naprawa integracji DeleteTaskDialog
       - Zapewnienie wyświetlania we wszystkich miejscach
     12. Integracja Powiadomień
       - Powiadomienia o nowych zadaniach
       - Przypomnienia o zbliżających się terminach
       - Powiadomienia o wykonanych zadaniach
       - Ustawienia preferencji użytkownika

     Faza 5: Testy i Finalizacja

     13. Testy Funkcjonalne
       - Sprawdzenie wszystkich napraw
       - Weryfikacja responsywności
       - Testy na różnych urządzeniach
       - Testy systemu e-mail
     14. Dokumentacja
       - Utworzenie pliku docs/lista-zmian.md
       - Dokumentacja nowych funkcji
       - Instrukcje dla użytkowników

     🔧 Kluczowe Zmiany Techniczne

     Database Schema

     enum TaskAssignmentType {
       ALL  // Wszyscy przypisani muszą wykonać
       ANY  // Jeden z przypisanych musi wykonać
     }

     model TaskAssignment {
       // ... existing fields
       assignmentType TaskAssignmentType @default(ANY)
     }

     model Task {
       // ... existing fields
       recurrenceRule String? // RRULE dla zadań cyklicznych
       recurrenceEnd  DateTime? // Koniec serii
     }

     model EmailNotification {
       id        String   @id @default(cuid())
       userId    String
       type      NotificationType
       subject   String
       content   String
       sentAt    DateTime?
       createdAt DateTime @default(now())
     }

     Email Service

     class EmailService {
       static async sendTaskAssignment(taskId: string, memberIds: string[])
       static async sendTaskReminder(taskId: string, memberId: string)
       static async sendTaskCompletion(taskId: string, completedBy: string)
       static async sendTaskVerification(taskId: string, verifiedBy: string)
     }

     Color Palette Extension

     - Rozszerzenie z 6 do 12 kolorów
     - Lepsze kontrasty dla accessibility
     - Spójność z design system

     Mobile Components

     - MobileCalendarView.tsx
     - MobileTaskForm.tsx
     - MobileNavigation.tsx

     ⚡ Priorytety Wykonania

     1. Wysokie: Model danych, zadania cykliczne, mobile calendar, email service
     2. Średnie: Kolory, scrollowanie, dialogi, powiadomienia
     3. Niskie: Dokumentacja, dodatkowe testy

     🎯 Oczekiwane Rezultaty

     - ✅ Zadania przypisane do wielu osób wyświetlają się jako jedno zadanie
     - ✅ Zadania cykliczne poprawnie generowane i wyświetlane
     - ✅ 12 kolorów dostępnych z poprawnym wyświetlaniem
     - ✅ Pełne scrollowanie we wszystkich widokach kalendarza
     - ✅ Użyteczny kalendarz na urządzeniach mobilnych
     - ✅ Mobile-first design dla wszystkich formularzy
     - ✅ Dialogi potwierdzenia działają poprawnie
     - ✅ Funkcjonalny system powiadomień e-mail z konfiguracją SMTP

     📧 Konfiguracja E-mail

     Wykorzystanie zmiennych środowiskowych z .env.local:
     - MAIL_HOST - serwer SMTP
     - MAIL_PORT - port SMTP
     - MAIL_USER - nazwa użytkownika
     - MAIL_PASS - hasło

     Czy zatwierdzasz ten rozszerzony plan do realizacji?

⏺ Update Todos
⎿  ☐ Rozszerzenie modelu TaskAssignment - dodanie typu przypisania (ALL/ANY)
☐ Implementacja systemu powiadomień e-mail z konfiguracją SMTP
☐ Naprawy zadań cyklicznych w kalendarzu - generowanie instancji
☐ Implementacja mobile-first design dla kalendarza
☐ Rozszerzenie palety kolorów z 6 do 12 + naprawy wyświetlania
☐ Naprawy scrollowania w kalendarzu - dostęp do wszystkich zadań
☐ Przekształcenie formularzy mobile z dialogów na pełnoekranowe widoki
☐ Testy funkcjonalne i weryfikacja responsywności
☐ Sprawdzenie i naprawa integracji DeleteTaskDialog
☐ Utworzenie dokumentacji zmian w docs/lista-zmian.md
