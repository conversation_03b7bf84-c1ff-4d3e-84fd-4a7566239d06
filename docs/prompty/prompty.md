# 0001

ultrathink - Używając @docs/prd.md stw<PERSON>rz szczegółowy plan implementacji aplikacji z zadaniami i podzadaniami do wykonania.
<<<<<<< ours
Przykłady layoutu z prototypu znajdziesz w plikach w folderze @docs/design/\*
Projektując wizualną strukturę aplikacji, zastanów się jak najlepiej zorganizować elementy tak aby były czytelne i łatwe w użyciu i stosuj barwy i styl z przykładowych ekranów.
=======
Przykłady layoutu z prototypu znajdziesz w plikach w folderze @docs/design/\*
Projektując wizualną strukturę aplikacji, zastanów się jak najlepiej zorganizować elementy tak aby były czytelne i łatwe w użyciu i stosuj barwy i styl z przykładowych ekranów.
Gotowy plan zapisz w folderze @docs/tasks/ jako listę zadań z podziałem na etapy, gdzie każdy etap będzie w oddzielnym pliku.

# 0002

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-01-fundament.md

# 0003

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-02-auth.md na koniec zweryfikuj wykonane zadania i zaznacz ich wykonanie w pliku

# 0004

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-03-rodzina.md na koniec zweryfikuj wykonane zadania i zaznacz ich wykonanie w pliku

# 0005

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-04-zadania.md na koniec zweryfikuj wykonane zadania i zaznacz ich wykonanie w pliku

# 0006

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-05-zawieszenia.md na koniec zweryfikuj wykonane zadania i zaznacz ich wykonanie w pliku

# 0007

think - zweryfikuj oraz dokończ realizację zadań z pliku @docs/tasks/etap-05-zawieszenia.md\
wykonane zadania zaznacz odpowiednio w pliku @docs/tasks/etap-05-zawieszenia.md \
po wykonaniu wszystkich punktów sprawdź czy projekt się buduje i popraw ewentualne błędy oraz ostrzeżenia

# 0008

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-06-statystyki.md na koniec zweryfikuj wykonane zadania i odpowiednio oznacz status w pliku,
po wykonaniu wszystkich punktów sprawdź czy projekt się buduje i popraw ewentualne błędy

# 0009

think - przejdź do realizacji zadań z pliku @docs/tasks/etap-07-kalendarz.md na koniec zweryfikuj wykonane zadania i odpowiednio oznacz status w pliku,
po wykonaniu wszystkich punktów sprawdź czy projekt się buduje i popraw ewentualne błędy

# 0010

think hard - przejdź do realizacji zadań z pliku @docs/tasks/etap-08-ui-ux.md na koniec zweryfikuj wykonane zadania i odpowiednio oznacz status w pliku, po wykonaniu wszystkich punktów sprawdź czy projekt się buduje i popraw ewentualne błędy

# 0011

think hard - przejdź do realizacji zadań z pliku @docs/tasks/etap-09-i18n.md na koniec zweryfikuj wykonane zadania i odpowiednio oznacz status w pliku, po wykonaniu wszystkich punktów sprawdź czy projekt się buduje i popraw ewentualne błędy

# 0012

think hard - przejdź do realizacji zadań z pliku @docs/tasks/etap-10-testy.md na koniec zweryfikuj wykonane zadania i odpowiednio oznacz status w pliku, po wykonaniu wszystkich punktów sprawdź czy projekt się buduje i popraw ewentualne błędy

# 0013

ok, przystąp do realizacji tego planu - stwórz szczegółową listę todo i wykonuj ją punkt po punkcie, na końcu przeprowadź wszystkie niezbędne testy by zweryfikować poprawność działania programu

# 0014

ultrathing, lista zadań i błędów do poprawy:

- nadal nie można przypisać wielu osób do zadania tak jak to wyspecyfikowano bo przypisując zadanie do dwóch osób potem w kalendarzu widzimy dwa takie same zadania,  
  każde do innej osoby, a założenie jest takie by to było zadanie, które mają wykonac dwie osoby lub jedna z nich (zależy od ustawienia - tego też brakuje w  
  konfiguracji zadania),
- zadania cykliczne w kalendarzu są widoczne jako trwajace od daty do daty, a to nie ma sensu skoro np. jest to zadanie od godziny 18:00 do 19:00 co dwa dni  
  powtarzane 5 razy,
- mimo przypisania koloru nie każde zadanie ma kolor na podglądzie miesięcznym w kalendarzu,
- należy dodać więcej kolorów do wyboru podczas tworzenia i edycji zadania, teraz jest 6 niech będzie 12,
- podczas usuwania zadania brakuje okna dialogowego (modalnego) do potwierdzenia,
- w kalendarzu na podglądzie miesięcznym, tygodniowym i dziennym oraz w agendzie nie widać wszystkich zadań, są gdzieś na dole ale nie da się tam dotrzeć do nie ma  
  opcji przewiania, te dane nie mieszczą się na ekranie i tylko duże pomniejszenie pozwoli do nich dotrzeć,
- w wersji mobile aplikacji kalendarz jest nieużyteczny, trzeba jego funkcjonalność dostosować do urządzeń mobilnych w pierwszej kolejności,
- ogólna zasada to stosuj zasadę mobile first,
- w wersji mobile wszelkie formularze nie powinny być w oknach modalnych

# 0015

think hard:

- formularz edycji i tworzenia zadania powinien być na całej szerokości ekranu, a nie w oknie modalnym w wersji mobile,
- formularz edycji i tworzenia zadania powinien być responsywny, to powinien być ten sam formularz (utwórz odpowiedni komponent) bo teraz mamy dwa różne formularze, jeden do tworzenia, drugi do edycji,

# 0016

- utwórz w ustawieniach narzędzie do testowania wysyłania e-maili, jako klucza api użyj parametru MAIL_API_KEY, który jest w .env.local, jako email osoby wysyłającej uzyj parametru MAIL_FROM, który jest w .env.local, do wysyłania maili używamy usługi mailersend.com, przykładowy kod użycia API:
  const Recipient = require("mailersend").Recipient;
  const EmailParams = require("mailersend").EmailParams;
  const MailerSend = require("mailersend");

          const mailersend = new MailerSend({
              apiKey: "key",
          });

          const recipients = [new Recipient("<EMAIL>", "Recipient")];

          const emailParams = new EmailParams()
              .setFrom("<EMAIL>")
              .setFromName("Your Name")
              .setRecipients(recipients)
              .setSubject("Subject")
              .setHtml("Greetings from the team, you got this message through MailerSend.")
              .setText("Greetings from the team, you got this message through MailerSend.");

          mailersend.send(emailParams);

# 0017

think hard, w tabeli Task jest pole assignedToId, które już nie jest uzywane, a przynajmniej nie powinno być - zweryfikuj to, sprawdź w całym projekcie czy tak jest faktycznie i przygotuj plan usunięcia pola z tabeli i projektu, zanim zaczniesz coś zmienić przedstaw mi szczegółowy plan realizacji do zatwierdzenia, wykonaj też kopię danych w bazie by móc odnowić dane w razie potrzeby
