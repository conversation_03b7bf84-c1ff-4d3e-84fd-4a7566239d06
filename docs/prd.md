# FamilyTasks - Dokument Wymagań Produktu (PRD)

## 1. Wprowadzenie i cel projektu

### 1.1 Cel produktu

FamilyTasks to aplikacja internetowa służąca do zarządzania zadaniami w rodzinie. Jej głównym celem jest usprawnienie organizacji obowiązków domowych, rozdzielanie ich między członków rodziny oraz zwiększenie motywacji poprzez system punktów i nagród. Oprócz organizacji zadań, aplikacja oferuje również narzędzia do planowania i monitorowania czasu spędzonego na różnych aktywnościach (np. posiłki, rozrywka, nauka). Każda rodzina może planować i przydzielać zadania swoim członkom, ustalać ich harmonogram, punktację i śledzić postępy.

### 1.2 Grupa docelowa

- Rodziny z dziećmi w różnym wieku
- Rodzice chcący wprowadzić system obowiązków domowych
- Dzieci uczące się odpowiedzialności i współpracy
- Rodziny wielopokoleniowe potrzebujące organizacji zadań domowych

### 1.3 Główne funkcjonalności

- Zarządzanie członkami rodziny i ich rolami
- Tworzenie, przydzielanie i śledzenie zadań
- System punktowy za wykonane zadania
- Zawieszanie zadań na określone okresy (np. wakacje, choroba)
- Statystyki i raporty aktywności
- Kalendarz zadań
- Historia wykonanych zadań

## 2. Opis funkcjonalności systemu z podziałem na moduły

### 2.1 Moduł Rodziny

**Cel**: Umożliwienie tworzenia, zarządzania i administrowania strukturą rodziny w aplikacji. Każdy uzytkownik może przynależeć do wielu rodzin i posiadać w nich różne uprawnienia.

**Funkcjonalności**:

- Tworzenie nowej rodziny
- Zapraszanie nowych członków rodziny
- Zarządzanie rolami członków (właściciel, rodzic, opiekun, dziecko)
- Edycja danych rodziny
- Usuwanie członków rodziny
- Opuszczanie rodziny
- Zarządzanie zaproszeniami

### 2.2 Moduł Zadań

**Cel**: Umożliwienie tworzenia, przydzielania i zarządzania zadaniami rodzinnymi.

**Funkcjonalności**:

- Dodawanie nowych zadań
- Edycja istniejących zadań
- Usuwanie zadań
- Przydzielanie zadań do członków rodziny
- Oznaczanie zadań jako wykonane
- Weryfikacja wykonania zadań przez rodziców/opiekunów
- Przyznawanie punktów za wykonane zadania
- Filtrowanie i sortowanie zadań
- Określanie różnych częstotliwości wykonywania zadań (jednorazowe, codzienne, tygodniowe, miesięczne)

### 2.3 Moduł Zawieszeń Zadań

**Cel**: Umożliwienie czasowego zawieszania zadań w określonych okolicznościach.

**Funkcjonalności**:

- Tworzenie zawieszeń zadań na określony okres
- Określanie powodu zawieszenia (święto/urlop, wyjazd, choroba, inny)
- Zawieszanie pojedynczych zadań lub wszystkich zadań
- Przegląd aktywnych zawieszeń
- Edycja i usuwanie zawieszeń

### 2.4 Moduł Statystyk

**Cel**: Dostarczanie informacji o aktywności i wykonaniu zadań przez członków rodziny.

**Funkcjonalności**:

- Generowanie statystyk wykonania zadań
- Wyświetlanie punktów zdobytych przez członków rodziny
- Tworzenie raportów aktywności w czasie
- Wizualizacja danych w formie wykresów i diagramów

### 2.5 Moduł Historii

**Cel**: Przechowywanie i udostępnianie historii wykonanych zadań.

**Funkcjonalności**:

- Rejestracja historii wykonanych zadań
- Przeglądanie historii zadań
- Filtrowanie i sortowanie historii

### 2.6 Moduł Kalendarza

**Cel**: Zapewnienie widoku kalendarzowego dla zadań rodzinnych.

**Funkcjonalności**:

- Wyświetlanie zadań w widoku kalendarza
- Planowanie zadań z wyprzedzeniem
- Nawigacja po kalendarzu

## 3. Szczegółowe wymagania dla każdego komponentu

### 3.1 Komponenty Modułu Rodziny

#### 3.1.1 Panel zarządzania rodziną

- Wyświetlanie danych rodziny (nazwa, data utworzenia)
- Zmiana rodziny jeśli użytkownik jest członkiem wielu rodzin
- Zakładki dla różnych funkcji (zarządzanie, tworzenie, zaproszenia)
- Lista członków rodziny z ich rolami i punktami
- Przyciski akcji dla edycji rodziny, usuwania członków, zmiany ról

#### 3.1.2 Formularz tworzenia rodziny

- Pole do wprowadzenia nazwy rodziny
- Przycisk do utworzenia rodziny
- Informacja zwrotna o statusie utworzenia

#### 3.1.3 Formularz zapraszania członków

- Pole do wprowadzenia adresu e-mail zapraszanej osoby
- Wybór roli dla nowego członka
- Dodatkowe opcje dla roli "dziecko" (określenie czy jest dorosłe)
- Przycisk do wysłania zaproszenia

#### 3.1.4 Dialog zmiany roli

- Wybór nowej roli dla członka rodziny
- Dodatkowe opcje dla roli "dziecko"
- Przyciski potwierdzenia i anulowania zmiany

### 3.2 Komponenty Modułu Zadań

#### 3.2.1 Lista zadań

- Tabela/lista zadań z informacjami (nazwa, opis, przypisana osoba, termin, status)
- Przyciski akcji dla każdego zadania (edycja, usunięcie, oznaczenie jako wykonane)
- Filtrowanie zadań według różnych kryteriów
- Komunikat dla pustej listy zadań z przyciskiem dodawania

#### 3.2.2 Formularz zadania

- Pola do wprowadzenia danych zadania (nazwa, opis, waga, przypisana osoba, częstotliwość, termin)
- Opcja włączenia przypomnień
- Przyciski zapisania i anulowania

#### 3.2.3 Dialog edycji zadania

- Te same pola co w formularzu zadania, ale wypełnione aktualnymi danymi
- Przyciski zapisania zmian i anulowania

#### 3.2.4 Dialog potwierdzenia usunięcia

- Komunikat ostrzegawczy
- Przyciski potwierdzenia i anulowania usunięcia

### 3.3 Komponenty Modułu Zawieszeń Zadań

#### 3.3.1 Lista zawieszeń

- Lista aktywnych zawieszeń z informacjami (powód, opis, okres, zadania)
- Przyciski akcji (edycja, usunięcie)
- Filtrowanie zawieszeń według kryteriów

#### 3.3.2 Formularz zawieszenia

- Pola wyboru zadań do zawieszenia
- Wybór powodu zawieszenia
- Pola dat początku i końca zawieszenia
- Pole opisu
- Przyciski zapisania i anulowania

### 3.4 Komponenty Modułu Statystyk

#### 3.4.1 Panel statystyk

- Wykresy aktywności członków rodziny
- Podsumowanie punktów
- Filtry czasowe dla danych
- Możliwość exportu danych

### 3.5 Komponenty Modułu Historii

#### 3.5.1 Tabela historii zadań

- Lista wykonanych zadań z informacjami (nazwa, wykonawca, data, punkty)
- Filtrowanie i sortowanie według różnych kryteriów
- Paginacja dla większej ilości danych

### 3.6 Komponenty Modułu Kalendarza

#### 3.6.1 Widok kalendarza

- Interaktywny kalendarz z zadaniami
- Możliwość przełączania widoków (miesiąc, tydzień, dzień)
- Możliwość dodawania zadań bezpośrednio z kalendarza

## 4. Specyfikacja techniczna

### 4.1 Architektura

### 4.2 Technologie

- **Frontend**:
  - Shadcn UI (komponenty UI)
  - React 18+
  - TypeScript
  - TailwindCSS 4

- **Backend**:
  - Next.js 15+ (framework frontend)
  - Prisma (ORM)
  - Supabase/Neon/PostgreSQL (baza danych), SQLite w trybie deweloperskim
  - Clerk (autoryzacja i uwierzytelnianie)

### 4.3 Integracje

- Powiadomienia e-mail
- Powiadomienia push
- Kalendarz Google/Apple (planowane)

## 5. Wymagania dotyczące UI/UX

### 5.1 Ogólne wytyczne projektowe

- Przejrzysty, intuicyjny interfejs
- Spójna kolorystyka z głównym akcentem kolorystycznym "family-purple"
- Komponenty UI bazujące na bibliotece Shadcn UI
- Responsywność dla różnych urządzeń (desktop, tablet, mobile)
- Dostępność zgodna z podstawowymi standardami WCAG

### 5.2 Tryb ciemny

- Pełne wsparcie dla trybu ciemnego
- Automatyczne przełączanie w zależności od ustawień systemowych
- Możliwość ręcznego wyboru trybu przez użytkownika
- Dostosowana paleta kolorów dla trybu ciemnego z zachowaniem czytelności

### 5.3 Responsywność

- Układ dostosowujący się do różnych rozmiarów ekranów
- Mobile-first approach w projektowaniu UI
- Odpowiednie zagęszczenie elementów interfejsu na małych ekranach
- Specjalne komponenty dla urządzeń mobilnych (bottom navigation, modalne dialogi)

### 5.4 Animacje i przejścia

- Subtelne animacje dla poprawy UX
- Płynne przejścia między widokami
- Animacje informacyjne (loading, success, error)
- Optymalizacja wydajności animacji

## 6. Wymagania dotyczące wielojęzyczności

### 6.1 Obsługiwane języki

- Polski (język domyślny)
- Angielski
- Niemiecki (planowany)
- Hiszpański (planowany)

### 6.2 System tłumaczeń

- Framework i18n do zarządzania tłumaczeniami
- Automatyczne wykrywanie języka przeglądarki
- Możliwość ręcznego wyboru języka przez użytkownika
- Przechowywanie preferowanego języka w profilu użytkownika

### 6.3 Tłumaczenia zasobów

- Interfejs użytkownika
- Komunikaty i powiadomienia
- Pomoc i dokumentacja
- E-maile i powiadomienia systemowe

## 7. Role i uprawnienia w systemie

### 7.1 Role systemowe

- **Niezalogowany użytkownik**: dostęp tylko do stron publicznych i logowania
- **Zalogowany użytkownik**: podstawowy dostęp do funkcji aplikacji

### 7.2 Role rodzinne

- **Właściciel rodziny (owner)**:
  - Pełna kontrola nad rodziną
  - Możliwość usunięcia rodziny
  - Zarządzanie wszystkimi członkami i ich rolami
  - Dostęp do wszystkich funkcji

- **Rodzic (parent)**:
  - Dodawanie i zarządzanie zadaniami
  - Weryfikacja wykonania zadań
  - Zapraszanie nowych członków rodziny
  - Tworzenie zawieszeń zadań
  - Brak możliwości usunięcia rodziny

- **Opiekun (guardian)**:
  - Dodawanie i zarządzanie zadaniami
  - Weryfikacja wykonania zadań
  - Tworzenie zawieszeń zadań
  - Brak możliwości zarządzania członkami rodziny

- **Dziecko (child)**:
  - Przeglądanie przydzielonych zadań
  - Oznaczanie zadań jako wykonane
  - Przeglądanie historii i statystyk własnych zadań
  - Ograniczone uprawnienia w zależności od tego, czy jest dorosłe

### 7.3 System kontroli dostępu

- Zabezpieczenie dostępu do danych rodzinnych (tylko dla członków rodziny)
- Zabezpieczenie operacji administracyjnych (tylko dla właścicieli rodziny)
- Row-Level Security w bazie danych
- Weryfikacja uprawnień na poziomie interfejsu użytkownika

## 8. Metryki sukcesu i KPI

### 8.1 Metryki użytkowania

- Liczba aktywnych rodzin
- Średnia liczba zadań per rodzina
- Średnia liczba członków per rodzina
- Współczynnik ukończenia zadań (% wykonanych zadań w stosunku do wszystkich)

### 8.2 Metryki zaangażowania

- Dzienny/tygodniowy współczynnik aktywności (% użytkowników aktywnych)
- Średni czas spędzany w aplikacji
- Współczynnik powracających użytkowników
- Liczba interakcji (dodane zadania, oznaczenia wykonania, weryfikacje)

### 8.3 Metryki wzrostu

- Tempo przyrostu nowych użytkowników
- Współczynnik konwersji (od rejestracji do utworzenia/dołączenia do rodziny)
- Współczynnik retencji po 30/60/90 dniach
- Liczba zaproszeń wysłanych przez użytkowników

### 8.4 Techniczne KPI

- Czas ładowania aplikacji (poniżej 3 sekund)
- Dostępność aplikacji (uptime > 99.9%)
- Liczba błędów zgłaszanych przez użytkowników
- Wskaźnik responsywności UI (opóźnienia < 100ms)
