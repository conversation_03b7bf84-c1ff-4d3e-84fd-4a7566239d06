# 📱 FamilyTasks – system do zarządzania obowiązkami domowymi

## 🎯 Cel aplikacji

System do organizacji i przypominania o domowych obowiązkach oraz czasie przeznaczonym na różne aktywności (np. pos<PERSON><PERSON><PERSON>, roz<PERSON>w<PERSON>, nauka). <PERSON>ż<PERSON> rodzina może planować i przydzielać zadania swoim członkom, ustalać ich harmonogram, punktację i śledzić postępy.

---

## 👨‍👩‍👧‍👦 Grupa docelowa

Rodziny z dziećmi w różnym wieku. System zakłada istnienie kont rodzinnych, do których można przypisać różnych użytkowników z odpowiednimi rolami i zakresem uprawnień.

---

## 🧩 Główne funkcje

- 🔐 Rejestracja i logowanie (obsługa wielu rodzin)
- 👥 Tworzenie konta rodziny i zarządzanie członkami
- ✅ Tworzenie zadań: nazwa, opis, waga, c<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, czas
- 📆 Kalendarz zadań domowych (widok dzienny i tygodniowy)
- 🛎️ Powiadomienia push i e-mail
- 📊 Dashboard z punktacją i aktywnością
- 🔄 Obsługa zadań powtarzalnych
- 📈 Historia wykonania zadań i system motywacyjny

---

## 🧪 Technologie

| Obszar         | Technologia                           |
| -------------- | ------------------------------------- |
| Frontend       | React + TypeScript + TailwindCSS      |
| Backend / Baza | Supabase (PostgreSQL, Edge, Realtime) |
| Autoryzacja    | Clerk (zintegrowane z Supabase)       |
| Powiadomienia  | Web Push API, e-mail (SMTP / Resend)  |
| Mobile         | PWA + CapacitorJS (iOS/Android)       |
| Statystyki     | Recharts                              |

---

## 👤 Role użytkowników

- **Właściciel rodziny** – tworzy rodzinę, pełna administracja
- **Rodzic** – zarządzanie zadaniami i użytkownikami
- **Opiekun** – potwierdza wykonanie zadań
- **Dziecko dorosłe** – wykonuje zadania, ograniczone opcje
- **Dziecko niepełnoletnie** – wykonuje przypisane zadania

> 🔁 Jeden użytkownik może należeć do wielu rodzin.

---

## 🗂️ Etapy realizacji

1. **Inicjalizacja projektu**
   - Konfiguracja repozytorium, Supabase, Clerk, PWA

2. **Rejestracja i logowanie użytkowników**
   - Obsługa wielu rodzin i ról

3. **Zarządzanie rodzinami i członkami**
   - Tworzenie i dołączanie do rodzin, przypisywanie ról

4. **Moduł zadań**
   - Dodawanie, edytowanie, harmonogramowanie, przypisywanie

5. **System powiadomień**
   - Push (Web i CapacitorJS), e-mail

6. **Panel punktów i wykresów**
   - Zestawienia, rankingi, historia

7. **Budowanie aplikacji mobilnej (CapacitorJS)**
   - Eksport jako natywna aplikacja PWA dla Android / iOS

---

## 📝 Uwagi końcowe

- System ma charakter edukacyjno-organizacyjny.
- Waga zadania i użytkownika pozwala na skalowanie punktów.
- Projekt może być rozszerzany o integrację z kalendarzami Google i opcjonalny system nagród.
