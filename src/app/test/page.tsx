import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

export default function TestPage() {
  return (
    <div className="container mx-auto p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-family-purple mb-4">
          FamilyTasks - Test Komponentów
        </h1>
        <p className="text-lg text-muted-foreground">Test podstawowych komponentów Shadcn UI</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Przycisk</CardTitle>
            <CardDescription>R<PERSON><PERSON>ne warianty przycisków</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button>Domyślny</Button>
            <Button variant="secondary">Drugorzędny</Button>
            <Button variant="destructive">Usunięcie</Button>
            <Button variant="outline">Obramowanie</Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pole tekstowe</CardTitle>
            <CardDescription>Przykład pola input</CardDescription>
          </CardHeader>
          <CardContent>
            <Input placeholder="Wpisz tekst..." />
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Dialog</CardTitle>
            <CardDescription>Modal dialog</CardDescription>
          </CardHeader>
          <CardContent>
            <Dialog>
              <DialogTrigger asChild>
                <Button>Otwórz Dialog</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Przykładowy Dialog</DialogTitle>
                  <DialogDescription>
                    To jest przykład modala w aplikacji FamilyTasks.
                  </DialogDescription>
                </DialogHeader>
                <div className="py-4">
                  <p>Treść dialogu...</p>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      </div>

      <div className="text-center">
        <p className="text-sm text-muted-foreground">
          Kolory family-purple zostały zastosowane do głównego nagłówka.
        </p>
      </div>
    </div>
  );
}
