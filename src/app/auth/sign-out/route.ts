import { auth, clerkClient } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.redirect(
        new URL('/auth/sign-in', process.env.NEXT_PUBLIC_URL || 'http://localhost:3000')
      );
    }

    // Clerk automatycznie obsługuje wylogowanie przez przekierowanie na odpowiedni URL
    // Przekieruj na URL wylogowania Clerk
    const signOutUrl = new URL(
      '/auth/sign-in',
      process.env.NEXT_PUBLIC_URL || 'http://localhost:3000'
    );

    // Ustaw header do wylogowania sesji Clerk
    const response = NextResponse.redirect(signOutUrl);
    response.headers.set('Clear-Site-Data', '"cookies", "storage"');

    return response;
  } catch (error) {
    console.error('Error during sign out:', error);
    return NextResponse.redirect(
      new URL('/auth/sign-in', process.env.NEXT_PUBLIC_URL || 'http://localhost:3000')
    );
  }
}

export async function POST() {
  return GET(); // Obsługuj zarówno GET jak i POST
}
