'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { Bell, Save, Mail } from 'lucide-react';

interface NotificationPreferences {
  id: string;
  emailNotificationsEnabled: boolean;
  notifyTaskAssigned: boolean;
  notifyTaskCompleted: boolean;
  notifyTaskVerified: boolean;
  notifyTaskReminder: boolean;
}

export default function NotificationsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchPreferences();
    }
  }, [isLoaded, isSignedIn]);

  const fetchPreferences = async () => {
    try {
      const response = await fetch('/api/user/preferences/notifications');
      if (!response.ok) {
        throw new Error('Nie udało się pobrać preferencji');
      }
      const data = await response.json();
      setPreferences(data);
    } catch (error) {
      console.error('Error fetching preferences:', error);
      toast.error('Nie udało się pobrać ustawień powiadomień');
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = (key: keyof NotificationPreferences, value: boolean) => {
    if (!preferences) return;
    setPreferences({ ...preferences, [key]: value });
  };

  const savePreferences = async () => {
    if (!preferences) return;

    setSaving(true);
    try {
      const response = await fetch('/api/user/preferences/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailNotificationsEnabled: preferences.emailNotificationsEnabled,
          notifyTaskAssigned: preferences.notifyTaskAssigned,
          notifyTaskCompleted: preferences.notifyTaskCompleted,
          notifyTaskVerified: preferences.notifyTaskVerified,
          notifyTaskReminder: preferences.notifyTaskReminder,
        }),
      });

      if (!response.ok) {
        throw new Error('Nie udało się zapisać preferencji');
      }

      toast.success('Ustawienia powiadomień zostały zapisane');
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast.error('Nie udało się zapisać ustawień');
    } finally {
      setSaving(false);
    }
  };

  if (!isLoaded || loading) {
    return (
      <div className="container mx-auto py-8 max-w-2xl">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-6">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center justify-between">
                <div>
                  <Skeleton className="h-4 w-48 mb-2" />
                  <Skeleton className="h-3 w-64" />
                </div>
                <Skeleton className="h-6 w-12" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="container mx-auto py-8 max-w-2xl">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Musisz być zalogowany, aby zarządzać ustawieniami powiadomień.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className="container mx-auto py-8 max-w-2xl">
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Nie udało się załadować ustawień powiadomień.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Ustawienia powiadomień
          </CardTitle>
          <CardDescription>
            Zarządzaj swoimi preferencjami dotyczącymi powiadomień e-mail
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Master switch */}
          <div className="flex items-center justify-between space-x-2">
            <div className="flex-1">
              <Label className="flex items-center gap-2 text-base font-medium">
                <Mail className="h-4 w-4" />
                Powiadomienia e-mail
              </Label>
              <p className="text-sm text-muted-foreground mt-1">
                Główny przełącznik dla wszystkich powiadomień e-mail
              </p>
            </div>
            <Switch
              checked={preferences.emailNotificationsEnabled}
              onCheckedChange={checked => updatePreference('emailNotificationsEnabled', checked)}
            />
          </div>

          <div className="border-t pt-6">
            <h3 className="text-sm font-medium mb-4 text-muted-foreground">RODZAJE POWIADOMIEŃ</h3>
            <div className="space-y-4">
              {/* Task assigned */}
              <div className="flex items-center justify-between space-x-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">Przypisanie zadania</Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    Otrzymuj powiadomienia gdy zostanie Ci przypisane nowe zadanie
                  </p>
                </div>
                <Switch
                  checked={preferences.notifyTaskAssigned}
                  onCheckedChange={checked => updatePreference('notifyTaskAssigned', checked)}
                  disabled={!preferences.emailNotificationsEnabled}
                />
              </div>

              {/* Task completed */}
              <div className="flex items-center justify-between space-x-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">Ukończenie zadania</Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    Otrzymuj powiadomienia gdy ktoś oznaczył zadanie jako ukończone
                  </p>
                </div>
                <Switch
                  checked={preferences.notifyTaskCompleted}
                  onCheckedChange={checked => updatePreference('notifyTaskCompleted', checked)}
                  disabled={!preferences.emailNotificationsEnabled}
                />
              </div>

              {/* Task verified */}
              <div className="flex items-center justify-between space-x-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">Weryfikacja zadania</Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    Otrzymuj powiadomienia gdy Twoje zadanie zostanie zatwierdzone lub odrzucone
                  </p>
                </div>
                <Switch
                  checked={preferences.notifyTaskVerified}
                  onCheckedChange={checked => updatePreference('notifyTaskVerified', checked)}
                  disabled={!preferences.emailNotificationsEnabled}
                />
              </div>

              {/* Task reminder */}
              <div className="flex items-center justify-between space-x-2">
                <div className="flex-1">
                  <Label className="text-sm font-medium">Przypomnienia</Label>
                  <p className="text-xs text-muted-foreground mt-1">
                    Otrzymuj przypomnienia o zadaniach zbliżających się do terminu
                  </p>
                </div>
                <Switch
                  checked={preferences.notifyTaskReminder}
                  onCheckedChange={checked => updatePreference('notifyTaskReminder', checked)}
                  disabled={!preferences.emailNotificationsEnabled}
                />
              </div>
            </div>
          </div>

          <div className="border-t pt-6 flex justify-end">
            <Button onClick={savePreferences} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Zapisywanie...' : 'Zapisz ustawienia'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
