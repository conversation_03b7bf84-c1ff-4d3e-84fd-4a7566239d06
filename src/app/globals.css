@import 'tailwindcss';
@import 'tw-animate-css';
@import 'react-day-picker/style.css';

@custom-variant dark (&:is(.dark *));

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar: var(--sidebar);
    --color-chart-5: var(--chart-5);
    --color-chart-4: var(--chart-4);
    --color-chart-3: var(--chart-3);
    --color-chart-2: var(--chart-2);
    --color-chart-1: var(--chart-1);
    --color-ring: var(--ring);
    --color-input: var(--input);
    --color-border: var(--border);
    --color-destructive: var(--destructive);
    --color-accent-foreground: var(--accent-foreground);
    --color-accent: var(--accent);
    --color-muted-foreground: var(--muted-foreground);
    --color-muted: var(--muted);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-secondary: var(--secondary);
    --color-primary-foreground: var(--primary-foreground);
    --color-primary: var(--primary);
    --color-popover-foreground: var(--popover-foreground);
    --color-popover: var(--popover);
    --color-card-foreground: var(--card-foreground);
    --color-card: var(--card);
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
}

:root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.647 0.154 264.54);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.647 0.154 264.54);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);

    /* Family Purple Brand Colors */
    --family-purple: #8b5cf6;
    --family-purple-50: #f3f4f6;
    --family-purple-100: #e5e7eb;
    --family-purple-200: #d1d5db;
    --family-purple-300: #9ca3af;
    --family-purple-400: #6b7280;
    --family-purple-500: #8b5cf6;
    --family-purple-600: #7c3aed;
    --family-purple-700: #6d28d9;
    --family-purple-800: #5b21b6;
    --family-purple-900: #4c1d95;


    /* 🟣 Kolory główne (na podstawie projektu) */
    /* fioletowy - kolor główny */
    --family-primary: #a259ff;
    --family-primary-50: #a259ff;
    --family-primary-100: #ac6bff;
    --family-primary-200: #b77eff;
    --family-primary-300: #c190ff;
    --family-primary-400: #cba3ff;
    --family-primary-500: #d6b5ff;
    --family-primary-600: #e0c8ff;
    --family-primary-700: #eadaff;
    --family-primary-800: #f5edff;
    --family-primary-900: #ffffff;
    /* ciemniejszy fiolet (hover, aktywny) */
    --family-primary-dark: #7e22ce;
    --family-primary-dark-50: #7e22ce;
    --family-primary-dark-100: #8c3bd3;
    --family-primary-dark-200: #9b53d9;
    --family-primary-dark-300: #a96cde;
    --family-primary-dark-400: #b784e4;
    --family-primary-dark-500: #c69de9;
    --family-primary-dark-600: #d4b5ef;
    --family-primary-dark-700: #e2cef4;
    --family-primary-dark-800: #f1e6fa;
    --family-primary-dark-900: #ffffff;
    /* tło główne */
    --family-background-main: #ffffff;
    --family-background-main-50: #ffffff;
    --family-background-main-100: #ffffff;
    --family-background-main-200: #ffffff;
    --family-background-main-300: #ffffff;
    --family-background-main-400: #ffffff;
    --family-background-main-500: #ffffff;
    --family-background-main-600: #ffffff;
    --family-background-main-700: #ffffff;
    --family-background-main-800: #ffffff;
    --family-background-main-900: #ffffff;
    /* gradient dolny */
    --family-background-soft: #efeaff;
    --family-background-soft-50: #efeaff;
    --family-background-soft-100: #f1ecff;
    --family-background-soft-200: #f3efff;
    --family-background-soft-300: #f4f1ff;
    --family-background-soft-400: #f6f3ff;
    --family-background-soft-500: #f8f6ff;
    --family-background-soft-600: #faf8ff;
    --family-background-soft-700: #fbfaff;
    --family-background-soft-800: #fdfdff;
    --family-background-soft-900: #ffffff;
    /* główny tekst */
    --family-text-primary: #1a1a1a;
    --family-text-primary-50: #1a1a1a;
    --family-text-primary-100: #333333;
    --family-text-primary-200: #4d4d4d;
    --family-text-primary-300: #666666;
    --family-text-primary-400: #808080;
    --family-text-primary-500: #999999;
    --family-text-primary-600: #b3b3b3;
    --family-text-primary-700: #cccccc;
    --family-text-primary-800: #e6e6e6;
    --family-text-primary-900: #ffffff;
    /* pomocniczy tekst */
    --family-text-secondary: #6b6b6b;
    --family-text-secondary-50: #6b6b6b;
    --family-text-secondary-100: #7b7b7b;
    --family-text-secondary-200: #8c8c8c;
    --family-text-secondary-300: #9c9c9c;
    --family-text-secondary-400: #adadad;
    --family-text-secondary-500: #bdbdbd;
    --family-text-secondary-600: #cecece;
    --family-text-secondary-700: #dedede;
    --family-text-secondary-800: #efefef;
    --family-text-secondary-900: #ffffff;
    /* zielony - zadanie wykonane */
    --family-complete: #22c55e;
    --family-complete-50: #22c55e;
    --family-complete-100: #3bcb70;
    --family-complete-200: #53d282;
    --family-complete-300: #6cd894;
    --family-complete-400: #84dfa6;
    --family-complete-500: #9de5b7;
    --family-complete-600: #b5ecc9;
    --family-complete-700: #cef2db;
    --family-complete-800: #e6f9ed;
    --family-complete-900: #ffffff;

    /* 🌈 Wyraziste kolory komplementarne */
    /* pastelowy żółty */
    --family-accent-yellow: #ffe066;
    --family-accent-yellow-50: #ffe066;
    --family-accent-yellow-100: #ffe377;
    --family-accent-yellow-200: #ffe788;
    --family-accent-yellow-300: #ffea99;
    --family-accent-yellow-400: #ffeeaa;
    --family-accent-yellow-500: #fff1bb;
    --family-accent-yellow-600: #fff5cc;
    --family-accent-yellow-700: #fff8dd;
    --family-accent-yellow-800: #fffcee;
    --family-accent-yellow-900: #ffffff;
    /* jasny pomarańcz */
    --family-accent-orange: #ffb347;
    --family-accent-orange-50: #ffb347;
    --family-accent-orange-100: #ffbb5b;
    --family-accent-orange-200: #ffc470;
    --family-accent-orange-300: #ffcc84;
    --family-accent-orange-400: #ffd599;
    --family-accent-orange-500: #ffddad;
    --family-accent-orange-600: #ffe6c2;
    --family-accent-orange-700: #ffeed6;
    --family-accent-orange-800: #fff7eb;
    --family-accent-orange-900: #ffffff;
    /* złoty - np. nagrody */
    --family-accent-gold: #facc15;
    --family-accent-gold-50: #facc15;
    --family-accent-gold-100: #fbd22f;
    --family-accent-gold-200: #fbd749;
    --family-accent-gold-300: #fcdd63;
    --family-accent-gold-400: #fce37d;
    --family-accent-gold-500: #fde897;
    --family-accent-gold-600: #fdeeb1;
    --family-accent-gold-700: #fef4cb;
    --family-accent-gold-800: #fef9e5;
    --family-accent-gold-900: #ffffff;
    /* czerwony - błędy */
    --family-accent-red: #ef4444;
    --family-accent-red-50: #ef4444;
    --family-accent-red-100: #f15959;
    --family-accent-red-200: #f36e6e;
    --family-accent-red-300: #f48282;
    --family-accent-red-400: #f69797;
    --family-accent-red-500: #f8acac;
    --family-accent-red-600: #fac1c1;
    --family-accent-red-700: #fbd5d5;
    --family-accent-red-800: #fdeaea;
    --family-accent-red-900: #ffffff;
    /* malinowy - emocjonalne akcenty */
    --family-accent-rose: #fb7185;
    --family-accent-rose-50: #fb7185;
    --family-accent-rose-100: #fb8193;
    --family-accent-rose-200: #fc91a0;
    --family-accent-rose-300: #fca0ae;
    --family-accent-rose-400: #fdb0bb;
    --family-accent-rose-500: #fdc0c9;
    --family-accent-rose-600: #fed0d6;
    --family-accent-rose-700: #fedfe4;
    --family-accent-rose-800: #ffeff1;
    --family-accent-rose-900: #ffffff;

    /* 🌿 Stonowane kolory uzupełniające */
    /* ciepły beżowy */
    --family-muted-beige: #f7f3e9;
    --family-muted-beige-50: #f7f3e9;
    --family-muted-beige-100: #f8f4eb;
    --family-muted-beige-200: #f9f6ee;
    --family-muted-beige-300: #faf7f0;
    --family-muted-beige-400: #fbf8f3;
    --family-muted-beige-500: #fbfaf5;
    --family-muted-beige-600: #fcfbf8;
    --family-muted-beige-700: #fdfcfa;
    --family-muted-beige-800: #fefefd;
    --family-muted-beige-900: #ffffff;
    /* jasny oliwkowy */
    --family-muted-olive: #dde7d3;
    --family-muted-olive-50: #dde7d3;
    --family-muted-olive-100: #e1ead8;
    --family-muted-olive-200: #e5ecdd;
    --family-muted-olive-300: #e8efe2;
    --family-muted-olive-400: #ecf2e7;
    --family-muted-olive-500: #f0f4eb;
    --family-muted-olive-600: #f4f7f0;
    --family-muted-olive-700: #f7faf5;
    --family-muted-olive-800: #fbfcfa;
    --family-muted-olive-900: #ffffff;
    /* pudrowy łosoś */
    --family-muted-salmon: #f6d6c8;
    --family-muted-salmon-50: #f6d6c8;
    --family-muted-salmon-100: #f7dbce;
    --family-muted-salmon-200: #f8dfd4;
    --family-muted-salmon-300: #f9e4da;
    --family-muted-salmon-400: #fae8e0;
    --family-muted-salmon-500: #fbede7;
    --family-muted-salmon-600: #fcf1ed;
    --family-muted-salmon-700: #fdf6f3;
    --family-muted-salmon-800: #fefaf9;
    --family-muted-salmon-900: #ffffff;
    /* błękit stalowy */
    --family-muted-steel: #d4e1ec;
    --family-muted-steel-50: #d4e1ec;
    --family-muted-steel-100: #d9e4ee;
    --family-muted-steel-200: #dee8f0;
    --family-muted-steel-300: #e2ebf2;
    --family-muted-steel-400: #e7eef4;
    --family-muted-steel-500: #ecf2f7;
    --family-muted-steel-600: #f1f5f9;
    --family-muted-steel-700: #f5f8fb;
    --family-muted-steel-800: #fafcfd;
    --family-muted-steel-900: #ffffff;
    /* zgaszony żółty */
    --family-muted-yellow: #f5edc1;
    --family-muted-yellow-50: #f5edc1;
    --family-muted-yellow-100: #f6efc8;
    --family-muted-yellow-200: #f7f1cf;
    --family-muted-yellow-300: #f8f3d6;
    --family-muted-yellow-400: #f9f5dd;
    --family-muted-yellow-500: #fbf7e3;
    --family-muted-yellow-600: #fcf9ea;
    --family-muted-yellow-700: #fdfbf1;
    --family-muted-yellow-800: #fefdf8;
    --family-muted-yellow-900: #ffffff;

    /* 🟦 Inne tła i sekcje */
    /* jasny szary do kart */
    --family-card-background: #f4f4f5;
    --family-card-background-50: #f4f4f5;
    --family-card-background-100: #f5f5f6;
    --family-card-background-200: #f6f6f7;
    --family-card-background-300: #f8f8f8;
    --family-card-background-400: #f9f9f9;
    --family-card-background-500: #fafafb;
    --family-card-background-600: #fbfbfc;
    --family-card-background-700: #fdfdfd;
    --family-card-background-800: #fefefe;
    --family-card-background-900: #ffffff;
    /* pastelowy błękit (sekcje informacyjne) */
    --family-info-background: #cffafe;
    --family-info-background-50: #cffafe;
    --family-info-background-100: #d4fbfe;
    --family-info-background-200: #dafbfe;
    --family-info-background-300: #dffcfe;
    --family-info-background-400: #e4fcfe;
    --family-info-background-500: #eafdff;
    --family-info-background-600: #effdff;
    --family-info-background-700: #f4feff;
    --family-info-background-800: #fafeff;
    --family-info-background-900: #ffffff;


    /* Task Status Colors */
    --task-completed: #10b981;
    --task-pending: #6b7280;
    --task-overdue: #ef4444;
    --task-in-progress: #f59e0b;

    /* Semantic Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-900: #111827;

    /* Background & Surface */
    --surface: #f9fafb;
    --surface-secondary: #f3f4f6;

    /* Spacing Scale */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 9999px;
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);

    /* Dark mode specific colors */
    --family-purple: #a78bfa;
    --surface: #1f2937;
    --surface-secondary: #374151;

    /* Dark mode task status colors */
    --task-completed: #34d399;
    --task-pending: #9ca3af;
    --task-overdue: #f87171;
    --task-in-progress: #fbbf24;

    /* Dark mode semantic colors */
    --success: #34d399;
    --warning: #fbbf24;
    --error: #f87171;
    --info: #60a5fa;

    /* Dark mode neutral colors */
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-900: #f9fafb;
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply bg-background text-foreground;
    }

    /* Smooth theme transitions */
    html {
        transition:
            background-color 0.3s ease,
            color 0.3s ease;
    }

    * {
        transition:
            background-color 0.3s ease,
            border-color 0.3s ease,
            color 0.3s ease,
            fill 0.3s ease,
            stroke 0.3s ease,
            box-shadow 0.3s ease;
    }
}

/* Typography System */
.font-display {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.font-body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    line-height: 1.6;
}

.text-display {
    font-size: 3.75rem;
}

.text-h1 {
    font-size: 2.25rem;
}

.text-h2 {
    font-size: 1.875rem;
}

.text-h3 {
    font-size: 1.5rem;
}

.text-body {
    font-size: 1rem;
}

.text-small {
    font-size: 0.875rem;
}

.text-xs {
    font-size: 0.75rem;
}

/* Shadow System */
.shadow-sm {
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.shadow-md {
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}

.shadow-xl {
    box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

.card-shadow {
    box-shadow:
        0 1px 3px 0 rgb(0 0 0 / 0.1),
        0 1px 2px 0 rgb(0 0 0 / 0.06);
}

/* Task Status Indicators */
.task-completed {
    border-left: 4px solid var(--task-completed);
}

.task-pending {
    border-left: 4px solid var(--task-pending);
}

.task-overdue {
    border-left: 4px solid var(--task-overdue);
}

.task-in-progress {
    border-left: 4px solid var(--task-in-progress);
}

/* Animations and Transitions */
.transition-all {
    transition: all 0.15s ease-in-out;
}

.transition-colors {
    transition:
        color,
        background-color,
        border-color 0.15s ease-in-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* Success Checkmark Animation */
@keyframes checkmark {
    0% {
        transform: scale(0) rotate(45deg);
    }

    50% {
        transform: scale(1.2) rotate(45deg);
    }

    100% {
        transform: scale(1) rotate(45deg);
    }
}

.success-checkmark {
    animation: checkmark 0.3s ease-in-out;
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide In Right Animation */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Interaction Feedback */
.button:active {
    transform: scale(0.98);
}

.card:active {
    transform: scale(0.99);
}

/* Accessibility Improvements */
/* Focus styles for keyboard navigation */
*:focus {
    outline: 2px solid var(--family-purple);
    outline-offset: 2px;
}

/* Skip to content link */
.skip-to-content {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--family-purple);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 100;
    font-weight: 600;
}

.skip-to-content:focus {
    top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --family-purple: #6d28d9;
        --task-completed: #059669;
        --task-overdue: #dc2626;
        --task-in-progress: #d97706;
    }

    .dark {
        --family-purple: #a78bfa;
        --task-completed: #10b981;
        --task-overdue: #f87171;
        --task-in-progress: #fbbf24;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .hover-lift:hover {
        transform: none;
    }

    .hover-scale:hover {
        transform: none;
    }
}

/* Screen reader only content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Minimum contrast ratios */
.text-low-contrast {
    color: #757575;
    /* 4.5:1 ratio */
}

.dark .text-low-contrast {
    color: #a1a1aa;
    /* 4.5:1 ratio on dark background */
}

/* Breakpoint System */
@custom-media --mobile (max-width: 767px);
@custom-media --tablet (min-width: 768px) and (max-width: 1023px);
@custom-media --desktop (min-width: 1024px);
@custom-media --desktop-lg (min-width: 1280px);

/* Responsive Grid System */
.grid-responsive {
    display: grid;
    gap: var(--space-4);
    grid-template-columns: 1fr;
}

@media (min-width: 768px) {
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .grid-responsive {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Grid Variants */
.grid-1 {
    grid-template-columns: 1fr;
}

.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

.grid-6 {
    grid-template-columns: repeat(6, 1fr);
}

.grid-12 {
    grid-template-columns: repeat(12, 1fr);
}

/* Responsive Grid Classes */
@media (min-width: 768px) {
    .md\:grid-1 {
        grid-template-columns: 1fr;
    }

    .md\:grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .md\:grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    .md\:grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (min-width: 1024px) {
    .lg\:grid-1 {
        grid-template-columns: 1fr;
    }

    .lg\:grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .lg\:grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    .lg\:grid-4 {
        grid-template-columns: repeat(4, 1fr);
    }

    .lg\:grid-6 {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Mobile-First Utilities */
.mobile-only {
    display: block;
}

@media (min-width: 768px) {
    .mobile-only {
        display: none;
    }
}

.desktop-only {
    display: none;
}

@media (min-width: 1024px) {
    .desktop-only {
        display: block;
    }
}

.tablet-only {
    display: none;
}

@media (min-width: 768px) and (max-width: 1023px) {
    .tablet-only {
        display: block;
    }
}

/* Touch-friendly sizing */
.touch-target {
    min-height: 44px;
    min-width: 44px;
}

/* Safe area padding for mobile devices */
.safe-area-pb {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-pt {
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
}

/* Mobile spacing */
.mobile-spacing {
    padding: var(--space-4);
}

@media (min-width: 768px) {
    .mobile-spacing {
        padding: var(--space-6);
    }
}

@media (min-width: 1024px) {
    .mobile-spacing {
        padding: var(--space-8);
    }
}

/* React Day Picker Customizations */
.rdp-root {
    --rdp-dropdown-gap: 0.25rem;
}

/* Override react-day-picker dropdown styles for better visibility */
.rdp-dropdown_root {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    border-radius: 6px;
    border: 1px solid hsl(var(--border));
    background-color: hsl(var(--background));
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    color: hsl(var(--foreground));
    cursor: pointer;
    transition: all 0.2s ease;
}

.rdp-dropdown_root:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
}

.rdp-dropdown_root:focus-within {
    outline: none;
    ring: 1px solid hsl(var(--ring));
}

.rdp-dropdown_root[disabled] {
    pointer-events: none;
    opacity: 0.5;
}

.rdp-dropdown {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    cursor: pointer;
    opacity: 0;
}

.rdp-dropdowns {
    display: flex;
    align-items: center;
    gap: 4px;
}