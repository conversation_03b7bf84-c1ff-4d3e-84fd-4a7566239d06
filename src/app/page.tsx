'use client';

import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Link from 'next/link';
import { Header } from '@/components/layout/Header';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Users, CheckCircle, Star, ArrowRight } from 'lucide-react';

export default function Home() {
  const { isLoaded, isSignedIn } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      router.push('/dashboard');
    }
  }, [isLoaded, isSignedIn, router]);

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (isSignedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Przekierowywanie do dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-purple-600 to-purple-800">
        <Container size="xl">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-display font-bold text-white leading-tight">
                FamilyTasks
              </h1>
              <p className="text-xl lg:text-2xl text-purple-100 max-w-4xl mx-auto leading-relaxed">
                Organizuj zadania domowe dla całej rodziny. Przydzielaj punkty, śledź postępy i
                buduj lepsze nawyki razem.
              </p>
            </div>
          </div>
        </Container>
      </section>

      {/* Features Section */}
      <section className="py-20 lg:py-32 bg-white">
        <Container size="lg">
          <div className="space-y-16">
            <div className="text-center space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Funkcje dla całej rodziny
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Wszystkie narzędzia potrzebne do zorganizowania domowych obowiązków w jednej,
                prostej aplikacji.
              </p>
            </div>

            <Grid cols={1} mdCols={3} gap="lg">
              <GridItem>
                <Card className="text-center p-8 h-full transition-all hover:shadow-lg hover:scale-105">
                  <div className="space-y-6">
                    <div className="mx-auto w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center">
                      <Users className="h-8 w-8 text-purple-600" />
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-xl font-display font-semibold text-gray-900">
                        Zarządzanie rodziną
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        Stwórz rodzinę, dodaj członków i przypisz odpowiednie role rodzicom i
                        dzieciom.
                      </p>
                    </div>
                  </div>
                </Card>
              </GridItem>

              <GridItem>
                <Card className="text-center p-8 h-full transition-all hover:shadow-lg hover:scale-105">
                  <div className="space-y-6">
                    <div className="mx-auto w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center">
                      <CheckCircle className="h-8 w-8 text-purple-600" />
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-xl font-display font-semibold text-gray-900">
                        System zadań
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        Twórz zadania, przydzielaj je członkom rodziny i śledź postępy w realizacji.
                      </p>
                    </div>
                  </div>
                </Card>
              </GridItem>

              <GridItem>
                <Card className="text-center p-8 h-full transition-all hover:shadow-lg hover:scale-105">
                  <div className="space-y-6">
                    <div className="mx-auto w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center">
                      <Star className="h-8 w-8 text-purple-600" />
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-xl font-display font-semibold text-gray-900">
                        Punkty i nagrody
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        Motywuj przez system punktów i nagród za wykonane zadania domowe.
                      </p>
                    </div>
                  </div>
                </Card>
              </GridItem>
            </Grid>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-purple-50">
        <Container size="lg">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Gotowy na zorganizowaną rodzinę?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Rozpocznij swoją podróż z FamilyTasks i odkryj, jak proste może być zorganizowane
                życie rodzinne.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/auth/sign-up">
                  Rozpocznij za darmo
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/features">Zobacz funkcje</Link>
              </Button>
            </div>
          </div>
        </Container>
      </section>

      {/* Additional CTA Section */}
      <section className="py-20 bg-gray-50">
        <Container size="lg">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Gotowy na lepszą organizację rodziny?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Dołącz do tysięcy rodzin, które już odkryły jak proste może być zarządzanie domowymi
                obowiązkami z FamilyTasks.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/auth/sign-up">
                  Rozpocznij 14-dniowy trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/about">Poznaj naszą historię</Link>
              </Button>
            </div>
          </div>
        </Container>
      </section>
    </div>
  );
}
