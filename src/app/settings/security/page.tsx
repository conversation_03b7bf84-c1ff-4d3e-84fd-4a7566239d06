'use client';

import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Shield, ExternalLink, Bell, ArrowLeft, User, Lock, Key } from 'lucide-react';
import Link from 'next/link';

export default function SecuritySettingsPage() {
  const { isLoaded, isSignedIn } = useAuth();

  const openClerkUserProfile = () => {
    // Clerk User Profile można otworzyć przez ich komponent lub przekierowanie
    window.open('https://accounts.dev/user', '_blank');
  };

  if (!isLoaded) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6 space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Musisz być zalogowany, aby zarządzać ustawieniami konta.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-2xl">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/settings" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Powrót do ustawień
          </Link>
        </Button>
      </div>

      <div className="space-y-6">
        {/* Account Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Zarządzanie kontem
            </CardTitle>
            <CardDescription>
              Zarządzaj hasłem, uwierzytelnianiem dwuskładnikowym i ustawieniami konta
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                <div className="flex-1">
                  <div className="font-medium text-blue-900">
                    Twoje konto jest zabezpieczone przez Clerk
                  </div>
                  <div className="text-sm text-blue-700 mt-1">
                    Zarządzanie hasłem, uwierzytelnianiem dwuskładnikowym i sesjami odbywa się przez
                    bezpieczny panel Clerk.
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium text-sm">Zmień hasło</div>
                    <div className="text-xs text-muted-foreground">Aktualizuj hasło konta</div>
                  </div>
                </div>
                <Button variant="outline" size="sm" onClick={openClerkUserProfile}>
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Otwórz
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Key className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="font-medium text-sm">2FA i sesje</div>
                    <div className="text-xs text-muted-foreground">Zarządzaj bezpieczeństwem</div>
                  </div>
                </div>
                <Button variant="outline" size="sm" onClick={openClerkUserProfile}>
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Otwórz
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
