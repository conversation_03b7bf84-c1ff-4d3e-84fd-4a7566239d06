'use client';

import { useAuth, useUserFamilies } from '@/hooks/useAuth';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

interface SettingsLayoutProps {
  children: React.ReactNode;
}

export default function SettingsLayout({ children }: SettingsLayoutProps) {
  const { user } = useAuth();
  const { families, loading } = useUserFamilies();

  if (loading) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <DashboardLayout
        user={user}
        families={families}
        currentFamilyId={undefined} // No family context for settings
        title="Ustawienia"
        showSidebar={true} // Keep sidebar for consistency
      >
        {children}
      </DashboardLayout>
    </AuthGuard>
  );
}
