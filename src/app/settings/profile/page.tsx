'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { User, Save, Mail, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface UserProfile {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function ProfileSettingsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchProfile();
    }
  }, [isLoaded, isSignedIn]);

  const fetchProfile = async () => {
    try {
      const response = await fetch('/api/user/profile');
      if (!response.ok) {
        throw new Error('Nie udało się pobrać profilu');
      }
      const data = await response.json();
      setProfile(data);
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast.error('Nie udało się załadować profilu');
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = (field: keyof UserProfile, value: string) => {
    if (!profile) return;
    setProfile({ ...profile, [field]: value });
  };

  const saveProfile = async () => {
    if (!profile) return;

    setSaving(true);
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: profile.firstName?.trim() || null,
          lastName: profile.lastName?.trim() || null,
        }),
      });

      if (!response.ok) {
        throw new Error('Nie udało się zapisać profilu');
      }

      const updatedProfile = await response.json();
      setProfile(updatedProfile);
      toast.success('Profil został zaktualizowany');
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error('Nie udało się zapisać profilu');
    } finally {
      setSaving(false);
    }
  };

  if (!isLoaded || loading) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Musisz być zalogowany, aby zarządzać swoim profilem.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">Nie udało się załadować profilu.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-2xl">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/settings" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Powrót do ustawień
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Ustawienia profilu
          </CardTitle>
          <CardDescription>Zarządzaj podstawowymi informacjami o swoim koncie</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Email - read only */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2 text-sm font-medium">
              <Mail className="h-4 w-4" />
              Adres e-mail
            </Label>
            <Input type="email" value={profile.email} disabled className="bg-gray-50" />
            <p className="text-xs text-muted-foreground">
              Adres e-mail nie może być zmieniony. Aby zmienić e-mail, skontaktuj się z obsługą.
            </p>
          </div>

          {/* First Name */}
          <div className="space-y-2">
            <Label htmlFor="firstName" className="text-sm font-medium">
              Imię
            </Label>
            <Input
              id="firstName"
              type="text"
              placeholder="Wprowadź swoje imię"
              value={profile.firstName || ''}
              onChange={e => updateProfile('firstName', e.target.value)}
            />
          </div>

          {/* Last Name */}
          <div className="space-y-2">
            <Label htmlFor="lastName" className="text-sm font-medium">
              Nazwisko
            </Label>
            <Input
              id="lastName"
              type="text"
              placeholder="Wprowadź swoje nazwisko"
              value={profile.lastName || ''}
              onChange={e => updateProfile('lastName', e.target.value)}
            />
          </div>

          {/* Account info */}
          <div className="border-t pt-6">
            <h3 className="text-sm font-medium mb-4 text-muted-foreground">INFORMACJE O KONCIE</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="text-xs text-muted-foreground">Data utworzenia</Label>
                <p className="font-medium">
                  {new Date(profile.createdAt).toLocaleDateString('pl-PL', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </p>
              </div>
              <div>
                <Label className="text-xs text-muted-foreground">Ostatnia aktualizacja</Label>
                <p className="font-medium">
                  {new Date(profile.updatedAt).toLocaleDateString('pl-PL', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </p>
              </div>
            </div>
          </div>

          <div className="border-t pt-6 flex justify-end">
            <Button onClick={saveProfile} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Zapisywanie...' : 'Zapisz zmiany'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
