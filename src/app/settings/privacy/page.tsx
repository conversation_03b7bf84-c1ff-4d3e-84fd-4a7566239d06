'use client';

import { useState } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Shield, Eye, Users, Mail, Bell, Download, Trash2, Save, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import {
  usePrivacyPreferences,
  useUpdatePrivacyPreferences,
} from '@/hooks/use-privacy-preferences';
import { toast } from 'sonner';

export default function PrivacySettingsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const [localPreferences, setLocalPreferences] = useState<any>(null);

  const { data: preferences, isLoading, error } = usePrivacyPreferences();
  const updatePreferencesMutation = useUpdatePrivacyPreferences();

  // Initialize local state when data loads
  if (preferences && !localPreferences) {
    setLocalPreferences(preferences);
  }

  const updatePreference = (key: string, value: any) => {
    if (!localPreferences) return;
    setLocalPreferences({ ...localPreferences, [key]: value });
  };

  const savePreferences = async () => {
    if (!localPreferences) return;
    updatePreferencesMutation.mutate(localPreferences);
  };

  const exportUserData = async () => {
    try {
      toast.info('Generowanie eksportu danych...');
      // Symulacja - będzie zaimplementowane w przyszłości
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Eksport danych zostanie wysłany email');
    } catch (error) {
      toast.error('Nie udało się rozpocząć eksportu danych');
    }
  };

  const deleteAccount = async () => {
    const confirmed = window.confirm(
      'Czy na pewno chcesz usunąć swoje konto? Ta operacja jest nieodwracalna.'
    );

    if (confirmed) {
      toast.error('Funkcja usuwania konta będzie dostępna wkrótce');
    }
  };

  if (!isLoaded || isLoading || !localPreferences) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-6 w-12" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Musisz być zalogowany, aby zarządzać ustawieniami prywatności.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Nie udało się załadować ustawień prywatności.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-2xl">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/settings" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Powrót do ustawień
          </Link>
        </Button>
      </div>

      <div className="space-y-6">
        {/* Profile Visibility */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Widoczność profilu
            </CardTitle>
            <CardDescription>
              Kontroluj, kto może przeglądać informacje o Twoim profilu
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="visibility-public"
                  name="visibility"
                  value="public"
                  checked={localPreferences.profileVisibility === 'public'}
                  onChange={e => updatePreference('profileVisibility', e.target.value as 'public')}
                  className="w-4 h-4 text-purple-600"
                />
                <Label htmlFor="visibility-public" className="flex-1 cursor-pointer">
                  <div>
                    <div className="font-medium">Publiczny</div>
                    <div className="text-sm text-muted-foreground">
                      Twój profil jest widoczny dla wszystkich użytkowników
                    </div>
                  </div>
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="visibility-family"
                  name="visibility"
                  value="family"
                  checked={localPreferences.profileVisibility === 'family'}
                  onChange={e => updatePreference('profileVisibility', e.target.value as 'family')}
                  className="w-4 h-4 text-purple-600"
                />
                <Label htmlFor="visibility-family" className="flex-1 cursor-pointer">
                  <div>
                    <div className="font-medium">Tylko rodzina</div>
                    <div className="text-sm text-muted-foreground">
                      Profil widoczny tylko dla członków Twoich rodzin
                    </div>
                  </div>
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="visibility-private"
                  name="visibility"
                  value="private"
                  checked={localPreferences.profileVisibility === 'private'}
                  onChange={e => updatePreference('profileVisibility', e.target.value as 'private')}
                  className="w-4 h-4 text-purple-600"
                />
                <Label htmlFor="visibility-private" className="flex-1 cursor-pointer">
                  <div>
                    <div className="font-medium">Prywatny</div>
                    <div className="text-sm text-muted-foreground">
                      Profil widoczny tylko dla Ciebie
                    </div>
                  </div>
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Informacje w profilu
            </CardTitle>
            <CardDescription>
              Wybierz, które informacje mają być widoczne w Twoim profilu
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="show-email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Pokaż adres email
              </Label>
              <Switch
                id="show-email"
                checked={localPreferences.showEmail}
                onCheckedChange={checked => updatePreference('showEmail', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="show-activity" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Pokaż ostatnią aktywność
              </Label>
              <Switch
                id="show-activity"
                checked={localPreferences.showActivity}
                onCheckedChange={checked => updatePreference('showActivity', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="show-stats" className="flex items-center gap-2">
                Pokaż statystyki zadań
              </Label>
              <Switch
                id="show-stats"
                checked={localPreferences.showStats}
                onCheckedChange={checked => updatePreference('showStats', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Communication Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Komunikacja i zaproszenia
            </CardTitle>
            <CardDescription>Zarządzaj sposobem komunikacji i zaproszeniami</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="allow-invitations">
                <div>
                  <div className="font-medium">Zezwalaj na zaproszenia</div>
                  <div className="text-sm text-muted-foreground">
                    Pozwól innym użytkownikom zapraszać Cię do rodzin
                  </div>
                </div>
              </Label>
              <Switch
                id="allow-invitations"
                checked={localPreferences.allowInvitations}
                onCheckedChange={checked => updatePreference('allowInvitations', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="marketing-emails">
                <div>
                  <div className="font-medium">Wiadomości marketingowe</div>
                  <div className="text-sm text-muted-foreground">
                    Otrzymuj informacje o nowych funkcjach i aktualizacjach
                  </div>
                </div>
              </Label>
              <Switch
                id="marketing-emails"
                checked={localPreferences.marketingEmails}
                onCheckedChange={checked => updatePreference('marketingEmails', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Data and Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Dane i analityka
            </CardTitle>
            <CardDescription>Kontroluj sposób wykorzystania Twoich danych</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="data-sharing">
                <div>
                  <div className="font-medium">Udostępnianie danych</div>
                  <div className="text-sm text-muted-foreground">
                    Pozwól na udostępnianie anonimowych danych w celach ulepszenia aplikacji
                  </div>
                </div>
              </Label>
              <Switch
                id="data-sharing"
                checked={localPreferences.dataSharing}
                onCheckedChange={checked => updatePreference('dataSharing', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="analytics-optout">
                <div>
                  <div className="font-medium">Rezygnacja z analityki</div>
                  <div className="text-sm text-muted-foreground">
                    Wyłącz śledzenie zachowań w aplikacji
                  </div>
                </div>
              </Label>
              <Switch
                id="analytics-optout"
                checked={localPreferences.analyticsOptOut}
                onCheckedChange={checked => updatePreference('analyticsOptOut', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Data Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Zarządzanie danymi
            </CardTitle>
            <CardDescription>Eksportuj lub usuń swoje dane</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium">Eksport danych</div>
                <div className="text-sm text-muted-foreground">
                  Pobierz kopię wszystkich swoich danych
                </div>
              </div>
              <Button variant="outline" onClick={exportUserData}>
                <Download className="h-4 w-4 mr-2" />
                Eksportuj
              </Button>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-red-600">Usuń konto</div>
                <div className="text-sm text-muted-foreground">
                  Trwale usuń swoje konto i wszystkie dane
                </div>
              </div>
              <Button variant="destructive" onClick={deleteAccount}>
                <Trash2 className="h-4 w-4 mr-2" />
                Usuń konto
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button onClick={savePreferences} disabled={updatePreferencesMutation.isPending}>
            <Save className="h-4 w-4 mr-2" />
            {updatePreferencesMutation.isPending ? 'Zapisywanie...' : 'Zapisz ustawienia'}
          </Button>
        </div>
      </div>
    </div>
  );
}
