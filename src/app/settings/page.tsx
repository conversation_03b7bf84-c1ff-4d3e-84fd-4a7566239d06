import { Metadata } from 'next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { User, Bell, Palette, Shield, Lock, FileText, Settings, ChevronRight } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Ustawienia',
  description: 'Zarządzaj swoimi preferencjami i ustawieniami konta',
};

export default function SettingsPage() {
  const settingsCategories = [
    {
      title: 'Profil użytkownika',
      description: 'Zarządzaj podstawowymi informacjami o sobie',
      icon: User,
      href: '/settings/profile',
      available: true,
    },
    {
      title: 'Powiadomienia',
      description: 'Wszystkie powiadomienia: zadania, rodzina, bezpieczeństwo',
      icon: Bell,
      href: '/settings/notifications',
      available: true,
    },
    {
      title: 'Wygląd i język',
      description: 'Personalizuj interfejs, motyw i ustawienia regionalne',
      icon: Palette,
      href: '/settings/appearance',
      available: true,
    },
    {
      title: '<PERSON><PERSON>wat<PERSON><PERSON>',
      description: 'Kontroluj widoczność profilu i udostępnianie danych',
      icon: Shield,
      href: '/settings/privacy',
      available: true,
    },
    {
      title: 'Bezpieczeństwo konta',
      description: 'Zarządzanie kontem i bezpieczeństwem (Clerk)',
      icon: Lock,
      href: '/settings/security',
      available: true,
    },
    {
      title: 'Eksport danych',
      description: 'Pobierz kopię swoich danych i statystyk',
      icon: FileText,
      href: '/settings/export',
      available: false, // TODO: implement in next phase
    },
    {
      title: 'Zaawansowane',
      description: 'Opcje dla zaawansowanych użytkowników',
      icon: Settings,
      href: '/settings/advanced',
      available: false, // TODO: implement in next phase
    },
  ];

  return (
    <div className="container mx-auto py-6 px-4 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Ustawienia</h1>
        <p className="text-gray-600">
          Personalizuj swoje doświadczenie i zarządzaj preferencjami konta
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {settingsCategories.map(category => {
          const Icon = category.icon;

          return (
            <Card
              key={category.href}
              className={`
                transition-all duration-200 hover:shadow-md 
                ${
                  category.available
                    ? 'hover:border-purple-200 cursor-pointer'
                    : 'opacity-60 cursor-not-allowed'
                }
              `}
            >
              {category.available ? (
                <Link href={category.href} className="block h-full">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Icon className="h-5 w-5 text-purple-600" />
                        </div>
                        <span className="text-lg">{category.title}</span>
                      </div>
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm">{category.description}</CardDescription>
                  </CardContent>
                </Link>
              ) : (
                <div className="block h-full">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          <Icon className="h-5 w-5 text-gray-400" />
                        </div>
                        <span className="text-lg text-gray-500">{category.title}</span>
                      </div>
                      <div className="text-xs bg-gray-100 text-gray-500 px-2 py-1 rounded">
                        Wkrótce
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-sm text-gray-400">
                      {category.description}
                    </CardDescription>
                  </CardContent>
                </div>
              )}
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="text-lg">Szybkie akcje</CardTitle>
          <CardDescription>Najczęściej używane ustawienia i akcje</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            <Button asChild variant="outline" size="sm">
              <Link href="/settings/notifications">
                <Bell className="h-4 w-4 mr-2" />
                Powiadomienia
              </Link>
            </Button>
            <Button asChild variant="outline" size="sm">
              <Link href="/settings/appearance">
                <Palette className="h-4 w-4 mr-2" />
                Zmień motyw
              </Link>
            </Button>
            <Button asChild variant="outline" size="sm">
              <Link href="/settings/profile">
                <User className="h-4 w-4 mr-2" />
                Edytuj profil
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
