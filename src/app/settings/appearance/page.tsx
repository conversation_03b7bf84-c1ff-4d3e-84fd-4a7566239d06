'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { Palette, Save, Globe, Clock, Calendar, ArrowLeft, Monitor, Sun, Moon } from 'lucide-react';
import Link from 'next/link';
import { locales, localeConfig } from '@/lib/i18n/config';
import { useTheme } from 'next-themes';

interface LanguagePreferences {
  preferredLanguage: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
}

const timezones = [
  'Europe/Warsaw',
  'Europe/London',
  'Europe/Berlin',
  'Europe/Madrid',
  'America/New_York',
  'America/Los_Angeles',
  'Asia/Tokyo',
  'Australia/Sydney',
];

const timezoneLabels: Record<string, string> = {
  'Europe/Warsaw': 'Warszawa (CET/CEST)',
  'Europe/London': 'Londyn (GMT/BST)',
  'Europe/Berlin': 'Berlin (CET/CEST)',
  'Europe/Madrid': 'Madryt (CET/CEST)',
  'America/New_York': 'Nowy Jork (EST/EDT)',
  'America/Los_Angeles': 'Los Angeles (PST/PDT)',
  'Asia/Tokyo': 'Tokio (JST)',
  'Australia/Sydney': 'Sydney (AEST/AEDT)',
};

const dateFormats = ['dd.MM.yyyy', 'MM/dd/yyyy', 'yyyy-MM-dd', 'dd/MM/yyyy'];

export default function AppearanceSettingsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { theme, setTheme } = useTheme();
  const [preferences, setPreferences] = useState<LanguagePreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (isLoaded && isSignedIn) {
      fetchPreferences();
    }
  }, [isLoaded, isSignedIn]);

  const fetchPreferences = async () => {
    try {
      const response = await fetch('/api/user/preferences/language');
      if (!response.ok) {
        throw new Error('Nie udało się pobrać preferencji');
      }
      const data = await response.json();
      setPreferences(data);
    } catch (error) {
      console.error('Error fetching preferences:', error);
      toast.error('Nie udało się pobrać ustawień wyglądu');
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = (key: keyof LanguagePreferences, value: string) => {
    if (!preferences) return;
    setPreferences({ ...preferences, [key]: value });
  };

  const savePreferences = async () => {
    if (!preferences) return;

    setSaving(true);
    try {
      const response = await fetch('/api/user/preferences/language', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      if (!response.ok) {
        throw new Error('Nie udało się zapisać preferencji');
      }

      toast.success('Ustawienia wyglądu zostały zapisane');
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast.error('Nie udało się zapisać ustawień');
    } finally {
      setSaving(false);
    }
  };

  if (!isLoaded || loading) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
          <CardContent className="space-y-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Musisz być zalogowany, aby zarządzać ustawieniami wyglądu.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className="container mx-auto py-6 px-4 max-w-2xl">
        <div className="mb-6">
          <Button asChild variant="ghost" size="sm">
            <Link href="/settings" className="flex items-center">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Powrót do ustawień
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              Nie udało się załadować ustawień wyglądu.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4 max-w-2xl">
      <div className="mb-6">
        <Button asChild variant="ghost" size="sm">
          <Link href="/settings" className="flex items-center">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Powrót do ustawień
          </Link>
        </Button>
      </div>

      <div className="space-y-6">
        {/* Theme */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Motyw
            </CardTitle>
            <CardDescription>Wybierz motyw kolorystyczny dla interfejsu</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup value={theme} onValueChange={setTheme}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="light" id="light" />
                <Label htmlFor="light" className="flex items-center gap-2">
                  <Sun className="h-4 w-4" />
                  Jasny
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="dark" id="dark" />
                <Label htmlFor="dark" className="flex items-center gap-2">
                  <Moon className="h-4 w-4" />
                  Ciemny
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="system" id="system" />
                <Label htmlFor="system" className="flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  Automatyczny (zgodnie z systemem)
                </Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Language and Regional */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Język i region
            </CardTitle>
            <CardDescription>Skonfiguruj język interfejsu i ustawienia regionalne</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Language */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Język interfejsu</Label>
              <Select
                value={preferences.preferredLanguage}
                onValueChange={value => updatePreference('preferredLanguage', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {locales.map(locale => {
                    const config = localeConfig[locale];
                    return (
                      <SelectItem key={locale} value={locale}>
                        <div className="flex items-center gap-2">
                          <span>{config.flag}</span>
                          <span>{config.name}</span>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            {/* Timezone */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Strefa czasowa</Label>
              <Select
                value={preferences.timezone}
                onValueChange={value => updatePreference('timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map(timezone => (
                    <SelectItem key={timezone} value={timezone}>
                      {timezoneLabels[timezone]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date Format */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <Calendar className="h-4 w-4" />
                Format daty
              </Label>
              <Select
                value={preferences.dateFormat}
                onValueChange={value => updatePreference('dateFormat', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dateFormats.map(format => (
                    <SelectItem key={format} value={format}>
                      <div className="flex items-center justify-between w-full">
                        <span>{format}</span>
                        <span className="text-muted-foreground text-xs ml-4">
                          {new Date()
                            .toLocaleDateString('pl-PL', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit',
                            })
                            .replace(/\//g, format.includes('/') ? '/' : '.')}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Time Format */}
            <div className="space-y-2">
              <Label className="flex items-center gap-2 text-sm font-medium">
                <Clock className="h-4 w-4" />
                Format czasu
              </Label>
              <Select
                value={preferences.timeFormat}
                onValueChange={value => updatePreference('timeFormat', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">
                    <div className="flex items-center justify-between w-full">
                      <span>24-godzinny</span>
                      <span className="text-muted-foreground text-xs ml-4">14:30</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="12h">
                    <div className="flex items-center justify-between w-full">
                      <span>12-godzinny</span>
                      <span className="text-muted-foreground text-xs ml-4">2:30 PM</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="border-t pt-6 flex justify-end">
              <Button onClick={savePreferences} disabled={saving}>
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Zapisywanie...' : 'Zapisz ustawienia'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
