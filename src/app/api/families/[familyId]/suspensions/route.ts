import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { FamilyService } from '@/lib/services/family.service';

const createSuspensionSchema = z
  .object({
    title: z.string().min(1).max(200),
    description: z.string().max(1000).optional(),
    reason: z.enum([
      'HOLIDAY_VACATION',
      'TRAVEL',
      'ILLNESS',
      'SCHOOL_BREAK',
      'MAINTENANCE',
      'OTHER',
    ]),
    startDate: z.string().transform(str => new Date(str)),
    endDate: z.string().transform(str => new Date(str)),
    taskIds: z.array(z.string()).optional().default([]),
    memberIds: z.array(z.string()).optional().default([]),
  })
  .refine(data => data.endDate > data.startDate, {
    message: 'Data końca musi być późniejsza niż data rozpocz<PERSON>cia',
    path: ['endDate'],
  });

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { familyId } = await params;

    // Sprawdź czy użytkownik należy do rodziny
    const member = await FamilyService.getFamilyMember(familyId, localUser.id);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Pobierz zawieszenia
    const suspensions = await prisma.taskSuspension.findMany({
      where: { familyId },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(suspensions);
  } catch (error) {
    console.error('Error fetching suspensions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { familyId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, localUser.id);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą tworzyć zawieszenia
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can create suspensions',
        },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = createSuspensionSchema.parse(body);

    // Sprawdź czy wszystkie zadania należą do tej rodziny
    if (validatedData.taskIds.length > 0) {
      const tasks = await prisma.task.findMany({
        where: {
          id: { in: validatedData.taskIds },
          familyId,
        },
      });

      if (tasks.length !== validatedData.taskIds.length) {
        return NextResponse.json(
          {
            error: 'Some tasks do not belong to this family',
          },
          { status: 400 }
        );
      }
    }

    // Sprawdź czy wszyscy członkowie należą do tej rodziny
    if (validatedData.memberIds.length > 0) {
      const members = await prisma.familyMember.findMany({
        where: {
          id: { in: validatedData.memberIds },
          familyId,
        },
      });

      if (members.length !== validatedData.memberIds.length) {
        return NextResponse.json(
          {
            error: 'Some members do not belong to this family',
          },
          { status: 400 }
        );
      }
    }

    // Utwórz zawieszenie w transakcji
    const suspension = await prisma.$transaction(async tx => {
      // Utwórz zawieszenie
      const newSuspension = await tx.taskSuspension.create({
        data: {
          familyId,
          title: validatedData.title,
          description: validatedData.description,
          reason: validatedData.reason,
          startDate: validatedData.startDate,
          endDate: validatedData.endDate,
          createdById: member.id,
        },
      });

      // Dodaj zawieszone zadania
      if (validatedData.taskIds.length > 0) {
        await tx.suspendedTask.createMany({
          data: validatedData.taskIds.map(taskId => ({
            suspensionId: newSuspension.id,
            taskId,
          })),
        });
      }

      // Dodaj zawieszonych członków
      if (validatedData.memberIds.length > 0) {
        await tx.suspendedMember.createMany({
          data: validatedData.memberIds.map(memberId => ({
            suspensionId: newSuspension.id,
            memberId,
          })),
        });
      }

      return newSuspension;
    });

    // Pobierz pełne dane zawieszenia
    const fullSuspension = await prisma.taskSuspension.findUnique({
      where: { id: suspension.id },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json(fullSuspension, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error creating suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
