import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { FamilyService } from '@/lib/services/family.service';

const updateSuspensionSchema = z
  .object({
    title: z.string().min(1).max(200).optional(),
    description: z.string().max(1000).optional(),
    reason: z
      .enum(['HOLIDAY_VACATION', 'TRAVEL', 'ILLNESS', 'SCHOOL_BREAK', 'MAINTENANCE', 'OTHER'])
      .optional(),
    startDate: z
      .string()
      .transform(str => new Date(str))
      .optional(),
    endDate: z
      .string()
      .transform(str => new Date(str))
      .optional(),
    status: z.enum(['ACTIVE', 'ENDED', 'CANCELLED']).optional(),
  })
  .refine(
    data => {
      if (data.startDate && data.endDate) {
        return data.endDate > data.startDate;
      }
      return true;
    },
    {
      message: 'Data końca musi być późniejsza niż data rozpoczęcia',
      path: ['endDate'],
    }
  );

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Pobierz zawieszenie
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    return NextResponse.json(suspension);
  } catch (error) {
    console.error('Error fetching suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą edytować zawieszenia
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can edit suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const existingSuspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!existingSuspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    const body = await request.json();
    const validatedData = updateSuspensionSchema.parse(body);

    // Zaktualizuj zawieszenie
    const updatedSuspension = await prisma.taskSuspension.update({
      where: { id: suspensionId },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json(updatedSuspension);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error updating suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą usuwać zawieszenia
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can delete suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const existingSuspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!existingSuspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    // Usuń zawieszenie (cascade delete zajmie się powiązanymi rekordami)
    await prisma.taskSuspension.delete({
      where: { id: suspensionId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
