import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { FamilyService } from '@/lib/services/family.service';

const manageTasksSchema = z.object({
  taskIds: z.array(z.string()).min(1),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą zarządzać zawieszeniami
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can manage suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    const body = await request.json();
    const { taskIds } = manageTasksSchema.parse(body);

    // Sprawdź czy wszystkie zadania należą do tej rodziny
    const tasks = await prisma.task.findMany({
      where: {
        id: { in: taskIds },
        familyId,
      },
    });

    if (tasks.length !== taskIds.length) {
      return NextResponse.json(
        {
          error: 'Some tasks do not belong to this family',
        },
        { status: 400 }
      );
    }

    // Sprawdź które zadania już są w zawieszeniu
    const existingSuspensions = await prisma.suspendedTask.findMany({
      where: {
        suspensionId,
        taskId: { in: taskIds },
      },
    });

    const existingTaskIds = existingSuspensions.map(s => s.taskId);
    const newTaskIds = taskIds.filter(id => !existingTaskIds.includes(id));

    // Dodaj nowe zadania do zawieszenia
    if (newTaskIds.length > 0) {
      await prisma.suspendedTask.createMany({
        data: newTaskIds.map(taskId => ({
          suspensionId,
          taskId,
        })),
      });
    }

    // Pobierz zaktualizowane zawieszenie
    const updatedSuspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
      include: {
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: `Added ${newTaskIds.length} new tasks to suspension`,
      addedTasks: newTaskIds.length,
      alreadySuspended: existingTaskIds.length,
      suspension: updatedSuspension,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error adding tasks to suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą zarządzać zawieszeniami
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can manage suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    const body = await request.json();
    const { taskIds } = manageTasksSchema.parse(body);

    // Usuń zadania z zawieszenia
    const deletedCount = await prisma.suspendedTask.deleteMany({
      where: {
        suspensionId,
        taskId: { in: taskIds },
      },
    });

    // Pobierz zaktualizowane zawieszenie
    const updatedSuspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
      include: {
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: `Removed ${deletedCount.count} tasks from suspension`,
      removedTasks: deletedCount.count,
      suspension: updatedSuspension,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error removing tasks from suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
