import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { FamilyService } from '@/lib/services/family.service';

const manageMembersSchema = z.object({
  memberIds: z.array(z.string()).min(1),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą zarządzać zawieszeniami
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can manage suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    const body = await request.json();
    const { memberIds } = manageMembersSchema.parse(body);

    // Sprawdź czy wszyscy członkowie należą do tej rodziny
    const members = await prisma.familyMember.findMany({
      where: {
        id: { in: memberIds },
        familyId,
      },
    });

    if (members.length !== memberIds.length) {
      return NextResponse.json(
        {
          error: 'Some members do not belong to this family',
        },
        { status: 400 }
      );
    }

    // Sprawdź którzy członkowie już są w zawieszeniu
    const existingSuspensions = await prisma.suspendedMember.findMany({
      where: {
        suspensionId,
        memberId: { in: memberIds },
      },
    });

    const existingMemberIds = existingSuspensions.map(s => s.memberId);
    const newMemberIds = memberIds.filter(id => !existingMemberIds.includes(id));

    // Dodaj nowych członków do zawieszenia
    if (newMemberIds.length > 0) {
      await prisma.suspendedMember.createMany({
        data: newMemberIds.map(memberId => ({
          suspensionId,
          memberId,
        })),
      });
    }

    // Pobierz zaktualizowane zawieszenie
    const updatedSuspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
      include: {
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: `Added ${newMemberIds.length} new members to suspension`,
      addedMembers: newMemberIds.length,
      alreadySuspended: existingMemberIds.length,
      suspension: updatedSuspension,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error adding members to suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą zarządzać zawieszeniami
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can manage suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    const body = await request.json();
    const { memberIds } = manageMembersSchema.parse(body);

    // Usuń członków z zawieszenia
    const deletedCount = await prisma.suspendedMember.deleteMany({
      where: {
        suspensionId,
        memberId: { in: memberIds },
      },
    });

    // Pobierz zaktualizowane zawieszenie
    const updatedSuspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
      include: {
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: `Removed ${deletedCount.count} members from suspension`,
      removedMembers: deletedCount.count,
      suspension: updatedSuspension,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    console.error('Error removing members from suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
