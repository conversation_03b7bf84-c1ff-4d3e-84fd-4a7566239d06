import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { FamilyService } from '@/lib/services/family.service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą aktivować zawieszenia
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can activate suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    // Sprawdź czy zawieszenie może być aktywowane
    if (suspension.status === 'ACTIVE') {
      return NextResponse.json(
        {
          error: 'Suspension is already active',
        },
        { status: 400 }
      );
    }

    if (suspension.status === 'ENDED') {
      return NextResponse.json(
        {
          error: 'Cannot activate ended suspension',
        },
        { status: 400 }
      );
    }

    // Sprawdź czy data rozpoczęcia i zakończenia są poprawne
    const now = new Date();
    if (suspension.endDate < now) {
      return NextResponse.json(
        {
          error: 'Cannot activate suspension that has already ended',
        },
        { status: 400 }
      );
    }

    // Aktywuj zawieszenie
    const updatedSuspension = await prisma.taskSuspension.update({
      where: { id: suspensionId },
      data: {
        status: 'ACTIVE',
        updatedAt: new Date(),
      },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Suspension activated successfully',
      suspension: updatedSuspension,
    });
  } catch (error) {
    console.error('Error activating suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; suspensionId: string }> }
) {
  try {
    const { userId } = getAuth(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, suspensionId } = await params;

    // Sprawdź czy użytkownik należy do rodziny i ma uprawnienia
    const member = await FamilyService.getFamilyMember(familyId, userId);
    if (!member) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Tylko rodzice/opiekunowie mogą dezaktywować zawieszenia
    if (!['OWNER', 'PARENT', 'GUARDIAN'].includes(member.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can deactivate suspensions',
        },
        { status: 403 }
      );
    }

    // Sprawdź czy zawieszenie istnieje i należy do rodziny
    const suspension = await prisma.taskSuspension.findFirst({
      where: {
        id: suspensionId,
        familyId,
      },
    });

    if (!suspension) {
      return NextResponse.json({ error: 'Suspension not found' }, { status: 404 });
    }

    // Sprawdź czy zawieszenie może być dezaktywowane
    if (suspension.status !== 'ACTIVE') {
      return NextResponse.json(
        {
          error: 'Only active suspensions can be deactivated',
        },
        { status: 400 }
      );
    }

    // Dezaktywuj zawieszenie (ustawiaj status na CANCELLED)
    const updatedSuspension = await prisma.taskSuspension.update({
      where: { id: suspensionId },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date(),
      },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Suspension deactivated successfully',
      suspension: updatedSuspension,
    });
  } catch (error) {
    console.error('Error deactivating suspension:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
