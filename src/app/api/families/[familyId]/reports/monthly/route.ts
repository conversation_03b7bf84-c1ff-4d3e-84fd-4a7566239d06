import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { ReportsService } from '@/lib/services/reports.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const monthOffset = parseInt(searchParams.get('monthOffset') || '0'); // 0 = current month, -1 = last month, etc.

    // Parse year and month if provided
    const year = searchParams.get('year');
    const month = searchParams.get('month');

    let reportMonth: Date;

    if (year && month) {
      // Use specific year and month
      reportMonth = new Date(parseInt(year), parseInt(month) - 1, 1);
    } else {
      // Use month offset from current month
      const today = new Date();
      reportMonth = new Date(today.getFullYear(), today.getMonth() + monthOffset, 1);
    }

    // Generate monthly report
    const report = await ReportsService.generateMonthlyReport(familyId, reportMonth);

    // Add navigation context
    const previousMonth = new Date(reportMonth);
    previousMonth.setMonth(previousMonth.getMonth() - 1);

    const nextMonth = new Date(reportMonth);
    nextMonth.setMonth(nextMonth.getMonth() + 1);

    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    const reportWithContext = {
      ...report,
      meta: {
        generatedAt: new Date(),
        generatedBy: userId,
        monthOffset,
        isCurrentMonth: reportMonth.getTime() === currentMonth.getTime(),
        isPastMonth: reportMonth < currentMonth,
        isFutureMonth: reportMonth > currentMonth,
      },
      navigation: {
        previousMonth: {
          year: previousMonth.getFullYear(),
          month: previousMonth.getMonth() + 1,
          offset: monthOffset - 1,
        },
        nextMonth: {
          year: nextMonth.getFullYear(),
          month: nextMonth.getMonth() + 1,
          offset: monthOffset + 1,
        },
        currentMonth: {
          year: currentMonth.getFullYear(),
          month: currentMonth.getMonth() + 1,
          offset: 0,
        },
      },
      monthInfo: {
        name: reportMonth.toLocaleDateString('pl-PL', { month: 'long', year: 'numeric' }),
        year: reportMonth.getFullYear(),
        monthNumber: reportMonth.getMonth() + 1,
        daysInMonth: new Date(reportMonth.getFullYear(), reportMonth.getMonth() + 1, 0).getDate(),
      },
    };

    return NextResponse.json(reportWithContext);
  } catch (error) {
    console.error('Error generating monthly report:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
