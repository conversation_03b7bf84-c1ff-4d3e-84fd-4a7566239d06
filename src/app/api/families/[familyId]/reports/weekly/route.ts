import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { ReportsService } from '@/lib/services/reports.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const weekOffset = parseInt(searchParams.get('weekOffset') || '0'); // 0 = current week, -1 = last week, etc.

    // Calculate week start date
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)
    currentWeekStart.setHours(0, 0, 0, 0);

    const weekStart = new Date(currentWeekStart);
    weekStart.setDate(weekStart.getDate() + weekOffset * 7);

    // Generate weekly report
    const report = await ReportsService.generateWeeklyReport(familyId, weekStart);

    // Add some additional context
    const reportWithContext = {
      ...report,
      meta: {
        generatedAt: new Date(),
        generatedBy: userId,
        weekOffset,
        isCurrentWeek: weekOffset === 0,
        isPastWeek: weekOffset < 0,
        isFutureWeek: weekOffset > 0,
      },
      navigation: {
        previousWeek: weekOffset - 1,
        nextWeek: weekOffset + 1,
        currentWeek: 0,
      },
    };

    return NextResponse.json(reportWithContext);
  } catch (error) {
    console.error('Error generating weekly report:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
