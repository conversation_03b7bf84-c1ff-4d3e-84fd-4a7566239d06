import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import {
  getFamilyMemberWithRole,
  hasPermission,
  canManageRole,
  getOrCreateLocalUser,
} from '@/lib/auth';
import { changeMemberRoleSchema } from '@/lib/validations/family';
import { FamilyRole } from '@prisma/client';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; memberId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, memberId } = await params;

    // Convert Clerk ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found in database' }, { status: 404 });
    }

    const userId = localUser.id;

    // Check if user has permission to manage members
    const userMember = await getFamilyMemberWithRole(userId, familyId);
    if (!userMember || !hasPermission(userMember.role, 'MANAGE_MEMBERS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get the member to be updated
    const targetMember = await prisma.familyMember.findUnique({
      where: { id: memberId },
      include: { user: true },
    });

    if (!targetMember || targetMember.familyId !== familyId) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    const body = await request.json();
    const validation = changeMemberRoleSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { role, isAdult } = validation.data;

    // Check if user can manage the target role
    if (!canManageRole(userMember.role, role)) {
      return NextResponse.json({ error: 'You cannot assign this role' }, { status: 403 });
    }

    // Prevent changing own role (except for owners)
    if (targetMember.userId === userId && userMember.role !== 'OWNER') {
      return NextResponse.json({ error: 'You cannot change your own role' }, { status: 403 });
    }

    // Prevent removing the last owner
    if (targetMember.role === 'OWNER' && role !== 'OWNER') {
      const ownerCount = await prisma.familyMember.count({
        where: {
          familyId,
          role: 'OWNER',
        },
      });

      if (ownerCount <= 1) {
        return NextResponse.json({ error: 'Family must have at least one owner' }, { status: 400 });
      }
    }

    const updatedMember = await prisma.familyMember.update({
      where: { id: memberId },
      data: {
        role,
        isAdult: isAdult ?? targetMember.isAdult,
        lastActive: new Date(),
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json(updatedMember);
  } catch (error) {
    console.error('Error updating family member:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; memberId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, memberId } = await params;

    // Convert Clerk ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found in database' }, { status: 404 });
    }

    const userId = localUser.id;

    // Get the member to be removed
    const targetMember = await prisma.familyMember.findUnique({
      where: { id: memberId },
      include: { user: true },
    });

    if (!targetMember || targetMember.familyId !== familyId) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Check if user is removing themselves (leaving family)
    if (targetMember.userId === userId) {
      // Check if user is the last owner
      if (targetMember.role === 'OWNER') {
        const ownerCount = await prisma.familyMember.count({
          where: {
            familyId,
            role: 'OWNER',
          },
        });

        if (ownerCount <= 1) {
          const memberCount = await prisma.familyMember.count({
            where: { familyId },
          });

          if (memberCount > 1) {
            return NextResponse.json(
              { error: 'Transfer ownership before leaving the family' },
              { status: 400 }
            );
          }
        }
      }
    } else {
      // Check if user has permission to remove other members
      const userMember = await getFamilyMemberWithRole(userId, familyId);
      if (!userMember || !hasPermission(userMember.role, 'MANAGE_MEMBERS')) {
        return NextResponse.json(
          {
            error: `Brak uprawnień do usuwania członków. Twoja rola: ${userMember?.role || 'brak'}, wymagane: OWNER lub PARENT`,
          },
          { status: 403 }
        );
      }

      // Check if user can manage the target member's role
      if (!canManageRole(userMember.role, targetMember.role)) {
        return NextResponse.json(
          {
            error: `Nie możesz usunąć członka o roli ${targetMember.role}. Twoja rola: ${userMember.role}`,
          },
          { status: 403 }
        );
      }
    }

    // Remove member (cascade will handle related records)
    await prisma.familyMember.delete({
      where: { id: memberId },
    });

    return NextResponse.json({ message: 'Member removed successfully' });
  } catch (error) {
    console.error('Error removing family member:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
