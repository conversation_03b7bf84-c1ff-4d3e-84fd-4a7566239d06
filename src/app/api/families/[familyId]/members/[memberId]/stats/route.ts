import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { StatsService } from '@/lib/services/stats.service';
import { ActivityLogService } from '@/lib/services/activity.service';
import { StatsPeriod } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; memberId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, memberId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify the requested member exists and belongs to the same family
    const targetMember = await prisma.familyMember.findFirst({
      where: {
        id: memberId,
        familyId,
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!targetMember) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = (searchParams.get('period') as StatsPeriod) || 'DAILY';
    const days = parseInt(searchParams.get('days') || '30');

    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

    // Get member statistics
    const memberStats = await StatsService.getMemberStats(
      memberId,
      { start: startDate, end: endDate },
      period
    );

    // Get member activity
    const memberActivity = await ActivityLogService.getMemberActivity(memberId, {
      start: startDate,
      end: endDate,
    });

    // Calculate additional metrics
    const totalPointsEarned = memberStats.reduce((sum, stat) => sum + stat.pointsEarned, 0);
    const totalTasksCompleted = memberStats.reduce((sum, stat) => sum + stat.tasksCompleted, 0);
    const totalTasksAssigned = memberStats.reduce((sum, stat) => sum + stat.tasksAssigned, 0);
    const averageCompletionRate =
      memberStats.length > 0
        ? memberStats.reduce((sum, stat) => sum + stat.completionRate, 0) / memberStats.length
        : 0;

    // Get best streak
    const bestStreak = Math.max(...memberStats.map(stat => stat.streakDays), 0);
    const currentStreak = memberStats[memberStats.length - 1]?.streakDays || 0;

    // Calculate trend data for charts
    const completionRateTrend = memberStats.map(stat => ({
      date: stat.date,
      value: stat.completionRate,
      period: stat.period,
    }));

    const pointsTrend = memberStats.map(stat => ({
      date: stat.date,
      value: stat.pointsEarned,
      period: stat.period,
    }));

    const tasksTrend = memberStats.map(stat => ({
      date: stat.date,
      value: stat.tasksCompleted,
      period: stat.period,
    }));

    // Get rankings for context
    const pointsRanking = await StatsService.getMemberRanking(familyId, 'points', period);
    const completionRanking = await StatsService.getMemberRanking(
      familyId,
      'completion_rate',
      period
    );

    const memberPointsRank = pointsRanking.find(r => r.memberId === memberId)?.rank || 0;
    const memberCompletionRank = completionRanking.find(r => r.memberId === memberId)?.rank || 0;

    // Generate achievements and badges
    const achievements = generateMemberAchievements(memberStats, {
      bestStreak,
      totalPointsEarned,
      totalTasksCompleted,
      averageCompletionRate,
      memberPointsRank,
      memberCompletionRank,
    });

    // Recent performance (last 7 days)
    const recentStartDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    const recentStats = await StatsService.getMemberStats(
      memberId,
      { start: recentStartDate, end: endDate },
      'DAILY'
    );

    const recentPerformance = {
      tasksCompleted: recentStats.reduce((sum, stat) => sum + stat.tasksCompleted, 0),
      pointsEarned: recentStats.reduce((sum, stat) => sum + stat.pointsEarned, 0),
      activeDays: recentStats.reduce((sum, stat) => sum + (stat.activeDays > 0 ? 1 : 0), 0),
      averageCompletionRate:
        recentStats.length > 0
          ? recentStats.reduce((sum, stat) => sum + stat.completionRate, 0) / recentStats.length
          : 0,
    };

    return NextResponse.json({
      member: {
        id: targetMember.id,
        name: `${targetMember.user.firstName} ${targetMember.user.lastName}`,
        email: targetMember.user.email,
        role: targetMember.role,
        totalPoints: targetMember.points,
        joinedAt: targetMember.joinedAt,
        lastActive: targetMember.lastActive,
      },
      summary: {
        totalPointsEarned,
        totalTasksCompleted,
        totalTasksAssigned,
        averageCompletionRate: Math.round(averageCompletionRate),
        bestStreak,
        currentStreak,
        rankings: {
          points: memberPointsRank,
          completion: memberCompletionRank,
        },
      },
      trends: {
        completionRate: completionRateTrend,
        points: pointsTrend,
        tasks: tasksTrend,
      },
      recentPerformance,
      achievements,
      activity: memberActivity.slice(0, 20), // Latest 20 activities
      stats: memberStats,
      meta: {
        period,
        dateRange: {
          start: startDate,
          end: endDate,
        },
        generatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('Error fetching member stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to generate achievements
function generateMemberAchievements(
  stats: any[],
  metrics: {
    bestStreak: number;
    totalPointsEarned: number;
    totalTasksCompleted: number;
    averageCompletionRate: number;
    memberPointsRank: number;
    memberCompletionRank: number;
  }
): Array<{
  id: string;
  title: string;
  description: string;
  icon: string;
  earned: boolean;
  earnedAt?: Date;
}> {
  const achievements = [
    {
      id: 'first_task',
      title: 'Pierwsze zadanie',
      description: 'Ukończ swoje pierwsze zadanie',
      icon: '🎯',
      earned: metrics.totalTasksCompleted > 0,
    },
    {
      id: 'streak_7',
      title: 'Tygodniowa passa',
      description: 'Utrzymaj 7-dniową passę wykonywania zadań',
      icon: '🔥',
      earned: metrics.bestStreak >= 7,
    },
    {
      id: 'streak_30',
      title: 'Miesięczna passa',
      description: 'Utrzymaj 30-dniową passę wykonywania zadań',
      icon: '💫',
      earned: metrics.bestStreak >= 30,
    },
    {
      id: 'points_100',
      title: 'Pierwsza setka',
      description: 'Zdobądź 100 punktów',
      icon: '💯',
      earned: metrics.totalPointsEarned >= 100,
    },
    {
      id: 'points_500',
      title: 'Pół tysiąca',
      description: 'Zdobądź 500 punktów',
      icon: '🏆',
      earned: metrics.totalPointsEarned >= 500,
    },
    {
      id: 'perfectionist',
      title: 'Perfekcjonista',
      description: 'Osiągnij 95% wskaźnik ukończenia zadań',
      icon: '⭐',
      earned: metrics.averageCompletionRate >= 95,
    },
    {
      id: 'top_performer',
      title: 'Najlepszy wykonawca',
      description: 'Zajmij 1. miejsce w rankingu punktów',
      icon: '🥇',
      earned: metrics.memberPointsRank === 1,
    },
    {
      id: 'consistent',
      title: 'Konsekwentny',
      description: 'Zajmij 1. miejsce w rankingu ukończenia zadań',
      icon: '🎖️',
      earned: metrics.memberCompletionRank === 1,
    },
    {
      id: 'productive',
      title: 'Produktywny',
      description: 'Ukończ 50 zadań',
      icon: '⚡',
      earned: metrics.totalTasksCompleted >= 50,
    },
    {
      id: 'super_productive',
      title: 'Super produktywny',
      description: 'Ukończ 100 zadań',
      icon: '🚀',
      earned: metrics.totalTasksCompleted >= 100,
    },
  ];

  return achievements;
}
