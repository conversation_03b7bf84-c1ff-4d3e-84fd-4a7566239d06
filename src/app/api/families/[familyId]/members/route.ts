import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hasPermission } from '@/lib/auth';
import { inviteMemberSchema } from '@/lib/validations/family';
import { FamilyService } from '@/lib/services/family.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Check if user is a family member - using same method as stats endpoint
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const members = await prisma.familyMember.findMany({
      where: { familyId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        taskAssignments: {
          where: {
            task: {
              completions: {
                none: {},
              },
            },
          },
          include: {
            task: {
              select: {
                id: true,
                title: true,
                points: true,
              },
            },
          },
        },
        _count: {
          select: {
            completedTasks: true,
            taskAssignments: true,
          },
        },
      },
      orderBy: [{ role: 'asc' }, { joinedAt: 'asc' }],
    });

    return NextResponse.json(members);
  } catch (error) {
    console.error('Error fetching family members:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Check if user has permission to manage members
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_MEMBERS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const validation = inviteMemberSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { email, role, isAdult } = validation.data;

    // Check if user is already a member or has pending invitation
    const existingMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { email },
      },
    });

    if (existingMember) {
      return NextResponse.json({ error: 'User is already a family member' }, { status: 400 });
    }

    const existingInvitation = await prisma.familyInvitation.findFirst({
      where: {
        familyId,
        email,
        status: 'PENDING',
      },
    });

    if (existingInvitation) {
      return NextResponse.json({ error: 'User already has a pending invitation' }, { status: 400 });
    }

    // Create invitation
    const invitation = await FamilyService.inviteMember(familyId, {
      email,
      role,
      isAdult: isAdult ?? false,
    });

    return NextResponse.json(invitation, { status: 201 });
  } catch (error) {
    console.error('Error inviting family member:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
