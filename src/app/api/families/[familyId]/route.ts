import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFamilyMemberWithRole, hasPermission } from '@/lib/auth';
import { updateFamilySchema } from '@/lib/validations/family';
import { FamilyRole } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Check if user is a family member
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const family = await prisma.family.findUnique({
      where: { id: familyId },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
          orderBy: [{ role: 'asc' }, { joinedAt: 'asc' }],
        },
        tasks: {
          include: {
            assignments: {
              include: {
                member: {
                  include: {
                    user: {
                      select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                      },
                    },
                  },
                },
              },
            },
            completions: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 10, // Latest 10 tasks
        },
        invitations: {
          where: { status: 'PENDING' },
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            tasks: true,
            members: true,
          },
        },
      },
    });

    if (!family) {
      return NextResponse.json({ error: 'Family not found' }, { status: 404 });
    }

    return NextResponse.json(family);
  } catch (error) {
    console.error('Error fetching family:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Check if user has permission to manage family
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_FAMILY')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const validation = updateFamilySchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { name, description } = validation.data;

    const updatedFamily = await prisma.family.update({
      where: { id: familyId },
      data: {
        name,
        description,
      },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        _count: {
          select: {
            tasks: true,
            members: true,
          },
        },
      },
    });

    return NextResponse.json(updatedFamily);
  } catch (error) {
    console.error('Error updating family:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Check if user is the family owner
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember || familyMember.role !== FamilyRole.OWNER) {
      return NextResponse.json(
        { error: 'Only family owner can delete the family' },
        { status: 403 }
      );
    }

    // Delete family (cascade will handle related records)
    await prisma.family.delete({
      where: { id: familyId },
    });

    return NextResponse.json({ message: 'Family deleted successfully' });
  } catch (error) {
    console.error('Error deleting family:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
