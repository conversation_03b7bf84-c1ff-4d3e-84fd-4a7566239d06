import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { CalendarService } from '@/lib/services/calendar.service';
import { FamilyService } from '@/lib/services/family.service';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify family membership
    const isMember = await FamilyService.isFamilyMember(familyId, localUser.id);
    if (!isMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const weekStart = searchParams.get('weekStart');

    if (!weekStart) {
      return NextResponse.json(
        { error: 'weekStart is required (ISO date string)' },
        { status: 400 }
      );
    }

    const weekStartDate = new Date(weekStart);
    if (isNaN(weekStartDate.getTime())) {
      return NextResponse.json({ error: 'Invalid weekStart date' }, { status: 400 });
    }

    const tasks = await CalendarService.getWeekTasks(familyId, weekStartDate);

    return NextResponse.json({
      tasks,
      weekStart: weekStartDate.toISOString(),
      count: tasks.length,
    });
  } catch (error) {
    console.error('Calendar week API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
