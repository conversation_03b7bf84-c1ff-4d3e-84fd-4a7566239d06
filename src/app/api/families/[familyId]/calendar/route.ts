import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { CalendarService } from '@/lib/services/calendar.service';
import { calendarFiltersSchema } from '@/lib/validations/calendar';
import { FamilyService } from '@/lib/services/family.service';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify family membership
    const isMember = await FamilyService.isFamilyMember(familyId, localUser.id);
    if (!isMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const memberId = searchParams.get('memberId');
    const taskStatus = searchParams.getAll('taskStatus');
    const taskCategory = searchParams.getAll('taskCategory');
    const showCompleted = searchParams.get('showCompleted') === 'true';

    if (!startDate || !endDate) {
      return NextResponse.json({ error: 'startDate and endDate are required' }, { status: 400 });
    }

    // Validate filters
    const filters = calendarFiltersSchema.parse({
      startDate: new Date(startDate),
      endDate: new Date(endDate),
      memberId: memberId || undefined,
      taskStatus: taskStatus.length > 0 ? taskStatus : undefined,
      taskCategory: taskCategory.length > 0 ? taskCategory : undefined,
      showCompleted,
    });

    const tasks = await CalendarService.getCalendarTasks(
      familyId,
      filters.startDate,
      filters.endDate,
      filters
    );

    return NextResponse.json({
      tasks,
      filters,
      count: tasks.length,
    });
  } catch (error) {
    console.error('Calendar API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
