import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { CalendarService } from '@/lib/services/calendar.service';
import { createCalendarEventSchema } from '@/lib/validations/calendar';
import { FamilyService } from '@/lib/services/family.service';
import { getOrCreateLocalUser } from '@/lib/auth';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Convert Clerk ID to local user ID
    const localUser = await getOrCreateLocalUser(clerkUserId);
    const userId = localUser.id;

    // Verify family membership
    const isMember = await FamilyService.isFamilyMember(familyId, userId);
    if (!isMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    const body = await request.json();

    // Validate the request body
    const validatedData = createCalendarEventSchema.parse(body);

    // Get current member to set as creator
    const currentMember = await FamilyService.getFamilyMember(familyId, userId);
    if (!currentMember) {
      return NextResponse.json({ error: 'Member not found' }, { status: 404 });
    }

    // Create calendar task
    const task = await CalendarService.createCalendarTask(
      familyId,
      {
        ...validatedData,
        // If no assignees specified, assign to creator
        assignedMemberIds: validatedData.assignedMemberIds?.length
          ? validatedData.assignedMemberIds
          : [currentMember.id],
      },
      currentMember.id
    );

    return NextResponse.json(task, { status: 201 });
  } catch (error) {
    console.error('Calendar events POST error:', error);

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
