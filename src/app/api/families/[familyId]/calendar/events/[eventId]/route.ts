import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { CalendarService } from '@/lib/services/calendar.service';
import { updateCalendarEventSchema } from '@/lib/validations/calendar';
import { FamilyService } from '@/lib/services/family.service';
import { getOrCreateLocalUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; eventId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, eventId } = await params;

    // Convert Clerk ID to local user ID
    const localUser = await getOrCreateLocalUser(clerkUserId);
    const userId = localUser.id;

    // Verify family membership
    const isMember = await FamilyService.isFamilyMember(familyId, userId);
    if (!isMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    // Check if event exists and belongs to family
    const existingTask = await prisma.task.findFirst({
      where: {
        id: eventId,
        familyId,
      },
    });

    if (!existingTask) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    const body = await request.json();

    // Validate the request body
    const validatedData = updateCalendarEventSchema.parse(body);

    // Update calendar task
    const task = await CalendarService.updateCalendarTask(eventId, validatedData);

    return NextResponse.json(task);
  } catch (error) {
    console.error('Calendar event PATCH error:', error);

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; eventId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, eventId } = await params;

    // Convert Clerk ID to local user ID
    const localUser = await getOrCreateLocalUser(clerkUserId);
    const userId = localUser.id;

    // Verify family membership
    const isMember = await FamilyService.isFamilyMember(familyId, userId);
    if (!isMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    // Check if event exists and belongs to family
    const existingTask = await prisma.task.findFirst({
      where: {
        id: eventId,
        familyId,
      },
      include: {
        assignedByUser: true,
        assignments: {
          include: {
            member: {
              include: {
                user: true,
              },
            },
          },
        },
      },
    });

    if (!existingTask) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Check permissions - only creator, assignee, or family admin can delete
    const currentMember = await FamilyService.getFamilyMember(familyId, userId);
    const memberRole = await FamilyService.getMemberRole(familyId, userId);

    // Check if current member is assigned to this task
    const isAssignedMember =
      (await prisma.taskAssignment.count({
        where: {
          taskId: existingTask.id,
          memberId: currentMember?.id,
        },
      })) > 0;

    const canDelete =
      existingTask.assignedBy === currentMember?.id ||
      isAssignedMember ||
      ['OWNER', 'PARENT', 'GUARDIAN'].includes(memberRole || '');

    if (!canDelete) {
      return NextResponse.json(
        {
          error: 'Permission denied - only task creator, assignee, or family admin can delete',
        },
        { status: 403 }
      );
    }

    // Delete calendar task
    await CalendarService.deleteCalendarTask(eventId);

    return NextResponse.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Calendar event DELETE error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
