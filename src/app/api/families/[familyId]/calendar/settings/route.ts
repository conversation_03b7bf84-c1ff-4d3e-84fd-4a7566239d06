import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { calendarSettingsSchema } from '@/lib/validations/calendar';
import { FamilyService } from '@/lib/services/family.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify family membership
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });
    if (!familyMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    // Get or create default calendar settings
    let settings = await prisma.calendarSettings.findUnique({
      where: { familyId },
    });

    if (!settings) {
      // Create default settings
      settings = await prisma.calendarSettings.create({
        data: {
          familyId,
          defaultView: 'MONTH',
          startWeekOn: 'MONDAY',
          timeFormat: 'HOUR_24',
          showWeekends: true,
          showCompleted: false,
          colorScheme: 'default',
          googleCalendarEnabled: false,
          appleCalendarEnabled: false,
        },
      });
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Calendar settings GET error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify family membership and admin/parent role
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember || !['OWNER', 'PARENT', 'GUARDIAN'].includes(familyMember.role)) {
      return NextResponse.json(
        {
          error: 'Only parents/guardians can modify calendar settings',
        },
        { status: 403 }
      );
    }

    const body = await request.json();

    // Validate the request body
    const validatedData = calendarSettingsSchema.parse(body);

    // Update calendar settings
    const settings = await prisma.calendarSettings.upsert({
      where: { familyId },
      update: {
        ...validatedData,
        updatedAt: new Date(),
      },
      create: {
        familyId,
        ...validatedData,
      },
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Calendar settings PATCH error:', error);

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
