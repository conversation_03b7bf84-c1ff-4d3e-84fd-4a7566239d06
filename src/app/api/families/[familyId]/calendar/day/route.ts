import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { CalendarService } from '@/lib/services/calendar.service';
import { FamilyService } from '@/lib/services/family.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify family membership
    const isMember = await FamilyService.isFamilyMember(familyId, userId);
    if (!isMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json({ error: 'date is required (ISO date string)' }, { status: 400 });
    }

    const targetDate = new Date(date);
    if (isNaN(targetDate.getTime())) {
      return NextResponse.json({ error: 'Invalid date' }, { status: 400 });
    }

    const tasks = await CalendarService.getDayTasks(familyId, targetDate);

    return NextResponse.json({
      tasks,
      date: targetDate.toISOString(),
      count: tasks.length,
    });
  } catch (error) {
    console.error('Calendar day API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
