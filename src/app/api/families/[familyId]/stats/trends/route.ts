import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { StatsService } from '@/lib/services/stats.service';
import { StatsPeriod } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = (searchParams.get('period') as StatsPeriod) || 'DAILY';
    const metric = searchParams.get('metric') || 'completion_rate';
    const days = parseInt(searchParams.get('days') || '30');

    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

    let trendData;

    switch (metric) {
      case 'completion_rate':
        trendData = await StatsService.getProductivityTrends(familyId, {
          start: startDate,
          end: endDate,
        });
        break;

      case 'tasks_completed':
        const stats = await StatsService.getFamilyStats(
          familyId,
          { start: startDate, end: endDate },
          period
        );
        trendData = stats.map(stat => ({
          date: stat.date,
          value: stat.tasksCompleted,
          period: stat.period,
        }));
        break;

      case 'points_awarded':
        const pointsStats = await StatsService.getFamilyStats(
          familyId,
          { start: startDate, end: endDate },
          period
        );
        trendData = pointsStats.map(stat => ({
          date: stat.date,
          value: stat.totalPointsAwarded,
          period: stat.period,
        }));
        break;

      case 'active_members':
        const memberStats = await StatsService.getFamilyStats(
          familyId,
          { start: startDate, end: endDate },
          period
        );
        trendData = memberStats.map(stat => ({
          date: stat.date,
          value: stat.activeMembersCount,
          period: stat.period,
        }));
        break;

      default:
        return NextResponse.json({ error: 'Invalid metric parameter' }, { status: 400 });
    }

    // Calculate trend direction and percentage change
    const firstValue = trendData[0]?.value || 0;
    const lastValue = trendData[trendData.length - 1]?.value || 0;
    const percentageChange = firstValue > 0 ? ((lastValue - firstValue) / firstValue) * 100 : 0;

    const trend = {
      direction: percentageChange > 5 ? 'up' : percentageChange < -5 ? 'down' : 'stable',
      percentage: Math.abs(Math.round(percentageChange)),
      isPositive: percentageChange >= 0,
    };

    return NextResponse.json({
      data: trendData,
      trend,
      metric,
      period,
      dateRange: {
        start: startDate,
        end: endDate,
      },
      summary: {
        total: trendData.reduce((sum, item) => sum + item.value, 0),
        average:
          trendData.length > 0
            ? trendData.reduce((sum, item) => sum + item.value, 0) / trendData.length
            : 0,
        peak: Math.max(...trendData.map(item => item.value)),
        lowest: Math.min(...trendData.map(item => item.value)),
      },
    });
  } catch (error) {
    console.error('Error fetching trend data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
