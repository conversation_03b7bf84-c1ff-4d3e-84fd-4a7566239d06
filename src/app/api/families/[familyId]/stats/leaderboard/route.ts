import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { StatsService } from '@/lib/services/stats.service';
import { StatsPeriod } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = (searchParams.get('period') as StatsPeriod) || 'MONTHLY';
    const metric = (searchParams.get('metric') as 'points' | 'completion_rate') || 'points';

    // Calculate date range for the period
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'WEEKLY':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'MONTHLY':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'YEARLY':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default: // DAILY
        startDate.setDate(endDate.getDate() - 1);
    }

    // Get member rankings
    const rankings = await StatsService.getMemberRanking(familyId, metric, period);

    // Get additional member details
    const detailedRankings = await Promise.all(
      rankings.map(async ranking => {
        const member = await prisma.familyMember.findUnique({
          where: { id: ranking.memberId },
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        });

        // Get recent member stats for additional context

        const memberStats = await StatsService.getMemberStats(
          ranking.memberId,
          { start: startDate, end: endDate },
          period
        );

        const latestStats = memberStats[memberStats.length - 1];

        return {
          ...ranking,
          member: {
            id: member?.id,
            name: `${member?.user.firstName} ${member?.user.lastName}`,
            email: member?.user.email,
            role: member?.role,
            totalPoints: member?.points,
          },
          stats: {
            tasksCompleted: latestStats?.tasksCompleted || 0,
            tasksAssigned: latestStats?.tasksAssigned || 0,
            completionRate: latestStats?.completionRate || 0,
            pointsEarned: latestStats?.pointsEarned || 0,
            streakDays: latestStats?.streakDays || 0,
            activeDays: latestStats?.activeDays || 0,
          },
          badge: getBadgeForRank(ranking.rank),
          achievement: getAchievementForPerformance(ranking, latestStats),
        };
      })
    );

    // Calculate period statistics
    const totalPoints = detailedRankings.reduce((sum, r) => sum + r.stats.pointsEarned, 0);
    const totalTasks = detailedRankings.reduce((sum, r) => sum + r.stats.tasksCompleted, 0);
    const averageCompletionRate =
      detailedRankings.length > 0
        ? detailedRankings.reduce((sum, r) => sum + r.stats.completionRate, 0) /
          detailedRankings.length
        : 0;

    return NextResponse.json({
      rankings: detailedRankings,
      summary: {
        totalMembers: detailedRankings.length,
        totalPoints,
        totalTasks,
        averageCompletionRate: Math.round(averageCompletionRate),
        period,
        metric,
      },
      meta: {
        generatedAt: new Date(),
        period,
        metric,
        dateRange: {
          start: startDate,
          end: endDate,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching leaderboard:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper functions (these would normally be in a separate utility file)
function getBadgeForRank(rank: number): string {
  switch (rank) {
    case 1:
      return '🥇';
    case 2:
      return '🥈';
    case 3:
      return '🥉';
    default:
      return rank <= 5 ? '⭐' : '👤';
  }
}

function getAchievementForPerformance(ranking: any, stats: any): string | null {
  if (stats?.completionRate >= 95) {
    return 'Perfekcjonista';
  }
  if (stats?.streakDays >= 7) {
    return 'Na fali';
  }
  if (stats?.tasksCompleted >= 20) {
    return 'Pracowity';
  }
  if (ranking.rank === 1) {
    return 'Lider rodziny';
  }
  return null;
}
