import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { StatsService } from '@/lib/services/stats.service';
import { ActivityLogService } from '@/lib/services/activity.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get current date ranges
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get key metrics
    const [
      monthlyCompletionRate,
      weeklyCompletionRate,
      recentActivity,
      pointsRanking,
      completionRanking,
      insights,
    ] = await Promise.all([
      StatsService.getFamilyCompletionRate(familyId, { start: thirtyDaysAgo, end: today }),
      StatsService.getFamilyCompletionRate(familyId, { start: sevenDaysAgo, end: today }),
      ActivityLogService.getRecentActivity(familyId, 10),
      StatsService.getMemberRanking(familyId, 'points', 'MONTHLY'),
      StatsService.getMemberRanking(familyId, 'completion_rate', 'MONTHLY'),
      StatsService.generateInsights(familyId),
    ]);

    // Get quick stats for the current week
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() - today.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weekStats = await StatsService.getFamilyStats(
      familyId,
      { start: weekStart, end: today },
      'DAILY'
    );

    const weekSummary = {
      tasksCompleted: weekStats?.reduce((sum, stat) => sum + (stat.tasksCompleted || 0), 0) || 0,
      totalPoints: weekStats?.reduce((sum, stat) => sum + (stat.totalPointsAwarded || 0), 0) || 0,
      activeMembersCount:
        weekStats?.length > 0
          ? Math.max(...weekStats.map(stat => stat.activeMembersCount || 0), 0)
          : 0,
    };

    // Get family member count
    const totalMembersCount = await prisma.familyMember.count({
      where: { familyId },
    });

    return NextResponse.json({
      summary: {
        monthlyCompletionRate: Math.round(monthlyCompletionRate || 0),
        weeklyCompletionRate: Math.round(weeklyCompletionRate || 0),
        thisWeek: weekSummary,
        totalMembers: totalMembersCount || 0,
      },
      rankings: {
        points: pointsRanking?.slice(0, 5) || [], // Top 5
        completion: completionRanking?.slice(0, 5) || [], // Top 5
      },
      recentActivity: recentActivity || [],
      insights: insights || {
        familyId,
        totalTasks: 0,
        completionRate: 0,
        totalPointsAwarded: 0,
        mostActiveDay: 'Poniedziałek',
        topPerformer: {
          memberId: '',
          memberName: 'Brak danych',
          points: 0,
        },
        trends: {
          tasksCreated: 'stable',
          completionRate: 'stable',
          memberActivity: 'stable',
        },
      },
      trends: {
        completionRateChange: (weeklyCompletionRate || 0) - (monthlyCompletionRate || 0),
        isImproving: (weeklyCompletionRate || 0) > (monthlyCompletionRate || 0),
      },
    });
  } catch (error) {
    console.error('Error fetching stats overview:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
