import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { StatsService } from '@/lib/services/stats.service';
import { StatsPeriod } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = (searchParams.get('period') as StatsPeriod) || 'DAILY';
    const startDate = searchParams.get('startDate')
      ? new Date(searchParams.get('startDate')!)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // 30 days ago
    const endDate = searchParams.get('endDate')
      ? new Date(searchParams.get('endDate')!)
      : new Date();

    // Get family stats
    const stats = await StatsService.getFamilyStats(
      familyId,
      { start: startDate, end: endDate },
      period
    );

    // Get completion rate
    const completionRate = await StatsService.getFamilyCompletionRate(familyId, {
      start: startDate,
      end: endDate,
    });

    // Get member rankings
    const pointsRanking = await StatsService.getMemberRanking(familyId, 'points', period);

    const completionRanking = await StatsService.getMemberRanking(
      familyId,
      'completion_rate',
      period
    );

    return NextResponse.json({
      stats,
      completionRate,
      rankings: {
        points: pointsRanking,
        completion: completionRanking,
      },
      period,
      dateRange: {
        start: startDate,
        end: endDate,
      },
    });
  } catch (error) {
    console.error('Error fetching family stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
