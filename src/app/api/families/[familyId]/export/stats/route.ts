import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { ReportsService } from '@/lib/services/reports.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family with appropriate permissions
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Only allow parents, guardians, and owners to export data
    if (!['PARENT', 'GUARDIAN', 'OWNER'].includes(familyMember.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const exportType = searchParams.get('type') || 'stats'; // 'stats' or 'activity'
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Set default date range if not provided (last 90 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date(defaultEndDate.getTime() - 90 * 24 * 60 * 60 * 1000);

    const dateRange = {
      start: startDate ? new Date(startDate) : defaultStartDate,
      end: endDate ? new Date(endDate) : defaultEndDate,
    };

    // Validate date range
    if (dateRange.start > dateRange.end) {
      return NextResponse.json({ error: 'Start date must be before end date' }, { status: 400 });
    }

    // Limit export to maximum 1 year of data
    const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
    if (dateRange.end.getTime() - dateRange.start.getTime() > maxRange) {
      return NextResponse.json({ error: 'Date range cannot exceed 1 year' }, { status: 400 });
    }

    let csvBuffer: Buffer;
    let filename: string;

    try {
      if (exportType === 'activity') {
        csvBuffer = await ReportsService.exportActivityToCSV(familyId, dateRange);
        filename = `family-${familyId}-activity-${dateRange.start.toISOString().split('T')[0]}-to-${dateRange.end.toISOString().split('T')[0]}.csv`;
      } else {
        csvBuffer = await ReportsService.exportStatsToCSV(familyId, dateRange);
        filename = `family-${familyId}-stats-${dateRange.start.toISOString().split('T')[0]}-to-${dateRange.end.toISOString().split('T')[0]}.csv`;
      }
    } catch (error) {
      console.error('Error generating CSV export:', error);
      return NextResponse.json({ error: 'Failed to generate export' }, { status: 500 });
    }

    // Set appropriate headers for file download
    const headers = new Headers();
    headers.set('Content-Type', 'text/csv; charset=utf-8');
    headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    headers.set('Content-Length', csvBuffer.length.toString());

    // Add BOM for Excel compatibility
    const bom = Buffer.from('\uFEFF', 'utf8');
    const csvWithBom = Buffer.concat([bom, csvBuffer]);

    return new NextResponse(csvWithBom, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Error exporting data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = getAuth(request);

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Verify user is member of the family with appropriate permissions
    const familyMember = await prisma.familyMember.findFirst({
      where: {
        familyId,
        user: { clerkId: userId },
      },
    });

    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    if (!['PARENT', 'GUARDIAN', 'OWNER'].includes(familyMember.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { exportType, dateRange, format = 'csv', includePersonalData = false } = body;

    // Validate input
    if (!exportType || !['stats', 'activity', 'reports'].includes(exportType)) {
      return NextResponse.json({ error: 'Invalid export type' }, { status: 400 });
    }

    if (!dateRange || !dateRange.start || !dateRange.end) {
      return NextResponse.json({ error: 'Date range is required' }, { status: 400 });
    }

    const startDate = new Date(dateRange.start);
    const endDate = new Date(dateRange.end);

    if (startDate > endDate) {
      return NextResponse.json({ error: 'Start date must be before end date' }, { status: 400 });
    }

    // For now, we only support CSV format
    if (format !== 'csv') {
      return NextResponse.json(
        { error: 'Only CSV format is currently supported' },
        { status: 400 }
      );
    }

    // Generate export based on type
    let exportBuffer: Buffer;
    let filename: string;

    switch (exportType) {
      case 'stats':
        exportBuffer = await ReportsService.exportStatsToCSV(familyId, {
          start: startDate,
          end: endDate,
        });
        filename = `family-stats-export-${Date.now()}.csv`;
        break;

      case 'activity':
        exportBuffer = await ReportsService.exportActivityToCSV(familyId, {
          start: startDate,
          end: endDate,
        });
        filename = `family-activity-export-${Date.now()}.csv`;
        break;

      case 'reports':
        // For reports, we could generate a comprehensive report
        // For now, we'll use stats export
        exportBuffer = await ReportsService.exportStatsToCSV(familyId, {
          start: startDate,
          end: endDate,
        });
        filename = `family-reports-export-${Date.now()}.csv`;
        break;

      default:
        return NextResponse.json({ error: 'Unsupported export type' }, { status: 400 });
    }

    // Return download URL and metadata instead of direct file
    // In a real implementation, you might store the file temporarily and return a download link
    return NextResponse.json({
      success: true,
      export: {
        type: exportType,
        format,
        filename,
        size: exportBuffer.length,
        generatedAt: new Date(),
        dateRange: {
          start: startDate,
          end: endDate,
        },
        includePersonalData,
      },
      // For demo purposes, we return base64 encoded data
      // In production, you'd store this and return a download URL
      data: exportBuffer.toString('base64'),
    });
  } catch (error) {
    console.error('Error creating export:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
