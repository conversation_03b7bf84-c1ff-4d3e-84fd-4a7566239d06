import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFamilyMemberWithRole, hasPermission } from '@/lib/auth';
import { createTaskSchema } from '@/lib/validations/task';
import { TaskService } from '@/lib/services/task.service';
import { TaskStatus, TaskWeight, TaskFrequency } from '@prisma/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is a family member with VIEW_TASKS permission
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'VIEW_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Parse query parameters for filtering
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    const filters: Record<string, unknown> = {};

    // Status filter
    const statusParam = searchParams.get('status');
    if (statusParam) {
      const statusList = statusParam
        .split(',')
        .filter(status => Object.values(TaskStatus).includes(status as TaskStatus));
      if (statusList.length > 0) {
        filters.status = statusList as TaskStatus[];
      }
    }

    // Assigned member filter
    const assignedMemberIds = searchParams.get('assignedMemberIds');
    if (assignedMemberIds) {
      filters.assignedMemberIds = assignedMemberIds.split(',');
    }

    // Due date filter
    const dueDateFrom = searchParams.get('dueDateFrom');
    const dueDateTo = searchParams.get('dueDateTo');
    if (dueDateFrom || dueDateTo) {
      const dueDateFilter: { from?: Date; to?: Date } = {};
      if (dueDateFrom) dueDateFilter.from = new Date(dueDateFrom);
      if (dueDateTo) dueDateFilter.to = new Date(dueDateTo);
      filters.dueDate = dueDateFilter;
    }

    // Weight filter
    const weightParam = searchParams.get('weight');
    if (weightParam) {
      const weightList = weightParam
        .split(',')
        .filter(weight => Object.values(TaskWeight).includes(weight as TaskWeight));
      if (weightList.length > 0) {
        filters.weight = weightList as TaskWeight[];
      }
    }

    // Frequency filter
    const frequencyParam = searchParams.get('frequency');
    if (frequencyParam) {
      const frequencyList = frequencyParam
        .split(',')
        .filter(frequency => Object.values(TaskFrequency).includes(frequency as TaskFrequency));
      if (frequencyList.length > 0) {
        filters.frequency = frequencyList as TaskFrequency[];
      }
    }

    // Search filter
    const searchParam = searchParams.get('search');
    if (searchParam) {
      filters.search = searchParam;
    }

    // Completion status filter
    const completionStatusParam = searchParams.get('completionStatus');
    if (completionStatusParam) {
      const validStatuses = ['pending', 'approved', 'rejected', 'overdue', 'due_soon'];
      const statusList = completionStatusParam
        .split(',')
        .filter(status => validStatuses.includes(status));
      if (statusList.length > 0) {
        filters.completionStatus = statusList;
      }
    }

    // Points range filter
    const pointsMin = searchParams.get('pointsMin');
    const pointsMax = searchParams.get('pointsMax');
    if (pointsMin || pointsMax) {
      const pointsRange: { min?: number; max?: number } = {};
      if (pointsMin) pointsRange.min = parseInt(pointsMin);
      if (pointsMax) pointsRange.max = parseInt(pointsMax);
      filters.pointsRange = pointsRange;
    }

    // Include completed filter
    const includeCompleted = searchParams.get('includeCompleted');
    if (includeCompleted === 'true') {
      filters.includeCompleted = true;
    }

    const tasks = await TaskService.getFamilyTasks(familyId, filters);

    return NextResponse.json(tasks);
  } catch (error) {
    console.error('Error fetching tasks:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user has permission to manage tasks
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    console.log('Received task data:', JSON.stringify(body, null, 2));

    const validation = createTaskSchema.safeParse(body);

    if (!validation.success) {
      console.log('Validation errors:', validation.error.errors);
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const data = validation.data;

    // If assigning to members, check if those members exist in the family
    if (data.assignedMemberIds && data.assignedMemberIds.length > 0) {
      const assignedMembers = await prisma.familyMember.findMany({
        where: {
          id: { in: data.assignedMemberIds },
          familyId: familyId,
        },
      });

      if (assignedMembers.length !== data.assignedMemberIds.length) {
        return NextResponse.json(
          { error: 'Some assigned members not found in this family' },
          { status: 400 }
        );
      }
    }

    const task = await TaskService.createTask(familyId, familyMember.id, {
      title: data.title,
      description: data.description,
      points: data.points,
      weight: data.weight,
      frequency: data.frequency,
      dueDate: data.dueDate,
      assignedMemberIds: data.assignedMemberIds,
      assignmentType: data.assignmentType,
      allDay: data.allDay,
      color: data.color,
      recurrenceRule: data.recurrenceRule,
      startDate: data.startDate,
      endDate: data.endDate,
    });

    return NextResponse.json(task, { status: 201 });
  } catch (error) {
    console.error('Error creating task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
