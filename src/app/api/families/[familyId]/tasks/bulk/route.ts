import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFamilyMemberWithRole, hasPermission } from '@/lib/auth';
import { bulkTaskOperationSchema } from '@/lib/validations/task';
import { TaskService } from '@/lib/services/task.service';
import { TaskStatus } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Check if user has appropriate permissions
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const validation = bulkTaskOperationSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { taskIds, operation, assignedMemberIds } = validation.data;

    // Verify all tasks exist and belong to family
    const tasks = await prisma.task.findMany({
      where: {
        id: { in: taskIds },
        familyId,
      },
      include: {
        completions: {
          where: {
            status: { in: ['PENDING', 'APPROVED'] },
          },
        },
      },
    });

    if (tasks.length !== taskIds.length) {
      return NextResponse.json(
        { error: 'Some tasks not found or do not belong to this family' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Check permissions based on operation
    switch (operation) {
      case 'assign':
        if (!hasPermission(familyMember.role, 'ASSIGN_TASKS')) {
          return NextResponse.json({ error: 'No permission to assign tasks' }, { status: 403 });
        }
        if (!assignedMemberIds || assignedMemberIds.length === 0) {
          return NextResponse.json(
            { error: 'assignedMemberIds is required for assign operation' },
            { status: 400 }
          );
        }
        // Verify assignees exist in family
        const assignees = await prisma.familyMember.findMany({
          where: {
            id: { in: assignedMemberIds },
            familyId: familyId,
          },
        });
        if (assignees.length !== assignedMemberIds.length) {
          return NextResponse.json(
            { error: 'Some assignees not found in this family' },
            { status: 400 }
          );
        }
        break;

      case 'complete':
        if (!hasPermission(familyMember.role, 'COMPLETE_TASKS')) {
          return NextResponse.json({ error: 'No permission to complete tasks' }, { status: 403 });
        }
        break;

      case 'delete':
      case 'suspend':
      case 'activate':
        if (!hasPermission(familyMember.role, 'MANAGE_TASKS')) {
          return NextResponse.json({ error: 'No permission to manage tasks' }, { status: 403 });
        }
        break;

      default:
        return NextResponse.json({ error: 'Invalid operation' }, { status: 400 });
    }

    // Process each task
    for (const task of tasks) {
      try {
        let result;

        switch (operation) {
          case 'assign':
            result = await TaskService.updateTask(task.id, {
              assignedMemberIds: assignedMemberIds!,
            });
            results.push({ taskId: task.id, success: true, data: result });
            break;

          case 'complete':
            // Check if already completed by this member
            const existingCompletion = task.completions.find(
              comp => comp.completedById === familyMember.id
            );
            if (existingCompletion) {
              errors.push({ taskId: task.id, error: 'Task already completed by this member' });
            } else {
              result = await TaskService.completeTask(task.id, familyMember.id);
              results.push({ taskId: task.id, success: true, data: result });
            }
            break;

          case 'delete':
            // Check if task has pending completions
            if (task.completions.length > 0) {
              errors.push({
                taskId: task.id,
                error: 'Cannot delete task with pending completions',
              });
            } else {
              await TaskService.deleteTask(task.id);
              results.push({ taskId: task.id, success: true, message: 'Task deleted' });
            }
            break;

          case 'suspend':
            result = await TaskService.updateTask(task.id, { status: TaskStatus.SUSPENDED });
            results.push({ taskId: task.id, success: true, data: result });
            break;

          case 'activate':
            result = await TaskService.updateTask(task.id, { status: TaskStatus.ACTIVE });
            results.push({ taskId: task.id, success: true, data: result });
            break;
        }
      } catch (error: any) {
        errors.push({ taskId: task.id, error: error.message || 'Unknown error' });
      }
    }

    const response = {
      operation,
      totalTasks: taskIds.length,
      successful: results.length,
      failed: errors.length,
      results,
      errors,
    };

    // Return 207 Multi-Status if there were partial failures
    const statusCode = errors.length > 0 && results.length > 0 ? 207 : 200;

    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
