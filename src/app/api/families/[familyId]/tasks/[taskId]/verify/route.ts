import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFamilyMemberWithRole, hasPermission } from '@/lib/auth';
import { verifyTaskSchema } from '@/lib/validations/task';
import { TaskService } from '@/lib/services/task.service';
import { CompletionStatus } from '@prisma/client';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { familyId, taskId } = await params;

    // Check if user has permission to manage tasks (for verification)
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family
    const task = await TaskService.getTask(taskId);
    if (!task || task.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    const body = await request.json();
    const validation = verifyTaskSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { approved, feedback } = validation.data;

    // Get completion ID from query params or find the latest pending completion
    const url = new URL(request.url);
    const completionId = url.searchParams.get('completionId');

    let completion;

    if (completionId) {
      // Verify specific completion
      completion = await prisma.taskCompletion.findUnique({
        where: { id: completionId },
        include: {
          task: true,
          completedBy: true,
        },
      });

      if (!completion || completion.taskId !== taskId) {
        return NextResponse.json({ error: 'Completion not found' }, { status: 404 });
      }
    } else {
      // Find the latest pending completion for this task
      completion = await prisma.taskCompletion.findFirst({
        where: {
          taskId,
          status: CompletionStatus.PENDING,
        },
        include: {
          task: true,
          completedBy: true,
        },
        orderBy: {
          completedAt: 'desc',
        },
      });

      if (!completion) {
        return NextResponse.json(
          { error: 'No pending completion found for this task' },
          { status: 404 }
        );
      }
    }

    try {
      const verifiedCompletion = await TaskService.verifyCompletion(
        completion.id,
        familyMember.id,
        approved,
        feedback
      );

      return NextResponse.json(verifiedCompletion);
    } catch (error: any) {
      if (error.message === 'Completion not found') {
        return NextResponse.json({ error: 'Completion not found' }, { status: 404 });
      }
      if (error.message === 'Completion already verified') {
        return NextResponse.json({ error: 'Completion already verified' }, { status: 400 });
      }
      throw error;
    }
  } catch (error) {
    console.error('Error verifying task completion:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { familyId, taskId } = await params;

    // Check if user has permission to manage tasks
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get completion ID from query params
    const url = new URL(request.url);
    const completionId = url.searchParams.get('completionId');

    if (!completionId) {
      return NextResponse.json({ error: 'Completion ID is required' }, { status: 400 });
    }

    // Verify completion exists and belongs to the task
    const completion = await prisma.taskCompletion.findUnique({
      where: { id: completionId },
      include: {
        task: true,
        completedBy: true,
      },
    });

    if (!completion || completion.taskId !== taskId || completion.task.familyId !== familyId) {
      return NextResponse.json({ error: 'Completion not found' }, { status: 404 });
    }

    const body = await request.json();
    const validation = verifyTaskSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { approved, feedback } = validation.data;

    // If changing from approved to rejected, subtract points
    if (
      completion.status === CompletionStatus.APPROVED &&
      !approved &&
      completion.pointsAwarded > 0
    ) {
      await prisma.familyMember.update({
        where: { id: completion.completedById },
        data: {
          points: {
            decrement: completion.pointsAwarded,
          },
        },
      });
    }

    // If changing from rejected to approved, award points
    if (completion.status === CompletionStatus.REJECTED && approved) {
      const points = await TaskService.calculatePoints(completion.task);
      await prisma.familyMember.update({
        where: { id: completion.completedById },
        data: {
          points: {
            increment: points,
          },
        },
      });
    }

    // Update completion status
    const updatedCompletion = await prisma.taskCompletion.update({
      where: { id: completionId },
      data: {
        status: approved ? CompletionStatus.APPROVED : CompletionStatus.REJECTED,
        verifiedById: familyMember.id,
        verifiedAt: new Date(),
        notes: feedback ? `${completion.notes || ''}\n\nFeedback: ${feedback}` : completion.notes,
        pointsAwarded: approved ? await TaskService.calculatePoints(completion.task) : 0,
      },
      include: {
        task: true,
        completedBy: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        verifiedBy: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(updatedCompletion);
  } catch (error) {
    console.error('Error updating verification status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
