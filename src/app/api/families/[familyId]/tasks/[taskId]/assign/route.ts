import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFamilyMemberWithRole, hasPermission } from '@/lib/auth';
import { assignTaskSchema } from '@/lib/validations/task';
import { TaskService } from '@/lib/services/task.service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, taskId } = await params;

    // Check if user has permission to assign tasks
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'ASSIGN_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family
    const task = await TaskService.getTask(taskId);
    if (!task || task.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    const body = await request.json();
    const validation = assignTaskSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { memberId } = validation.data;

    // Verify member exists and belongs to family
    const assignedMember = await prisma.familyMember.findUnique({
      where: { id: memberId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!assignedMember || assignedMember.familyId !== familyId) {
      return NextResponse.json({ error: 'Member not found in this family' }, { status: 400 });
    }

    const updatedTask = await TaskService.assignTask(taskId, memberId);

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Error assigning task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, taskId } = await params;

    // Check if user has permission to assign tasks
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'ASSIGN_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family
    const task = await TaskService.getTask(taskId);
    if (!task || task.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Check if task has any assignments
    const hasAssignments = await prisma.taskAssignment.count({
      where: { taskId: taskId },
    });
    if (hasAssignments === 0) {
      return NextResponse.json({ error: 'Task is not assigned to anyone' }, { status: 400 });
    }

    // Check if task has pending or approved completions
    const completions = await prisma.taskCompletion.findMany({
      where: {
        taskId,
        status: { in: ['PENDING', 'APPROVED'] },
      },
    });

    if (completions.length > 0) {
      return NextResponse.json(
        { error: 'Cannot unassign task with pending or approved completions' },
        { status: 400 }
      );
    }

    const updatedTask = await TaskService.unassignTask(taskId);

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Error unassigning task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
