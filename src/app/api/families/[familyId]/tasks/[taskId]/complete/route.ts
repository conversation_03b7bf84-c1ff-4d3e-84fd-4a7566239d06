import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFamilyMemberWithRole, hasPermission, canModifyCompletedTask } from '@/lib/auth';
import { completeTaskSchema } from '@/lib/validations/task';
import { TaskService } from '@/lib/services/task.service';
import { CompletionStatus } from '@prisma/client';
import { ActivityLogService } from '@/lib/services/activity.service';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { familyId, taskId } = await params;

    // Check if user is a family member with COMPLETE_TASKS permission
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'COMPLETE_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family
    const task = await TaskService.getTask(taskId);
    if (!task || task.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    const body = await request.json();
    const validation = completeTaskSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const { notes } = validation.data;

    try {
      const completion = await TaskService.completeTask(taskId, familyMember.id, notes);
      return NextResponse.json(completion, { status: 201 });
    } catch (error: unknown) {
      if (error instanceof Error) {
        if (error.message === 'Task not found') {
          return NextResponse.json({ error: 'Task not found' }, { status: 404 });
        }
        if (error.message === 'Task is not active') {
          return NextResponse.json({ error: 'Task is not active' }, { status: 400 });
        }
        if (error.message === 'Task already completed by this member') {
          return NextResponse.json(
            { error: 'Task already completed by this member' },
            { status: 400 }
          );
        }
      }
      throw error;
    }
  } catch (error) {
    console.error('Error completing task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { familyId, taskId } = await params;

    // Check if user is a family member
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family
    const task = await TaskService.getTask(taskId);
    if (!task || task.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Find user's completion for this task
    const completion = await prisma.taskCompletion.findFirst({
      where: {
        taskId,
        completedById: familyMember.id,
        status: { in: ['PENDING', 'APPROVED'] },
      },
    });

    if (!completion) {
      return NextResponse.json({ error: 'Completion not found' }, { status: 404 });
    }

    // Enhanced permission check for approved completions
    if (completion.status === CompletionStatus.APPROVED) {
      // Get task details to check permissions
      const task = await TaskService.getTask(taskId);
      if (!task) {
        return NextResponse.json({ error: 'Task not found' }, { status: 404 });
      }

      // Get task creator details
      const taskCreator = await prisma.familyMember.findUnique({
        where: { id: task.assignedById },
        include: { user: true },
      });

      if (!taskCreator) {
        return NextResponse.json({ error: 'Task creator not found' }, { status: 404 });
      }

      // Get completion details with user info
      const completionWithUser = await prisma.taskCompletion.findUnique({
        where: { id: completion.id },
        include: {
          completedBy: {
            include: { user: true },
          },
        },
      });

      if (!completionWithUser) {
        return NextResponse.json({ error: 'Completion details not found' }, { status: 404 });
      }

      // Check if user can modify this completed task
      const canModify = canModifyCompletedTask(
        familyMember.role,
        taskCreator.role,
        completionWithUser.completedBy.role,
        completion.completedAt,
        familyMember.id === taskCreator.id, // isTaskCreator
        familyMember.id === completion.completedById // isTaskCompleter
      );

      if (!canModify) {
        return NextResponse.json(
          {
            error:
              'Nie możesz usunąć tego zatwierdzonego ukończenia zadania. Zatwierdzone ukończenia mogą być usuwane tylko przez właściciela rodziny lub w określonych przypadkach przez twórców zadań.',
          },
          { status: 403 }
        );
      }
    }

    // If completion was approved, subtract points from member
    if (completion.status === 'APPROVED' && completion.pointsAwarded > 0) {
      await prisma.familyMember.update({
        where: { id: familyMember.id },
        data: {
          points: {
            decrement: completion.pointsAwarded,
          },
        },
      });
    }

    // Log activity if this was an approved completion deletion
    if (completion.status === CompletionStatus.APPROVED) {
      await ActivityLogService.logActivity({
        familyId,
        userId: localUser.id,
        memberId: familyMember.id,
        action: 'APPROVED_COMPLETION_DELETED',
        entityType: 'task_completion',
        entityId: completion.id,
        metadata: {
          taskId: taskId,
          taskTitle: task?.title || 'Unknown Task',
          completionDate: completion.completedAt,
          pointsAwarded: completion.pointsAwarded,
          userRole: familyMember.role,
          completedById: completion.completedById,
        },
      });
    }

    // Delete the completion
    await prisma.taskCompletion.delete({
      where: { id: completion.id },
    });

    return NextResponse.json({ message: 'Task completion removed successfully' });
  } catch (error) {
    console.error('Error removing task completion:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
