import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import {
  getFamilyMemberWithRole,
  hasPermission,
  canModifyCompletedTask,
  canDeleteCompletedTask,
} from '@/lib/auth';
import { updateTaskSchema } from '@/lib/validations/task';
import { TaskService } from '@/lib/services/task.service';
import { parseOccurrenceId } from '@/lib/utils/calendar-helpers';
import { CompletionStatus } from '@prisma/client';
import { ActivityLogService } from '@/lib/services/activity.service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, taskId } = await params;

    // Parse occurrence ID to get original task ID if needed
    const { originalTaskId, isOccurrence, occurrenceTimestamp } = parseOccurrenceId(taskId);

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is a family member with VIEW_TASKS permission
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'VIEW_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Use original task ID to fetch the task from database
    const task = await TaskService.getTask(originalTaskId);

    if (!task) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Verify task belongs to the family
    if (task.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // If this is an occurrence request, add occurrence information to response
    if (isOccurrence && occurrenceTimestamp) {
      return NextResponse.json({
        ...task,
        occurrenceInfo: {
          isOccurrence: true,
          originalTaskId,
          occurrenceDate: new Date(occurrenceTimestamp),
          occurrenceTimestamp,
        },
      });
    }

    return NextResponse.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, taskId } = await params;

    // Parse occurrence ID to get original task ID if needed
    const { originalTaskId } = parseOccurrenceId(taskId);

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user has permission to manage tasks
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family (use original task ID)
    const existingTask = await TaskService.getTask(originalTaskId);
    if (!existingTask || existingTask.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Check if task has approved completions - if so, apply stricter permission checks
    const approvedCompletions = await prisma.taskCompletion.findMany({
      where: {
        taskId: originalTaskId,
        status: CompletionStatus.APPROVED,
      },
      include: {
        completedBy: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        completedAt: 'desc',
      },
    });

    // If task has approved completions, check enhanced permissions
    if (approvedCompletions.length > 0) {
      const latestCompletion = approvedCompletions[0];

      // Get task creator details
      const taskCreator = await prisma.familyMember.findUnique({
        where: { id: existingTask.assignedById },
        include: { user: true },
      });

      if (!taskCreator) {
        return NextResponse.json({ error: 'Task creator not found' }, { status: 404 });
      }

      // Check if user can modify this completed task
      const canModify = canModifyCompletedTask(
        familyMember.role,
        taskCreator.role,
        latestCompletion.completedBy.role,
        latestCompletion.completedAt,
        familyMember.id === taskCreator.id, // isTaskCreator
        familyMember.id === latestCompletion.completedById // isTaskCompleter
      );

      if (!canModify) {
        return NextResponse.json(
          {
            error:
              'Nie możesz modyfikować tego ukończonego zadania. Ukończone zadania mogą być modyfikowane tylko przez właściciela rodziny lub w określonych przypadkach przez ich twórców.',
          },
          { status: 403 }
        );
      }
    }

    const body = await request.json();
    const validation = updateTaskSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const data = validation.data;

    // If changing assignment, check if those members exist in the family
    if (data.assignedMemberIds !== undefined) {
      if (data.assignedMemberIds && data.assignedMemberIds.length > 0) {
        const assignedMembers = await prisma.familyMember.findMany({
          where: {
            id: { in: data.assignedMemberIds },
            familyId: familyId,
          },
        });

        if (assignedMembers.length !== data.assignedMemberIds.length) {
          return NextResponse.json(
            { error: 'Some assigned members not found in this family' },
            { status: 400 }
          );
        }
      }
    }

    const updatedTask = await TaskService.updateTask(originalTaskId, data);

    // Log activity if this was a completed task modification
    if (approvedCompletions.length > 0) {
      await ActivityLogService.logActivity({
        familyId,
        userId: localUser.id,
        memberId: familyMember.id,
        action: 'COMPLETED_TASK_MODIFIED',
        entityType: 'task',
        entityId: originalTaskId,
        metadata: {
          taskTitle: existingTask.title,
          modifiedFields: Object.keys(data),
          approvedCompletionsCount: approvedCompletions.length,
          lastCompletionDate: approvedCompletions[0].completedAt,
          userRole: familyMember.role,
        },
      });
    }

    return NextResponse.json(updatedTask);
  } catch (error) {
    console.error('Error updating task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string; taskId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId, taskId } = await params;

    // Parse occurrence ID to get original task ID if needed
    const { originalTaskId } = parseOccurrenceId(taskId);

    // Convert Clerk user ID to local user ID
    const localUser = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!localUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user has permission to manage tasks
    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);
    if (!familyMember || !hasPermission(familyMember.role, 'MANAGE_TASKS')) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Verify task exists and belongs to family (use original task ID)
    const existingTask = await TaskService.getTask(originalTaskId);
    if (!existingTask || existingTask.familyId !== familyId) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }

    // Check if task has pending completions (use original task ID)
    const pendingCompletions = await prisma.taskCompletion.count({
      where: {
        taskId: originalTaskId,
        status: 'PENDING',
      },
    });

    if (pendingCompletions > 0) {
      return NextResponse.json(
        {
          error: 'Cannot delete task with pending completions. Please verify or reject them first.',
        },
        { status: 400 }
      );
    }

    // Check if task has approved completions - if so, apply stricter permission checks
    const approvedCompletions = await prisma.taskCompletion.findMany({
      where: {
        taskId: originalTaskId,
        status: CompletionStatus.APPROVED,
      },
      include: {
        completedBy: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        completedAt: 'desc',
      },
    });

    // If task has approved completions, check enhanced permissions for deletion
    if (approvedCompletions.length > 0) {
      const latestCompletion = approvedCompletions[0];

      // Get task creator details
      const taskCreator = await prisma.familyMember.findUnique({
        where: { id: existingTask.assignedById },
        include: { user: true },
      });

      if (!taskCreator) {
        return NextResponse.json({ error: 'Task creator not found' }, { status: 404 });
      }

      // Check if user can delete this completed task
      const canDelete = canDeleteCompletedTask(
        familyMember.role,
        taskCreator.role,
        latestCompletion.completedBy.role,
        latestCompletion.completedAt,
        familyMember.id === taskCreator.id // isTaskCreator
      );

      if (!canDelete) {
        return NextResponse.json(
          {
            error:
              'Nie możesz usunąć tego ukończonego zadania. Ukończone zadania mogą być usuwane tylko przez właściciela rodziny lub w bardzo wyjątkowych przypadkach przez ich twórców (do 1 godziny po ukończeniu).',
          },
          { status: 403 }
        );
      }
    }

    // Log activity if this was a completed task deletion
    if (approvedCompletions.length > 0) {
      await ActivityLogService.logActivity({
        familyId,
        userId: localUser.id,
        memberId: familyMember.id,
        action: 'COMPLETED_TASK_DELETED',
        entityType: 'task',
        entityId: originalTaskId,
        metadata: {
          taskTitle: existingTask.title,
          approvedCompletionsCount: approvedCompletions.length,
          lastCompletionDate: approvedCompletions[0].completedAt,
          userRole: familyMember.role,
          taskCreatorId: existingTask.assignedById,
        },
      });
    }

    await TaskService.deleteTask(originalTaskId);

    return NextResponse.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Error deleting task:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
