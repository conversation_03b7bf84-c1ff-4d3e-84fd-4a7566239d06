import { headers } from 'next/headers';
import { Webhook } from 'svix';
import { WebhookEvent } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';

export async function POST(req: Request) {
  // Pobierz webhook secret z zmiennych środowiskowych
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;

  if (!WEBHOOK_SECRET) {
    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local');
  }

  // Pobierz headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // Jeś<PERSON> nie ma wymaganych headers, zwr<PERSON><PERSON> błąd
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occurred -- no svix headers', {
      status: 400,
    });
  }

  // Pobierz body
  const payload = await req.text();
  const body = JSON.parse(payload);

  // Stwórz nową instancję Svix z sekretnym kluczem
  const wh = new Webhook(WEBHOOK_SECRET);

  let evt: WebhookEvent;

  // Zweryfikuj webhook
  try {
    evt = wh.verify(payload, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error occurred', {
      status: 400,
    });
  }

  // Obsłuż event
  const { id } = evt.data;
  const eventType = evt.type;

  console.log(`Webhook with ID: ${id} and type of ${eventType}`);
  console.log('Webhook body:', body);

  try {
    switch (eventType) {
      case 'user.created':
        await handleUserCreated(evt);
        break;
      case 'user.updated':
        await handleUserUpdated(evt);
        break;
      case 'user.deleted':
        await handleUserDeleted(evt);
        break;
      default:
        console.log(`Unhandled webhook event type: ${eventType}`);
    }
  } catch (error) {
    console.error(`Error handling webhook event ${eventType}:`, error);
    return new Response('Error occurred', { status: 500 });
  }

  return new Response('', { status: 200 });
}

async function handleUserCreated(evt: WebhookEvent) {
  const data = evt.data as unknown as {
    id: string;
    email_addresses: Array<{ email_address: string }>;
    first_name?: string;
    last_name?: string;
  };
  const { id, email_addresses, first_name, last_name } = data;

  try {
    await prisma.user.create({
      data: {
        clerkId: id!,
        email: email_addresses?.[0]?.email_address || '',
        firstName: first_name || null,
        lastName: last_name || null,
        preferredLanguage: 'pl',
      },
    });
    console.log(`User created in database: ${id}`);
  } catch (error) {
    console.error('Error creating user in database:', error);
    throw error;
  }
}

async function handleUserUpdated(evt: WebhookEvent) {
  const data = evt.data as unknown as {
    id: string;
    email_addresses: Array<{ email_address: string }>;
    first_name?: string;
    last_name?: string;
  };
  const { id, email_addresses, first_name, last_name } = data;

  try {
    await prisma.user.update({
      where: { clerkId: id! },
      data: {
        email: email_addresses?.[0]?.email_address || '',
        firstName: first_name || null,
        lastName: last_name || null,
      },
    });
    console.log(`User updated in database: ${id}`);
  } catch (error) {
    console.error('Error updating user in database:', error);
    throw error;
  }
}

async function handleUserDeleted(evt: WebhookEvent) {
  const { id } = evt.data;

  try {
    await prisma.user.delete({
      where: { clerkId: id! },
    });
    console.log(`User deleted from database: ${id}`);
  } catch (error) {
    console.error('Error deleting user from database:', error);
    throw error;
  }
}
