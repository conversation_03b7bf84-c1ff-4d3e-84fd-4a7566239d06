import { auth, currentUser } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { getFamilyMemberWithRole, getOrCreateLocalUser } from '@/lib/auth';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ familyId: string }> }
) {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { familyId } = await params;

    // Pobierz dane użytkownika z Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json({ error: 'User not found in Clerk' }, { status: 404 });
    }

    // Pobierz lub stwórz lokalnego użytkownika
    const localUser = await getOrCreateLocalUser(clerkUserId, {
      email: clerkUser.emailAddresses[0]?.emailAddress || '',
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName,
    });

    const familyMember = await getFamilyMemberWithRole(localUser.id, familyId);

    if (!familyMember) {
      return NextResponse.json({ error: 'Not a family member' }, { status: 404 });
    }

    return NextResponse.json(familyMember);
  } catch (error) {
    console.error('Error fetching family member:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
