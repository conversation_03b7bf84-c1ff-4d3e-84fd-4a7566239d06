import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';

const privacyPreferencesSchema = z.object({
  profileVisibility: z.enum(['public', 'family', 'private']).optional(),
  showEmail: z.boolean().optional(),
  showActivity: z.boolean().optional(),
  showStats: z.boolean().optional(),
  allowInvitations: z.boolean().optional(),
  dataSharing: z.boolean().optional(),
  analyticsOptOut: z.boolean().optional(),
  marketingEmails: z.boolean().optional(),
});

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { clerkId: userId },
      select: {
        profileVisibility: true,
        showEmail: true,
        showActivity: true,
        showStats: true,
        allowInvitations: true,
        dataSharing: true,
        analyticsOptOut: true,
        marketingEmails: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching privacy preferences:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = privacyPreferencesSchema.parse(body);

    const updatedUser = await prisma.user.update({
      where: { clerkId: userId },
      data: validatedData,
      select: {
        profileVisibility: true,
        showEmail: true,
        showActivity: true,
        showStats: true,
        allowInvitations: true,
        dataSharing: true,
        analyticsOptOut: true,
        marketingEmails: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid data', details: error.errors }, { status: 400 });
    }

    console.error('Error updating privacy preferences:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
