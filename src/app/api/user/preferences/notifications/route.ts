import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateNotificationPreferencesSchema = z.object({
  emailNotificationsEnabled: z.boolean().optional(),
  notifyTaskAssigned: z.boolean().optional(),
  notifyTaskCompleted: z.boolean().optional(),
  notifyTaskVerified: z.boolean().optional(),
  notifyTaskReminder: z.boolean().optional(),
  notifyFamilyInvitations: z.boolean().optional(),
  notifySuspiciousActivity: z.boolean().optional(),
});

export async function GET() {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
      select: {
        id: true,
        emailNotificationsEnabled: true,
        notifyTaskAssigned: true,
        notifyTaskCompleted: true,
        notifyTaskVerified: true,
        notifyTaskReminder: true,
        notifyFamilyInvitations: true,
        notifySuspiciousActivity: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching notification preferences:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { userId: clerkUserId } = await auth();
    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Znajdź lokalnego użytkownika na podstawie Clerk ID
    const user = await prisma.user.findUnique({
      where: { clerkId: clerkUserId },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const body = await request.json();
    const validation = updateNotificationPreferencesSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validation.error.errors },
        { status: 400 }
      );
    }

    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: validation.data,
      select: {
        id: true,
        emailNotificationsEnabled: true,
        notifyTaskAssigned: true,
        notifyTaskCompleted: true,
        notifyTaskVerified: true,
        notifyTaskReminder: true,
        notifyFamilyInvitations: true,
        notifySuspiciousActivity: true,
      },
    });

    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating notification preferences:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
