import { auth, currentUser } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { getUserFamilies, getOrCreateLocalUser } from '@/lib/auth';

export async function GET() {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Pobierz dane użytkownika z Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json({ error: 'User not found in Clerk' }, { status: 404 });
    }

    // Pobierz lub stwórz lokalnego użytkownika
    const localUser = await getOrCreateLocalUser(clerkUserId, {
      email: clerkUser.emailAddresses[0]?.emailAddress || '',
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName,
    });

    // Pobierz rodziny dla lokalnego użytkownika
    const families = await getUserFamilies(localUser.id);

    return NextResponse.json(families);
  } catch (error) {
    console.error('Error fetching user families:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
