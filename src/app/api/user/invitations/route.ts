import { auth, currentUser } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { getOrCreateLocalUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { InvitationStatus } from '@prisma/client';

export async function GET() {
  try {
    const { userId: clerkUserId } = await auth();

    if (!clerkUserId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Pobierz dane użytkownika z Clerk
    const clerkUser = await currentUser();

    if (!clerkUser) {
      return NextResponse.json({ error: 'User not found in Clerk' }, { status: 404 });
    }

    // Pobierz lub stwórz lokalnego użytkownika
    const localUser = await getOrCreateLocalUser(clerkUserId, {
      email: clerkUser.emailAddresses[0]?.emailAddress || '',
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName,
    });

    // Znajdź zaproszenia dla email użytkownika
    const invitations = await prisma.familyInvitation.findMany({
      where: {
        email: localUser.email,
        status: InvitationStatus.PENDING,
        expiresAt: {
          gt: new Date(), // Tylko nie wygasłe zaproszenia
        },
      },
      include: {
        family: {
          select: {
            id: true,
            name: true,
            description: true,
            members: {
              where: {
                role: 'OWNER',
              },
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
              take: 1,
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Automatycznie oznacz wygasłe zaproszenia
    await prisma.familyInvitation.updateMany({
      where: {
        email: localUser.email,
        status: InvitationStatus.PENDING,
        expiresAt: {
          lt: new Date(),
        },
      },
      data: {
        status: InvitationStatus.EXPIRED,
      },
    });

    return NextResponse.json(invitations);
  } catch (error) {
    console.error('Error fetching user invitations:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
