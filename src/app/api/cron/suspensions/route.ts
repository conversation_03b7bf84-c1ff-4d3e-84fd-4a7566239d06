import { NextRequest, NextResponse } from 'next/server';
import { SuspensionService } from '@/lib/services/suspension.service';

export async function GET(request: NextRequest) {
  try {
    // Verify the request is coming from a cron job or authorized source
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('Running suspension management cron job...');

    // Process expired suspensions
    await SuspensionService.processExpiredSuspensions();

    console.log('Suspension management cron job completed successfully');

    return NextResponse.json({
      success: true,
      message: 'Suspension management completed',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in suspension management cron job:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Also support POST for manual triggering
export async function POST(request: NextRequest) {
  return GET(request);
}
