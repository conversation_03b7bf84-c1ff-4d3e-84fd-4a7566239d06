import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { StatsService } from '@/lib/services/stats.service';
import { ActivityLogService } from '@/lib/services/activity.service';

// This endpoint should be called by a cron service (like Vercel Cron)
// or by a scheduled task runner
export async function GET(request: NextRequest) {
  try {
    // Verify the request is coming from a valid cron service
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;

    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    console.log('Starting daily stats generation cron job');

    // Get all active families
    const families = await prisma.family.findMany({
      select: {
        id: true,
        name: true,
        members: {
          select: {
            id: true,
          },
        },
      },
    });

    let statsGenerated = 0;
    let errors = 0;

    // Process each family
    for (const family of families) {
      try {
        // Generate daily family stats for yesterday
        await StatsService.generateFamilyStats(family.id, yesterday, 'DAILY');

        // Generate daily member stats for yesterday
        for (const member of family.members) {
          await StatsService.generateMemberStats(member.id, yesterday, 'DAILY');
        }

        statsGenerated++;

        // Check if it's the beginning of the week (Sunday/Monday)
        if (today.getDay() === 1) {
          // Monday
          const weekStart = new Date(yesterday);
          weekStart.setDate(yesterday.getDate() - yesterday.getDay()); // Start of last week

          await StatsService.generateFamilyStats(family.id, weekStart, 'WEEKLY');

          for (const member of family.members) {
            await StatsService.generateMemberStats(member.id, weekStart, 'WEEKLY');
          }
        }

        // Check if it's the beginning of the month
        if (today.getDate() === 1) {
          const monthStart = new Date(yesterday.getFullYear(), yesterday.getMonth(), 1);

          await StatsService.generateFamilyStats(family.id, monthStart, 'MONTHLY');

          for (const member of family.members) {
            await StatsService.generateMemberStats(member.id, monthStart, 'MONTHLY');
          }
        }

        // Check if it's the beginning of the year
        if (today.getMonth() === 0 && today.getDate() === 1) {
          const yearStart = new Date(yesterday.getFullYear(), 0, 1);

          await StatsService.generateFamilyStats(family.id, yearStart, 'YEARLY');

          for (const member of family.members) {
            await StatsService.generateMemberStats(member.id, yearStart, 'YEARLY');
          }
        }
      } catch (familyError) {
        console.error(`Error processing family ${family.id}:`, familyError);
        errors++;
      }
    }

    // Cleanup old activity logs (older than 2 years)
    const twoYearsAgo = new Date(today);
    twoYearsAgo.setFullYear(today.getFullYear() - 2);

    const deletedActivityLogs = await ActivityLogService.deleteOldActivityLogs(twoYearsAgo);

    // Cleanup old daily stats (older than 1 year)
    const oneYearAgo = new Date(today);
    oneYearAgo.setFullYear(today.getFullYear() - 1);

    const deletedDailyStats = await prisma.familyStats.deleteMany({
      where: {
        period: 'DAILY',
        date: {
          lt: oneYearAgo,
        },
      },
    });

    const deletedDailyMemberStats = await prisma.memberStats.deleteMany({
      where: {
        period: 'DAILY',
        date: {
          lt: oneYearAgo,
        },
      },
    });

    const summary = {
      familiesProcessed: families.length,
      statsGenerated,
      errors,
      cleanup: {
        deletedActivityLogs,
        deletedDailyStats: deletedDailyStats.count,
        deletedDailyMemberStats: deletedDailyMemberStats.count,
      },
      executedAt: today,
      duration: Date.now() - today.getTime(),
    };

    console.log('Daily stats generation completed:', summary);

    return NextResponse.json({
      success: true,
      message: 'Daily stats generation completed successfully',
      summary,
    });
  } catch (error) {
    console.error('Error in stats generation cron job:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: 'Failed to generate daily stats',
        executedAt: new Date(),
      },
      { status: 500 }
    );
  }
}

// Endpoint to manually trigger stats generation (for testing)
export async function POST(request: NextRequest) {
  try {
    // This should be protected in production
    const body = await request.json();
    const { familyId, date, period } = body;

    if (!familyId || !date || !period) {
      return NextResponse.json(
        { error: 'familyId, date, and period are required' },
        { status: 400 }
      );
    }

    const targetDate = new Date(date);

    // Generate family stats
    const familyStats = await StatsService.generateFamilyStats(familyId, targetDate, period);

    // Get all family members
    const members = await prisma.familyMember.findMany({
      where: { familyId },
      select: { id: true },
    });

    // Generate member stats
    const memberStats = await Promise.all(
      members.map(member => StatsService.generateMemberStats(member.id, targetDate, period))
    );

    return NextResponse.json({
      success: true,
      message: 'Stats generated successfully',
      data: {
        familyStats,
        memberStats,
        generatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error('Error in manual stats generation:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
