'use client';

import { useAuth, useUserFamilies } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CreateFamilyDialog } from '@/components/family/CreateFamilyDialog';
import { UserInvitationsDialog } from '@/components/family/UserInvitationsDialog';
import { InviteMemberDialog } from '@/components/family/InviteMemberDialog';
import { TaskDialog } from '@/components/tasks/TaskDialog';
import { UserTasksDashboard } from '@/components/tasks/UserTasksDashboard';
import { Button } from '@/components/ui/button';
import { Home, Mail, Users, Plus, CheckSquare, UserPlus } from 'lucide-react';
import { useFamilyMembers } from '@/hooks/useFamily';
import Link from 'next/link';

export default function DashboardPage() {
  const { firstName, lastName, userId, email } = useAuth();
  const { families, loading, error, refetch } = useUserFamilies();

  const displayName =
    firstName && lastName ? `${firstName} ${lastName}` : firstName || lastName || 'Użytkownik';

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Witaj, {displayName}!</h2>
        <p className="text-gray-600">Oto przegląd Twoich rodzin i zadań.</p>
      </div>

      {loading ? (
        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700 mb-4">{error}</p>
            <Button onClick={refetch} variant="outline">
              Spróbuj ponownie
            </Button>
          </div>
        </div>
      ) : families.length === 0 ? (
        <OnboardingSection onFamilyCreated={refetch} />
      ) : (
        <ExistingUserSection
          families={families}
          currentUser={{
            id: userId || '',
            firstName: firstName || undefined,
            lastName: lastName || undefined,
            email: email || '',
          }}
        />
      )}
    </div>
  );
}

function OnboardingSection({ onFamilyCreated }: { onFamilyCreated?: () => void }) {
  return (
    <div className="text-center py-12">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <Home className="mx-auto h-16 w-16 text-blue-500 mb-4" />
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Witaj w FamilyTasks!</h3>
          <p className="text-gray-600 text-lg">
            Aby rozpocząć, musisz stworzyć nową rodzinę lub dołączyć do istniejącej.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="border-2 border-blue-200 hover:border-blue-300 transition-colors">
            <CardContent className="p-6 text-center">
              <Users className="mx-auto h-12 w-12 text-blue-500 mb-4" />
              <h4 className="text-lg font-semibold mb-2">Stwórz nową rodzinę</h4>
              <p className="text-gray-600 mb-4">
                Rozpocznij organizację zadań domowych ze swoimi bliskimi
              </p>
              <CreateFamilyDialog
                trigger={
                  <Button className="w-full">
                    <Home className="mr-2 h-4 w-4" />
                    Utwórz rodzinę
                  </Button>
                }
                onSuccess={() => {
                  onFamilyCreated?.();
                  window.location.reload();
                }}
              />
            </CardContent>
          </Card>

          <Card className="border-2 border-green-200 hover:border-green-300 transition-colors">
            <CardContent className="p-6 text-center">
              <Mail className="mx-auto h-12 w-12 text-green-500 mb-4" />
              <h4 className="text-lg font-semibold mb-2">Sprawdź zaproszenia</h4>
              <p className="text-gray-600 mb-4">Może ktoś już Cię zaprosił do swojej rodziny?</p>
              <UserInvitationsDialog
                trigger={
                  <Button variant="outline" className="w-full">
                    <Mail className="mr-2 h-4 w-4" />
                    Sprawdź zaproszenia
                  </Button>
                }
                onInvitationAccepted={() => {
                  onFamilyCreated?.();
                  window.location.reload();
                }}
              />
            </CardContent>
          </Card>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h4 className="font-semibold text-blue-900 mb-2">Jak działa FamilyTasks?</h4>
          <div className="grid md:grid-cols-3 gap-4 text-sm text-blue-800">
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                1
              </div>
              <div>
                <strong>Stwórz rodzinę</strong> - Dodaj członków swojej rodziny
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                2
              </div>
              <div>
                <strong>Dodaj zadania</strong> - Twórz zadania domowe i przypisuj je członkom
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                3
              </div>
              <div>
                <strong>Śledź postępy</strong> - Zdobywaj punkty i monitoruj statystyki
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function ExistingUserSection({
  families,
  currentUser,
}: {
  families: any[];
  currentUser: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
}) {
  // Użyj pierwszej dostępnej rodziny jako domyślnej
  const defaultFamily = families[0];
  const familyId = defaultFamily?.family?.id;

  // Pobierz dane z hooków dla funkcjonalności dialopgów
  const { members } = useFamilyMembers(familyId || '');

  return (
    <div className="space-y-6">
      {/* Main tasks dashboard */}
      <UserTasksDashboard currentUser={currentUser} families={families} />

      {/* Quick Actions and Family Management */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Moje rodziny</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {families.map(familyMember => (
                <div key={familyMember.family.id} className="p-2 bg-gray-50 rounded-md">
                  <div className="font-medium">{familyMember.family.name}</div>
                  <div className="text-sm text-gray-500">
                    Rola: {familyMember.role} • {familyMember.points} pkt
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t">
              <CreateFamilyDialog
                trigger={
                  <Button variant="outline" size="sm" className="w-full">
                    <Home className="mr-2 h-4 w-4" />
                    Utwórz nową rodzinę
                  </Button>
                }
                onSuccess={() => window.location.reload()}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Statystyki</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Łączne punkty:</span>
                <span className="font-medium">
                  {families.reduce((sum, f) => sum + f.points, 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Aktywne rodziny:</span>
                <span className="font-medium">{families.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Szybkie akcje</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {familyId ? (
                <Link href={`/families/${familyId}/tasks`}>
                  <Button variant="outline" size="sm" className="w-full">
                    <CheckSquare className="mr-2 h-4 w-4" />
                    Przeglądaj zadania rodziny
                  </Button>
                </Link>
              ) : (
                <Button variant="outline" size="sm" className="w-full" disabled>
                  <CheckSquare className="mr-2 h-4 w-4" />
                  Przeglądaj zadania rodziny
                </Button>
              )}

              {familyId ? (
                <Link href={`/families/${familyId}/members`}>
                  <Button variant="outline" size="sm" className="w-full">
                    <Users className="mr-2 h-4 w-4" />
                    Zarządzaj członkami
                  </Button>
                </Link>
              ) : (
                <Button variant="outline" size="sm" className="w-full" disabled>
                  <Users className="mr-2 h-4 w-4" />
                  Zarządzaj członkami
                </Button>
              )}

              {familyId && (
                <>
                  <TaskDialog
                    familyId={familyId}
                    familyMembers={members}
                    mode="create"
                    trigger={
                      <Button variant="outline" size="sm" className="w-full">
                        <Plus className="mr-2 h-4 w-4" />
                        Szybkie tworzenie zadania
                      </Button>
                    }
                    onTaskSuccess={() => {}}
                  />

                  <div className="flex gap-2">
                    <UserInvitationsDialog
                      trigger={
                        <Button variant="outline" size="sm" className="flex-1">
                          <Mail className="mr-2 h-4 w-4" />
                          Sprawdź zaproszenia
                        </Button>
                      }
                      onInvitationAccepted={() => window.location.reload()}
                    />

                    <InviteMemberDialog
                      familyId={familyId}
                      trigger={
                        <Button variant="outline" size="sm" className="flex-1">
                          <UserPlus className="mr-2 h-4 w-4" />
                          Zaproś członka
                        </Button>
                      }
                      onInviteSent={() => {}}
                    />
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
