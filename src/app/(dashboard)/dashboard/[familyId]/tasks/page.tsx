'use client';

import React from 'react';
import { useTasks } from '@/hooks/useTasks';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole, useCanManageTasks } from '@/hooks/useAuth';
import { TasksDashboard } from '@/components/tasks/TasksDashboard';

interface TasksPageProps {
  params: Promise<{ familyId: string }>;
}

export default function TasksPage({ params }: TasksPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  const { tasks, loading: tasksLoading, error, refetch } = useTasks(familyId);
  const { members, loading: membersLoading } = useFamilyMembers(familyId);
  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canCreateTasks, canAssignTasks, canCompleteTasks } = useCanManageTasks(familyId);

  if (tasksLoading || membersLoading || authLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie zadań...</p>
        </div>
      </div>
    );
  }

  if (error || !familyMember) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">{error || 'Brak dostępu do tej rodziny'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <TasksDashboard
        familyId={familyId}
        tasks={tasks}
        currentMember={familyMember}
        familyMembers={members}
        canManageTasks={canCreateTasks}
        canAssignTasks={canAssignTasks}
        canCompleteTasks={canCompleteTasks}
        onTaskUpdate={refetch}
      />
    </div>
  );
}
