'use client';

import React from 'react';
import { useFamilyRole, useCanManageFamily } from '@/hooks/useAuth';
import { InviteMemberDialog } from '@/components/family/InviteMemberDialog';
import { UserInvitationsDialog } from '@/components/family/UserInvitationsDialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { UserPlus, Mail } from 'lucide-react';

interface InvitationsPageProps {
  params: Promise<{ familyId: string }>;
}

export default function InvitationsPage({ params }: InvitationsPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canManageMembers } = useCanManageFamily(familyId);

  if (authLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (!familyMember) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">Brak dostępu do tej rodziny</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Zaproś nowego członka
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              {canManageMembers
                ? 'Wyślij zaproszenie do rodziny nowej osobie.'
                : 'Tylko właściciele i rodzice mogą zapraszać nowych członków.'}
            </p>
            {canManageMembers ? (
              <InviteMemberDialog
                familyId={familyId}
                trigger={
                  <Button className="w-full">
                    <UserPlus className="mr-2 h-4 w-4" />
                    Wyślij zaproszenie
                  </Button>
                }
                onInviteSent={() => {}}
              />
            ) : (
              <Button disabled className="w-full">
                <UserPlus className="mr-2 h-4 w-4" />
                Wyślij zaproszenie
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Sprawdź otrzymane zaproszenia
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Sprawdź czy otrzymałeś jakieś zaproszenia do innych rodzin.
            </p>
            <UserInvitationsDialog
              trigger={
                <Button variant="outline" className="w-full">
                  <Mail className="mr-2 h-4 w-4" />
                  Sprawdź zaproszenia
                </Button>
              }
              onInvitationAccepted={() => window.location.reload()}
            />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informacje o zaproszeniach</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                1
              </div>
              <div>
                <strong>Wysyłanie zaproszeń:</strong> Tylko właściciele i rodzice mogą zapraszać
                nowych członków do rodziny.
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                2
              </div>
              <div>
                <strong>Otrzymywanie zaproszeń:</strong> Zaproszenia są wysyłane na adres email.
                Możesz je zaakceptować lub odrzucić.
              </div>
            </div>
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                3
              </div>
              <div>
                <strong>Ważność zaproszeń:</strong> Zaproszenia wygasają po 7 dniach od wysłania.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
