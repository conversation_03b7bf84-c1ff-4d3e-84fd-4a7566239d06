'use client';

import React from 'react';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole, useCanManageFamily } from '@/hooks/useAuth';
import { FamilyMembersList } from '@/components/family/FamilyMembersList';

interface MembersPageProps {
  params: Promise<{ familyId: string }>;
}

export default function MembersPage({ params }: MembersPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  const { members, loading, error } = useFamilyMembers(familyId);
  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canManageMembers } = useCanManageFamily(familyId);

  if (loading || authLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie członków...</p>
        </div>
      </div>
    );
  }

  if (error || !familyMember) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">{error || 'Brak dostępu do tej rodziny'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <FamilyMembersList
        familyId={familyId}
        members={members}
        currentMember={familyMember}
        canManageMembers={canManageMembers}
      />
    </div>
  );
}
