'use client';

import { AuthGuard } from '@/components/auth/AuthGuard';
import { DashboardLayout as DashLayout } from '@/components/layout/DashboardLayout';
import { useAuth, useUserFamilies } from '@/hooks/useAuth';

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const { families, loading } = useUserFamilies();

  // Wybierz pierwszą dostępną rodzinę jako currentFamilyId
  const currentFamilyId = families?.[0]?.family?.id;

  // Pokaż loading gdy dane się ładują
  if (loading) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <DashLayout
        user={user}
        families={families}
        currentFamilyId={currentFamilyId}
        title="Dashboard"
      >
        {children}
      </DashLayout>
    </AuthGuard>
  );
}
