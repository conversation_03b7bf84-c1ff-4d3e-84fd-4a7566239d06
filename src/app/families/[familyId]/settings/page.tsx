'use client';

import React, { useState, useEffect } from 'react';
import { useFamilyRole, useCanManageFamily } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Save, Users, Calendar } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SettingsPageProps {
  params: Promise<{ familyId: string }>;
}

interface FamilySettings {
  id: string;
  name: string;
  description: string | null;
  createdAt: string;
  updatedAt: string;
  _count: {
    members: number;
    tasks: number;
  };
}

export default function SettingsPage({ params }: SettingsPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canManageFamily } = useCanManageFamily(familyId);
  const { toast } = useToast();

  const [settings, setSettings] = useState<FamilySettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/families/${familyId}/settings`);

        if (!response.ok) {
          if (response.status === 403) {
            throw new Error('Brak uprawnień do przeglądania ustawień rodziny');
          } else if (response.status === 404) {
            throw new Error('Rodzina nie została znaleziona');
          }
          throw new Error('Błąd podczas pobierania ustawień');
        }

        const data = await response.json();
        setSettings(data);
        setFormData({
          name: data.name,
          description: data.description || '',
        });
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Wystąpił błąd');
      } finally {
        setLoading(false);
      }
    };

    if (familyId) {
      fetchSettings();
    }
  }, [familyId]);

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch(`/api/families/${familyId}/settings`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Błąd podczas zapisywania ustawień');
      }

      const updatedSettings = await response.json();
      setSettings(updatedSettings);

      toast({
        title: 'Sukces',
        description: 'Ustawienia rodziny zostały zaktualizowane',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Wystąpił błąd';
      setError(errorMessage);
      toast({
        title: 'Błąd',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie ustawień...</p>
        </div>
      </div>
    );
  }

  if (!familyMember) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">Brak dostępu do tej rodziny</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-6 max-w-2xl">
        {/* Informacje o rodzinie */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Informacje o rodzinie
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Nazwa rodziny
              </label>
              <Input
                id="name"
                value={formData.name}
                onChange={e => setFormData({ ...formData, name: e.target.value })}
                disabled={!canManageFamily}
                placeholder="Wprowadź nazwę rodziny"
              />
            </div>

            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Opis rodziny
              </label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={e => setFormData({ ...formData, description: e.target.value })}
                disabled={!canManageFamily}
                placeholder="Wprowadź opis rodziny (opcjonalnie)"
                rows={3}
              />
            </div>

            {canManageFamily && (
              <div className="flex justify-end">
                <Button onClick={handleSave} disabled={saving} className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {saving ? 'Zapisywanie...' : 'Zapisz zmiany'}
                </Button>
              </div>
            )}

            {!canManageFamily && (
              <p className="text-sm text-gray-500">
                Tylko właściciele i rodzice mogą edytować ustawienia rodziny.
              </p>
            )}
          </CardContent>
        </Card>

        {/* Statystyki rodziny */}
        {settings && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Statystyki rodziny
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{settings._count.members}</div>
                  <div className="text-sm text-blue-600">Członkowie</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{settings._count.tasks}</div>
                  <div className="text-sm text-green-600">Zadania</div>
                </div>
              </div>

              <div className="mt-4 text-sm text-gray-600">
                <p>Rodzina utworzona: {new Date(settings.createdAt).toLocaleDateString('pl-PL')}</p>
                <p>
                  Ostatnia aktualizacja: {new Date(settings.updatedAt).toLocaleDateString('pl-PL')}
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
