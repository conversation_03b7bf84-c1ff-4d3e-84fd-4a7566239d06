'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { TaskForm } from '@/components/tasks/TaskForm/index';
import { TaskMobileForm } from '@/components/tasks/TaskMobileForm';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole, useCanManageTasks } from '@/hooks/useAuth';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import { useTask } from '@/hooks/useTasks';

interface TaskEditPageProps {
  params: Promise<{
    familyId: string;
    taskId: string;
  }>;
}

export default function TaskEditPage({ params }: TaskEditPageProps) {
  const resolvedParams = React.use(params);
  const { familyId, taskId } = resolvedParams;
  const router = useRouter();
  const isMobile = useMediaQuery('(max-width: 768px)');

  const { familyMember } = useFamilyRole(familyId);
  const { canCreateTasks } = useCanManageTasks(familyId);
  const { members, loading: membersLoading } = useFamilyMembers(familyId);
  const { task, loading: taskLoading, error } = useTask(familyId, taskId);

  const handleClose = () => {
    router.push(`/families/${familyId}/tasks`);
  };

  const handleSuccess = () => {
    router.push(`/families/${familyId}/tasks`);
  };

  // Loading state
  if (taskLoading || membersLoading) {
    return (
      <div className="max-w-4xl mx-auto space-y-6 p-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleClose}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Powrót do zadań
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Edytuj zadanie</h1>
            <p className="text-gray-600">Ładowanie...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error or access denied
  if (error || !familyMember || !canCreateTasks || !task) {
    return (
      <div className="max-w-4xl mx-auto space-y-6 p-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={handleClose}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Powrót do zadań
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Błąd</h1>
            <p className="text-gray-600">
              {!canCreateTasks
                ? 'Brak uprawnień do edycji zadań'
                : !task
                  ? 'Zadanie nie zostało znalezione'
                  : 'Wystąpił błąd podczas ładowania zadania'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Use mobile form for mobile devices
  if (isMobile) {
    return (
      <TaskMobileForm
        familyId={familyId}
        familyMembers={members}
        mode="edit"
        task={task}
        onClose={handleClose}
        onTaskSuccess={handleSuccess}
      />
    );
  }

  // Desktop layout
  return (
    <div className="max-w-4xl mx-auto space-y-6 p-4">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={handleClose}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Powrót do zadań
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Edytuj zadanie</h1>
          <p className="text-gray-600">Wprowadź zmiany w zadaniu: {task.title}</p>
        </div>
      </div>

      {/* Form Card */}
      <Card>
        <CardHeader>
          <CardTitle>Szczegóły zadania</CardTitle>
        </CardHeader>
        <CardContent>
          <TaskForm
            familyId={familyId}
            familyMembers={members}
            mode="edit"
            task={task}
            isMobile={false}
            onClose={handleClose}
            onSuccess={handleSuccess}
          />
        </CardContent>
      </Card>
    </div>
  );
}
