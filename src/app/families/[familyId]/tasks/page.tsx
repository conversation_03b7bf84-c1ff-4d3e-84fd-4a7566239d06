'use client';

import React from 'react';
import { useSearchParams } from 'next/navigation';
import { useTasks } from '@/hooks/useTasks';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole, useCanManageTasks } from '@/hooks/useAuth';
import { TasksDashboard } from '@/components/tasks/TasksDashboard';
import { TaskList } from '@/components/tasks/TaskList';
import { TaskVerificationPanel } from '@/components/tasks/TaskVerificationPanel';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface TasksPageProps {
  params: Promise<{ familyId: string }>;
}

export default function TasksPage({ params }: TasksPageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;
  const searchParams = useSearchParams();
  const view = searchParams.get('view');
  const status = searchParams.get('status');

  const { tasks, loading: tasksLoading, error, refetch, updateTaskLocally } = useTasks(familyId);
  const { members, loading: membersLoading } = useFamilyMembers(familyId);
  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canCreateTasks, canAssignTasks, canCompleteTasks } = useCanManageTasks(familyId);

  if (tasksLoading || membersLoading || authLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie zadań...</p>
        </div>
      </div>
    );
  }

  if (error || !familyMember) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">{error || 'Brak dostępu do tej rodziny'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {view === 'all' && (
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}/tasks`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do dashboard zadań
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">
            {view === 'all'
              ? 'Wszystkie zadania'
              : status === 'pending_verification'
                ? 'Zadania do weryfikacji'
                : 'Zadania rodziny'}
          </h1>
        </div>
      )}

      {status === 'pending_verification' ? (
        <TaskVerificationPanel
          familyId={familyId}
          tasks={tasks}
          currentMember={familyMember}
          familyMembers={members}
          canManageTasks={canCreateTasks}
          canAssignTasks={canAssignTasks}
          canCompleteTasks={canCompleteTasks}
          onTaskUpdate={refetch}
          updateTaskLocally={updateTaskLocally}
        />
      ) : view === 'all' || status ? (
        <TaskList
          familyId={familyId}
          tasks={tasks}
          currentMember={familyMember}
          familyMembers={members}
          canManageTasks={canCreateTasks}
          canAssignTasks={canAssignTasks}
          canCompleteTasks={canCompleteTasks}
          onTaskUpdate={refetch}
          updateTaskLocally={updateTaskLocally}
        />
      ) : (
        <TasksDashboard
          familyId={familyId}
          tasks={tasks}
          currentMember={familyMember}
          familyMembers={members}
          canManageTasks={canCreateTasks}
          canAssignTasks={canAssignTasks}
          canCompleteTasks={canCompleteTasks}
          onTaskUpdate={refetch}
          updateTaskLocally={updateTaskLocally}
        />
      )}
    </div>
  );
}
