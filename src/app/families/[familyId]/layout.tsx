'use client';

import React from 'react';
import { useAuth, useUserFamilies } from '@/hooks/useAuth';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

interface FamilyIdLayoutProps {
  children: React.ReactNode;
  params: Promise<{ familyId: string }>;
}

export default function FamilyIdLayout({ children, params }: FamilyIdLayoutProps) {
  const { user } = useAuth();
  const { families, loading } = useUserFamilies();

  // Extract familyId from URL parameters
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  if (loading) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </AuthGuard>
    );
  }

  if (!user) {
    return (
      <AuthGuard>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <DashboardLayout user={user} families={families} currentFamilyId={familyId} title="Rodzina">
        {children}
      </DashboardLayout>
    </AuthGuard>
  );
}
