'use client';

import React from 'react';
import { redirect } from 'next/navigation';

interface InvitePageProps {
  params: Promise<{ familyId: string }>;
}

export default function InvitePage({ params }: InvitePageProps) {
  const resolvedParams = React.use(params);
  const { familyId } = resolvedParams;

  // Redirect to the correct invitations page
  React.useEffect(() => {
    redirect(`/families/${familyId}/invitations`);
  }, [familyId]);

  return null;
}
