'use client';

import React from 'react';
import { useFamilyMembers } from '@/hooks/useFamily';
import { useFamilyRole, useCanManageFamily } from '@/hooks/useAuth';
import { ChangeMemberRoleDialog } from '@/components/family/ChangeMemberRoleDialog';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getRoleDisplayName } from '@/lib/auth';

interface EditMemberPageProps {
  params: Promise<{ familyId: string; memberId: string }>;
}

export default function EditMemberPage({ params }: EditMemberPageProps) {
  const resolvedParams = React.use(params);
  const { familyId, memberId } = resolvedParams;

  const { members, loading, error } = useFamilyMembers(familyId);
  const { familyMember, loading: authLoading } = useFamilyRole(familyId);
  const { canManageMembers } = useCanManageFamily(familyId);

  const member = members?.find(m => m.id === memberId);

  if (loading || authLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}/members`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do członków
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Edycja członka</h1>
        </div>

        <div className="text-center py-12">
          <p className="text-gray-500">Ładowanie...</p>
        </div>
      </div>
    );
  }

  if (error || !familyMember || !member) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}/members`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do członków
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Edycja członka</h1>
        </div>

        <div className="text-center py-12">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
            <p className="text-red-700">
              {!member ? 'Nie znaleziono członka rodziny' : error || 'Brak dostępu do tej rodziny'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!canManageMembers) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Link href={`/families/${familyId}/members`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Powrót do członków
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">Edycja członka</h1>
        </div>

        <div className="text-center py-12">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-yellow-900 mb-2">Brak uprawnień</h3>
            <p className="text-yellow-700">
              Tylko właściciele i rodzice mogą edytować role członków rodziny.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const getInitials = () => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName[0]}${member.user.lastName[0]}`;
    }
    return member.user.email[0].toUpperCase();
  };

  const getDisplayName = () => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    return member.user.email.split('@')[0];
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href={`/families/${familyId}/members`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Powrót do członków
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">Edycja członka</h1>
      </div>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white font-semibold text-lg">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <h2 className="text-xl font-semibold text-gray-900 truncate">{getDisplayName()}</h2>
                <p className="text-sm text-gray-600 truncate">{member.user.email}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="outline">{getRoleDisplayName(member.role)}</Badge>
                  <span className="text-sm text-gray-500">{member.points} punktów</span>
                  {member.isAdult && (
                    <Badge variant="secondary" className="text-xs">
                      Dorosły
                    </Badge>
                  )}
                </div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-4">Zarządzanie rolą</h3>
              <p className="text-gray-600 mb-4">
                Zmień rolę i uprawnienia tego członka rodziny. Różne role mają różne uprawnienia w
                zarządzaniu rodziną i zadaniami.
              </p>

              <ChangeMemberRoleDialog
                familyId={familyId}
                member={member}
                currentUserRole={familyMember.role}
                onSuccess={() => window.location.reload()}
                trigger={<Button className="w-full sm:w-auto">Zmień rolę i uprawnienia</Button>}
              />
            </div>

            <div className="border-t pt-6">
              <h3 className="text-lg font-medium mb-2">Informacje o rolach</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="grid gap-3 sm:grid-cols-2">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-1">Właściciel</h4>
                    <p>Pełne uprawnienia do zarządzania rodziną, członkami i zadaniami</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-1">Rodzic</h4>
                    <p>Może zarządzać członkami, tworzyć i weryfikować zadania</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-1">Opiekun</h4>
                    <p>Może tworzyć i weryfikować zadania, ale nie zarządzać członkami</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <h4 className="font-semibold text-gray-900 mb-1">Dziecko</h4>
                    <p>Może wykonywać przydzielone zadania i zbierać punkty</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
