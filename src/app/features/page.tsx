import { Metadata } from 'next';
import { Header } from '@/components/layout/Header';
import { FeaturesSection } from '@/components/landing/FeaturesSection';
import { Container } from '@/components/ui/Grid';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Funkcje - FamilyTasks',
  description:
    'Odkryj pełen zestaw narzędzi, które pomogą Twojej rodzinie lepiej organizować codzienne obowiązki.',
};

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-purple-600 to-purple-800">
        <Container size="xl">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-display font-bold text-white leading-tight">
                Funk<PERSON>je FamilyTasks
              </h1>
              <p className="text-xl lg:text-2xl text-purple-100 max-w-4xl mx-auto leading-relaxed">
                Odkryj pełen zestaw narzędzi, które pomogą Twojej rodzinie lepiej organizować
                codzienne obowiązki.
              </p>
            </div>
          </div>
        </Container>
      </section>

      {/* Features Content */}
      <FeaturesSection />

      {/* Additional CTA Section */}
      <section className="py-20 bg-gray-50">
        <Container size="lg">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Gotowy na lepszą organizację rodziny?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Dołącz do tysięcy rodzin, które już odkryły jak proste może być zarządzanie domowymi
                obowiązkami z FamilyTasks.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/auth/sign-up">
                  Rozpocznij 14-dniowy trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/about">Poznaj naszą historię</Link>
              </Button>
            </div>
          </div>
        </Container>
      </section>
    </div>
  );
}
