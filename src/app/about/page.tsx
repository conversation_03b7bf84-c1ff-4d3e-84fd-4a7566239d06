import { Metadata } from 'next';
import { Header } from '@/components/layout/Header';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Users, GraduationCap, Calendar, ArrowRight } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'O nas - FamilyTasks',
  description:
    'Poznaj naszą misję i zespół, który tworzy rozwiązania wspierające organizację życia rodzinnego.',
};

export default function AboutPage() {
  const values = [
    {
      icon: Users,
      title: 'Współpraca',
      description: 'Promowanie współpracy i wspólnej odpowiedzialności',
    },
    {
      icon: GraduationCap,
      title: 'Edukacja',
      description: 'Budowanie odpowiedzialności i samodzielności u dzieci poprzez',
    },
    {
      icon: Calendar,
      title: 'Organizacja',
      description: 'Usprawnienie organizacji życia rodzinnego i redukcja stresu',
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-purple-600 to-purple-800">
        <Container size="xl">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-display font-bold text-white leading-tight">
                O FamilyTasks
              </h1>
              <p className="text-xl lg:text-2xl text-purple-100 max-w-4xl mx-auto leading-relaxed">
                Poznaj naszą misję i zespół, który tworzy rozwiązania wspierające organizację życia
                rodzinnego.
              </p>
            </div>
          </div>
        </Container>
      </section>

      {/* History Section */}
      <section className="py-20 lg:py-32 bg-white">
        <Container size="lg">
          <div className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Nasza historia
              </h2>
            </div>

            <div className="prose prose-lg prose-gray max-w-4xl mx-auto">
              <p className="text-gray-600 leading-relaxed">
                FamilyTasks powstało w 2023 roku z prostej obserwacji: wiele rodzin zmaga się z
                organizacją codziennych obowiązków domowych. Jako rodzice, sami doświadczyliśmy
                frustracji związanej z ciągłym przypominaniem o zadaniach do wykonania i
                trudnościami w sprawiedliwym podziale obowiązków.
              </p>

              <p className="text-gray-600 leading-relaxed">
                Nasz zespół programistów i projektantów UX, będących również rodzicami, połączył
                siły, aby stworzyć aplikację, która rzeczywiście rozwiązuje problemy, z którymi sami
                się mierzyli. Po miesiącach badań, rozmów z rodzinami i iteracji projektowych,
                powstało FamilyTasks - kompletny system do organizacji zadań domowych.
              </p>

              <p className="text-gray-600 leading-relaxed">
                Dziś pomagamy tysiącom rodzin w Polsce lepiej organizować codzienne życie, budować
                zdrowe nawyki i uczyć dzieci odpowiedzialności przy jednoczesnym zmniejszeniu liczby
                konfliktów związanych z obowiązkami domowymi.
              </p>
            </div>
          </div>
        </Container>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-gray-50">
        <Container size="lg">
          <div className="space-y-12">
            <div className="text-center space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Nasza misja
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Wierzymy, że zorganizowane i sprawiedliwie podzielone obowiązki domowe to klucz do
                harmonijnego życia rodzinnego.
              </p>
            </div>
          </div>
        </Container>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-white">
        <Container size="lg">
          <div className="space-y-16">
            <div className="text-center space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Nasze wartości
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Trzy filary, na których opieramy nasze rozwiązania
              </p>
            </div>

            <Grid cols={1} mdCols={3} gap="lg">
              {values.map(value => (
                <GridItem key={value.title}>
                  <Card className="text-center p-8 h-full transition-all hover:shadow-lg hover:scale-105">
                    <div className="space-y-6">
                      <div className="mx-auto w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center">
                        <value.icon className="h-8 w-8 text-purple-600" />
                      </div>

                      <div className="space-y-3">
                        <h3 className="text-xl font-display font-semibold text-gray-900">
                          {value.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed">{value.description}</p>
                      </div>
                    </div>
                  </Card>
                </GridItem>
              ))}
            </Grid>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-purple-50">
        <Container size="lg">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl lg:text-4xl font-display font-bold text-gray-900">
                Dołącz do naszej społeczności
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Rozpocznij swoją podróż z FamilyTasks i odkryj, jak proste może być zorganizowane
                życie rodzinne.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/auth/sign-up">
                  Rozpocznij za darmo
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 text-lg font-semibold transition-all hover:scale-105"
                asChild
              >
                <Link href="/features">Zobacz funkcje</Link>
              </Button>
            </div>
          </div>
        </Container>
      </section>
    </div>
  );
}
