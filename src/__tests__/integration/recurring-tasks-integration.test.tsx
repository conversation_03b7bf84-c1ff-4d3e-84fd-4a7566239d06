/**
 * Testy integracyjne dla zadań cyklicznych
 * Testują współpracę komponentów, formularzy i logiki biznesowej
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskForm } from '@/components/tasks/TaskForm';
import RecurringTaskMarker from '@/components/calendar/RecurringTaskMarker';
import EditRecurringTaskDialog from '@/components/tasks/EditRecurringTaskDialog';
import { CalendarTask } from '@/lib/validations/calendar';
import { taskToCalendarTask, combineTasksForCalendar } from '@/lib/utils/calendar-helpers';
import { RecurringTaskService } from '@/lib/services/recurring-task.service';
import { addDays, addWeeks } from 'date-fns';

import { useTaskMutations } from '@/hooks/useTasks';

// Mock dependencies
jest.mock('@/hooks/useTasks');
jest.mock('@/lib/utils/recurring-tasks', () => ({
    generateRecurringDates: jest.fn().mockReturnValue([
        { date: new Date('2025-01-01'), isActive: true },
        { date: new Date('2025-01-02'), isActive: true },
        { date: new Date('2025-01-03'), isActive: true },
    ]),
    parseIntervalFromRRule: jest.fn().mockReturnValue(1),
    getNextTaskOccurrence: jest.fn().mockReturnValue(new Date('2025-01-04')),
}));

const mockFamilyMembers = [
    {
        id: 'member-1',
        familyId: 'family-1',
        userId: 'user-1',
        role: 'PARENT',
        user: {
            id: 'user-1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
        },
    },
];

const mockRecurringTask: CalendarTask = {
    id: 'task-1',
    title: 'Codzienne sprzątanie',
    description: 'Codzienne sprzątanie kuchni',
    start: new Date('2025-01-01T10:00:00'),
    end: new Date('2025-01-01T11:00:00'),
    allDay: false,
    color: '#3b82f6',
    type: 'task',
    isRecurring: true,
    frequency: 'DAILY',
    recurringInterval: 1,
    originalTaskId: undefined,
    recurrenceRule: 'FREQ=DAILY',
    assignments: [],
};

describe('Recurring Tasks Integration', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('TaskForm Integration', () => {
        it('should create recurring task with simple interval', async () => {
            const mockCreateTask = jest.fn().mockResolvedValue({});
            const mockOnClose = jest.fn();
            const mockOnSuccess = jest.fn();

            // Mock useTaskMutations hook
            (useTaskMutations as jest.Mock).mockReturnValue({
                createTask: mockCreateTask,
                updateTask: jest.fn(),
                loading: false,
                error: null,
            });

            const user = userEvent.setup();

            render(
                <TaskForm
                    familyId="family-1"
                    familyMembers={mockFamilyMembers}
                    mode="create"
                    onClose={mockOnClose}
                    onSuccess={mockOnSuccess}
                />
            );

            // Wypełnij podstawowe informacje
            await user.type(screen.getByLabelText(/tytuł/i), 'Codzienne sprzątanie');
            await user.type(screen.getByLabelText(/opis/i), 'Sprzątanie kuchni');

            // Ustaw częstotliwość na codziennie
            const frequencySelect = screen.getByLabelText(/częstotliwość/i);
            await user.click(frequencySelect);
            await user.click(screen.getByText('Codziennie'));

            // Sprawdź czy pojawiło się pole interwału
            expect(screen.getByLabelText(/co ile dni/i)).toBeInTheDocument();

            // Ustaw interwał na co 3 dni
            const intervalSelect = screen.getByLabelText(/co ile dni/i);
            await user.click(intervalSelect);
            await user.click(screen.getByText('Co 3 dni'));

            // Sprawdź czy pokazuje się feedback o cykliczności
            expect(screen.getByText(/Będzie powtarzane co 3 dni/i)).toBeInTheDocument();

            // Zapisz zadanie
            const submitButton = screen.getByRole('button', { name: /zapisz/i });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockCreateTask).toHaveBeenCalledWith(
                    expect.objectContaining({
                        title: 'Codzienne sprzątanie',
                        description: 'Sprzątanie kuchni',
                        frequency: 'DAILY',
                        recurrenceRule: 'FREQ=DAILY;INTERVAL=3',
                    })
                );
            });
        });

        it('should handle weekly task with custom interval', async () => {
            const mockCreateTask = jest.fn().mockResolvedValue({});
            const mockOnClose = jest.fn();

            (useTaskMutations as jest.Mock).mockReturnValue({
                createTask: mockCreateTask,
                updateTask: jest.fn(),
                loading: false,
                error: null,
            });

            const user = userEvent.setup();

            render(
                <TaskForm
                    familyId="family-1"
                    familyMembers={mockFamilyMembers}
                    mode="create"
                    onClose={mockOnClose}
                />
            );

            await user.type(screen.getByLabelText(/tytuł/i), 'Zakupy');

            // Ustaw częstotliwość na tygodniowo
            const frequencySelect = screen.getByLabelText(/częstotliwość/i);
            await user.click(frequencySelect);
            await user.click(screen.getByText('Tygodniowo'));

            // Ustaw interwał na co 2 tygodnie
            const intervalSelect = screen.getByLabelText(/co ile tygodni/i);
            await user.click(intervalSelect);
            await user.click(screen.getByText('Co 2 tygodnie'));

            const submitButton = screen.getByRole('button', { name: /zapisz/i });
            await user.click(submitButton);

            await waitFor(() => {
                expect(mockCreateTask).toHaveBeenCalledWith(
                    expect.objectContaining({
                        frequency: 'WEEKLY',
                        recurrenceRule: 'FREQ=WEEKLY;INTERVAL=2',
                    })
                );
            });
        });

        it('should show RecurrenceSection for CUSTOM frequency', async () => {
            const mockOnClose = jest.fn();

            (useTaskMutations as jest.Mock).mockReturnValue({
                createTask: jest.fn(),
                updateTask: jest.fn(),
                loading: false,
                error: null,
            });

            const user = userEvent.setup();

            render(
                <TaskForm
                    familyId="family-1"
                    familyMembers={mockFamilyMembers}
                    mode="create"
                    onClose={mockOnClose}
                />
            );

            // Ustaw częstotliwość na niestandardowe
            const frequencySelect = screen.getByLabelText(/częstotliwość/i);
            await user.click(frequencySelect);
            await user.click(screen.getByText('Niestandardowe'));

            // Sprawdź czy pojawiła się sekcja zaawansowana
            expect(screen.getByText(/Ustawienia powtarzania/i)).toBeInTheDocument();
            expect(screen.getByText(/Prosty/i)).toBeInTheDocument();
            expect(screen.getByText(/Zaawansowany/i)).toBeInTheDocument();
        });
    });

    describe('RecurringTaskMarker Integration', () => {
        it('should render correctly for daily task', () => {
            const mockOnClick = jest.fn();

            render(
                <RecurringTaskMarker
                    task={mockRecurringTask}
                    date={mockRecurringTask.start}
                    size="md"
                    showTitle={true}
                    onClick={mockOnClick}
                />
            );

            expect(screen.getByText('Codzienne sprzątanie')).toBeInTheDocument();
            expect(screen.getByText('10:00')).toBeInTheDocument(); // Badge z czasem
        });

        it('should show tooltip with task details', async () => {
            const mockOnClick = jest.fn();
            const user = userEvent.setup();

            render(
                <RecurringTaskMarker
                    task={mockRecurringTask}
                    date={mockRecurringTask.start}
                    size="md"
                    showTitle={true}
                    onClick={mockOnClick}
                />
            );

            // Hover nad markerem
            const marker = screen.getByText('Codzienne sprzątanie').closest('div');
            await user.hover(marker!);

            await waitFor(() => {
                expect(screen.getAllByText('Codzienne sprzątanie')).toHaveLength(2); // Marker + tooltip
                expect(screen.getByText('Codziennie')).toBeInTheDocument();
            });
        });

        it('should handle click events', async () => {
            const mockOnClick = jest.fn();
            const user = userEvent.setup();

            render(
                <RecurringTaskMarker
                    task={mockRecurringTask}
                    date={mockRecurringTask.start}
                    onClick={mockOnClick}
                />
            );

            const marker = screen.getByText('Codzienne sprzątanie').closest('div');
            await user.click(marker!);

            expect(mockOnClick).toHaveBeenCalledWith(mockRecurringTask);
        });
    });

    describe('EditRecurringTaskDialog Integration', () => {
        const mockTaskOccurrence: CalendarTask = {
            ...mockRecurringTask,
            id: 'task-1-occurrence-1641024000000',
            originalTaskId: 'task-1',
        };

        it('should show edit options for recurring task occurrence', () => {
            const mockOnEditOccurrence = jest.fn();
            const mockOnEditSeries = jest.fn();
            const mockOnClose = jest.fn();

            render(
                <EditRecurringTaskDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    task={mockTaskOccurrence}
                    onEditOccurrence={mockOnEditOccurrence}
                    onEditSeries={mockOnEditSeries}
                />
            );

            expect(screen.getByText('Edycja zadania cyklicznego')).toBeInTheDocument();
            expect(screen.getByText('Edytuj tylko to wystąpienie')).toBeInTheDocument();
            expect(screen.getByText('Edytuj całą serię')).toBeInTheDocument();
        });

        it('should handle occurrence edit selection', async () => {
            const mockOnEditOccurrence = jest.fn();
            const mockOnEditSeries = jest.fn();
            const mockOnClose = jest.fn();
            const user = userEvent.setup();

            render(
                <EditRecurringTaskDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    task={mockTaskOccurrence}
                    onEditOccurrence={mockOnEditOccurrence}
                    onEditSeries={mockOnEditSeries}
                />
            );

            // Wybierz edycję wystąpienia
            const occurrenceOption = screen.getByText('Edytuj tylko to wystąpienie').closest('div');
            await user.click(occurrenceOption!);

            // Potwierdź
            const confirmButton = screen.getByText('Edytuj wystąpienie');
            await user.click(confirmButton);

            expect(mockOnEditOccurrence).toHaveBeenCalledWith(mockTaskOccurrence);
            expect(mockOnClose).toHaveBeenCalled();
        });

        it('should handle series edit selection', async () => {
            const mockOnEditOccurrence = jest.fn();
            const mockOnEditSeries = jest.fn();
            const mockOnClose = jest.fn();
            const user = userEvent.setup();

            render(
                <EditRecurringTaskDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    task={mockTaskOccurrence}
                    onEditOccurrence={mockOnEditOccurrence}
                    onEditSeries={mockOnEditSeries}
                />
            );

            // Wybierz edycję serii
            const seriesOption = screen.getByText('Edytuj całą serię').closest('div');
            await user.click(seriesOption!);

            // Sprawdź ostrzeżenie
            expect(
                screen.getByText(/Edycja całej serii wpłynie na wszystkie przyszłe wystąpienia/i)
            ).toBeInTheDocument();

            // Potwierdź
            const confirmButton = screen.getByText('Edytuj serię');
            await user.click(confirmButton);

            expect(mockOnEditSeries).toHaveBeenCalledWith('task-1');
            expect(mockOnClose).toHaveBeenCalled();
        });
    });

    describe('Calendar Integration', () => {
        it('should combine one-time and recurring tasks correctly', () => {
            const oneTimeTasks = [
                {
                    id: 'onetime-1',
                    title: 'Jednorazowe zadanie',
                    frequency: 'ONCE',
                    startDate: new Date('2025-01-02T10:00:00Z'),
                    allDay: true,
                    assignments: [],
                },
            ];

            const recurringTasks = [
                {
                    id: 'recurring-1',
                    title: 'Codzienne zadanie',
                    frequency: 'DAILY',
                    startDate: new Date('2025-01-01T10:00:00Z'),
                    recurrenceRule: 'FREQ=DAILY',
                    allDay: true,
                    assignments: [],
                },
            ];

            const rangeStart = new Date('2025-01-01');
            const rangeEnd = new Date('2025-01-05');

            const combinedTasks = combineTasksForCalendar(
                oneTimeTasks as any,
                recurringTasks as any,
                rangeStart,
                rangeEnd
            );

            // Sprawdź czy mamy zadanie jednorazowe + 5 wystąpień cyklicznego
            expect(combinedTasks).toHaveLength(6);

            const oneTimeTask = combinedTasks.find(t => t.id === 'onetime-1');
            expect(oneTimeTask).toBeDefined();
            expect(oneTimeTask!.isRecurring).toBe(false);

            const recurringOccurrences = combinedTasks.filter(t => t.originalTaskId === 'recurring-1');
            expect(recurringOccurrences).toHaveLength(5);
        });

        it('should convert Task to CalendarTask with recurring info', () => {
            const dbTask = {
                id: 'task-1',
                title: 'Testowe zadanie',
                description: 'Opis zadania',
                frequency: 'WEEKLY',
                recurrenceRule: 'FREQ=WEEKLY;INTERVAL=2',
                startDate: new Date('2025-01-01T10:00:00Z'),
                endDate: new Date('2025-01-01T11:00:00Z'),
                allDay: false,
                color: '#ff0000',
                assignments: [],
            };

            const calendarTask = taskToCalendarTask(dbTask as any);

            expect(calendarTask.isRecurring).toBe(true);
            expect(calendarTask.frequency).toBe('WEEKLY');
            expect(calendarTask.recurringInterval).toBe(2);
            expect(calendarTask.recurrenceRule).toBe('FREQ=WEEKLY;INTERVAL=2');
        });
    });

    describe('Service Integration', () => {
        it('should create recurring task with correct RRULE', async () => {
            const mockPrisma = {
                task: {
                    create: jest.fn().mockResolvedValue({ id: 'task-1' }),
                },
            };

            const taskData = {
                familyId: 'family-1',
                title: 'Test task',
                frequency: 'DAILY' as const,
                interval: 3,
                startDateTime: new Date('2025-01-01T10:00:00'),
            };

            await RecurringTaskService.createRecurringTask(taskData, mockPrisma);

            expect(mockPrisma.task.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    title: 'Test task',
                    frequency: 'DAILY',
                    recurrenceRule: 'FREQ=DAILY;INTERVAL=3',
                }),
            });
        });

        it('should handle recurring task stats calculation', async () => {
            const mockPrisma = {
                task: {
                    findUnique: jest.fn().mockResolvedValue({
                        id: 'task-1',
                        frequency: 'DAILY',
                        startDate: new Date('2025-01-01T10:00:00'),
                        createdAt: new Date('2025-01-01T10:00:00'),
                        recurrenceRule: 'FREQ=DAILY',
                        completions: [{ id: 'completion-1' }, { id: 'completion-2' }],
                    }),
                },
            };

            const stats = await RecurringTaskService.getRecurringTaskStats('task-1', mockPrisma);

            expect(stats.completedOccurrences).toBe(2);
            expect(stats.totalOccurrences).toBe(3); // Mocked to return 3 dates
            expect(stats.nextOccurrence).toBeDefined();
        });
    });

    describe('Error Handling', () => {
        it('should handle invalid recurring task data gracefully', async () => {
            const mockPrisma = {
                task: {
                    create: jest.fn().mockRejectedValue(new Error('Invalid data')),
                },
            };

            const invalidTaskData = {
                familyId: 'family-1',
                title: '', // Invalid - empty title
                frequency: 'DAILY' as const,
                startDateTime: new Date('2025-01-01T10:00:00'),
            };

            await expect(
                RecurringTaskService.createRecurringTask(invalidTaskData, mockPrisma)
            ).rejects.toThrow('Invalid data');
        });

        it('should handle missing originalTaskId in edit dialog', () => {
            const mockTaskWithoutOriginal: CalendarTask = {
                ...mockRecurringTask,
                originalTaskId: undefined,
            };

            const mockOnEditOccurrence = jest.fn();
            const mockOnEditSeries = jest.fn();
            const mockOnClose = jest.fn();

            render(
                <EditRecurringTaskDialog
                    isOpen={true}
                    onClose={mockOnClose}
                    task={mockTaskWithoutOriginal}
                    onEditOccurrence={mockOnEditOccurrence}
                    onEditSeries={mockOnEditSeries}
                />
            );

            // Dialog powinien się renderować bez błędów
            expect(screen.getByText('Edycja zadania cyklicznego')).toBeInTheDocument();
        });
    });
});
