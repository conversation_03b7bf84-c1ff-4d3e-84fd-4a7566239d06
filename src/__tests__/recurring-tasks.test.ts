/**
 * <PERSON>y jednostkowe dla funkcjonalności zadań cyklicznych
 */

import {
  generateRecurringDates,
  isTaskActiveOnDate,
  getNextTaskOccurrence,
  parseIntervalFromRRule,
  createSimpleRRule,
  isRecurringTask,
} from '@/lib/utils/recurring-tasks';
import { RecurringTasksCache } from '@/lib/utils/recurring-tasks-cache';
import { addDays, addWeeks, addMonths, startOfDay } from 'date-fns';

describe('Recurring Tasks Utilities', () => {
  const baseDate = new Date('2025-01-01T10:00:00Z');

  describe('generateRecurringDates', () => {
    it('should generate daily occurrences', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const rangeStart = baseDate;
      const rangeEnd = addDays(baseDate, 5);

      const occurrences = generateRecurringDates(options, rangeStart, rangeEnd);

      expect(occurrences).toHaveLength(6); // 5 dni + dzień startowy
      expect(occurrences[0].date).toEqual(startOfDay(baseDate));
      expect(occurrences[1].date).toEqual(startOfDay(addDays(baseDate, 1)));
    });

    it('should generate daily occurrences with interval', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 3, // co 3 dni
      };

      const rangeStart = baseDate;
      const rangeEnd = addDays(baseDate, 10);

      const occurrences = generateRecurringDates(options, rangeStart, rangeEnd);

      expect(occurrences).toHaveLength(4); // dzień 0, 3, 6, 9
      expect(occurrences[1].date).toEqual(startOfDay(addDays(baseDate, 3)));
      expect(occurrences[2].date).toEqual(startOfDay(addDays(baseDate, 6)));
    });

    it('should generate weekly occurrences', () => {
      const options = {
        startDate: baseDate,
        frequency: 'WEEKLY' as const,
        interval: 1,
      };

      const rangeStart = baseDate;
      const rangeEnd = addWeeks(baseDate, 3);

      const occurrences = generateRecurringDates(options, rangeStart, rangeEnd);

      expect(occurrences).toHaveLength(4); // tydzień 0, 1, 2, 3
      expect(occurrences[1].date).toEqual(startOfDay(addWeeks(baseDate, 1)));
    });

    it('should respect end date', () => {
      const options = {
        startDate: baseDate,
        endDate: addDays(baseDate, 3),
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const rangeStart = baseDate;
      const rangeEnd = addDays(baseDate, 10);

      const occurrences = generateRecurringDates(options, rangeStart, rangeEnd);

      expect(occurrences).toHaveLength(4); // tylko do end date
    });

    it('should handle range outside task dates', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const rangeStart = addDays(baseDate, -10);
      const rangeEnd = addDays(baseDate, -5);

      const occurrences = generateRecurringDates(options, rangeStart, rangeEnd);

      expect(occurrences).toHaveLength(0);
    });
  });

  describe('isTaskActiveOnDate', () => {
    it('should return true for active daily task', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const testDate = addDays(baseDate, 5);
      const isActive = isTaskActiveOnDate(options, testDate);

      expect(isActive).toBe(true);
    });

    it('should return false for date before start', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const testDate = addDays(baseDate, -1);
      const isActive = isTaskActiveOnDate(options, testDate);

      expect(isActive).toBe(false);
    });

    it('should return false for date after end', () => {
      const options = {
        startDate: baseDate,
        endDate: addDays(baseDate, 5),
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const testDate = addDays(baseDate, 10);
      const isActive = isTaskActiveOnDate(options, testDate);

      expect(isActive).toBe(false);
    });

    it('should handle interval correctly', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 3,
      };

      expect(isTaskActiveOnDate(options, addDays(baseDate, 3))).toBe(true);
      expect(isTaskActiveOnDate(options, addDays(baseDate, 1))).toBe(false);
      expect(isTaskActiveOnDate(options, addDays(baseDate, 6))).toBe(true);
    });
  });

  describe('getNextTaskOccurrence', () => {
    it('should return next occurrence for daily task', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const afterDate = addDays(baseDate, 2);
      const nextOccurrence = getNextTaskOccurrence(options, afterDate);

      expect(nextOccurrence).toEqual(startOfDay(addDays(baseDate, 3)));
    });

    it('should return null for ONCE frequency', () => {
      const options = {
        startDate: baseDate,
        frequency: 'ONCE' as const,
        interval: 1,
      };

      const nextOccurrence = getNextTaskOccurrence(options, baseDate);

      expect(nextOccurrence).toBeNull();
    });

    it('should return start date if after date is before start', () => {
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const afterDate = addDays(baseDate, -5);
      const nextOccurrence = getNextTaskOccurrence(options, afterDate);

      expect(nextOccurrence).toEqual(startOfDay(baseDate));
    });

    it('should return null if past end date', () => {
      const options = {
        startDate: baseDate,
        endDate: addDays(baseDate, 5),
        frequency: 'DAILY' as const,
        interval: 1,
      };

      const afterDate = addDays(baseDate, 10);
      const nextOccurrence = getNextTaskOccurrence(options, afterDate);

      expect(nextOccurrence).toBeNull();
    });
  });

  describe('RRULE utilities', () => {
    it('should parse interval from RRULE', () => {
      expect(parseIntervalFromRRule('FREQ=DAILY;INTERVAL=3')).toBe(3);
      expect(parseIntervalFromRRule('FREQ=WEEKLY')).toBe(1);
      expect(parseIntervalFromRRule('')).toBe(1);
      expect(parseIntervalFromRRule(undefined)).toBe(1);
    });

    it('should create simple RRULE', () => {
      expect(createSimpleRRule('DAILY', 1)).toBe('FREQ=DAILY');
      expect(createSimpleRRule('WEEKLY', 2)).toBe('FREQ=WEEKLY;INTERVAL=2');
      expect(createSimpleRRule('ONCE', 1)).toBe('');
    });

    it('should identify recurring tasks', () => {
      expect(isRecurringTask('DAILY')).toBe(true);
      expect(isRecurringTask('WEEKLY')).toBe(true);
      expect(isRecurringTask('ONCE')).toBe(false);
    });
  });
});

describe('RecurringTasksCache', () => {
  const baseDate = new Date('2025-01-01T10:00:00Z');

  beforeEach(() => {
    RecurringTasksCache.clearCache();
  });

  describe('caching behavior', () => {
    it('should cache and retrieve occurrences', () => {
      const taskId = 'test-task-1';
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };
      const startDate = baseDate;
      const endDate = addDays(baseDate, 5);

      // Pierwsze wywołanie - cache miss
      const firstResult = RecurringTasksCache.getOccurrences(taskId, options, startDate, endDate);
      const stats1 = RecurringTasksCache.getStats();

      expect(stats1.misses).toBe(1);
      expect(stats1.hits).toBe(0);
      expect(firstResult).toHaveLength(6);

      // Drugie wywołanie - cache hit
      const secondResult = RecurringTasksCache.getOccurrences(taskId, options, startDate, endDate);
      const stats2 = RecurringTasksCache.getStats();

      expect(stats2.misses).toBe(1);
      expect(stats2.hits).toBe(1);
      expect(secondResult).toEqual(firstResult);
    });

    it('should invalidate cache for specific task', () => {
      const taskId = 'test-task-1';
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };
      const startDate = baseDate;
      const endDate = addDays(baseDate, 5);

      // Dodaj do cache
      RecurringTasksCache.getOccurrences(taskId, options, startDate, endDate);
      expect(RecurringTasksCache.getStats().entries).toBe(1);

      // Invaliduj
      RecurringTasksCache.invalidateTask(taskId);
      expect(RecurringTasksCache.getStats().entries).toBe(0);
    });

    it('should not cache very large ranges', () => {
      const taskId = 'test-task-1';
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };
      const startDate = baseDate;
      const endDate = addDays(baseDate, 400); // > 365 dni

      const result = RecurringTasksCache.getOccurrences(taskId, options, startDate, endDate);
      const stats = RecurringTasksCache.getStats();

      expect(stats.entries).toBe(0); // nie dodano do cache
      expect(result.length).toBeGreaterThan(0); // ale wynik został wygenerowany
    });
  });

  describe('TTL and expiration', () => {
    it('should respect TTL for different frequencies', () => {
      const dailyOptions = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };
      const monthlyOptions = {
        startDate: baseDate,
        frequency: 'MONTHLY' as const,
        interval: 1,
      };

      const startDate = baseDate;
      const endDate = addDays(baseDate, 30);

      RecurringTasksCache.getOccurrences('daily-task', dailyOptions, startDate, endDate);
      RecurringTasksCache.getOccurrences('monthly-task', monthlyOptions, startDate, endDate);

      const debugInfo = RecurringTasksCache.getDebugInfo();
      expect(debugInfo.entries).toHaveLength(2);

      // Miesięczne zadania powinny mieć dłuższy TTL
      const dailyEntry = debugInfo.entries.find(e => e.taskId === 'daily-task');
      const monthlyEntry = debugInfo.entries.find(e => e.taskId === 'monthly-task');

      expect(monthlyEntry!.expiresAt.getTime()).toBeGreaterThan(dailyEntry!.expiresAt.getTime());
    });
  });

  describe('performance and cleanup', () => {
    it('should cleanup expired entries', () => {
      // Dodaj entry które natychmiast wygasają (symulacja)
      const taskId = 'test-task-1';
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      RecurringTasksCache.getOccurrences(taskId, options, baseDate, addDays(baseDate, 5));
      expect(RecurringTasksCache.getStats().entries).toBe(1);

      // Symuluj upływ czasu (w rzeczywistości modyfikowalibyśmy wewnętrzny timestamp)
      RecurringTasksCache.cleanup();

      // Entry powinno nadal istnieć (TTL = 30min)
      expect(RecurringTasksCache.getStats().entries).toBe(1);
    });

    it('should calculate hit rate correctly', () => {
      const taskId = 'test-task-1';
      const options = {
        startDate: baseDate,
        frequency: 'DAILY' as const,
        interval: 1,
      };

      // 1 miss, 2 hits
      RecurringTasksCache.getOccurrences(taskId, options, baseDate, addDays(baseDate, 5));
      RecurringTasksCache.getOccurrences(taskId, options, baseDate, addDays(baseDate, 5));
      RecurringTasksCache.getOccurrences(taskId, options, baseDate, addDays(baseDate, 5));

      const stats = RecurringTasksCache.getStats();
      expect(stats.hitRate).toBe(66.67); // 2/3 * 100
    });
  });
});

describe('Edge cases and error handling', () => {
  const baseDate = new Date('2025-01-01T10:00:00Z');

  it('should handle invalid dates gracefully', () => {
    const options = {
      startDate: new Date('invalid'),
      frequency: 'DAILY' as const,
      interval: 1,
    };

    expect(() => {
      generateRecurringDates(options, baseDate, addDays(baseDate, 5));
    }).toThrow();
  });

  it('should handle zero or negative intervals', () => {
    const options = {
      startDate: baseDate,
      frequency: 'DAILY' as const,
      interval: 0,
    };

    const occurrences = generateRecurringDates(options, baseDate, addDays(baseDate, 5));
    expect(occurrences.length).toBeGreaterThan(0); // Powinno użyć domyślnego interval = 1
  });

  it('should handle very large intervals', () => {
    const options = {
      startDate: baseDate,
      frequency: 'DAILY' as const,
      interval: 999,
    };

    const occurrences = generateRecurringDates(options, baseDate, addDays(baseDate, 10));
    expect(occurrences).toHaveLength(1); // Tylko data startowa
  });
});
