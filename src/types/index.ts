export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Family {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  members: FamilyMember[];
  createdAt: Date;
  updatedAt: Date;
}

export interface FamilyMember {
  id: string;
  userId: string;
  familyId: string;
  role: 'parent' | 'child' | 'guardian';
  name: string;
  avatar?: string;
  joinedAt: Date;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in_progress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  dueDate?: Date;
  assigneeId?: string;
  assignedBy: string;
  familyId: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export interface Reward {
  id: string;
  title: string;
  description?: string;
  points: number;
  isActive: boolean;
  familyId: string;
  createdAt: Date;
  updatedAt: Date;
}
