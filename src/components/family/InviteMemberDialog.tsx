'use client';

import { useState } from 'react';
import { inviteMemberSchema } from '@/lib/validations/family';
import { FamilyRole } from '@prisma/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { UserPlus, Loader2, Mail, Shield, Users, Crown } from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';

interface InviteMemberDialogProps {
  familyId: string;
  trigger?: React.ReactNode;
  onSuccess?: (invitation: { id: string; email: string; role: FamilyRole }) => void;
  onInviteSent?: () => void;
}

export function InviteMemberDialog({
  familyId,
  trigger,
  onSuccess,
  onInviteSent,
}: InviteMemberDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    role: 'CHILD' as FamilyRole,
    isAdult: false,
  });
  const [errors, setErrors] = useState<{ email?: string; role?: string }>({});
  const { toast } = useToast();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate form data
      const validation = inviteMemberSchema.safeParse(formData);
      if (!validation.success) {
        const fieldErrors: { email?: string; role?: string } = {};
        validation.error.errors.forEach(error => {
          if (error.path[0] === 'email') fieldErrors.email = error.message;
          if (error.path[0] === 'role') fieldErrors.role = error.message;
        });
        setErrors(fieldErrors);
        return;
      }

      const response = await fetch(`/api/families/${familyId}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validation.data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się wysłać zaproszenia');
      }

      const invitation = await response.json();

      // Reset form and close dialog
      setFormData({ email: '', role: 'CHILD', isAdult: false });
      setIsOpen(false);

      // Call success callback or reload
      if (onSuccess) {
        onSuccess(invitation);
      } else {
        window.location.reload();
      }
    } catch (error) {
      console.error('Error inviting member:', error);
      toast({
        title: 'Błąd',
        description:
          error instanceof Error ? error.message : 'Wystąpił błąd podczas wysyłania zaproszenia',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setFormData({ email: '', role: 'CHILD', isAdult: false });
      setErrors({});
    }
  };

  const getRoleIcon = (role: FamilyRole) => {
    switch (role) {
      case 'OWNER':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'PARENT':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'GUARDIAN':
        return <Shield className="h-4 w-4 text-green-500" />;
      case 'CHILD':
        return <Users className="h-4 w-4 text-purple-500" />;
    }
  };

  const getRoleDescription = (role: FamilyRole) => {
    switch (role) {
      case 'OWNER':
        return 'Pełne uprawnienia - może zarządzać rodziną, członkami i wszystkimi zadaniami';
      case 'PARENT':
        return 'Może zarządzać członkami, tworzyć zadania i weryfikować ich wykonanie';
      case 'GUARDIAN':
        return 'Może tworzyć i weryfikować zadania, ale nie zarządzać członkami';
      case 'CHILD':
        return 'Może wykonywać przydzielone zadania i zbierać punkty';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <UserPlus className="mr-2 h-4 w-4" />
            Zaproś członka
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-blue-600" />
            Zaproś nowego członka
          </DialogTitle>
          <DialogDescription>
            Wyślij zaproszenie do dołączenia do rodziny na adres email.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={onSubmit} className="space-y-6">
          <div className="space-y-4">
            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">
                Adres email <span className="text-red-500">*</span>
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="email"
                  type="email"
                  placeholder="np. <EMAIL>"
                  value={formData.email}
                  onChange={e => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  disabled={isLoading}
                  className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
              <p className="text-xs text-gray-500">
                Osoba otrzyma email z linkiem do akceptacji zaproszenia
              </p>
            </div>

            {/* Role Selection */}
            <div className="space-y-2">
              <Label htmlFor="role">
                Rola w rodzinie <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value: FamilyRole) =>
                  setFormData(prev => ({ ...prev, role: value }))
                }
                disabled={isLoading}
              >
                <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Wybierz rolę" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(FamilyRole).map(role => (
                    <SelectItem key={role} value={role}>
                      <div className="flex items-center gap-2">
                        {getRoleIcon(role)}
                        <span>{getRoleDisplayName(role)}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.role && <p className="text-sm text-red-500">{errors.role}</p>}
            </div>

            {/* Role Description */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                {getRoleIcon(formData.role)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900">
                      {getRoleDisplayName(formData.role)}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {formData.role}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{getRoleDescription(formData.role)}</p>
                </div>
              </div>
            </div>

            {/* Adult Checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isAdult"
                checked={formData.isAdult}
                onCheckedChange={(checked: boolean) =>
                  setFormData(prev => ({ ...prev, isAdult: checked === true }))
                }
                disabled={isLoading}
              />
              <Label htmlFor="isAdult" className="text-sm">
                Ta osoba jest dorosła
              </Label>
            </div>
            <p className="text-xs text-gray-500 ml-6">
              Dorosli członkowie mają dostęp do dodatkowych funkcji i statystyk
            </p>
          </div>

          {/* Info Box */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Mail className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-blue-900 mb-1">
                  Co się stanie po wysłaniu zaproszenia?
                </h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Osoba otrzyma email z zaproszeniem</li>
                  <li>• Zaproszenie będzie ważne przez 7 dni</li>
                  <li>• Może zaakceptować lub odrzucić zaproszenie</li>
                  <li>• Po akceptacji zostanie dodana do rodziny</li>
                </ul>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              Anuluj
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Wysyłanie...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Wyślij zaproszenie
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
