'use client';

import { Family, FamilyMember, Task, User, TaskCompletion } from '@prisma/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Trophy,
  CheckCircle,
  Clock,
  Users,
  Calendar,
  TrendingUp,
  Plus,
  Star,
  Pause,
} from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';
import { useSuspensions } from '@/hooks/useSuspensions';

type TaskWithDetails = Task & {
  assignedTo?: FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName'>;
  };
  completions: TaskCompletion[];
};

type FamilyWithDetails = Family & {
  members: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  tasks: TaskWithDetails[];
  _count: {
    tasks: number;
    members: number;
  };
};

interface FamilyDashboardProps {
  family: FamilyWithDetails;
  currentMember: FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  };
}

export function FamilyDashboard({ family, currentMember }: FamilyDashboardProps) {
  const canCreateTasks = ['OWNER', 'PARENT', 'GUARDIAN'].includes(currentMember.role);
  const canInviteMembers = ['OWNER', 'PARENT'].includes(currentMember.role);
  const canManageSuspensions = ['OWNER', 'PARENT', 'GUARDIAN'].includes(currentMember.role);

  const { suspensionStats, getTaskSuspensionStatus } = useSuspensions(family.id);

  // Calculate statistics
  const activeTasks = family.tasks.filter(task => task.completions.length === 0);
  const completedTasks = family.tasks.filter(task => task.completions.length > 0);
  const myTasks = family.tasks.filter(task => task.assignedTo?.userId === currentMember.userId);
  const myActiveTasks = myTasks.filter(task => task.completions.length === 0);

  // Get suspended tasks count
  const suspendedTasks = family.tasks.filter(task => {
    const suspensionInfo = getTaskSuspensionStatus(task.id, task.assignedTo?.id);
    return suspensionInfo.isSuspended;
  }).length;

  const myRanking =
    family.members
      .sort((a, b) => b.points - a.points)
      .findIndex(member => member.id === currentMember.id) + 1;

  // Recent activity (last 5 completed tasks)
  const recentCompletions = family.tasks
    .filter(task => task.completions.length > 0)
    .sort((a, b) => {
      const aLatest = Math.max(...a.completions.map(c => new Date(c.completedAt).getTime()));
      const bLatest = Math.max(...b.completions.map(c => new Date(c.completedAt).getTime()));
      return bLatest - aLatest;
    })
    .slice(0, 5);

  // Weekly progress (mock data - would come from proper date filtering)
  const weeklyProgress = Math.round(
    (completedTasks.length / Math.max(family.tasks.length, 1)) * 100
  );

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Witaj, {currentMember.user.firstName || 'użytkowniku'}!
          </h1>
          <p className="text-gray-600">Oto co dzieje się w rodzinie &quot;{family.name}&quot;</p>
        </div>
        <div className="flex items-center gap-3">
          {canCreateTasks && (
            <Button asChild>
              <a href={`/families/${family.id}/tasks/new`}>
                <Plus className="mr-2 h-4 w-4" />
                Dodaj zadanie
              </a>
            </Button>
          )}
          {canInviteMembers && (
            <Button variant="outline" asChild>
              <a href={`/families/${family.id}/invite`}>
                <Users className="mr-2 h-4 w-4" />
                Zaproś członka
              </a>
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card className="border-l-4 border-l-yellow-500 bg-gradient-to-r from-yellow-50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-700">Moje punkty</CardTitle>
            <Trophy className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-800">{currentMember.points}</div>
            <p className="text-xs text-yellow-600">#{myRanking} w rodzinie</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500 bg-gradient-to-r from-blue-50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700">Moje zadania</CardTitle>
            <Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800">{myActiveTasks.length}</div>
            <p className="text-xs text-blue-600">do wykonania</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500 bg-gradient-to-r from-green-50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700">Członkowie</CardTitle>
            <Users className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800">{family._count.members}</div>
            <p className="text-xs text-green-600">aktywnych</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500 bg-gradient-to-r from-purple-50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700">Postęp</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800">{weeklyProgress}%</div>
            <p className="text-xs text-purple-600">w tym tygodniu</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500 bg-gradient-to-r from-orange-50 to-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700">Zawieszone</CardTitle>
            <Pause className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800">{suspendedTasks}</div>
            <p className="text-xs text-orange-600">zadań zawieszonych</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Active Tasks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Moje zadania ({myActiveTasks.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {myActiveTasks.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">Świetna robota!</p>
                <p className="text-sm">Nie masz żadnych aktywnych zadań</p>
              </div>
            ) : (
              <div className="space-y-3">
                {myActiveTasks.slice(0, 5).map(task => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{task.title}</h4>
                      {task.description && (
                        <p className="text-sm text-gray-600 truncate">{task.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">{task.points} pkt</Badge>
                      <Button size="sm" asChild>
                        <a href={`/families/${family.id}/tasks/${task.id}`}>Wykonaj</a>
                      </Button>
                    </div>
                  </div>
                ))}
                {myActiveTasks.length > 5 && (
                  <div className="text-center pt-2">
                    <Button variant="ghost" size="sm" asChild>
                      <a href={`/families/${family.id}/tasks`}>
                        Zobacz wszystkie ({myActiveTasks.length})
                      </a>
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Family Leaderboard */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Ranking rodziny
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {family.members
                .sort((a, b) => b.points - a.points)
                .slice(0, 5)
                .map((member, index) => (
                  <div
                    key={member.id}
                    className={`flex items-center gap-3 p-2 rounded-lg ${
                      member.id === currentMember.id ? 'bg-blue-50 border border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100">
                      {index === 0 && <Trophy className="h-4 w-4 text-yellow-500" />}
                      {index === 1 && <span className="text-sm font-bold text-gray-400">2</span>}
                      {index === 2 && <span className="text-sm font-bold text-amber-600">3</span>}
                      {index > 2 && (
                        <span className="text-sm font-medium text-gray-600">{index + 1}</span>
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">
                        {member.user.firstName && member.user.lastName
                          ? `${member.user.firstName} ${member.user.lastName}`
                          : member.user.email.split('@')[0]}
                        {member.id === currentMember.id && (
                          <span className="text-blue-600 ml-1">(Ty)</span>
                        )}
                      </p>
                      <p className="text-sm text-gray-500">{getRoleDisplayName(member.role)}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-gray-900">{member.points}</p>
                      <p className="text-xs text-gray-500">punktów</p>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Suspensions */}
      {suspensionStats.active > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Pause className="h-5 w-5 text-orange-500" />
                Aktywne zawieszenia ({suspensionStats.active})
              </CardTitle>
              {canManageSuspensions && (
                <Button variant="outline" size="sm" asChild>
                  <a href={`/families/${family.id}/suspensions`}>Zarządzaj</a>
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Pause className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">
                  {suspensionStats.active}{' '}
                  {suspensionStats.active === 1 ? 'zawieszenie jest' : 'zawieszeń jest'} aktywne
                </span>
              </div>
              <p className="text-sm text-orange-700">
                {suspendedTasks > 0 &&
                  `${suspendedTasks} ${suspendedTasks === 1 ? 'zadanie jest zawieszone' : 'zadań jest zawieszonych'}.`}{' '}
                Zawieszone zadania nie generują przypomnień i nie mogą być wykonywane.
              </p>
              {canManageSuspensions && (
                <div className="mt-3 flex gap-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/families/${family.id}/suspensions`}>
                      <Pause className="h-3 w-3 mr-1" />
                      Zobacz wszystkie
                    </a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/families/${family.id}/suspensions/new`}>
                      <Plus className="h-3 w-3 mr-1" />
                      Nowe zawieszenie
                    </a>
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Ostatnia aktywność
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentCompletions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Brak aktywności</p>
              <p className="text-sm">Wykonaj pierwsze zadanie, aby zobaczyć aktywność</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentCompletions.map(task => {
                const latestCompletion = task.completions[task.completions.length - 1];
                const completedBy = family.members.find(m => task.assignedTo?.userId === m.userId);

                return (
                  <div key={task.id} className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{task.title}</p>
                      <p className="text-sm text-gray-600">
                        Wykonane przez{' '}
                        <span className="font-medium">
                          {completedBy?.user.firstName || 'Użytkownik'}
                        </span>
                        {' • '}
                        <span className="text-gray-500">
                          {new Date(latestCompletion.completedAt).toLocaleDateString('pl-PL')}
                        </span>
                      </p>
                    </div>
                    <Badge variant="secondary" className="text-green-700 bg-green-100">
                      +{task.points} pkt
                    </Badge>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Weekly Progress */}
      <Card className="border-l-4 border-l-teal-500 bg-gradient-to-r from-teal-50 to-white">
        <CardHeader>
          <CardTitle className="text-teal-700">Postęp tygodnia</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-teal-700">Ukończone zadania</span>
              <span className="text-sm text-teal-600 font-semibold">
                {completedTasks.length} z {family.tasks.length}
              </span>
            </div>
            <Progress value={weeklyProgress} className="h-3 bg-teal-100" />
            <p className="text-xs text-teal-600">
              {weeklyProgress === 100
                ? '🎉 Wszystkie zadania ukończone!'
                : `Jeszcze ${activeTasks.length} zadań do ukończenia`}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
