'use client';

import { useState } from 'react';
import { Family, FamilyMember } from '@prisma/client';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ChevronsUpDown, Check, Plus, Home, Users, BarChart3 } from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';

type FamilyWithMembership = FamilyMember & {
  family: Family & {
    _count: {
      tasks: number;
      members: number;
    };
  };
};

interface FamilySwitcherProps {
  families?: FamilyWithMembership[] | null;
  currentFamilyId?: string;
}

export function FamilySwitcher({ families, currentFamilyId }: FamilySwitcherProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Zabezpieczenie przed undefined/null families
  const safeFamilies = families || [];
  const currentFamily = safeFamilies.find(fm => fm.family.id === currentFamilyId);

  const handleFamilySelect = (familyId: string) => {
    setIsOpen(false);
    if (familyId !== currentFamilyId) {
      window.location.href = `/families/${familyId}`;
    }
  };

  const handleCreateFamily = () => {
    setIsOpen(false);
    window.location.href = '/families/new';
  };

  const getFamilyInitial = (familyName: string) => {
    return familyName[0]?.toUpperCase() || 'F';
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'OWNER':
        return 'bg-yellow-100 text-yellow-800';
      case 'PARENT':
        return 'bg-blue-100 text-blue-800';
      case 'GUARDIAN':
        return 'bg-green-100 text-green-800';
      case 'CHILD':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!families || families.length === 0) {
    return (
      <Button onClick={handleCreateFamily} className="w-full">
        <Plus className="mr-2 h-4 w-4" />
        Utwórz pierwszą rodzinę
      </Button>
    );
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={isOpen}
          className="w-full justify-between h-16"
        >
          <div className="flex items-center gap-2 min-w-0">
            {currentFamily ? (
              <>
                <Avatar className="h-6 w-6">
                  <AvatarFallback className="bg-blue-500 text-white text-xs">
                    {getFamilyInitial(currentFamily.family.name)}
                  </AvatarFallback>
                </Avatar>
                <div className="flex flex-col items-start min-w-0">
                  <span className="font-medium truncate text-sm">{currentFamily.family.name}</span>
                  <Badge
                    variant="secondary"
                    className={`text-xs ${getRoleColor(currentFamily.role)}`}
                  >
                    {getRoleDisplayName(currentFamily.role)}
                  </Badge>
                </div>
              </>
            ) : (
              <>
                <Home className="h-4 w-4" />
                <span>Wybierz rodzinę</span>
              </>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="w-80" align="start">
        <DropdownMenuLabel>Twoje rodziny</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {safeFamilies.map(familyMembership => {
          const family = familyMembership.family;
          const isSelected = family.id === currentFamilyId;

          return (
            <DropdownMenuItem
              key={family.id}
              onClick={() => handleFamilySelect(family.id)}
              className="flex items-center gap-3 p-3 cursor-pointer"
            >
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white text-sm font-semibold">
                  {getFamilyInitial(family.name)}
                </AvatarFallback>
              </Avatar>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium text-sm truncate">{family.name}</span>
                  {isSelected && <Check className="h-4 w-4 text-green-600" />}
                </div>

                <div className="flex items-center gap-2 mb-1">
                  <Badge
                    variant="secondary"
                    className={`text-xs ${getRoleColor(familyMembership.role)}`}
                  >
                    {getRoleDisplayName(familyMembership.role)}
                  </Badge>
                  <span className="text-xs text-gray-500">{familyMembership.points} pkt</span>
                </div>

                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {family._count?.members || 0}
                  </span>
                  <span className="flex items-center gap-1">
                    <BarChart3 className="h-3 w-3" />
                    {family._count?.tasks || 0}
                  </span>
                </div>

                {family.description && (
                  <p className="text-xs text-gray-600 truncate mt-1">{family.description}</p>
                )}
              </div>
            </DropdownMenuItem>
          );
        })}

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={handleCreateFamily}
          className="flex items-center gap-2 p-3 cursor-pointer text-blue-600"
        >
          <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100">
            <Plus className="h-4 w-4" />
          </div>
          <span className="font-medium">Utwórz nową rodzinę</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
