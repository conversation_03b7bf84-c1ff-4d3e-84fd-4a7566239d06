'use client';

import { useState } from 'react';
import { changeMemberRoleSchema } from '@/lib/validations/family';
import { FamilyRole, FamilyMember, User } from '@prisma/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserCheck, Loader2, Shield, Crown, Users, AlertTriangle } from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';

type MemberWithUser = FamilyMember & {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
};

interface ChangeMemberRoleDialogProps {
  familyId: string;
  member: MemberWithUser;
  currentUserRole: FamilyRole;
  trigger?: React.ReactNode;
  onSuccess?: (member: MemberWithUser) => void;
}

export function ChangeMemberRoleDialog({
  familyId,
  member,
  currentUserRole,
  trigger,
  onSuccess,
}: ChangeMemberRoleDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    role: member.role,
    isAdult: member.isAdult,
  });
  const [errors, setErrors] = useState<{ role?: string }>({});
  const { toast } = useToast();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate form data
      const validation = changeMemberRoleSchema.safeParse(formData);
      if (!validation.success) {
        const fieldErrors: { role?: string } = {};
        validation.error.errors.forEach(error => {
          if (error.path[0] === 'role') fieldErrors.role = error.message;
        });
        setErrors(fieldErrors);
        return;
      }

      const response = await fetch(`/api/families/${familyId}/members/${member.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validation.data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się zmienić roli');
      }

      const updatedMember = await response.json();

      // Reset form and close dialog
      setIsOpen(false);

      // Call success callback or reload
      if (onSuccess) {
        onSuccess(updatedMember);
      } else {
        window.location.reload();
      }
    } catch (error) {
      console.error('Error changing member role:', error);
      toast({
        title: 'Błąd podczas zmiany roli',
        description: error instanceof Error ? error.message : 'Wystąpił błąd podczas zmiany roli',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setFormData({ role: member.role, isAdult: member.isAdult });
      setErrors({});
    }
  };

  const getRoleIcon = (role: FamilyRole) => {
    switch (role) {
      case 'OWNER':
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'PARENT':
        return <Shield className="h-4 w-4 text-blue-500" />;
      case 'GUARDIAN':
        return <Shield className="h-4 w-4 text-green-500" />;
      case 'CHILD':
        return <Users className="h-4 w-4 text-purple-500" />;
    }
  };

  const getRoleDescription = (role: FamilyRole) => {
    switch (role) {
      case 'OWNER':
        return 'Pełne uprawnienia - może zarządzać rodziną, członkami i wszystkimi zadaniami';
      case 'PARENT':
        return 'Może zarządzać członkami, tworzyć zadania i weryfikować ich wykonanie';
      case 'GUARDIAN':
        return 'Może tworzyć i weryfikować zadania, ale nie zarządzać członkami';
      case 'CHILD':
        return 'Może wykonywać przydzielone zadania i zbierać punkty';
    }
  };

  const getAvailableRoles = () => {
    const roles: FamilyRole[] = [];

    if (currentUserRole === 'OWNER') {
      roles.push('OWNER', 'PARENT', 'GUARDIAN', 'CHILD');
    } else if (currentUserRole === 'PARENT') {
      roles.push('GUARDIAN', 'CHILD');
    }

    return roles;
  };

  const availableRoles = getAvailableRoles();
  const isChangingToOwner = formData.role === 'OWNER' && member.role !== 'OWNER';
  const isRemovingOwner = member.role === 'OWNER' && formData.role !== 'OWNER';
  const hasChanges = formData.role !== member.role || formData.isAdult !== member.isAdult;

  const getInitials = () => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName[0]}${member.user.lastName[0]}`;
    }
    return member.user.email[0].toUpperCase();
  };

  const getDisplayName = () => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    return member.user.email.split('@')[0];
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <UserCheck className="mr-2 h-4 w-4" />
            Zmień rolę
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5 text-blue-600" />
            Zmiana roli członka
          </DialogTitle>
          <DialogDescription>Zmień rolę i uprawnienia członka rodziny.</DialogDescription>
        </DialogHeader>

        {/* Member Info */}
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
          <Avatar className="h-12 w-12">
            <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white font-semibold">
              {getInitials()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 truncate">{getDisplayName()}</h3>
            <p className="text-sm text-gray-600 truncate">{member.user.email}</p>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className="text-xs">
                Aktualna rola: {getRoleDisplayName(member.role)}
              </Badge>
              <span className="text-xs text-gray-500">{member.points} punktów</span>
            </div>
          </div>
        </div>

        <form onSubmit={onSubmit} className="space-y-6">
          <div className="space-y-4">
            {/* Role Selection */}
            <div className="space-y-2">
              <Label htmlFor="role">
                Nowa rola <span className="text-red-500">*</span>
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value: FamilyRole) =>
                  setFormData(prev => ({ ...prev, role: value }))
                }
                disabled={isLoading}
              >
                <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Wybierz rolę" />
                </SelectTrigger>
                <SelectContent>
                  {availableRoles.map(role => (
                    <SelectItem key={role} value={role}>
                      <div className="flex items-center gap-2">
                        {getRoleIcon(role)}
                        <span>{getRoleDisplayName(role)}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.role && <p className="text-sm text-red-500">{errors.role}</p>}
            </div>

            {/* Role Description */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                {getRoleIcon(formData.role)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-gray-900">
                      {getRoleDisplayName(formData.role)}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {formData.role}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{getRoleDescription(formData.role)}</p>
                </div>
              </div>
            </div>

            {/* Adult Checkbox */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isAdult"
                checked={formData.isAdult}
                onCheckedChange={(checked: boolean) =>
                  setFormData(prev => ({ ...prev, isAdult: checked === true }))
                }
                disabled={isLoading}
              />
              <Label htmlFor="isAdult" className="text-sm">
                Ta osoba jest dorosła
              </Label>
            </div>
            <p className="text-xs text-gray-500 ml-6">
              Dorosli członkowie mają dostęp do dodatkowych funkcji i statystyk
            </p>
          </div>

          {/* Warnings */}
          {isChangingToOwner && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Uwaga!</strong> Nadanie roli właściciela da tej osobie pełne uprawnienia do
                zarządzania rodziną, w tym możliwość usunięcia innych członków.
              </AlertDescription>
            </Alert>
          )}

          {isRemovingOwner && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Uwaga!</strong> Odebranie roli właściciela spowoduje utratę uprawnień do
                zarządzania rodziną. Upewnij się, że w rodzinie będzie przynajmniej jeden
                właściciel.
              </AlertDescription>
            </Alert>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              Anuluj
            </Button>
            <Button type="submit" disabled={isLoading || !hasChanges}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Zapisywanie...
                </>
              ) : (
                <>
                  <UserCheck className="mr-2 h-4 w-4" />
                  Zapisz zmiany
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
