'use client';

import { ReactNode } from 'react';
import { Family, FamilyMember, User } from '@prisma/client';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Users, Settings, UserPlus, Trophy, Calendar, BarChart3, Home } from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';

type FamilyWithMembers = Family & {
  members: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  _count: {
    tasks: number;
    members: number;
  };
};

interface FamilyLayoutProps {
  family: FamilyWithMembers;
  currentMember: FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  };
  children: ReactNode;
}

export function FamilyLayout({ family, currentMember, children }: FamilyLayoutProps) {
  const canManageFamily = ['OWNER', 'PARENT'].includes(currentMember.role);
  const canInviteMembers = ['OWNER', 'PARENT'].includes(currentMember.role);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-sm border-r">
        <div className="p-6">
          {/* Family Header */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-2">
              <Home className="h-5 w-5 text-blue-600" />
              <h1 className="font-semibold text-lg truncate">{family.name}</h1>
            </div>
            {family.description && (
              <p className="text-sm text-gray-600 mb-3">{family.description}</p>
            )}
            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>{family._count.members}</span>
              </div>
              <div className="flex items-center gap-1">
                <BarChart3 className="h-4 w-4" />
                <span>{family._count.tasks}</span>
              </div>
            </div>
          </div>

          {/* Current User */}
          <Card className="mb-6">
            <CardContent className="pt-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">
                    {currentMember.user.firstName?.[0] || currentMember.user.email[0]}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {currentMember.user.firstName && currentMember.user.lastName
                      ? `${currentMember.user.firstName} ${currentMember.user.lastName}`
                      : currentMember.user.email}
                  </p>
                  <Badge variant="secondary" className="text-xs">
                    {getRoleDisplayName(currentMember.role)}
                  </Badge>
                </div>
              </div>
              <div className="mt-3 flex items-center gap-2">
                <Trophy className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">{currentMember.points} pkt</span>
              </div>
            </CardContent>
          </Card>

          {/* Navigation */}
          <nav className="space-y-2">
            <Button variant="ghost" className="w-full justify-start" asChild>
              <a href={`/families/${family.id}`}>
                <Home className="mr-2 h-4 w-4" />
                Dashboard
              </a>
            </Button>

            <Button variant="ghost" className="w-full justify-start" asChild>
              <a href={`/families/${family.id}/tasks`}>
                <BarChart3 className="mr-2 h-4 w-4" />
                Zadania
              </a>
            </Button>

            <Button variant="ghost" className="w-full justify-start" asChild>
              <a href={`/families/${family.id}/members`}>
                <Users className="mr-2 h-4 w-4" />
                Członkowie
              </a>
            </Button>

            <Button variant="ghost" className="w-full justify-start" asChild>
              <a href={`/families/${family.id}/calendar`}>
                <Calendar className="mr-2 h-4 w-4" />
                Kalendarz
              </a>
            </Button>

            {canInviteMembers && (
              <>
                <Separator className="my-2" />
                <Button variant="ghost" className="w-full justify-start" asChild>
                  <a href={`/families/${family.id}/invite`}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Zaproś członka
                  </a>
                </Button>
              </>
            )}

            {canManageFamily && (
              <Button variant="ghost" className="w-full justify-start" asChild>
                <a href={`/families/${family.id}/settings`}>
                  <Settings className="mr-2 h-4 w-4" />
                  Ustawienia
                </a>
              </Button>
            )}
          </nav>
        </div>

        {/* Family Members Preview */}
        <div className="px-6 pb-6">
          <Separator className="mb-4" />
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-900">Członkowie</h3>
            <div className="space-y-2">
              {family.members.slice(0, 3).map(member => (
                <div key={member.id} className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-gray-600">
                      {member.user.firstName?.[0] || member.user.email[0]}
                    </span>
                  </div>
                  <span className="text-sm text-gray-600 truncate flex-1">
                    {member.user.firstName || member.user.email.split('@')[0]}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {getRoleDisplayName(member.role)}
                  </Badge>
                </div>
              ))}
              {family.members.length > 3 && (
                <div className="text-xs text-gray-500">+{family.members.length - 3} więcej</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-6">{children}</main>
      </div>
    </div>
  );
}
