'use client';

import { useState } from 'react';
import { createFamilySchema } from '@/lib/validations/family';
import { Family } from '@prisma/client';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Plus, Loader2, Home } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CreateFamilyDialogProps {
  trigger?: React.ReactNode;
  onSuccess?: (family: Family) => void;
}

export function CreateFamilyDialog({ trigger, onSuccess }: CreateFamilyDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({ name: '', description: '' });
  const [errors, setErrors] = useState<{ name?: string; description?: string }>({});
  const { toast } = useToast();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    try {
      // Validate form data
      const validation = createFamilySchema.safeParse(formData);
      if (!validation.success) {
        const fieldErrors: { name?: string; description?: string } = {};
        validation.error.errors.forEach(error => {
          if (error.path[0] === 'name') fieldErrors.name = error.message;
          if (error.path[0] === 'description') fieldErrors.description = error.message;
        });
        setErrors(fieldErrors);
        return;
      }

      const response = await fetch('/api/families', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validation.data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się utworzyć rodziny');
      }

      const family = await response.json();

      // Reset form and close dialog
      setFormData({ name: '', description: '' });
      setIsOpen(false);

      // Call success callback or redirect
      if (onSuccess) {
        onSuccess(family);
      } else {
        window.location.href = `/families/${family.id}`;
      }
    } catch (error) {
      console.error('Error creating family:', error);
      toast({
        title: 'Błąd podczas tworzenia rodziny',
        description:
          error instanceof Error ? error.message : 'Wystąpił błąd podczas tworzenia rodziny',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setFormData({ name: '', description: '' });
      setErrors({});
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Utwórz rodzinę
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Home className="h-5 w-5 text-blue-600" />
            Utwórz nową rodzinę
          </DialogTitle>
          <DialogDescription>
            Stwórz nową rodzinę i zacznij organizować zadania domowe razem ze swoimi bliskimi.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={onSubmit} className="space-y-6">
          <div className="space-y-4">
            {/* Family Name */}
            <div className="space-y-2">
              <Label htmlFor="name">
                Nazwa rodziny <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                placeholder="np. Rodzina Kowalskich"
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                disabled={isLoading}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              <p className="text-xs text-gray-500">
                Wybierz nazwę, którą wszyscy członkowie będą mogli łatwo rozpoznać
              </p>
            </div>

            {/* Family Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Opis (opcjonalnie)</Label>
              <Textarea
                id="description"
                placeholder="Krótki opis rodziny, cele, zasady..."
                rows={3}
                value={formData.description}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                disabled={isLoading}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
              <p className="text-xs text-gray-500">
                Możesz opisać cele rodziny, zasady działania lub inne ważne informacje
              </p>
            </div>
          </div>

          {/* Info Box */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Home className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-blue-900 mb-1">
                  Co się stanie po utworzeniu rodziny?
                </h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Zostaniesz właścicielem rodziny z pełnymi uprawnieniami</li>
                  <li>• Będziesz mógł zapraszać nowych członków</li>
                  <li>• Otrzymasz dostęp do zarządzania zadaniami i statystykami</li>
                  <li>• Możesz rozpocząć tworzenie pierwszych zadań</li>
                </ul>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              Anuluj
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Tworzenie...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Utwórz rodzinę
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
