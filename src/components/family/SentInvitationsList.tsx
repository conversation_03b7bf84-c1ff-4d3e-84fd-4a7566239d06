'use client';

import { useState, useEffect } from 'react';
import { FamilyRole, InvitationStatus } from '@prisma/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MoreVertical,
  RefreshCw,
  X,
  Mail,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
} from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';

interface FamilyInvitation {
  id: string;
  email: string;
  role: FamilyRole;
  isAdult: boolean;
  status: InvitationStatus;
  expiresAt: string;
  createdAt: string;
}

interface SentInvitationsListProps {
  familyId: string;
  canManageMembers: boolean;
}

export function SentInvitationsList({ familyId, canManageMembers }: SentInvitationsListProps) {
  const [invitations, setInvitations] = useState<FamilyInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingAction, setLoadingAction] = useState<{ id: string; action: string } | null>(null);
  const { toast } = useToast();

  const fetchInvitations = async () => {
    try {
      const response = await fetch(`/api/families/${familyId}/invitations`);
      if (!response.ok) {
        throw new Error('Nie udało się pobrać zaproszeń');
      }
      const data = await response.json();
      setInvitations(data);
    } catch (error) {
      console.error('Error fetching invitations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (canManageMembers) {
      fetchInvitations();
    }
  }, [familyId, canManageMembers]);

  const handleCancelInvitation = async (invitationId: string) => {
    setLoadingAction({ id: invitationId, action: 'cancel' });

    try {
      const response = await fetch(`/api/families/${familyId}/invitations`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invitationId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się anulować zaproszenia');
      }

      // Refresh the list
      await fetchInvitations();
    } catch (error) {
      console.error('Error canceling invitation:', error);
      toast({
        title: 'Błąd podczas anulowania zaproszenia',
        description:
          error instanceof Error ? error.message : 'Wystąpił błąd podczas anulowania zaproszenia',
        variant: 'destructive',
      });
    } finally {
      setLoadingAction(null);
    }
  };

  const handleResendInvitation = async (invitation: FamilyInvitation) => {
    setLoadingAction({ id: invitation.id, action: 'resend' });

    try {
      // Only cancel if invitation is still pending
      if (invitation.status === 'PENDING') {
        const deleteResponse = await fetch(`/api/families/${familyId}/invitations`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ invitationId: invitation.id }),
        });

        if (!deleteResponse.ok) {
          const deleteError = await deleteResponse.json();
          throw new Error(deleteError.error || 'Nie udało się anulować starego zaproszenia');
        }
      }

      // Send a new invitation
      const response = await fetch(`/api/families/${familyId}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: invitation.email,
          role: invitation.role,
          isAdult: invitation.isAdult,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się ponownie wysłać zaproszenia');
      }

      // Refresh the list
      await fetchInvitations();
    } catch (error) {
      console.error('Error resending invitation:', error);
      toast({
        title: 'Błąd podczas ponownego wysyłania zaproszenia',
        description:
          error instanceof Error
            ? error.message
            : 'Wystąpił błąd podczas ponownego wysyłania zaproszenia',
        variant: 'destructive',
      });
    } finally {
      setLoadingAction(null);
    }
  };

  const getStatusIcon = (status: InvitationStatus) => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'ACCEPTED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'EXPIRED':
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: InvitationStatus) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ACCEPTED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusDisplayName = (status: InvitationStatus) => {
    switch (status) {
      case 'PENDING':
        return 'Oczekuje';
      case 'ACCEPTED':
        return 'Zaakceptowane';
      case 'REJECTED':
        return 'Odrzucone';
      case 'EXPIRED':
        return 'Wygasłe';
    }
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  const canCancelInvitation = (invitation: FamilyInvitation) => {
    return invitation.status === 'PENDING' && !isExpired(invitation.expiresAt);
  };

  const canResendInvitation = (invitation: FamilyInvitation) => {
    return (
      invitation.status === 'PENDING' ||
      invitation.status === 'EXPIRED' ||
      invitation.status === 'REJECTED'
    );
  };

  if (!canManageMembers) {
    return null;
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Wysłane zaproszenia
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          Wysłane zaproszenia
        </CardTitle>
      </CardHeader>
      <CardContent>
        {invitations.length === 0 ? (
          <div className="text-center py-8">
            <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Nie wysłano jeszcze żadnych zaproszeń</p>
          </div>
        ) : (
          <div className="space-y-4">
            {invitations.map(invitation => (
              <div
                key={invitation.id}
                className="flex items-center gap-4 p-4 rounded-lg border hover:bg-gray-50"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-gray-900 truncate">{invitation.email}</h3>
                    <Badge className={`text-xs border ${getStatusColor(invitation.status)}`}>
                      {getStatusIcon(invitation.status)}
                      <span className="ml-1">{getStatusDisplayName(invitation.status)}</span>
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>Rola: {getRoleDisplayName(invitation.role)}</span>
                    <span>
                      Wysłane: {new Date(invitation.createdAt).toLocaleDateString('pl-PL')}
                    </span>
                    <span>
                      Wygasa: {new Date(invitation.expiresAt).toLocaleDateString('pl-PL')}
                    </span>
                    {isExpired(invitation.expiresAt) && invitation.status === 'PENDING' && (
                      <span className="text-red-600 font-medium">Wygasłe</span>
                    )}
                  </div>
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      disabled={loadingAction?.id === invitation.id}
                    >
                      {loadingAction?.id === invitation.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <MoreVertical className="h-4 w-4" />
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {canResendInvitation(invitation) && (
                      <DropdownMenuItem
                        onClick={() => handleResendInvitation(invitation)}
                        disabled={loadingAction?.id === invitation.id}
                      >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        {loadingAction?.id === invitation.id && loadingAction.action === 'resend'
                          ? 'Wysyłanie...'
                          : 'Wyślij ponownie'}
                      </DropdownMenuItem>
                    )}
                    {canCancelInvitation(invitation) && (
                      <>
                        {canResendInvitation(invitation) && <DropdownMenuSeparator />}
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleCancelInvitation(invitation.id)}
                          disabled={loadingAction?.id === invitation.id}
                        >
                          <X className="mr-2 h-4 w-4" />
                          {loadingAction?.id === invitation.id && loadingAction.action === 'cancel'
                            ? 'Anulowanie...'
                            : 'Anuluj zaproszenie'}
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
