'use client';

import { useState } from 'react';
import { useUserInvitations } from '@/hooks/useAuth';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, Users, Calendar, Check, X, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { pl } from 'date-fns/locale';
import { useToast } from '@/hooks/use-toast';

interface UserInvitationsDialogProps {
  trigger?: React.ReactNode;
  onInvitationAccepted?: () => void;
}

export function UserInvitationsDialog({
  trigger,
  onInvitationAccepted,
}: UserInvitationsDialogProps) {
  const { invitations, loading, error, refetch } = useUserInvitations();
  const [isOpen, setIsOpen] = useState(false);
  const [processingToken, setProcessingToken] = useState<string | null>(null);
  const { toast } = useToast();

  const handleAcceptInvitation = async (token: string) => {
    try {
      setProcessingToken(token);

      const response = await fetch(`/api/families/invitations/${token}`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nie udało się zaakceptować zaproszenia');
      }

      // Odśwież zaproszenia
      await refetch();

      // Wywołaj callback jeśli istnieje
      if (onInvitationAccepted) {
        onInvitationAccepted();
      }

      toast({
        title: 'Sukces',
        description: 'Pomyślnie dołączyłeś do rodziny!',
        variant: 'success',
      });
      setIsOpen(false);
    } catch (error) {
      console.error('Error accepting invitation:', error);
      toast({
        title: 'Błąd podczas akceptowania zaproszenia',
        description:
          error instanceof Error ? error.message : 'Wystąpił błąd podczas akceptowania zaproszenia',
        variant: 'destructive',
      });
    } finally {
      setProcessingToken(null);
    }
  };

  const handleRejectInvitation = async (invitationId: string) => {
    try {
      setProcessingToken(invitationId);

      const response = await fetch(`/api/families/invitations/${invitationId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nie udało się odrzucić zaproszenia');
      }

      // Odśwież zaproszenia
      await refetch();

      toast({
        title: 'Sukces',
        description: 'Zaproszenie zostało odrzucone',
        variant: 'success',
      });
    } catch (error) {
      console.error('Error rejecting invitation:', error);
      toast({
        title: 'Błąd podczas odrzucania zaproszenia',
        description:
          error instanceof Error ? error.message : 'Wystąpił błąd podczas odrzucania zaproszenia',
        variant: 'destructive',
      });
    } finally {
      setProcessingToken(null);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Mail className="mr-2 h-4 w-4" />
            Sprawdź zaproszenia
            {invitations.length > 0 && (
              <Badge variant="destructive" className="ml-2">
                {invitations.length}
              </Badge>
            )}
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5 text-blue-600" />
            Zaproszenia do rodzin
          </DialogTitle>
          <DialogDescription>Sprawdź zaproszenia do rodzin, które otrzymałeś</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-gray-500">Ładowanie zaproszeń...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-700 mb-2">{error}</p>
                <Button onClick={refetch} variant="outline" size="sm">
                  Spróbuj ponownie
                </Button>
              </div>
            </div>
          ) : invitations.length === 0 ? (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Brak zaproszeń</h3>
              <p className="text-gray-600">Nie masz żadnych oczekujących zaproszeń do rodzin</p>
            </div>
          ) : (
            <div className="space-y-3">
              {invitations.map(invitation => (
                <Card key={invitation.id} className="border-2">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Users className="h-5 w-5 text-blue-500" />
                          <h4 className="font-semibold text-lg">{invitation.family.name}</h4>
                          <Badge variant="outline">
                            {invitation.role === 'CHILD'
                              ? 'Dziecko'
                              : invitation.role === 'PARENT'
                                ? 'Rodzic'
                                : invitation.role === 'GUARDIAN'
                                  ? 'Opiekun'
                                  : invitation.role}
                          </Badge>
                        </div>

                        {invitation.family.description && (
                          <p className="text-gray-600 mb-2">{invitation.family.description}</p>
                        )}

                        <div className="text-sm text-gray-500 space-y-1">
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3" />
                            <span>
                              Zaprosił: {invitation.family.members[0]?.user?.firstName}{' '}
                              {invitation.family.members[0]?.user?.lastName}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <span>
                              Zaproszenie wygasa za{' '}
                              {formatDistanceToNow(new Date(invitation.expiresAt), { locale: pl })}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2 ml-4">
                        <Button
                          onClick={() => handleAcceptInvitation(invitation.token)}
                          disabled={processingToken === invitation.token}
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {processingToken === invitation.token ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Check className="h-4 w-4" />
                          )}
                        </Button>
                        <Button
                          onClick={() => handleRejectInvitation(invitation.id)}
                          disabled={processingToken === invitation.id}
                          variant="outline"
                          size="sm"
                        >
                          {processingToken === invitation.id ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <X className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
