'use client';

import { useState } from 'react';
import { FamilyMember, User, Task } from '@prisma/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MoreVertical,
  UserMinus,
  UserCheck,
  Crown,
  Trophy,
  Clock,
  CheckCircle,
  Users,
  UserPlus,
} from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';
import { DeleteMemberDialog } from './DeleteMemberDialog';

type FamilyMemberWithDetails = FamilyMember & {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  assignedTasks: Task[];
  _count: {
    completedTasks: number;
    assignedTasks: number;
  };
};

interface FamilyMembersListProps {
  familyId: string;
  members: FamilyMemberWithDetails[];
  currentMember: FamilyMember;
  canManageMembers: boolean;
}

export function FamilyMembersList({
  familyId,
  members,
  currentMember,
  canManageMembers,
}: FamilyMembersListProps) {
  const [memberToDelete, setMemberToDelete] = useState<FamilyMemberWithDetails | null>(null);

  const sortedMembers = [...members].sort((a, b) => {
    // Sort by role hierarchy, then by points
    const roleOrder = { OWNER: 4, PARENT: 3, GUARDIAN: 2, CHILD: 1 };
    const roleA = roleOrder[a.role as keyof typeof roleOrder] || 0;
    const roleB = roleOrder[b.role as keyof typeof roleOrder] || 0;

    if (roleA !== roleB) {
      return roleB - roleA;
    }

    return b.points - a.points;
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'OWNER':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'PARENT':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'GUARDIAN':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CHILD':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'OWNER':
        return <Crown className="h-3 w-3" />;
      case 'PARENT':
      case 'GUARDIAN':
        return <UserCheck className="h-3 w-3" />;
      case 'CHILD':
        return <Users className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const getInitials = (member: FamilyMemberWithDetails) => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName[0]}${member.user.lastName[0]}`;
    }
    return member.user.email[0].toUpperCase();
  };

  const getDisplayName = (member: FamilyMemberWithDetails) => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    return member.user.email.split('@')[0];
  };

  const canManageMember = (member: FamilyMemberWithDetails) => {
    if (!canManageMembers) return false;
    if (member.id === currentMember.id) return false;

    // Owner can manage everyone except themselves
    if (currentMember.role === 'OWNER') return true;

    // Parent can manage guardians and children
    if (currentMember.role === 'PARENT') {
      return ['GUARDIAN', 'CHILD'].includes(member.role);
    }

    return false;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Członkowie rodziny</h2>
          <p className="text-gray-600">Zarządzaj członkami i ich rolami w rodzinie</p>
        </div>
        {canManageMembers && (
          <Button asChild>
            <a href={`/families/${familyId}/invite`}>
              <UserPlus className="mr-2 h-4 w-4" />
              Zaproś członka
            </a>
          </Button>
        )}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Łącznie członków</p>
                <p className="text-2xl font-bold">{members.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-600">Właściciele</p>
                <p className="text-2xl font-bold">
                  {members.filter(m => m.role === 'OWNER').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Łączne punkty</p>
                <p className="text-2xl font-bold">
                  {members.reduce((sum, m) => sum + m.points, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Aktywni dziś</p>
                <p className="text-2xl font-bold">
                  {
                    members.filter(
                      m =>
                        m.lastActive &&
                        new Date(m.lastActive).toDateString() === new Date().toDateString()
                    ).length
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Members List */}
      <Card>
        <CardHeader>
          <CardTitle>Lista członków</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sortedMembers.map(member => (
              <div
                key={member.id}
                className={`flex items-center gap-4 p-4 rounded-lg border transition-colors ${
                  member.id === currentMember.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                }`}
              >
                {/* Avatar */}
                <Avatar className="h-12 w-12">
                  <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white font-semibold">
                    {getInitials(member)}
                  </AvatarFallback>
                </Avatar>

                {/* Member Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-gray-900 truncate">
                      {getDisplayName(member)}
                      {member.id === currentMember.id && (
                        <span className="text-blue-600 ml-1">(Ty)</span>
                      )}
                    </h3>
                    <Badge className={`text-xs border ${getRoleColor(member.role)}`}>
                      {getRoleIcon(member.role)}
                      <span className="ml-1">{getRoleDisplayName(member.role)}</span>
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 truncate">{member.user.email}</p>
                  <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <Trophy className="h-3 w-3" />
                      {member.points} pkt
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {member._count.assignedTasks} aktywnych
                    </span>
                    <span className="flex items-center gap-1">
                      <CheckCircle className="h-3 w-3" />
                      {member._count.completedTasks} ukończonych
                    </span>
                    {member.lastActive && (
                      <span>
                        Aktywny: {new Date(member.lastActive).toLocaleDateString('pl-PL')}
                      </span>
                    )}
                  </div>
                </div>

                {/* Actions */}
                {canManageMember(member) && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <a href={`/families/${familyId}/members/${member.id}/edit`}>
                          <UserCheck className="mr-2 h-4 w-4" />
                          Zmień rolę
                        </a>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => setMemberToDelete(member)}
                      >
                        <UserMinus className="mr-2 h-4 w-4" />
                        Usuń z rodziny
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <DeleteMemberDialog
        familyId={familyId}
        member={memberToDelete}
        open={memberToDelete !== null}
        onOpenChange={open => {
          if (!open) {
            setMemberToDelete(null);
          }
        }}
        onSuccess={() => {
          setMemberToDelete(null);
          window.location.reload();
        }}
      />
    </div>
  );
}
