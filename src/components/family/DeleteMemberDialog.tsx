'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserMinus, Loader2, AlertTriangle } from 'lucide-react';
import { getRoleDisplayName } from '@/lib/auth';
import { FamilyMember, User } from '@prisma/client';
import { useToast } from '@/hooks/use-toast';

type MemberWithUser = FamilyMember & {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
};

interface DeleteMemberDialogProps {
  familyId: string;
  member: MemberWithUser | null;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
}

export function DeleteMemberDialog({
  familyId,
  member,
  trigger,
  open: externalOpen,
  onOpenChange: externalOnOpenChange,
  onSuccess,
}: DeleteMemberDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Use external state if provided, otherwise use internal state
  const isOpen = externalOpen !== undefined ? externalOpen : internalOpen;
  const setIsOpen = externalOnOpenChange || setInternalOpen;

  const handleDelete = async () => {
    if (!member) return;

    setIsLoading(true);

    try {
      const response = await fetch(`/api/families/${familyId}/members/${member.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się usunąć członka');
      }

      setIsOpen(false);

      if (onSuccess) {
        onSuccess();
      } else {
        window.location.reload();
      }
    } catch (error) {
      console.error('Error removing member:', error);
      toast({
        title: 'Błąd',
        description:
          error instanceof Error ? error.message : 'Wystąpił błąd podczas usuwania członka',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getInitials = () => {
    if (!member) return '';
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName[0]}${member.user.lastName[0]}`;
    }
    return member.user.email[0].toUpperCase();
  };

  const getDisplayName = () => {
    if (!member) return '';
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    return member.user.email.split('@')[0];
  };

  if (!member) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      {!trigger && externalOpen === undefined && (
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
            <UserMinus className="mr-2 h-4 w-4" />
            Usuń z rodziny
          </Button>
        </DialogTrigger>
      )}

      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <UserMinus className="h-5 w-5" />
            Usuń członka z rodziny
          </DialogTitle>
          <DialogDescription>
            Ta operacja nie może być cofnięta. Członek zostanie całkowicie usunięty z rodziny.
          </DialogDescription>
        </DialogHeader>

        {/* Member Info */}
        <div className="flex items-center gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
          <Avatar className="h-12 w-12">
            <AvatarFallback className="bg-gradient-to-br from-blue-400 to-purple-500 text-white font-semibold">
              {getInitials()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 truncate">{getDisplayName()}</h3>
            <p className="text-sm text-gray-600 truncate">{member.user.email}</p>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-gray-500">Rola: {getRoleDisplayName(member.role)}</span>
              <span className="text-xs text-gray-500">{member.points} punktów</span>
            </div>
          </div>
        </div>

        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Uwaga!</strong> Po usunięciu tego członka:
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>Wszystkie jego zadania zostaną odassgnowane</li>
              <li>Historia wykonanych zadań zostanie zachowana</li>
              <li>Punkty zostaną utracone</li>
              <li>Będzie musiał otrzymać nowe zaproszenie, aby wrócić do rodziny</li>
            </ul>
          </AlertDescription>
        </Alert>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Anuluj
          </Button>
          <Button type="button" variant="destructive" onClick={handleDelete} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Usuwanie...
              </>
            ) : (
              <>
                <UserMinus className="mr-2 h-4 w-4" />
                Usuń członka
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
