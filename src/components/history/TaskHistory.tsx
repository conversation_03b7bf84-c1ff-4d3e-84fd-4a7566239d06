'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Search, Filter, Download, Calendar, Clock, Star, User } from 'lucide-react';
import { useState, useMemo } from 'react';
import { format } from 'date-fns';
import { pl } from 'date-fns/locale';

interface CompletedTask {
  id: string;
  title: string;
  description?: string;
  category: string;
  points: number;
  completedAt: Date;
  completedBy: {
    id: string;
    name: string;
    avatar?: string;
  };
  verifiedBy?: {
    id: string;
    name: string;
    avatar?: string;
  };
  verifiedAt?: Date;
  difficulty: 'easy' | 'medium' | 'hard';
  timeSpent?: number; // w minutach
}

interface TaskHistoryProps {
  familyId: string;
  className?: string;
}

const CATEGORIES = ['Wszystkie', 'Sprzątanie', 'Nauka', 'Pomoc', 'Sport', 'Inne'];
const SORT_OPTIONS = [
  { value: 'completedAt', label: 'Data ukończenia' },
  { value: 'points', label: 'Punkty' },
  { value: 'title', label: 'Nazwa' },
];

export function TaskHistory({ familyId, className }: TaskHistoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Wszystkie');
  const [sortBy, setSortBy] = useState('completedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Mock data - w rzeczywistej aplikacji pobierałoby z API
  const completedTasks: CompletedTask[] = useMemo(
    () => [
      {
        id: '1',
        title: 'Posprzątanie pokoju',
        description: 'Odkurzenie i ścieranie kurzu',
        category: 'Sprzątanie',
        points: 10,
        completedAt: new Date(2024, 5, 25, 14, 30),
        completedBy: { id: '1', name: 'Anna' },
        verifiedBy: { id: '2', name: 'Mama' },
        verifiedAt: new Date(2024, 5, 25, 15, 0),
        difficulty: 'medium',
        timeSpent: 45,
      },
      {
        id: '2',
        title: 'Nauka matematyki',
        description: 'Rozwiązanie zadań z geometrii',
        category: 'Nauka',
        points: 15,
        completedAt: new Date(2024, 5, 24, 16, 0),
        completedBy: { id: '3', name: 'Paweł' },
        difficulty: 'hard',
        timeSpent: 60,
      },
      {
        id: '3',
        title: 'Pomoc w kuchni',
        description: 'Przygotowanie kolacji',
        category: 'Pomoc',
        points: 8,
        completedAt: new Date(2024, 5, 23, 18, 30),
        completedBy: { id: '1', name: 'Anna' },
        verifiedBy: { id: '2', name: 'Mama' },
        verifiedAt: new Date(2024, 5, 23, 19, 0),
        difficulty: 'easy',
        timeSpent: 30,
      },
      {
        id: '4',
        title: 'Trening biegowy',
        description: '30 minut biegania w parku',
        category: 'Sport',
        points: 12,
        completedAt: new Date(2024, 5, 22, 7, 0),
        completedBy: { id: '3', name: 'Paweł' },
        difficulty: 'medium',
        timeSpent: 30,
      },
    ],
    []
  );

  const filteredAndSortedTasks = useMemo(() => {
    const filtered = completedTasks.filter(task => {
      const matchesSearch =
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory =
        selectedCategory === 'Wszystkie' || task.category === selectedCategory;

      return matchesSearch && matchesCategory;
    });

    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'completedAt':
          comparison = a.completedAt.getTime() - b.completedAt.getTime();
          break;
        case 'points':
          comparison = a.points - b.points;
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [completedTasks, searchQuery, selectedCategory, sortBy, sortOrder]);

  const handleExport = (format: 'csv' | 'pdf') => {
    // Mock export functionality
    console.log(`Eksportowanie do ${format.toUpperCase()}...`);
  };

  const getDifficultyColor = (difficulty: CompletedTask['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-red-100 text-red-800';
    }
  };

  const getDifficultyLabel = (difficulty: CompletedTask['difficulty']) => {
    switch (difficulty) {
      case 'easy':
        return 'Łatwe';
      case 'medium':
        return 'Średnie';
      case 'hard':
        return 'Trudne';
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="text-lg">Historia zadań</CardTitle>

          <div className="flex flex-wrap items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Eksport
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleExport('csv')}>
                  Eksport do CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleExport('pdf')}>
                  Eksport do PDF
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Filtry i wyszukiwanie */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Szukaj zadań..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-full sm:w-40">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {CATEGORIES.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </Button>
        </div>

        {/* Lista zadań */}
        <div className="space-y-4">
          {filteredAndSortedTasks.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              Nie znaleziono zadań spełniających kryteria wyszukiwania.
            </div>
          ) : (
            filteredAndSortedTasks.map(task => (
              <div
                key={task.id}
                className="border rounded-lg p-4 hover:bg-muted/30 transition-colors"
              >
                <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                  <div className="flex-1">
                    <div className="flex items-start gap-3">
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{task.title}</h3>
                        {task.description && (
                          <p className="text-xs text-muted-foreground mt-1">{task.description}</p>
                        )}

                        <div className="flex flex-wrap items-center gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            {task.category}
                          </Badge>
                          <Badge className={`text-xs ${getDifficultyColor(task.difficulty)}`}>
                            {getDifficultyLabel(task.difficulty)}
                          </Badge>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                            {task.points} pkt
                          </div>
                          {task.timeSpent && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Clock className="w-3 h-3" />
                              {task.timeSpent} min
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end gap-2 text-xs text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <User className="w-3 h-3" />
                      <Avatar className="w-5 h-5">
                        <AvatarImage src={task.completedBy.avatar} />
                        <AvatarFallback className="text-xs">
                          {task.completedBy.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span>{task.completedBy.name}</span>
                    </div>

                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {format(task.completedAt, 'dd.MM.yyyy HH:mm', { locale: pl })}
                    </div>

                    {task.verifiedBy && task.verifiedAt && (
                      <div className="flex items-center gap-2 text-green-600">
                        <span className="text-xs">Zweryfikowane przez</span>
                        <Avatar className="w-4 h-4">
                          <AvatarImage src={task.verifiedBy.avatar} />
                          <AvatarFallback className="text-xs">
                            {task.verifiedBy.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs">{task.verifiedBy.name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Podsumowanie */}
        {filteredAndSortedTasks.length > 0 && (
          <div className="border-t pt-4 mt-6">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-primary">
                  {filteredAndSortedTasks.length}
                </div>
                <div className="text-xs text-muted-foreground">Ukończone zadania</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {filteredAndSortedTasks.reduce((sum, task) => sum + task.points, 0)}
                </div>
                <div className="text-xs text-muted-foreground">Łączne punkty</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(
                    filteredAndSortedTasks.reduce((sum, task) => sum + (task.timeSpent || 0), 0) /
                      60
                  )}
                  h
                </div>
                <div className="text-xs text-muted-foreground">Czas pracy</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  {filteredAndSortedTasks.filter(task => task.verifiedBy).length}
                </div>
                <div className="text-xs text-muted-foreground">Zweryfikowane</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
