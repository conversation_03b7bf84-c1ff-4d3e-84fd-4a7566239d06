'use client';

import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { locales, localeConfig, type Locale } from '@/lib/i18n/config';

interface LanguageSwitcherProps {
  variant?: 'dropdown' | 'tabs';
  showFlags?: boolean;
  className?: string;
}

export function LanguageSwitcher({
  variant = 'dropdown',
  showFlags = true,
  className = '',
}: LanguageSwitcherProps) {
  const locale = useLocale() as Locale;
  const router = useRouter();
  const pathname = usePathname();

  const handleLocaleChange = (newLocale: Locale) => {
    // Remove current locale from pathname if it exists
    const segments = pathname.split('/');
    const isCurrentLocaleInPath = locales.includes(segments[1] as Locale);

    let newPath = pathname;
    if (isCurrentLocaleInPath) {
      // Remove current locale
      segments.splice(1, 1);
      newPath = segments.join('/') || '/';
    }

    // Add new locale if it's not the default
    if (newLocale !== 'pl') {
      newPath = `/${newLocale}${newPath}`;
    }

    router.push(newPath);
  };

  if (variant === 'dropdown') {
    return (
      <Select value={locale} onValueChange={handleLocaleChange}>
        <SelectTrigger className={`w-[140px] ${className}`}>
          <SelectValue>
            <div className="flex items-center gap-2">
              {showFlags && <span>{localeConfig[locale].flag}</span>}
              <span>{localeConfig[locale].name}</span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {locales.map(loc => (
            <SelectItem key={loc} value={loc}>
              <div className="flex items-center gap-2">
                {showFlags && <span>{localeConfig[loc].flag}</span>}
                <span>{localeConfig[loc].name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    );
  }

  // Tabs variant for future implementation
  return (
    <div className={`flex gap-1 ${className}`}>
      {locales.map(loc => (
        <button
          key={loc}
          onClick={() => handleLocaleChange(loc)}
          className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
            locale === loc
              ? 'bg-primary text-primary-foreground'
              : 'text-muted-foreground hover:text-foreground hover:bg-muted'
          }`}
        >
          {showFlags && <span className="mr-1">{localeConfig[loc].flag}</span>}
          {localeConfig[loc].name}
        </button>
      ))}
    </div>
  );
}
