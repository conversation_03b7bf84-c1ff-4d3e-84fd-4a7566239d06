'use client';

import { useTranslations } from 'next-intl';
import { ReactNode } from 'react';

interface LocalizedTextProps {
  tKey: string;
  values?: Record<string, string | number>;
  fallback?: string;
  as?: keyof React.JSX.IntrinsicElements;
  className?: string;
  children?: ReactNode;
}

export function LocalizedText({
  tKey,
  values,
  fallback,
  as: Component = 'span',
  className,
  children,
}: LocalizedTextProps) {
  const t = useTranslations();

  try {
    const translatedText = t(tKey, values);
    return (
      <Component className={className}>
        {translatedText}
        {children}
      </Component>
    );
  } catch {
    console.warn(`Translation key not found: ${tKey}`);
    return (
      <Component className={className}>
        {fallback || tKey}
        {children}
      </Component>
    );
  }
}

// Convenience components for common use cases
export function LocalizedHeading({
  tKey,
  level = 2,
  ...props
}: Omit<LocalizedTextProps, 'as'> & { level?: 1 | 2 | 3 | 4 | 5 | 6 }) {
  const HeadingTag = `h${level}` as keyof React.JSX.IntrinsicElements;
  return <LocalizedText as={HeadingTag} tKey={tKey} {...props} />;
}

export function LocalizedParagraph(props: Omit<LocalizedTextProps, 'as'>) {
  return <LocalizedText as="p" {...props} />;
}

export function LocalizedButton({
  onClick,
  ...props
}: Omit<LocalizedTextProps, 'as'> & { onClick?: () => void }) {
  return (
    <button onClick={onClick}>
      <LocalizedText {...props} />
    </button>
  );
}
