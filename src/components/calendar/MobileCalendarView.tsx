'use client';

import React, { useState, useCallback, useMemo, useRef } from 'react';
import { CalendarView as CalendarViewType } from '@prisma/client';
import { CalendarTask } from '@/lib/validations/calendar';
import {
  format,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  isSameMonth,
  isSameDay,
  isToday,
  addMonths,
  subMonths,
  addWeeks,
  subWeeks,
  startOfDay,
  addHours,
  isWithinInterval,
} from 'date-fns';
import { pl } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight, Plus, Calendar, CalendarDays, List } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AssigneeDisplay } from '@/components/ui/AssigneeDisplay';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import RecurringTaskMarker, { CompactRecurringTaskMarker } from './RecurringTaskMarker';

interface MobileCalendarViewProps {
  view: CalendarViewType;
  date: Date;
  tasks: CalendarTask[];
  loading?: boolean;
  onDateChange: (date: Date) => void;
  onViewChange: (view: CalendarViewType) => void;
  onTaskClick: (task: CalendarTask) => void;
  onSlotSelect: (startDate: Date, endDate?: Date) => void;
  onCreateTask?: () => void;
  className?: string;
}

export default function MobileCalendarView({
  view,
  date: selectedDate,
  tasks,
  loading,
  onDateChange,
  onViewChange,
  onTaskClick,
  onSlotSelect,
  onCreateTask,
  className,
}: MobileCalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(selectedDate);
  const [selectedDay, setSelectedDay] = useState(selectedDate);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [mobileActiveTab, setMobileActiveTab] = useState<'calendar' | 'tasks'>('calendar');

  // Swipe gesture handling
  const touchStartX = useRef<number>(0);
  const touchEndX = useRef<number>(0);
  const calendarRef = useRef<HTMLDivElement>(null);

  // Get view-specific data
  const currentMonth = useMemo(() => startOfMonth(currentDate), [currentDate]);
  const currentWeek = useMemo(() => startOfWeek(currentDate, { weekStartsOn: 1 }), [currentDate]);
  const currentDayStart = useMemo(() => startOfDay(currentDate), [currentDate]);

  // Calculate data for different views
  const monthViewData = useMemo(() => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 1 });
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 1 });

    const days = [];
    let day = startDate;
    while (day <= endDate) {
      days.push(day);
      day = addDays(day, 1);
    }
    return days;
  }, [currentMonth]);

  const weekViewData = useMemo(() => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(currentWeek, i));
    }
    return days;
  }, [currentWeek]);

  const dayViewHours = useMemo(() => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(addHours(currentDayStart, i));
    }
    return hours;
  }, [currentDayStart]);

  // Get tasks for different periods
  const getTasksForDay = useCallback(
    (day: Date) => {
      return tasks.filter(task => {
        if (task.allDay) {
          return isSameDay(new Date(task.start), day);
        }
        const taskStart = new Date(task.start);
        return isSameDay(taskStart, day);
      });
    },
    [tasks]
  );

  const getTasksForWeek = useCallback(
    (weekStart: Date) => {
      const weekEnd = endOfWeek(weekStart, { weekStartsOn: 1 });
      return tasks.filter(task => {
        const taskStart = new Date(task.start);
        return isWithinInterval(taskStart, { start: weekStart, end: weekEnd });
      });
    },
    [tasks]
  );

  const getTasksForPeriod = useCallback(
    (startDate: Date, endDate: Date) => {
      return tasks.filter(task => {
        const taskStart = new Date(task.start);
        return isWithinInterval(taskStart, { start: startDate, end: endDate });
      });
    },
    [tasks]
  );

  // Get tasks for selected day - sorted: all-day first, then by time
  const selectedDayTasks = useMemo(() => {
    const dayTasks = getTasksForDay(selectedDay);
    return dayTasks.sort((a, b) => {
      // All-day tasks first
      if (a.allDay && !b.allDay) return -1;
      if (!a.allDay && b.allDay) return 1;

      // Both all-day or both timed - sort by start time
      if (a.allDay && b.allDay) return 0;

      const timeA = new Date(a.start).getTime();
      const timeB = new Date(b.start).getTime();
      return timeA - timeB;
    });
  }, [getTasksForDay, selectedDay]);

  const handleDayClick = useCallback(
    (day: Date) => {
      setSelectedDay(day);
      onDateChange(day);
      // Auto-switch to tasks tab on mobile if there are tasks for this day
      const dayTasks = getTasksForDay(day);
      if (dayTasks.length > 0) {
        setMobileActiveTab('tasks');
      }
    },
    [onDateChange, getTasksForDay]
  );

  const navigatePrevious = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);

    let newDate: Date;
    switch (view) {
      case CalendarViewType.MONTH:
        newDate = subMonths(currentDate, 1);
        break;
      case CalendarViewType.WEEK:
        newDate = subWeeks(currentDate, 1);
        break;
      case CalendarViewType.DAY:
        newDate = addDays(currentDate, -1);
        break;
      case CalendarViewType.AGENDA:
        newDate = subWeeks(currentDate, 1);
        break;
      default:
        newDate = subMonths(currentDate, 1);
    }

    setCurrentDate(newDate);
    onDateChange(newDate);
    setTimeout(() => setIsTransitioning(false), 300);
  }, [view, currentDate, isTransitioning, onDateChange]);

  const navigateNext = useCallback(() => {
    if (isTransitioning) return;
    setIsTransitioning(true);

    let newDate: Date;
    switch (view) {
      case CalendarViewType.MONTH:
        newDate = addMonths(currentDate, 1);
        break;
      case CalendarViewType.WEEK:
        newDate = addWeeks(currentDate, 1);
        break;
      case CalendarViewType.DAY:
        newDate = addDays(currentDate, 1);
        break;
      case CalendarViewType.AGENDA:
        newDate = addWeeks(currentDate, 1);
        break;
      default:
        newDate = addMonths(currentDate, 1);
    }

    setCurrentDate(newDate);
    onDateChange(newDate);
    setTimeout(() => setIsTransitioning(false), 300);
  }, [view, currentDate, isTransitioning, onDateChange]);

  // Swipe gesture handlers
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX;
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX;
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!touchStartX.current || !touchEndX.current) return;

    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      navigateNext();
    } else if (isRightSwipe) {
      navigatePrevious();
    }

    touchStartX.current = 0;
    touchEndX.current = 0;
  }, [navigateNext, navigatePrevious]);

  // Get title for current view
  const getViewTitle = useCallback(() => {
    switch (view) {
      case CalendarViewType.MONTH:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
      case CalendarViewType.WEEK:
        return `Tydzień ${format(currentDate, 'dd.MM', { locale: pl })} - ${format(addDays(currentWeek, 6), 'dd.MM.yyyy', { locale: pl })}`;
      case CalendarViewType.DAY:
        return format(currentDate, 'EEEE, d MMMM yyyy', { locale: pl });
      case CalendarViewType.AGENDA:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
      default:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
    }
  }, [view, currentDate, currentWeek]);

  const getViewLabel = useCallback((viewType: CalendarViewType) => {
    switch (viewType) {
      case CalendarViewType.MONTH:
        return 'Miesiąc';
      case CalendarViewType.WEEK:
        return 'Tydzień';
      case CalendarViewType.DAY:
        return 'Dzień';
      case CalendarViewType.AGENDA:
        return 'Agenda';
      default:
        return 'Miesiąc';
    }
  }, []);

  const handleCreateTaskForDay = useCallback(() => {
    onSlotSelect(selectedDay);
    onCreateTask?.();
  }, [selectedDay, onSlotSelect, onCreateTask]);

  // Render functions for different views
  const renderMonthView = () => (
    <Card
      className={cn(
        'm-4 overflow-hidden transition-all duration-300 ease-in-out',
        isTransitioning && 'opacity-75 scale-[0.98]'
      )}
    >
      {/* Week days header */}
      <div className="grid grid-cols-7 border-b bg-muted/30">
        {['Pon', 'Wt', 'Śr', 'Czw', 'Pt', 'Sob', 'Ndz'].map(day => (
          <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar days */}
      <div className="grid grid-cols-7">
        {monthViewData.map(day => {
          const dayTasks = getTasksForDay(day);
          const isCurrentMonth = isSameMonth(day, currentMonth);
          const isSelected = isSameDay(day, selectedDay);
          const isTodayDay = isToday(day);

          return (
            <div
              key={day.toISOString()}
              className={cn(
                'min-h-[60px] p-1 border-r border-b cursor-pointer transition-colors',
                'hover:bg-muted/50 active:bg-muted',
                !isCurrentMonth && 'text-muted-foreground bg-muted/20',
                isSelected && 'bg-primary/10 border-primary',
                isTodayDay && 'bg-blue-50 dark:bg-blue-950'
              )}
              onClick={() => handleDayClick(day)}
            >
              <div className="flex flex-col h-full">
                <div
                  className={cn(
                    'text-sm font-medium mb-1',
                    isTodayDay && 'text-blue-600 font-bold',
                    isSelected && 'text-primary font-bold'
                  )}
                >
                  {format(day, 'd')}
                </div>

                {/* Task indicators */}
                <div className="flex-1 space-y-0.5">
                  {dayTasks.slice(0, 2).map(task => {
                    // Używamy RecurringTaskMarker dla wszystkich zadań
                    // Zadania jednorazowe będą miały frequency='ONCE'
                    const taskWithFrequency = {
                      ...task,
                      frequency: task.frequency || ('ONCE' as const),
                      isRecurring: task.isRecurring || false,
                    };

                    return (
                      <RecurringTaskMarker
                        key={task.id}
                        task={taskWithFrequency}
                        date={day}
                        size="sm"
                        showTitle={true}
                        onClick={() => onTaskClick(task)}
                        className="w-full"
                      />
                    );
                  })}

                  {dayTasks.length > 2 && (
                    <div className="text-xs text-muted-foreground text-center">
                      +{dayTasks.length - 2} więcej
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );

  const renderWeekView = () => (
    <Card
      className={cn(
        'm-4 overflow-hidden transition-all duration-300 ease-in-out',
        isTransitioning && 'opacity-75 scale-[0.98]'
      )}
    >
      {/* Week days header */}
      <div className="grid grid-cols-7 border-b bg-muted/30">
        {weekViewData.map(day => (
          <div key={day.toISOString()} className="p-2 text-center">
            <div className="text-xs text-muted-foreground">
              {format(day, 'EEE', { locale: pl })}
            </div>
            <div
              className={cn(
                'text-sm font-medium',
                isToday(day) && 'text-blue-600 font-bold',
                isSameDay(day, selectedDay) && 'text-primary font-bold'
              )}
            >
              {format(day, 'd')}
            </div>
          </div>
        ))}
      </div>

      {/* Week timeline */}
      <div className="flex">
        {/* Time column */}
        <div className="w-16 border-r">
          {Array.from({ length: 12 }, (_, i) => i + 8).map(hour => (
            <div
              key={hour}
              className="h-12 border-b flex items-center justify-center text-xs text-muted-foreground"
            >
              {hour}:00
            </div>
          ))}
        </div>

        {/* Days grid */}
        <div className="flex-1 grid grid-cols-7">
          {weekViewData.map(day => {
            const dayTasks = getTasksForDay(day);
            const isSelected = isSameDay(day, selectedDay);
            const isTodayDay = isToday(day);

            return (
              <div
                key={day.toISOString()}
                className={cn(
                  'border-r cursor-pointer',
                  isSelected && 'bg-primary/5',
                  isTodayDay && 'bg-blue-50 dark:bg-blue-950'
                )}
                onClick={() => handleDayClick(day)}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <div key={i} className="h-12 border-b relative">
                    {/* Tasks in this hour slot */}
                    {dayTasks
                      .filter(task => {
                        if (task.allDay) return i === 0;
                        const taskHour = new Date(task.start).getHours();
                        return taskHour === i + 8;
                      })
                      .slice(0, 1)
                      .map(task => {
                        const taskWithFrequency = {
                          ...task,
                          frequency: task.frequency || ('ONCE' as const),
                          isRecurring: task.isRecurring || false,
                        };

                        return (
                          <div key={task.id} className="absolute inset-x-1 top-1 bottom-1">
                            <RecurringTaskMarker
                              task={taskWithFrequency}
                              date={new Date(task.start)}
                              size="sm"
                              showTitle={true}
                              onClick={task => onTaskClick(task)}
                              className="h-full"
                            />
                          </div>
                        );
                      })}
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    </Card>
  );

  const renderDayView = () => {
    const dayTasks = getTasksForDay(currentDate);

    return (
      <Card
        className={cn(
          'm-4 overflow-hidden transition-all duration-300 ease-in-out',
          isTransitioning && 'opacity-75 scale-[0.98]'
        )}
      >
        {/* Day header */}
        <div className="p-4 border-b bg-muted/30 text-center">
          <div className="text-sm text-muted-foreground">
            {format(currentDate, 'EEEE', { locale: pl })}
          </div>
          <div className={cn('text-2xl font-bold', isToday(currentDate) && 'text-blue-600')}>
            {format(currentDate, 'd')}
          </div>
          <div className="text-sm text-muted-foreground">
            {format(currentDate, 'MMMM yyyy', { locale: pl })}
          </div>
        </div>

        {/* Timeline */}
        <div className="overflow-y-auto max-h-96">
          {Array.from({ length: 24 }, (_, i) => {
            const hour = i;
            const hourTasks = dayTasks.filter(task => {
              if (task.allDay) return hour === 8; // Show all-day tasks at 8 AM
              const taskHour = new Date(task.start).getHours();
              return taskHour === hour;
            });

            return (
              <div key={hour} className="flex border-b">
                <div className="w-16 p-2 text-xs text-muted-foreground text-center border-r">
                  {hour.toString().padStart(2, '0')}:00
                </div>
                <div className="flex-1 min-h-[60px] p-2 relative">
                  {hourTasks.map(task => {
                    const taskWithFrequency = {
                      ...task,
                      frequency: task.frequency || ('ONCE' as const),
                      isRecurring: task.isRecurring || false,
                    };

                    return (
                      <div key={task.id} className="mb-2">
                        <RecurringTaskMarker
                          task={taskWithFrequency}
                          date={new Date(task.start)}
                          size="md"
                          showTitle={true}
                          onClick={task => onTaskClick(task)}
                          className="w-full"
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </Card>
    );
  };

  const renderAgendaView = () => {
    const agendaTasks = getTasksForWeek(currentWeek);
    const groupedTasks = agendaTasks.reduce(
      (groups, task) => {
        const date = format(new Date(task.start), 'yyyy-MM-dd');
        if (!groups[date]) groups[date] = [];
        groups[date].push(task);
        return groups;
      },
      {} as Record<string, CalendarTask[]>
    );

    return (
      <Card
        className={cn(
          'm-4 overflow-hidden transition-all duration-300 ease-in-out',
          isTransitioning && 'opacity-75 scale-[0.98]'
        )}
      >
        <div className="p-4 border-b bg-muted/30">
          <h3 className="font-semibold">Agenda na tydzień</h3>
        </div>

        <div className="overflow-y-auto max-h-96">
          {Object.entries(groupedTasks).length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">Brak zadań w tym tygodniu</div>
          ) : (
            Object.entries(groupedTasks)
              .sort(([a], [b]) => a.localeCompare(b))
              .map(([date, dayTasks]) => (
                <div key={date} className="border-b last:border-b-0">
                  <div className="p-3 bg-muted/50 font-medium text-sm">
                    {format(new Date(date), 'EEEE, d MMMM', { locale: pl })}
                  </div>
                  <div className="p-3 space-y-2">
                    {dayTasks.map(task => {
                      const taskWithFrequency = {
                        ...task,
                        frequency: task.frequency || ('ONCE' as const),
                        isRecurring: task.isRecurring || false,
                      };

                      return (
                        <div
                          key={task.id}
                          className="flex items-center space-x-3 p-2 rounded hover:bg-muted/50 cursor-pointer transition-colors"
                          onClick={() => onTaskClick(task)}
                        >
                          <CompactRecurringTaskMarker
                            task={taskWithFrequency}
                            date={new Date(task.start)}
                            onClick={task => onTaskClick(task)}
                          />
                          <div className="flex-1">
                            <div className="font-medium text-sm">{task.title}</div>
                            {!task.allDay && (
                              <div className="text-xs text-muted-foreground">
                                {format(new Date(task.start), 'HH:mm')}
                                {task.end && ` - ${format(new Date(task.end), 'HH:mm')}`}
                              </div>
                            )}
                          </div>
                          <AssigneeDisplay
                            assignments={task.assignments}
                            assignedTo={task.assignedTo}
                            assignmentType={task.assignmentType}
                            variant="calendar"
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))
          )}
        </div>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div
      className={cn('h-full flex flex-col bg-background', className)}
      ref={calendarRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Mobile Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur sticky top-0 z-10">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={navigatePrevious}
            disabled={isTransitioning}
            className="h-8 w-8 p-0 transition-all duration-200 active:scale-95"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <div className="flex flex-col items-center">
            <h2
              className={cn(
                'text-base font-semibold capitalize transition-all duration-300 text-center min-w-0',
                isTransitioning && 'opacity-75'
              )}
            >
              {getViewTitle()}
            </h2>

            {/* View Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-muted-foreground h-auto p-1"
                >
                  {getViewLabel(view)} <Calendar className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center">
                {Object.values(CalendarViewType).map(viewType => (
                  <DropdownMenuItem
                    key={viewType}
                    onClick={() => onViewChange(viewType)}
                    className={cn(view === viewType && 'bg-primary text-primary-foreground')}
                  >
                    {getViewLabel(viewType)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={navigateNext}
            disabled={isTransitioning}
            className="h-8 w-8 p-0 transition-all duration-200 active:scale-95"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <Button size="sm" onClick={handleCreateTaskForDay} className="flex items-center space-x-1">
          <Plus className="h-4 w-4" />
          <span>Dodaj</span>
        </Button>
      </div>

      {/* Mobile Tab Navigation - Only show on mobile */}
      <div className="lg:hidden border-b bg-background">
        <div className="flex">
          <button
            onClick={() => setMobileActiveTab('calendar')}
            className={cn(
              'flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors',
              mobileActiveTab === 'calendar'
                ? 'bg-primary text-primary-foreground border-b-2 border-primary'
                : 'text-muted-foreground hover:text-foreground'
            )}
          >
            <CalendarDays className="h-4 w-4" />
            <span>Kalendarz</span>
          </button>
          <button
            onClick={() => setMobileActiveTab('tasks')}
            className={cn(
              'flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors',
              mobileActiveTab === 'tasks'
                ? 'bg-primary text-primary-foreground border-b-2 border-primary'
                : 'text-muted-foreground hover:text-foreground'
            )}
          >
            <List className="h-4 w-4" />
            <span>Zadania</span>
            {selectedDayTasks.length > 0 && (
              <Badge variant="secondary" className="text-xs ml-1">
                {selectedDayTasks.length}
              </Badge>
            )}
          </button>
        </div>
      </div>

      <div className="flex-1 flex flex-col lg:flex-row overflow-hidden">
        {/* Main Calendar Area */}
        <div
          className={cn(
            'lg:w-1/2 border-r',
            // On mobile, show/hide based on active tab
            'lg:block',
            mobileActiveTab === 'calendar' ? 'block' : 'hidden lg:block'
          )}
        >
          {view === CalendarViewType.MONTH && renderMonthView()}
          {view === CalendarViewType.WEEK && renderWeekView()}
          {view === CalendarViewType.DAY && renderDayView()}
          {view === CalendarViewType.AGENDA && renderAgendaView()}
        </div>

        {/* Right Panel - Hide for agenda view */}
        {view !== CalendarViewType.AGENDA && (
          <div
            className={cn(
              'lg:w-1/2 flex flex-col',
              // On mobile, show/hide based on active tab
              'lg:block',
              mobileActiveTab === 'tasks' ? 'block' : 'hidden lg:block'
            )}
          >
            <div className="p-4 border-b bg-muted/30">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">
                  {view === CalendarViewType.DAY
                    ? format(currentDate, 'EEEE, d MMMM', { locale: pl })
                    : format(selectedDay, 'EEEE, d MMMM', { locale: pl })}
                </h3>
                <Badge variant="secondary">
                  {selectedDayTasks.length} {selectedDayTasks.length === 1 ? 'zadanie' : 'zadań'}
                </Badge>
              </div>
            </div>

            <div
              className={cn(
                'p-4 space-y-3 transition-all duration-300 ease-in-out flex-1 overflow-y-auto',
                isTransitioning && 'opacity-75'
              )}
            >
              {selectedDayTasks.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-muted-foreground mb-4">Brak zadań na ten dzień</div>
                  <Button onClick={handleCreateTaskForDay} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Dodaj zadanie
                  </Button>
                </div>
              ) : (
                selectedDayTasks.map(task => (
                  <Card
                    key={task.id}
                    className={cn(
                      'p-4 cursor-pointer transition-all duration-200',
                      'hover:shadow-md hover:-translate-y-0.5',
                      'border-l-4'
                    )}
                    style={{ borderLeftColor: task.color || '#3B82F6' }}
                    onClick={() => onTaskClick(task)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm mb-1">{task.title}</h4>

                        {task.description && (
                          <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                            {task.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            {!task.allDay && (
                              <div className="text-xs font-medium text-primary">
                                {format(new Date(task.start), 'HH:mm')}
                                {task.end && ` - ${format(new Date(task.end), 'HH:mm')}`}
                              </div>
                            )}

                            {(() => {
                              const points = task.metadata?.points;
                              return typeof points === 'number' && points > 0 ? (
                                <Badge variant="secondary" className="text-xs">
                                  {points}p
                                </Badge>
                              ) : null;
                            })()}
                          </div>

                          <AssigneeDisplay
                            assignments={task.assignments}
                            assignedTo={task.assignedTo}
                            assignmentType={task.assignmentType}
                            variant="calendar"
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
