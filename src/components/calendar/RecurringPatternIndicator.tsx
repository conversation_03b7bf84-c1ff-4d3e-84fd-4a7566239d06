/**
 * Komponent do wyświetlania wzorca cykliczności zadania
 * Pokazuje wizualną reprezentację jak często zadanie się powtarza
 */

import React from 'react';
import { TaskFrequency } from '@prisma/client';
import { Calendar1, Repeat2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

interface RecurringPatternIndicatorProps {
  frequency: TaskFrequency;
  interval?: number;
  startDate?: Date;
  endDate?: Date;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

const PATTERN_CONFIG = {
  DAILY: {
    icon: Repeat2,
    label: 'Codziennie',
    color: 'bg-green-100 text-green-800 border-green-200',
    pattern: '●●●●●●●',
    description: 'Powtarza się każdego dnia',
  },
  WEEKLY: {
    icon: Repeat2,
    label: 'Tygodniowo',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    pattern: '●○○○○○○',
    description: 'Powtarza się co tydzień',
  },
  MONTHLY: {
    icon: Repeat2,
    label: 'Miesięcznie',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    pattern: '●○○○',
    description: 'Powtarza się co miesiąc',
  },
  YEARLY: {
    icon: Repeat2,
    label: 'Rocznie',
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    pattern: '●○○○',
    description: 'Powtarza się co rok',
  },
  CUSTOM: {
    icon: Repeat2,
    label: 'Niestandardowe',
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    pattern: '●○●○●',
    description: 'Niestandardowy wzorzec',
  },
  ONCE: {
    icon: Calendar1,
    label: 'Jednorazowe',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    pattern: '●',
    description: 'Zadanie jednorazowe',
  },
} as const;

const SIZE_CONFIG = {
  sm: {
    icon: 'w-3 h-3',
    text: 'text-xs',
    badge: 'text-xs px-1.5 py-0.5',
    pattern: 'text-xs',
  },
  md: {
    icon: 'w-4 h-4',
    text: 'text-sm',
    badge: 'text-sm px-2 py-1',
    pattern: 'text-sm',
  },
  lg: {
    icon: 'w-5 h-5',
    text: 'text-base',
    badge: 'text-base px-3 py-1.5',
    pattern: 'text-base',
  },
} as const;

export default function RecurringPatternIndicator({
  frequency,
  interval = 1,
  startDate,
  endDate,
  size = 'md',
  showLabel = true,
  className,
}: RecurringPatternIndicatorProps) {
  const config = PATTERN_CONFIG[frequency];
  const sizeConfig = SIZE_CONFIG[size];
  const Icon = config.icon;

  const getDisplayLabel = () => {
    if (frequency === 'CUSTOM' && interval > 1) {
      return `Co ${interval} dni`;
    }

    if (frequency === 'DAILY' && interval > 1) {
      return `Co ${interval} dni`;
    }

    if (frequency === 'WEEKLY' && interval > 1) {
      return `Co ${interval} tygodni`;
    }

    if (frequency === 'MONTHLY' && interval > 1) {
      return `Co ${interval} miesięcy`;
    }

    if (frequency === 'YEARLY' && interval > 1) {
      return `Co ${interval} lat`;
    }

    return config.label;
  };

  const getVisualPattern = () => {
    if (frequency === 'CUSTOM' && interval > 1) {
      // Tworzymy wzorzec dla niestandardowego interwału
      const pattern = Array(7).fill('○');
      pattern[0] = '●'; // Pierwsza pozycja aktywna
      if (interval <= 7) {
        pattern[interval - 1] = '●'; // Następna pozycja aktywna
      }
      return pattern.join('');
    }

    return config.pattern;
  };

  if (!showLabel) {
    return (
      <div className={cn('flex items-center gap-1', className)}>
        <Icon className={cn(sizeConfig.icon, 'text-muted-foreground')} />
        <span className={cn(sizeConfig.pattern, 'font-mono text-muted-foreground')}>
          {getVisualPattern()}
        </span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Badge
        variant="secondary"
        className={cn('flex items-center gap-1.5', config.color, sizeConfig.badge)}
      >
        <Icon className={sizeConfig.icon} />
        <span className={sizeConfig.text}>{getDisplayLabel()}</span>
      </Badge>

      {/* Visual pattern representation */}
      <div className="flex items-center gap-1">
        <span className={cn(sizeConfig.pattern, 'font-mono text-muted-foreground')}>
          {getVisualPattern()}
        </span>
      </div>
    </div>
  );
}

/**
 * Kompaktowy wariant - tylko ikona z tooltipem
 */
export function CompactPatternIndicator({
  frequency,
  interval = 1,
  className,
}: Pick<RecurringPatternIndicatorProps, 'frequency' | 'interval' | 'className'>) {
  const config = PATTERN_CONFIG[frequency];
  const Icon = config.icon;

  return (
    <div
      className={cn(
        'inline-flex items-center justify-center w-5 h-5 rounded-full',
        'bg-muted text-muted-foreground',
        'transition-colors hover:bg-muted/80',
        className
      )}
      title={config.description}
    >
      <Icon className="w-3 h-3" />
    </div>
  );
}

/**
 * Detailed pattern indicator z informacjami o terminach
 */
export function DetailedPatternIndicator({
  frequency,
  interval = 1,
  startDate,
  endDate,
  className,
}: RecurringPatternIndicatorProps) {
  const config = PATTERN_CONFIG[frequency];
  const Icon = config.icon;

  const formatDateRange = () => {
    if (!startDate) return null;

    const start = startDate.toLocaleDateString('pl-PL');
    const end = endDate ? endDate.toLocaleDateString('pl-PL') : 'bez końca';

    return `${start} - ${end}`;
  };

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center gap-2">
        <RecurringPatternIndicator
          frequency={frequency}
          interval={interval}
          size="md"
          showLabel={true}
        />
      </div>

      {formatDateRange() && (
        <div className="text-sm text-muted-foreground">{formatDateRange()}</div>
      )}

      <div className="text-xs text-muted-foreground">{config.description}</div>
    </div>
  );
}
