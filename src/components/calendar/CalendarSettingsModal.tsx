'use client';

import React, { useState, useEffect } from 'react';
import { CalendarView, WeekDay, TimeFormat } from '@prisma/client';
import { CalendarSettings } from '@/lib/validations/calendar';
import { useCalendarSettings } from '@/hooks/useCalendar';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Calendar,
  Clock,
  Eye,
  Palette,
  CalendarDays,
  Smartphone,
  Settings,
  Save,
  RotateCcw,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CalendarSettingsModalProps {
  familyId: string;
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

const viewOptions = [
  { value: CalendarView.MONTH, label: 'Miesiąc', icon: Calendar },
  { value: CalendarView.WEEK, label: 'Tydzień', icon: CalendarDays },
  { value: CalendarView.DAY, label: 'Dzień', icon: Clock },
  { value: CalendarView.AGENDA, label: 'Agenda', icon: Eye },
];

const weekStartOptions = [
  { value: WeekDay.MONDAY, label: 'Poniedziałek' },
  { value: WeekDay.SUNDAY, label: 'Niedziela' },
];

const timeFormatOptions = [
  { value: TimeFormat.HOUR_24, label: '24-godzinny (14:30)' },
  { value: TimeFormat.HOUR_12, label: '12-godzinny (2:30 PM)' },
];

const colorSchemes = [
  { value: 'default', label: 'Domyślny', colors: ['#3B82F6', '#10B981', '#F59E0B', '#8B5CF6'] },
  { value: 'warm', label: 'Ciepły', colors: ['#EF4444', '#F97316', '#EAB308', '#84CC16'] },
  { value: 'cool', label: 'Chłodny', colors: ['#06B6D4', '#8B5CF6', '#EC4899', '#10B981'] },
  { value: 'pastel', label: 'Pastelowy', colors: ['#F1C0E8', '#B39EB5', '#FFB3C6', '#C7CEEA'] },
];

export default function CalendarSettingsModal({
  familyId,
  isOpen,
  onClose,
  className,
}: CalendarSettingsModalProps) {
  const { toast } = useToast();
  const { settings, isLoading, updateSettings, isUpdatingSettings, updateSettingsError } =
    useCalendarSettings(familyId);

  const [formData, setFormData] = useState<Partial<CalendarSettings>>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form data when settings load
  useEffect(() => {
    if (settings) {
      setFormData({
        defaultView: settings.defaultView,
        startWeekOn: settings.startWeekOn,
        timeFormat: settings.timeFormat,
        showWeekends: settings.showWeekends,
        showCompleted: settings.showCompleted,
        colorScheme: settings.colorScheme,
        googleCalendarEnabled: settings.googleCalendarEnabled,
        appleCalendarEnabled: settings.appleCalendarEnabled,
      });
    }
  }, [settings]);

  // Track changes
  useEffect(() => {
    if (settings) {
      const hasChanges = Object.keys(formData).some(
        key => formData[key as keyof CalendarSettings] !== settings[key as keyof CalendarSettings]
      );
      setHasChanges(hasChanges);
    }
  }, [formData, settings]);

  const handleSave = async () => {
    try {
      await updateSettings(formData);
      toast({
        title: 'Sukces',
        description: 'Ustawienia kalendarza zostały zapisane',
      });
      onClose();
    } catch (error) {
      toast({
        title: 'Błąd',
        description: 'Nie udało się zapisać ustawień kalendarza',
        variant: 'destructive',
      });
    }
  };

  const handleReset = () => {
    if (settings) {
      setFormData({
        defaultView: settings.defaultView,
        startWeekOn: settings.startWeekOn,
        timeFormat: settings.timeFormat,
        showWeekends: settings.showWeekends,
        showCompleted: settings.showCompleted,
        colorScheme: settings.colorScheme,
        googleCalendarEnabled: settings.googleCalendarEnabled,
        appleCalendarEnabled: settings.appleCalendarEnabled,
      });
    }
  };

  const updateFormData = (key: keyof CalendarSettings, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent" />
              <span>Ładowanie ustawień...</span>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn('max-w-2xl max-h-[90vh] overflow-y-auto', className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Ustawienia kalendarza</span>
          </DialogTitle>
          <DialogDescription>
            Dostosuj kalendarz do swoich preferencji. Zmiany będą widoczne dla wszystkich członków
            rodziny.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Widok i nawigacja */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Widok kalendarza</CardTitle>
              <CardDescription>Ustaw domyślny widok i sposób nawigacji</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="default-view">Domyślny widok</Label>
                <Select
                  value={formData.defaultView}
                  onValueChange={(value: CalendarView) => updateFormData('defaultView', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Wybierz widok" />
                  </SelectTrigger>
                  <SelectContent>
                    {viewOptions.map(option => {
                      const Icon = option.icon;
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center space-x-2">
                            <Icon className="h-4 w-4" />
                            <span>{option.label}</span>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="week-start">Pierwszy dzień tygodnia</Label>
                <Select
                  value={formData.startWeekOn}
                  onValueChange={(value: WeekDay) => updateFormData('startWeekOn', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Wybierz dzień" />
                  </SelectTrigger>
                  <SelectContent>
                    {weekStartOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time-format">Format czasu</Label>
                <Select
                  value={formData.timeFormat}
                  onValueChange={(value: TimeFormat) => updateFormData('timeFormat', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Wybierz format" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeFormatOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Wyświetlanie */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Opcje wyświetlania</CardTitle>
              <CardDescription>Dostosuj to, co widać w kalendarzu</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-weekends">Pokazuj weekendy</Label>
                  <p className="text-sm text-muted-foreground">
                    Wyświetlaj soboty i niedziele w kalendarzu
                  </p>
                </div>
                <Switch
                  id="show-weekends"
                  checked={formData.showWeekends}
                  onCheckedChange={checked => updateFormData('showWeekends', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-completed">Pokazuj ukończone zadania</Label>
                  <p className="text-sm text-muted-foreground">
                    Wyświetlaj zadania oznaczone jako ukończone
                  </p>
                </div>
                <Switch
                  id="show-completed"
                  checked={formData.showCompleted}
                  onCheckedChange={checked => updateFormData('showCompleted', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Kolory */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center space-x-2">
                <Palette className="h-4 w-4" />
                <span>Schemat kolorów</span>
              </CardTitle>
              <CardDescription>Wybierz zestaw kolorów dla zadań</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-3">
                {colorSchemes.map(scheme => (
                  <div
                    key={scheme.value}
                    className={cn(
                      'p-3 border rounded-lg cursor-pointer transition-colors',
                      formData.colorScheme === scheme.value
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    )}
                    onClick={() => updateFormData('colorScheme', scheme.value)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">{scheme.label}</span>
                      {formData.colorScheme === scheme.value && (
                        <div className="w-2 h-2 bg-primary rounded-full" />
                      )}
                    </div>
                    <div className="flex space-x-1">
                      {scheme.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Integracje */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center space-x-2">
                <Smartphone className="h-4 w-4" />
                <span>Integracje</span>
              </CardTitle>
              <CardDescription>Synchronizuj z zewnętrznymi kalendarzami</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="google-calendar">Google Calendar</Label>
                  <p className="text-sm text-muted-foreground">
                    Synchronizuj zadania z Google Calendar
                  </p>
                </div>
                <Switch
                  id="google-calendar"
                  checked={formData.googleCalendarEnabled}
                  onCheckedChange={checked => updateFormData('googleCalendarEnabled', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="apple-calendar">Apple Calendar</Label>
                  <p className="text-sm text-muted-foreground">
                    Synchronizuj zadania z Apple Calendar
                  </p>
                </div>
                <Switch
                  id="apple-calendar"
                  checked={formData.appleCalendarEnabled}
                  onCheckedChange={checked => updateFormData('appleCalendarEnabled', checked)}
                />
              </div>

              {(formData.googleCalendarEnabled || formData.appleCalendarEnabled) && (
                <div className="mt-4 p-3 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground">
                    Funkcje synchronizacji będą dostępne w przyszłych aktualizacjach.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <Separator />

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center space-x-2"
              >
                <RotateCcw className="h-4 w-4" />
                <span>Resetuj</span>
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Anuluj
            </Button>
            <Button
              type="button"
              onClick={handleSave}
              disabled={!hasChanges || isUpdatingSettings}
              className="flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{isUpdatingSettings ? 'Zapisywanie...' : 'Zapisz'}</span>
            </Button>
          </div>
        </DialogFooter>

        {updateSettingsError && (
          <div className="mt-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">
              Błąd podczas zapisywania ustawień. Spróbuj ponownie.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
