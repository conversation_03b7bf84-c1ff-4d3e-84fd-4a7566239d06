'use client';

import React from 'react';
import { CalendarFilters as CalendarFiltersType } from '@/lib/validations/calendar';
import { TaskStatus } from '@prisma/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Filter, X, Users, CheckCircle, Clock, Pause } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FamilyMember {
  id: string;
  user: {
    firstName: string | null;
    lastName: string | null;
  };
}

interface CalendarFiltersProps {
  filters: CalendarFiltersType;
  members: FamilyMember[];
  onFiltersChange: (filters: CalendarFiltersType) => void;
  className?: string;
}

const taskCategories = [
  { id: 'household', name: 'Gospodarstwo domowe', color: '#10B981' },
  { id: 'education', name: 'Edukacja', color: '#3B82F6' },
  { id: 'outdoor', name: 'Aktywności zewnętrzne', color: '#F59E0B' },
  { id: 'personal', name: 'Rozwój osobisty', color: '#8B5CF6' },
  { id: 'health', name: 'Zdrowie', color: '#EF4444' },
  { id: 'social', name: 'Społeczne', color: '#EC4899' },
];

const taskStatuses = [
  { value: TaskStatus.ACTIVE, label: 'Aktywne', icon: Clock, color: '#3B82F6' },
  { value: TaskStatus.COMPLETED, label: 'Ukończone', icon: CheckCircle, color: '#10B981' },
  { value: TaskStatus.SUSPENDED, label: 'Zawieszone', icon: Pause, color: '#F59E0B' },
];

export default function CalendarFilters({
  filters,
  members,
  onFiltersChange,
  className,
}: CalendarFiltersProps) {
  const handleMemberToggle = (memberId: string) => {
    onFiltersChange({
      ...filters,
      memberId: memberId === 'all' ? undefined : memberId,
    });
  };

  const handleStatusToggle = (status: string) => {
    const currentStatuses = filters.taskStatus || [];
    const newStatuses = currentStatuses.includes(status)
      ? currentStatuses.filter(s => s !== status)
      : [...currentStatuses, status];

    onFiltersChange({
      ...filters,
      taskStatus: newStatuses,
    });
  };

  const handleCategoryToggle = (category: string) => {
    const currentCategories = filters.taskCategory || [];
    const newCategories = currentCategories.includes(category)
      ? currentCategories.filter(c => c !== category)
      : [...currentCategories, category];

    onFiltersChange({
      ...filters,
      taskCategory: newCategories,
    });
  };

  const handleShowCompletedToggle = (checked: boolean) => {
    onFiltersChange({
      ...filters,
      showCompleted: checked,
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      ...filters,
      memberId: undefined,
      taskStatus: [],
      taskCategory: [],
      showCompleted: false,
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.memberId) count++;
    if (filters.taskStatus && filters.taskStatus.length > 0) count++;
    if (filters.taskCategory && filters.taskCategory.length > 0) count++;
    if (filters.showCompleted) count++;
    return count;
  };

  const getMemberName = (member: FamilyMember) => {
    const firstName = member.user.firstName || '';
    const lastName = member.user.lastName || '';
    return `${firstName} ${lastName}`.trim() || 'Nieznany';
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card className={cn('border-0 shadow-none', className)}>
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">Filtry</span>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          )}
        </div>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4 mr-1" />
            Wyczyść
          </Button>
        )}
      </div>

      <div className="p-4 space-y-6">
        {/* Member Filter */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <Label className="text-sm font-medium">Członek rodziny</Label>
          </div>

          <Select
            value={filters.memberId || 'all'}
            onValueChange={value => handleMemberToggle(value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Wszyscy członkowie" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Wszyscy członkowie</SelectItem>
              {members.map(member => (
                <SelectItem key={member.id} value={member.id}>
                  {getMemberName(member)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Task Status Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Status zadania</Label>
          <div className="space-y-2">
            {taskStatuses.map(status => {
              const Icon = status.icon;
              const isSelected = filters.taskStatus?.includes(status.value) || false;

              return (
                <div key={status.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`status-${status.value}`}
                    checked={isSelected}
                    onCheckedChange={() => handleStatusToggle(status.value)}
                  />
                  <Label
                    htmlFor={`status-${status.value}`}
                    className="flex items-center space-x-2 text-sm cursor-pointer"
                  >
                    <Icon className="h-4 w-4" style={{ color: status.color }} />
                    <span>{status.label}</span>
                  </Label>
                </div>
              );
            })}
          </div>
        </div>

        {/* Task Category Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Kategoria zadania</Label>
          <div className="space-y-2">
            {taskCategories.map(category => {
              const isSelected = filters.taskCategory?.includes(category.id) || false;

              return (
                <div key={category.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={`category-${category.id}`}
                    checked={isSelected}
                    onCheckedChange={() => handleCategoryToggle(category.id)}
                  />
                  <Label
                    htmlFor={`category-${category.id}`}
                    className="flex items-center space-x-2 text-sm cursor-pointer"
                  >
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    <span>{category.name}</span>
                  </Label>
                </div>
              );
            })}
          </div>
        </div>

        {/* Show Completed Tasks */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show-completed"
              checked={filters.showCompleted}
              onCheckedChange={handleShowCompletedToggle}
            />
            <Label htmlFor="show-completed" className="text-sm font-medium cursor-pointer">
              Pokaż ukończone zadania
            </Label>
          </div>
        </div>

        {/* Color Legend */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Legenda kolorów</Label>
          <div className="grid grid-cols-2 gap-2">
            {taskCategories.map(category => (
              <div key={category.id} className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: category.color }} />
                <span className="text-xs text-muted-foreground">{category.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}

// Compact version for mobile/sidebar
export function CalendarFiltersCompact({
  filters,
  members,
  onFiltersChange,
  className,
}: CalendarFiltersProps) {
  const activeFiltersCount = React.useMemo(() => {
    let count = 0;
    if (filters.memberId) count++;
    if (filters.taskStatus && filters.taskStatus.length > 0) count++;
    if (filters.taskCategory && filters.taskCategory.length > 0) count++;
    if (filters.showCompleted) count++;
    return count;
  }, [filters]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={cn('relative', className)}>
          <Filter className="h-4 w-4 mr-2" />
          Filtry
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-80" align="end">
        <CalendarFilters
          filters={filters}
          members={members}
          onFiltersChange={onFiltersChange}
          className="border-0 shadow-none"
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
