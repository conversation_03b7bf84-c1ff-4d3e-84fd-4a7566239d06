/**
 * Komponent do wyświetlania zadań cyklicznych w kalendarzu
 * Pokazuje małe markery zamiast ciągłych pasków
 */

import React from 'react';
import { Repeat2, Calendar1 } from 'lucide-react';
import { TaskFrequency } from '@prisma/client';
import { format } from 'date-fns';
import { pl } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { CalendarTask } from '@/lib/validations/calendar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';

interface RecurringTaskMarkerProps {
  task: CalendarTask;
  date: Date;
  size?: 'sm' | 'md' | 'lg';
  showTitle?: boolean;
  onClick?: (task: CalendarTask) => void;
  className?: string;
}

const FREQUENCY_ICONS = {
  DAILY: Repeat2,
  WEEKLY: Repeat2,
  MONTHLY: Repeat2,
  YEARLY: Repeat2,
  CUSTOM: Repeat2,
  ONCE: Calendar1,
} as const;

const FREQUENCY_LABELS = {
  DAILY: 'Codziennie',
  WEEKLY: 'Tygodniowo',
  MONTHLY: 'Miesięcznie',
  YEARLY: 'Rocznie',
  CUSTOM: 'Niestandardowe',
  ONCE: 'Jednorazowe',
} as const;

const FREQUENCY_COLORS = {
  DAILY: {
    bg: 'bg-green-100 dark:bg-green-900/20',
    border: 'border-green-300 dark:border-green-700',
    text: 'text-green-700 dark:text-green-300',
    marker: 'bg-green-500',
  },
  WEEKLY: {
    bg: 'bg-blue-100 dark:bg-blue-900/20',
    border: 'border-blue-300 dark:border-blue-700',
    text: 'text-blue-700 dark:text-blue-300',
    marker: 'bg-blue-500',
  },
  MONTHLY: {
    bg: 'bg-purple-100 dark:bg-purple-900/20',
    border: 'border-purple-300 dark:border-purple-700',
    text: 'text-purple-700 dark:text-purple-300',
    marker: 'bg-purple-500',
  },
  YEARLY: {
    bg: 'bg-orange-100 dark:bg-orange-900/20',
    border: 'border-orange-300 dark:border-orange-700',
    text: 'text-orange-700 dark:text-orange-300',
    marker: 'bg-orange-500',
  },
  CUSTOM: {
    bg: 'bg-indigo-100 dark:bg-indigo-900/20',
    border: 'border-indigo-300 dark:border-indigo-700',
    text: 'text-indigo-700 dark:text-indigo-300',
    marker: 'bg-indigo-500',
  },
  ONCE: {
    bg: 'bg-gray-100 dark:bg-gray-900/20',
    border: 'border-gray-300 dark:border-gray-700',
    text: 'text-gray-700 dark:text-gray-300',
    marker: 'bg-gray-500',
  },
} as const;

const SIZE_VARIANTS = {
  sm: {
    marker: 'w-2 h-2',
    container: 'p-1',
    text: 'text-xs',
    icon: 'w-3 h-3',
  },
  md: {
    marker: 'w-3 h-3',
    container: 'p-1.5',
    text: 'text-sm',
    icon: 'w-4 h-4',
  },
  lg: {
    marker: 'w-4 h-4',
    container: 'p-2',
    text: 'text-base',
    icon: 'w-5 h-5',
  },
} as const;

export default function RecurringTaskMarker({
  task,
  date,
  size = 'md',
  showTitle = false,
  onClick,
  className,
}: RecurringTaskMarkerProps) {
  // Pobieramy częstotliwość z task lub domyślnie ONCE
  const frequency = task.frequency || 'ONCE';

  const FrequencyIcon = FREQUENCY_ICONS[frequency];
  const colors = FREQUENCY_COLORS[frequency];
  const sizes = SIZE_VARIANTS[size];

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.(task);
  };

  const getTooltipContent = () => {
    const frequencyLabel = FREQUENCY_LABELS[frequency];
    const dateLabel = format(date, 'PPP', { locale: pl });

    return (
      <div className="space-y-2">
        <div className="font-semibold">{task.title}</div>
        {task.description && (
          <div className="text-sm text-muted-foreground">{task.description}</div>
        )}
        <div className="flex items-center gap-2 text-sm">
          <FrequencyIcon className="w-3 h-3" />
          <span>{frequencyLabel}</span>
        </div>
        <div className="text-xs text-muted-foreground">{dateLabel}</div>
        {task.assignments && task.assignments.length > 0 && (
          <div className="text-xs">
            <span className="text-muted-foreground">Przypisane do: </span>
            {task.assignments
              .map(assignment => assignment.member.user.firstName || assignment.member.user.email)
              .join(', ')}
          </div>
        )}
      </div>
    );
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'flex items-center gap-2 rounded-md border cursor-pointer',
              'transition-all duration-200 hover:scale-105 active:scale-95',
              'hover:shadow-md active:shadow-sm',
              colors.bg,
              colors.border,
              colors.text,
              sizes.container,
              className
            )}
            onClick={handleClick}
            style={{
              backgroundColor: task.color ? `${task.color}20` : undefined,
              borderColor: task.color || undefined,
              color: task.color || undefined,
            }}
          >
            {/* Marker dot */}
            <div
              className={cn(
                'rounded-full flex-shrink-0',
                sizes.marker,
                task.color ? '' : colors.marker
              )}
              style={{
                backgroundColor: task.color || undefined,
              }}
            />

            {/* Task info */}
            <div className="flex-1 min-w-0">
              {showTitle && (
                <div className={cn('font-medium truncate', sizes.text)}>{task.title}</div>
              )}

              {/* Frequency icon */}
              <div className="flex items-center gap-1">
                <FrequencyIcon className={cn('flex-shrink-0', sizes.icon)} />
                {!showTitle && <span className={cn('truncate', sizes.text)}>{task.title}</span>}
              </div>
            </div>

            {/* Time badge for timed tasks */}
            {!task.allDay && (
              <Badge variant="outline" className={cn('text-xs flex-shrink-0', colors.text)}>
                {format(task.start, 'HH:mm')}
              </Badge>
            )}
          </div>
        </TooltipTrigger>

        <TooltipContent side="top" align="start" className="max-w-xs">
          {getTooltipContent()}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Kompaktowy wariant markera - tylko kropka z tooltipem
 */
export function CompactRecurringTaskMarker({
  task,
  date,
  onClick,
  className,
}: Omit<RecurringTaskMarkerProps, 'size' | 'showTitle'>) {
  const frequency = task.frequency || 'ONCE';
  const colors = FREQUENCY_COLORS[frequency];
  const FrequencyIcon = FREQUENCY_ICONS[frequency];

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick?.(task);
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={500}>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'relative w-3 h-3 rounded-full cursor-pointer',
              'transition-all duration-200 hover:scale-125 active:scale-110',
              'hover:shadow-lg',
              task.color ? '' : colors.marker,
              className
            )}
            onClick={handleClick}
            style={{
              backgroundColor: task.color || undefined,
            }}
          >
            {/* Recurring indicator */}
            {task.isRecurring && (
              <div className="absolute -top-0.5 -right-0.5">
                <FrequencyIcon className="w-2 h-2 text-white bg-black/50 rounded-full p-0.5" />
              </div>
            )}
          </div>
        </TooltipTrigger>

        <TooltipContent side="top" className="max-w-xs">
          <div className="space-y-1">
            <div className="font-semibold">{task.title}</div>
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <FrequencyIcon className="w-3 h-3" />
              <span>{FREQUENCY_LABELS[frequency]}</span>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
