'use client';

import React, { useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Settings, Plus } from 'lucide-react';
import { CalendarView } from '@prisma/client';
import { format, addMonths, addWeeks, addDays, subMonths, subWeeks, subDays } from 'date-fns';
import { pl } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
// import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
// import { Calendar } from '@/components/ui/calendar';

interface CalendarNavigationProps {
  currentDate: Date;
  currentView: CalendarView;
  onDateChange: (date: Date) => void;
  onViewChange: (view: CalendarView) => void;
  onSettingsClick?: () => void;
  onCreateEvent?: () => void; // Kompatybilność wsteczna - tworzy zadanie
  className?: string;
}

export default function CalendarNavigation({
  currentDate,
  currentView,
  onDateChange,
  onViewChange,
  onSettingsClick,
  onCreateEvent,
  className,
}: CalendarNavigationProps) {
  const navigatePrevious = useCallback(() => {
    let newDate: Date;

    switch (currentView) {
      case CalendarView.MONTH:
        newDate = subMonths(currentDate, 1);
        break;
      case CalendarView.WEEK:
        newDate = subWeeks(currentDate, 1);
        break;
      case CalendarView.DAY:
        newDate = subDays(currentDate, 1);
        break;
      case CalendarView.AGENDA:
        newDate = subWeeks(currentDate, 1);
        break;
      default:
        newDate = subMonths(currentDate, 1);
    }

    onDateChange(newDate);
  }, [currentView, currentDate, onDateChange]);

  const navigateNext = useCallback(() => {
    let newDate: Date;

    switch (currentView) {
      case CalendarView.MONTH:
        newDate = addMonths(currentDate, 1);
        break;
      case CalendarView.WEEK:
        newDate = addWeeks(currentDate, 1);
        break;
      case CalendarView.DAY:
        newDate = addDays(currentDate, 1);
        break;
      case CalendarView.AGENDA:
        newDate = addWeeks(currentDate, 1);
        break;
      default:
        newDate = addMonths(currentDate, 1);
    }

    onDateChange(newDate);
  }, [currentView, currentDate, onDateChange]);

  const navigateToday = useCallback(() => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day
    onDateChange(today);
  }, [onDateChange]);

  const dateTitle = useMemo(() => {
    switch (currentView) {
      case CalendarView.MONTH:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
      case CalendarView.WEEK:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
      case CalendarView.DAY:
        return format(currentDate, 'EEEE, d MMMM yyyy', { locale: pl });
      case CalendarView.AGENDA:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
      default:
        return format(currentDate, 'LLLL yyyy', { locale: pl });
    }
  }, [currentView, currentDate]);

  const getViewLabel = (view: CalendarView) => {
    switch (view) {
      case CalendarView.MONTH:
        return 'Miesiąc';
      case CalendarView.WEEK:
        return 'Tydzień';
      case CalendarView.DAY:
        return 'Dzień';
      case CalendarView.AGENDA:
        return 'Agenda';
      default:
        return 'Miesiąc';
    }
  };

  const isViewActive = (view: CalendarView) => view === currentView;

  return (
    <>
      {/* Desktop Navigation */}
      <div className={cn('hidden lg:flex items-center justify-between p-4 border-b', className)}>
        {/* Left side - Navigation */}
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={navigatePrevious} className="h-8 w-8 p-0">
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <Button variant="outline" size="sm" onClick={navigateNext} className="h-8 w-8 p-0">
            <ChevronRight className="h-4 w-4" />
          </Button>

          <Button variant="outline" size="sm" onClick={navigateToday} className="px-3">
            Dziś
          </Button>
        </div>

        {/* Center - Date Title */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-lg font-semibold">
            <CalendarIcon className="h-4 w-4" />
            <span className="capitalize">{dateTitle}</span>
          </div>
        </div>

        {/* Right side - View Toggle, Create, Settings */}
        <div className="flex items-center space-x-2">
          {/* View Toggle */}
          <div className="flex border rounded-md">
            {Object.values(CalendarView).map(view => (
              <Button
                key={view}
                variant={isViewActive(view) ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange(view)}
                className={cn(
                  'rounded-none first:rounded-l-md last:rounded-r-md border-none',
                  isViewActive(view) && 'bg-primary text-primary-foreground'
                )}
              >
                {getViewLabel(view)}
              </Button>
            ))}
          </div>

          {/* Create Task Button */}
          {onCreateEvent && (
            <Button
              variant="default"
              size="sm"
              onClick={onCreateEvent}
              className="flex items-center space-x-1"
            >
              <Plus className="h-4 w-4" />
              <span>Dodaj zadanie</span>
            </Button>
          )}

          {/* Settings Button */}
          {onSettingsClick && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onSettingsClick}>
                  <Settings className="mr-2 h-4 w-4" />
                  Ustawienia kalendarza
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Mobile Navigation - Simplified */}
      <div
        className={cn(
          'lg:hidden flex items-center justify-between p-3 border-b bg-background/95 backdrop-blur sticky top-0 z-10',
          className
        )}
      >
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={navigatePrevious} className="h-8 w-8 p-0">
            <ChevronLeft className="h-4 w-4" />
          </Button>

          <h2 className="text-base font-semibold capitalize min-w-0 truncate">{dateTitle}</h2>

          <Button variant="ghost" size="sm" onClick={navigateNext} className="h-8 w-8 p-0">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" onClick={navigateToday} className="text-xs px-2">
            Dziś
          </Button>

          {onCreateEvent && (
            <Button size="sm" onClick={onCreateEvent} className="flex items-center space-x-1">
              <Plus className="h-4 w-4" />
              <span className="sr-only">Dodaj</span>
            </Button>
          )}

          {onSettingsClick && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <Settings className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onSettingsClick}>
                  <Settings className="mr-2 h-4 w-4" />
                  Ustawienia
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>
    </>
  );
}
