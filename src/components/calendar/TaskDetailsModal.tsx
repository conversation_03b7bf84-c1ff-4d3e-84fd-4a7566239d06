'use client';

/**
 * TaskDetailsModal - modal do wyświetlania szczegółów zadania w kalendarzu
 *
 * UWAGA:
 * - Ten komponent wyświetla szczegóły zadania (CalendarTask)
 * - Wszystkie elementy w kalendarzu to zadania, nie wydarzenia
 * - prismaTask to opcjonalny obiekt Task z bazy (dla operacji usuwania)
 * - getTaskTypeLabel zawsze zwraca "Zadanie" bo nie ma innych typów
 */

import React, { useState } from 'react';
import { CalendarTask } from '@/lib/validations/calendar';
import { formatEventTime } from '@/lib/utils/date.utils';
import { TimeFormat } from '@prisma/client';
import { format } from 'date-fns';
import { pl } from 'date-fns/locale';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { AssigneeDisplay, AssignmentInfo } from '@/components/ui/AssigneeDisplay';
import { Calendar, Clock, User, Star, Edit, Trash2, CheckCircle, Repeat } from 'lucide-react';
import { cn } from '@/lib/utils';

import { DeleteTaskDialog } from '@/components/tasks/DeleteTaskDialog';
import { Task, CompletionStatus } from '@prisma/client';
interface TaskDetailsModalProps {
  task: CalendarTask;
  prismaTask?: Task;
  fullTaskData?: any;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onComplete?: () => void;
  timeFormat?: TimeFormat;
  canEdit?: boolean;
  canDelete?: boolean;
  canComplete?: boolean;
}

export default function TaskDetailsModal({
  task,
  prismaTask,
  fullTaskData,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onComplete,
  timeFormat = TimeFormat.HOUR_24,
  canEdit = true,
  canDelete = true,
  canComplete = true,
}: TaskDetailsModalProps) {
  const [taskToDelete, setTaskToDelete] = useState<Task | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Sprawdź status completion zadania
  const getCompletionStatus = () => {
    if (!fullTaskData?.completions || fullTaskData.completions.length === 0) {
      return null;
    }

    // Znajdź najnowsze completion
    const latestCompletion = fullTaskData.completions.sort(
      (a: any, b: any) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
    )[0];

    return latestCompletion;
  };

  const latestCompletion = getCompletionStatus();
  const isPendingApproval = latestCompletion?.status === CompletionStatus.PENDING;
  const isApproved = latestCompletion?.status === CompletionStatus.APPROVED;
  const isRejected = latestCompletion?.status === CompletionStatus.REJECTED;

  // Logika czy pokazać przycisk "Ukończ"
  const shouldShowCompleteButton = canComplete && onComplete && !isPendingApproval && !isApproved;

  const handleDeleteTask = async (task: { id: string; familyId: string; title: string }) => {
    setIsDeleting(true);
    setDeleteError(null);

    try {
      const response = await fetch(`/api/families/${task.familyId}/tasks/${task.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nie udało się usunąć zadania');
      }

      setTaskToDelete(null);
      onDelete();
    } catch (err: any) {
      console.error('Error deleting task:', err);
      setDeleteError(err.message || 'Wystąpił nieznany błąd podczas usuwania zadania.');
    } finally {
      setIsDeleting(false);
    }
  };

  /**
   * Zwraca label dla typu zadania
   *
   * UWAGA: Zawsze zwraca "Zadanie" ponieważ aplikacja nie obsługuje
   * prawdziwych wydarzeń - wszystko w kalendarzu to zadania z datami
   */
  const getTaskTypeLabel = () => {
    // Wszystkie elementy kalendarza to zadania
    return 'Zadanie';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'SUSPENDED':
        return 'bg-yellow-100 text-yellow-800';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCompletionStatusLabel = () => {
    if (isPendingApproval) return 'Oczekuje na zatwierdzenie';
    if (isApproved) return 'Zatwierdzone';
    if (isRejected) return 'Odrzucone';
    return null;
  };

  const getPriorityColor = (weight: string) => {
    switch (weight) {
      case 'LIGHT':
        return 'bg-green-100 text-green-800';
      case 'NORMAL':
        return 'bg-blue-100 text-blue-800';
      case 'HEAVY':
        return 'bg-orange-100 text-orange-800';
      case 'CRITICAL':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const parseRRule = (rrule: string): string => {
    try {
      const parts = rrule.split(';');
      const ruleMap: Record<string, string> = {};

      parts.forEach(part => {
        const [key, value] = part.split('=');
        if (key && value) {
          ruleMap[key] = value;
        }
      });

      const freq = ruleMap['FREQ'];
      const interval = parseInt(ruleMap['INTERVAL'] || '1');
      const count = ruleMap['COUNT'];
      const until = ruleMap['UNTIL'];
      const byday = ruleMap['BYDAY'];

      let description = '';

      // Częstotliwość podstawowa
      switch (freq) {
        case 'MINUTELY':
          description = interval === 1 ? 'Co minutę' : `Co ${interval} minut`;
          break;
        case 'HOURLY':
          description = interval === 1 ? 'Co godzinę' : `Co ${interval} godzin`;
          break;
        case 'DAILY':
          description = interval === 1 ? 'Codziennie' : `Co ${interval} dni`;
          break;
        case 'WEEKLY':
          description = interval === 1 ? 'Co tydzień' : `Co ${interval} tygodni`;
          break;
        case 'MONTHLY':
          description = interval === 1 ? 'Co miesiąc' : `Co ${interval} miesięcy`;
          break;
        case 'YEARLY':
          description = interval === 1 ? 'Co rok' : `Co ${interval} lat`;
          break;
        default:
          description = 'Niestandardowe';
      }

      // Dodaj informacje o dniach tygodnia dla WEEKLY
      if (freq === 'WEEKLY' && byday) {
        const dayLabels: Record<string, string> = {
          MO: 'poniedziałek',
          TU: 'wtorek',
          WE: 'środa',
          TH: 'czwartek',
          FR: 'piątek',
          SA: 'sobota',
          SU: 'niedziela',
        };

        const days = byday
          .split(',')
          .map(day => dayLabels[day] || day)
          .join(', ');
        description += ` (${days})`;
      }

      // Dodaj informacje o końcu
      if (count) {
        description += `, ${count} razy`;
      } else if (until) {
        const untilDate = new Date(
          until.replace(/(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})Z/, '$1-$2-$3T$4:$5:$6Z')
        );
        description += `, do ${untilDate.toLocaleDateString('pl-PL')}`;
      }

      return description;
    } catch {
      return 'Niestandardowe powtarzanie';
    }
  };

  const getFrequencyLabel = (frequency: string, recurrenceRule?: string) => {
    switch (frequency) {
      case 'ONCE':
        return 'Jednorazowe';
      case 'DAILY':
        return 'Codziennie';
      case 'WEEKLY':
        return 'Co tydzień';
      case 'MONTHLY':
        return 'Co miesiąc';
      case 'YEARLY':
        return 'Co rok';
      case 'CUSTOM':
        return recurrenceRule ? parseRRule(recurrenceRule) : 'Niestandardowe';
      default:
        return 'Jednorazowe';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <div
              title={`ID: ${task.id}`}
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: task.color || '#3B82F6' }}
            />
            <span className="truncate">{task.title}</span>
          </DialogTitle>
          <DialogDescription>Szczegóły zadania i dostępne akcje</DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Task Type and Status */}
          <div className="flex items-center justify-between">
            <Badge variant="outline" className="text-xs">
              {getTaskTypeLabel()}
            </Badge>
            {getCompletionStatusLabel() ? (
              <Badge className={cn('text-xs', getStatusColor(latestCompletion?.status || ''))}>
                {getCompletionStatusLabel()}
              </Badge>
            ) : task.metadata?.status ? (
              <Badge className={cn('text-xs', getStatusColor(String(task.metadata.status)))}>
                {String(task.metadata.status)}
              </Badge>
            ) : null}
          </div>

          {/* Description */}
          {task.description && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-1">Opis</h4>
              <p className="text-sm text-gray-600">{task.description}</p>
            </div>
          )}

          <Separator />

          {/* Date and Time */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2 text-sm">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Data:</span>
              <span>{format(task.start, 'EEEE, d MMMM yyyy', { locale: pl })}</span>
            </div>

            <div className="flex items-center space-x-2 text-sm">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Czas:</span>
              <span>{formatEventTime(task, timeFormat)}</span>
            </div>
          </div>

          {/* Assigned To */}
          {task.assignments && task.assignments.length > 0 && (
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium">Przypisane do:</span>
              <AssigneeDisplay
                assignments={task.assignments.map(assignment => ({
                  id: assignment.id,
                  member: {
                    id: assignment.member.id,
                    user: {
                      id: assignment.member.user.email, // używamy email jako id
                      firstName: assignment.member.user.firstName,
                      lastName: assignment.member.user.lastName,
                      email: assignment.member.user.email,
                    },
                  },
                }))}
                variant="modal"
              />
            </div>
          )}

          {/* Task-specific metadata */}
          {task.metadata && (
            <>
              <Separator />
              <div className="grid grid-cols-2 gap-4 text-sm">
                {task.metadata.points ? (
                  <div className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>{String(task.metadata.points)} pkt</span>
                  </div>
                ) : null}

                {task.metadata.weight ? (
                  <div className="flex items-center space-x-2">
                    <Badge
                      className={cn('text-xs', getPriorityColor(String(task.metadata.weight)))}
                    >
                      {String(task.metadata.weight)}
                    </Badge>
                  </div>
                ) : null}

                {task.metadata.frequency && String(task.metadata.frequency) !== 'ONCE' ? (
                  <div className="flex items-center space-x-2 col-span-2">
                    <Repeat className="h-4 w-4 text-gray-500" />
                    <span>
                      Cykliczne:{' '}
                      {getFrequencyLabel(
                        String(task.metadata.frequency),
                        task.metadata.recurrenceRule as string
                      )}
                    </span>
                  </div>
                ) : null}
              </div>
            </>
          )}

          {/* Completion Information */}
          {latestCompletion && (
            <>
              <Separator />
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Informacje o ukończeniu</h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">Ukończone przez:</span>
                    <span>
                      {latestCompletion.completedBy.user.firstName}{' '}
                      {latestCompletion.completedBy.user.lastName}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">Data ukończenia:</span>
                    <span>
                      {format(new Date(latestCompletion.completedAt), 'dd.MM.yyyy HH:mm', {
                        locale: pl,
                      })}
                    </span>
                  </div>
                  {latestCompletion.verifiedBy && (
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Zweryfikowane przez:</span>
                      <span>
                        {latestCompletion.verifiedBy.user.firstName}{' '}
                        {latestCompletion.verifiedBy.user.lastName}
                      </span>
                    </div>
                  )}
                  {latestCompletion.verifiedAt && (
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Data weryfikacji:</span>
                      <span>
                        {format(new Date(latestCompletion.verifiedAt), 'dd.MM.yyyy HH:mm', {
                          locale: pl,
                        })}
                      </span>
                    </div>
                  )}
                  {latestCompletion.pointsAwarded && (
                    <div className="flex items-center space-x-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">Przyznane punkty:</span>
                      <span>{latestCompletion.pointsAwarded}</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex justify-between space-x-2">
            <div className="flex space-x-2">
              {canEdit && (
                <Button variant="outline" size="sm" onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-1" />
                  Edytuj
                </Button>
              )}

              {canDelete && prismaTask && (
                <Button variant="outline" size="sm" onClick={() => setTaskToDelete(prismaTask)}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Usuń
                </Button>
              )}
            </div>

            {shouldShowCompleteButton && (
              <Button size="sm" onClick={onComplete}>
                <CheckCircle className="h-4 w-4 mr-1" />
                Ukończ
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
      <DeleteTaskDialog
        task={taskToDelete}
        open={!!taskToDelete}
        onOpenChange={open => !open && setTaskToDelete(null)}
        onDelete={handleDeleteTask}
        isLoading={isDeleting}
        error={deleteError}
      />
    </Dialog>
  );
}
