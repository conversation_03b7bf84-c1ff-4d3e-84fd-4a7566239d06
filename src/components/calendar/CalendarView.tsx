'use client';

import React from 'react';
import { CalendarView as CalendarViewType, WeekDay } from '@prisma/client';
import { CalendarTask } from '@/lib/validations/calendar';
import MobileCalendarView from './MobileCalendarView';

interface CalendarViewProps {
  view: CalendarViewType;
  date: Date;
  events: CalendarTask[]; // Kompatybilność wsteczna - events to tak naprawdę tasks
  loading?: boolean;
  onDateChange: (date: Date) => void;
  onViewChange: (view: CalendarViewType) => void;
  onEventClick: (task: CalendarTask) => void; // Kompatybilność wsteczna
  onEventDrop: (task: CalendarTask, newDate: Date) => void; // Kompatybilność wsteczna
  onSlotSelect: (startDate: Date, endDate?: Date) => void;
  onCreateTask?: () => void;
  className?: string;
  startWeekOn?: WeekDay;
  timeFormat?: '12h' | '24h';
  showWeekends?: boolean;
}

export default function CalendarView({
  view,
  date,
  events: tasks, // Dla jasności - events to tak naprawdę tasks
  loading,
  onDateChange,
  onViewChange,
  onEventClick: onTaskClick, // Kompatybilność wsteczna
  onEventDrop: onTaskDrop, // Kompatybilność wsteczna
  onSlotSelect,
  onCreateTask,
  className,
  startWeekOn = WeekDay.MONDAY,
  timeFormat = '24h',
  showWeekends = true,
}: CalendarViewProps) {
  // Simply pass everything to the new MobileCalendarView
  // This component now serves as a compatibility layer

  return (
    <MobileCalendarView
      view={view}
      date={date}
      tasks={tasks}
      loading={loading}
      onDateChange={onDateChange}
      onViewChange={onViewChange}
      onTaskClick={onTaskClick}
      onSlotSelect={onSlotSelect}
      onCreateTask={onCreateTask}
      className={className}
    />
  );
}
