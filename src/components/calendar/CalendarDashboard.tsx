'use client';

import React, { useState, useCallback } from 'react';
import { useCalendarState } from '@/hooks/useCalendar';
import CalendarView from './CalendarView';
import CalendarSettingsModal from './CalendarSettingsModal';
import TaskDetailsModal from './TaskDetailsModal';
import EditRecurringTaskDialog from '@/components/tasks/EditRecurringTaskDialog';
import { TaskDialog } from '@/components/tasks/TaskDialog';
import { TimeFormat } from '@prisma/client';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { CalendarTask } from '@/lib/validations/calendar';
import { TaskWithRelations } from '@/lib/services/task.service';

import type { FamilyMember, User } from '@prisma/client';

interface FamilyMemberWithUser extends FamilyMember {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
}

interface CalendarDashboardProps {
  familyId: string;
  members: FamilyMemberWithUser[];
  className?: string;
}

export default function CalendarDashboard({
  familyId,
  members,
  className,
}: CalendarDashboardProps) {
  const { toast } = useToast();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isRecurringEditDialogOpen, setIsRecurringEditDialogOpen] = useState(false);
  const [selectedTaskForEdit, setSelectedTaskForEdit] = useState<any>(null);
  const [selectedRecurringTask, setSelectedRecurringTask] = useState<CalendarTask | null>(null);
  const [isEditingOccurrence, setIsEditingOccurrence] = useState(false);
  const [fullTaskData, setFullTaskData] = useState<TaskWithRelations | null>(null);

  const {
    // State
    currentView,
    currentDate,
    selectedTask,
    isDetailsModalOpen,

    // Data
    tasks,
    settings,

    // Loading states
    isLoadingTasks,

    // Actions
    setCurrentView,
    setCurrentDate,
    handleTaskClick: originalHandleTaskClick,
    handleTaskDrop,
    handleSlotSelect,
    refetchTasks,
    closeModals: originalCloseModals,

    // Modal controls
    setIsDetailsModalOpen,
    setSelectedTask,
  } = useCalendarState(familyId);

  // Funkcja do pobierania pełnych danych zadania z TaskCompletion
  const fetchFullTaskData = async (taskId: string): Promise<TaskWithRelations | null> => {
    try {
      const response = await fetch(`/api/families/${familyId}/tasks/${taskId}`);
      if (!response.ok) {
        throw new Error('Nie udało się pobrać pełnych danych zadania');
      }
      return await response.json();
    } catch (error) {
      console.error('Error fetching full task data:', error);
      toast({
        title: 'Błąd',
        description: 'Nie udało się pobrać szczegółów zadania',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Własna implementacja handleTaskClick która pobiera pełne dane
  const handleTaskClick = async (task: CalendarTask) => {
    setSelectedTask(task);
    setIsDetailsModalOpen(true);

    // Pobierz pełne dane zadania w tle
    const fullData = await fetchFullTaskData(task.id);
    setFullTaskData(fullData);
  };

  // Własna implementacja closeModals która czyści też fullTaskData
  const closeModals = () => {
    originalCloseModals();
    setFullTaskData(null);
  };

  const handleTaskCreated = async () => {
    try {
      setIsCreateDialogOpen(false);
      await refetchTasks();
      toast({
        title: 'Sukces',
        description: 'Zadanie zostało utworzone',
      });
    } catch {
      toast({
        title: 'Błąd',
        description: 'Nie udało się odświeżyć kalendarza',
        variant: 'destructive',
      });
    }
  };

  const handleEditTask = () => {
    if (!selectedTask) return;

    // Sprawdź czy to zadanie cykliczne z originalTaskId (wystąpienie)
    if (selectedTask.isRecurring && selectedTask.originalTaskId) {
      // To jest wystąpienie zadania cyklicznego - pokaż dialog wyboru
      setSelectedRecurringTask(selectedTask);
      setIsRecurringEditDialogOpen(true);
      setIsDetailsModalOpen(false);
    } else {
      // To jest zadanie jednorazowe lub główna definicja zadania cyklicznego - otwórz bezpośrednio formularz
      setSelectedTaskForEdit(selectedTask);
      setIsEditDialogOpen(true);
      setIsDetailsModalOpen(false);
    }
  };

  const handleTaskUpdated = async () => {
    try {
      setIsEditDialogOpen(false);
      setSelectedTaskForEdit(null);
      setSelectedTask(null);
      setIsEditingOccurrence(false); // Reset flagi edycji wystąpienia
      await refetchTasks();
      toast({
        title: 'Sukces',
        description: isEditingOccurrence
          ? 'Utworzono edytowane wystąpienie zadania'
          : 'Zadanie zostało zaktualizowane',
      });
    } catch {
      toast({
        title: 'Błąd',
        description: 'Nie udało się odświeżyć kalendarza',
        variant: 'destructive',
      });
    }
  };

  // Obsługa edycji zadań cyklicznych
  const handleEditOccurrence = useCallback(async (task: CalendarTask) => {
    // Ustaw flagę że edytujemy wystąpienie i otwórz formularz edycji
    setIsEditingOccurrence(true);
    setSelectedTaskForEdit(task);
    setIsRecurringEditDialogOpen(false);
    setSelectedRecurringTask(null);
    setIsDetailsModalOpen(false);
    setIsEditDialogOpen(true);
  }, []);

  const handleEditSeries = useCallback(
    async (originalTaskId: string) => {
      try {
        // Pobierz oryginalną definicję zadania cyklicznego z bazy danych
        const response = await fetch(`/api/families/${familyId}/tasks/${originalTaskId}`);
        if (!response.ok) {
          throw new Error('Nie udało się pobrać oryginalnego zadania');
        }

        const originalTask = await response.json();

        // DEBUG: Logowanie danych oryginalnego zadania
        console.log('📝 CalendarDashboard.handleEditSeries - pobrano zadanie:', {
          id: originalTask.id,
          title: originalTask.title,
          frequency: originalTask.frequency,
          recurrenceRule: originalTask.recurrenceRule,
          hasDirectFrequency: !!originalTask.frequency,
          hasDirectRecurrenceRule: !!originalTask.recurrenceRule,
          fullTask: originalTask,
        });

        // Zamknij dialog wyboru
        setIsRecurringEditDialogOpen(false);
        setSelectedRecurringTask(null);
        setIsDetailsModalOpen(false);

        // Otwórz formularz edycji z oryginalną definicją zadania
        setSelectedTaskForEdit(originalTask);
        setIsEditDialogOpen(true);
      } catch (error) {
        console.error('Error fetching original task:', error);
        toast({
          title: 'Błąd',
          description: 'Nie udało się pobrać oryginalnej definicji zadania',
          variant: 'destructive',
        });
      }
    },
    [familyId, toast]
  );

  const handleCloseRecurringEditDialog = useCallback(() => {
    setIsRecurringEditDialogOpen(false);
    setSelectedRecurringTask(null);
  }, []);

  const handleDeleteTaskSuccess = async () => {
    try {
      await refetchTasks();
      toast({
        title: 'Sukces',
        description: 'Zadanie zostało usunięte',
      });
      closeModals();
    } catch {
      toast({
        title: 'Błąd',
        description: 'Nie udało się odświeżyć kalendarza',
        variant: 'destructive',
      });
    }
  };

  const handleCompleteTask = async () => {
    if (!selectedTask) return;

    try {
      const response = await fetch(`/api/families/${familyId}/tasks/${selectedTask.id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes: undefined, // Można rozszerzyć w przyszłości o modal z notatkami
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nie udało się ukończyć zadania');
      }

      const completion = await response.json();

      toast({
        title: 'Sukces',
        description:
          selectedTask.isRecurring && selectedTask.originalTaskId
            ? 'Wystąpienie zadania cyklicznego zostało ukończone i oczekuje na weryfikację'
            : 'Zadanie zostało ukończone i oczekuje na weryfikację',
      });

      closeModals();
      await refetchTasks();
    } catch (error: any) {
      console.error('Error completing task:', error);
      toast({
        title: 'Błąd',
        description: error.message || 'Nie udało się ukończyć zadania',
        variant: 'destructive',
      });
    }
  };

  const handleCreateTaskClick = useCallback(() => {
    setIsCreateDialogOpen(true);
  }, [setIsCreateDialogOpen]);

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* Calendar Navigation - Now handled inside MobileCalendarView */}

      {/* Calendar Content - Simplified */}
      <div className="flex-1 overflow-hidden">
        <CalendarView
          view={currentView}
          date={currentDate}
          events={tasks}
          loading={isLoadingTasks}
          onDateChange={setCurrentDate}
          onViewChange={setCurrentView}
          onEventClick={handleTaskClick}
          onEventDrop={handleTaskDrop}
          onSlotSelect={handleSlotSelect}
          onCreateTask={handleCreateTaskClick}
          startWeekOn={settings?.startWeekOn}
          timeFormat={settings?.timeFormat === TimeFormat.HOUR_12 ? '12h' : '24h'}
          showWeekends={settings?.showWeekends}
          className="h-full"
        />
      </div>

      {/* Task Details Modal */}
      {selectedTask && (
        <TaskDetailsModal
          task={selectedTask}
          fullTaskData={fullTaskData}
          prismaTask={
            fullTaskData || {
              id: selectedTask.id,
              familyId,
              title: selectedTask.title,
              description: selectedTask.description || null,
              points: (selectedTask.metadata?.points as number) || 1,
              weight: (selectedTask.metadata?.weight as any) || 'NORMAL',
              frequency: (selectedTask.metadata?.frequency as any) || 'ONCE',
              status: (selectedTask.metadata?.status as any) || 'ACTIVE',
              // Nowe semantyczne pola czasowe
              startDateTime: selectedTask.start,
              endDateTime: selectedTask.end || null,
              cycleEndDate: null,
              // Przestarzałe pola - zachowane dla kompatybilności
              dueDate: selectedTask.start,
              startDate: selectedTask.start,
              endDate: selectedTask.end || null,
              recurrenceEnd: null,
              createdAt: new Date(),
              updatedAt: new Date(),
              assignedBy: '',
              assignedAt: null,
              allDay: selectedTask.allDay,
              recurrenceRule: null,
              color: selectedTask.color || null,
              googleEventId: null,
              appleEventId: null,
            }
          }
          isOpen={isDetailsModalOpen}
          onClose={closeModals}
          onEdit={handleEditTask}
          onDelete={handleDeleteTaskSuccess}
          onComplete={handleCompleteTask}
          timeFormat={settings?.timeFormat || TimeFormat.HOUR_24}
          canEdit={true}
          canDelete={true}
          canComplete={true}
        />
      )}

      {/* Calendar Settings Modal */}
      <CalendarSettingsModal
        familyId={familyId}
        isOpen={isSettingsModalOpen}
        onClose={() => setIsSettingsModalOpen(false)}
      />

      {/* Create Task Dialog */}
      <TaskDialog
        familyId={familyId}
        familyMembers={members}
        mode="create"
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        initialDate={currentDate}
        onTaskSuccess={handleTaskCreated}
      />

      {/* Edit Task Dialog */}
      <TaskDialog
        familyId={familyId}
        familyMembers={members}
        mode="edit"
        open={isEditDialogOpen}
        onOpenChange={open => {
          setIsEditDialogOpen(open);
          if (!open) {
            setSelectedTaskForEdit(null);
            setIsEditingOccurrence(false);
          }
        }}
        task={
          selectedTaskForEdit
            ? (() => {
                const taskForForm = {
                  ...selectedTaskForEdit,
                  // Priorytet dla bezpośrednich właściwości (z API), fallback na metadata (z kalendarza)
                  frequency:
                    selectedTaskForEdit.frequency ||
                    selectedTaskForEdit.metadata?.frequency ||
                    'ONCE',
                  weight:
                    selectedTaskForEdit.weight || selectedTaskForEdit.metadata?.weight || 'NORMAL',
                  points: selectedTaskForEdit.points || selectedTaskForEdit.metadata?.points || 3,
                  status:
                    selectedTaskForEdit.status || selectedTaskForEdit.metadata?.status || 'ACTIVE',
                  assignedBy:
                    selectedTaskForEdit.assignedBy ||
                    selectedTaskForEdit.metadata?.assignedBy ||
                    '',
                  familyId:
                    selectedTaskForEdit.familyId || selectedTaskForEdit.metadata?.familyId || '',
                  dueDate: selectedTaskForEdit.dueDate || selectedTaskForEdit.start,
                  endDate: selectedTaskForEdit.endDate || selectedTaskForEdit.end,
                  recurrenceRule:
                    selectedTaskForEdit.recurrenceRule ||
                    selectedTaskForEdit.metadata?.recurrenceRule ||
                    '',
                  assignedByUser:
                    selectedTaskForEdit.assignedByUser ||
                    selectedTaskForEdit.metadata?.assignedByUser ||
                    null,
                  completions:
                    selectedTaskForEdit.completions ||
                    selectedTaskForEdit.metadata?.completions ||
                    [],
                  assignments: selectedTaskForEdit.assignments || [],
                  assignedMembers: selectedTaskForEdit.assignedTo
                    ? [selectedTaskForEdit.assignedTo]
                    : [],
                };

                // DEBUG: Logowanie danych przekazywanych do formularza
                console.log('📋 CalendarDashboard - dane przekazywane do TaskForm:', {
                  taskId: taskForForm.id,
                  frequency: taskForForm.frequency,
                  recurrenceRule: taskForForm.recurrenceRule,
                  hasFrequency: !!taskForForm.frequency,
                  hasRecurrenceRule: !!taskForForm.recurrenceRule,
                  originalFrequency: selectedTaskForEdit.frequency,
                  metadataFrequency: selectedTaskForEdit.metadata?.frequency,
                });

                return taskForForm;
              })()
            : undefined
        }
        originalTaskId={selectedTaskForEdit?.originalTaskId || undefined}
        isEditingOccurrence={isEditingOccurrence}
        onTaskSuccess={handleTaskUpdated}
      />

      {/* Edit Recurring Task Dialog */}
      {selectedRecurringTask && (
        <EditRecurringTaskDialog
          isOpen={isRecurringEditDialogOpen}
          onClose={handleCloseRecurringEditDialog}
          task={selectedRecurringTask}
          onEditOccurrence={handleEditOccurrence}
          onEditSeries={handleEditSeries}
        />
      )}
    </div>
  );
}
