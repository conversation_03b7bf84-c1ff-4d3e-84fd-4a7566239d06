'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Container } from '@/components/ui/Grid';
import { Check, X, Crown, Users, Zap, Info } from 'lucide-react';

interface Feature {
  name: string;
  description?: string;
  basic: boolean | string;
  family: boolean | string;
  premium: boolean | string;
  category: string;
}

interface FeatureComparisonProps {
  className?: string;
}

export function FeatureComparison({ className }: FeatureComparisonProps) {
  const features: Feature[] = [
    // Podstawowe funkcje
    {
      category: 'Podstawowe funkcje',
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rodziny',
      description: 'Maksymalna liczba członków rodziny',
      basic: '4',
      family: 'Nieograniczona',
      premium: 'Nieograniczona',
    },
    {
      category: 'Podstawowe funkcje',
      name: 'Zadania',
      basic: true,
      family: true,
      premium: true,
    },
    {
      category: 'Podstawowe funkcje',
      name: '<PERSON><PERSON><PERSON><PERSON> rodzinny',
      basic: true,
      family: true,
      premium: true,
    },
    {
      category: 'Podstawowe funkcje',
      name: 'Powiadomienia email',
      basic: true,
      family: true,
      premium: true,
    },

    // <PERSON>aa<PERSON>sowane funkcje
    {
      category: 'Zaawansowane funkcje',
      name: 'System punktów i nagród',
      basic: false,
      family: true,
      premium: true,
    },
    {
      category: 'Zaawansowane funkcje',
      name: 'Powiadomienia push',
      basic: false,
      family: true,
      premium: true,
    },
    {
      category: 'Zaawansowane funkcje',
      name: 'Kategorie zadań',
      description: 'Tworzenie własnych kategorii',
      basic: false,
      family: true,
      premium: true,
    },
    {
      category: 'Zaawansowane funkcje',
      name: 'Zadania cykliczne',
      basic: false,
      family: true,
      premium: true,
    },

    // Statystyki i raporty
    {
      category: 'Statystyki i raporty',
      name: 'Podstawowe statystyki',
      basic: true,
      family: true,
      premium: true,
    },
    {
      category: 'Statystyki i raporty',
      name: 'Szczegółowe raporty',
      basic: false,
      family: true,
      premium: true,
    },
    {
      category: 'Statystyki i raporty',
      name: 'Zaawansowane analytics',
      basic: false,
      family: false,
      premium: true,
    },
    {
      category: 'Statystyki i raporty',
      name: 'Eksport danych',
      basic: false,
      family: true,
      premium: true,
    },

    // Integracje
    {
      category: 'Integracje',
      name: 'Synchronizacja kalendarza',
      description: 'Google Calendar, Apple Calendar',
      basic: false,
      family: true,
      premium: true,
    },
    {
      category: 'Integracje',
      name: 'Integracje zewnętrzne',
      description: 'API, webhooks, Zapier',
      basic: false,
      family: false,
      premium: true,
    },
    {
      category: 'Integracje',
      name: 'Automatyzacja zadań',
      basic: false,
      family: false,
      premium: true,
    },

    // Support i backup
    {
      category: 'Support i backup',
      name: 'Support społecznościowy',
      basic: true,
      family: false,
      premium: false,
    },
    {
      category: 'Support i backup',
      name: 'Priority support 24/7',
      basic: false,
      family: true,
      premium: true,
    },
    {
      category: 'Support i backup',
      name: 'Backup w chmurze',
      basic: false,
      family: false,
      premium: true,
    },
    {
      category: 'Support i backup',
      name: 'Dedykowany manager',
      basic: false,
      family: false,
      premium: true,
    },
  ];

  const plans = [
    {
      id: 'basic',
      name: 'Podstawowy',
      price: 'Darmowy',
      icon: Users,
      color: 'gray',
    },
    {
      id: 'family',
      name: 'Rodzinny',
      price: '19,99 zł/mies.',
      icon: Crown,
      color: 'purple',
      popular: true,
    },
    {
      id: 'premium',
      name: 'Premium',
      price: '29,99 zł/mies.',
      icon: Zap,
      color: 'blue',
    },
  ];

  const groupedFeatures = features.reduce(
    (acc, feature) => {
      if (!acc[feature.category]) {
        acc[feature.category] = [];
      }
      acc[feature.category].push(feature);
      return acc;
    },
    {} as Record<string, Feature[]>
  );

  const renderFeatureValue = (value: boolean | string, planId: string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className="h-5 w-5 text-green-600 mx-auto" />
      ) : (
        <X className="h-5 w-5 text-gray-400 mx-auto" />
      );
    }

    return (
      <span
        className={`text-sm font-medium ${
          planId === 'family' ? 'text-purple-600' : 'text-gray-900'
        }`}
      >
        {value}
      </span>
    );
  };

  return (
    <section className={`py-20 lg:py-32 bg-white ${className}`}>
      <Container size="xl">
        <div className="space-y-12">
          {/* Section Header */}
          <div className="text-center space-y-4">
            <h2 className="text-h1 font-display text-gray-900">Porównanie planów</h2>
            <p className="text-xl text-gray-600 font-body max-w-3xl mx-auto">
              Szczegółowe porównanie wszystkich funkcji dostępnych w każdym planie. Wybierz plan,
              który najlepiej odpowiada potrzebom Twojej rodziny.
            </p>
          </div>

          {/* Desktop Table */}
          <div className="hidden lg:block">
            <Card className="overflow-hidden">
              {/* Table Header */}
              <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
                <div className="grid grid-cols-4 gap-6">
                  <div className="font-semibold text-gray-900">Funkcje</div>
                  {plans.map(plan => (
                    <div key={plan.id} className="text-center">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <plan.icon
                          className={`h-5 w-5 ${
                            plan.color === 'purple'
                              ? 'text-purple-600'
                              : plan.color === 'blue'
                                ? 'text-blue-600'
                                : 'text-gray-600'
                          }`}
                        />
                        <span className="font-semibold text-gray-900">{plan.name}</span>
                        {plan.popular && (
                          <Badge className="bg-purple-600 text-white text-xs">Popularne</Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">{plan.price}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-200">
                {Object.entries(groupedFeatures).map(([category, categoryFeatures]) => (
                  <div key={category}>
                    {/* Category Header */}
                    <div className="bg-gray-25 px-6 py-3 border-b border-gray-100">
                      <h3 className="font-semibold text-gray-900">{category}</h3>
                    </div>

                    {/* Category Features */}
                    {categoryFeatures.map((feature, index) => (
                      <div key={index} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                        <div className="grid grid-cols-4 gap-6 items-center">
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium text-gray-900">{feature.name}</span>
                              {feature.description && (
                                <div className="group relative">
                                  <Info className="h-4 w-4 text-gray-400" />
                                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                    {feature.description}
                                  </div>
                                </div>
                              )}
                            </div>
                            {feature.description && (
                              <div className="text-sm text-gray-500 mt-1">
                                {feature.description}
                              </div>
                            )}
                          </div>

                          <div className="text-center">
                            {renderFeatureValue(feature.basic, 'basic')}
                          </div>

                          <div className="text-center">
                            {renderFeatureValue(feature.family, 'family')}
                          </div>

                          <div className="text-center">
                            {renderFeatureValue(feature.premium, 'premium')}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden space-y-6">
            {plans.map(plan => (
              <Card key={plan.id} className="overflow-hidden">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <div className="flex items-center justify-center gap-2">
                    <plan.icon
                      className={`h-5 w-5 ${
                        plan.color === 'purple'
                          ? 'text-purple-600'
                          : plan.color === 'blue'
                            ? 'text-blue-600'
                            : 'text-gray-600'
                      }`}
                    />
                    <span className="font-semibold text-gray-900">{plan.name}</span>
                    {plan.popular && (
                      <Badge className="bg-purple-600 text-white text-xs">Popularne</Badge>
                    )}
                  </div>
                  <div className="text-center text-sm text-gray-600 mt-1">{plan.price}</div>
                </div>

                <div className="p-4 space-y-4">
                  {Object.entries(groupedFeatures).map(([category, categoryFeatures]) => (
                    <div key={category}>
                      <h4 className="font-semibold text-gray-900 mb-2">{category}</h4>
                      <div className="space-y-2">
                        {categoryFeatures.map((feature, index) => (
                          <div key={index} className="flex items-center justify-between py-1">
                            <span className="text-sm text-gray-700">{feature.name}</span>
                            {renderFeatureValue(
                              feature[plan.id as keyof Feature] as boolean | string,
                              plan.id
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </Container>
    </section>
  );
}
