'use client';

import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { Check, Star, Crown, Zap, Users, Calendar, BarChart3, Shield } from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  highlighted?: boolean;
  icon: any;
  buttonText: string;
  buttonVariant: 'default' | 'outline';
  popular?: boolean;
}

interface PricingCardsProps {
  className?: string;
}

export function PricingCards({ className }: PricingCardsProps) {
  const plans: PricingPlan[] = [
    {
      id: 'basic',
      name: 'Podstawowy',
      price: '0',
      period: 'na zawsze',
      description: 'Idealne na start dla małych rodzin',
      icon: Users,
      buttonText: '<PERSON>oz<PERSON>cznij za darmo',
      buttonVariant: 'outline',
      features: [
        'Do 4 członków rodziny',
        'Podstawowe zadania',
        'Kalendarz rodzinny',
        'Powiadomienia email',
        'Statystyki podstawowe',
        'Support społecznościowy',
      ],
    },
    {
      id: 'family',
      name: 'Rodzinny',
      price: '19,99',
      period: '/miesiąc',
      description: 'Najlepszy wybór dla aktywnych rodzin',
      icon: Crown,
      buttonText: 'Wybierz plan',
      buttonVariant: 'default',
      highlighted: true,
      popular: true,
      features: [
        'Nieograniczona liczba członków',
        'Zaawansowane zadania i kategorie',
        'System punktów i nagród',
        'Powiadomienia push',
        'Szczegółowe statystyki',
        'Synchronizacja kalendarza',
        'Eksport danych',
        'Priority support 24/7',
      ],
    },
    {
      id: 'premium',
      name: 'Premium',
      price: '29,99',
      period: '/miesiąc',
      description: 'Dla rodzin potrzebujących maksymalnej kontroli',
      icon: Zap,
      buttonText: 'Wybierz plan',
      buttonVariant: 'outline',
      features: [
        'Wszystko z planu Rodzinnego',
        'Zaawansowane raporty i analytics',
        'Automatyzacja zadań',
        'Integracje zewnętrzne',
        'Backup w chmurze',
        'White-label opcje',
        'Dedykowany manager',
        'SLA 99.9% uptime',
      ],
    },
  ];

  return (
    <section className={`py-20 lg:py-32 bg-gray-50 ${className}`}>
      <Container size="xl">
        <div className="space-y-16">
          {/* Section Header */}
          <div className="text-center space-y-4">
            <Badge variant="secondary" className="bg-purple-100 text-purple-700 mb-4">
              <Star className="h-4 w-4 mr-2" />
              Cennik
            </Badge>
            <h2 className="text-h1 font-display text-gray-900">Wybierz plan dla swojej rodziny</h2>
            <p className="text-xl text-gray-600 font-body max-w-3xl mx-auto">
              Proste, przejrzyste ceny bez ukrytych kosztów. Rozpocznij za darmo i rozwijaj się wraz
              z rodziną.
            </p>
          </div>

          {/* Pricing Cards */}
          <Grid cols={1} mdCols={3} gap="lg" className="max-w-7xl mx-auto">
            {plans.map((plan, index) => (
              <GridItem key={plan.id}>
                <Card
                  className={`
                    relative h-full card-shadow hover-lift transition-all duration-300
                    ${
                      plan.highlighted
                        ? 'border-purple-200 bg-white ring-2 ring-purple-100 scale-105'
                        : 'border-gray-200 bg-white hover:border-purple-200'
                    }
                  `}
                  style={{
                    animationDelay: `${index * 0.1}s`,
                  }}
                >
                  {/* Popular Badge */}
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <Badge className="bg-purple-600 text-white px-4 py-1">
                        <Crown className="h-3 w-3 mr-1" />
                        Najpopularniejszy
                      </Badge>
                    </div>
                  )}

                  <div className="p-8 h-full flex flex-col">
                    {/* Plan Header */}
                    <div className="text-center mb-8">
                      <div
                        className={`
                        w-16 h-16 rounded-2xl mx-auto mb-4 flex items-center justify-center
                        ${
                          plan.highlighted
                            ? 'bg-gradient-to-br from-purple-500 to-purple-600'
                            : 'bg-gray-100'
                        }
                      `}
                      >
                        <plan.icon
                          className={`h-8 w-8 ${plan.highlighted ? 'text-white' : 'text-gray-600'}`}
                        />
                      </div>

                      <h3 className="text-h3 font-display text-gray-900 mb-2">{plan.name}</h3>

                      <p className="text-gray-600 font-body mb-4">{plan.description}</p>

                      {/* Price */}
                      <div className="flex items-baseline justify-center gap-1">
                        <span className="text-4xl font-bold text-gray-900">
                          {plan.price === '0' ? 'Darmowy' : `${plan.price} zł`}
                        </span>
                        {plan.price !== '0' && (
                          <span className="text-gray-600 font-medium">{plan.period}</span>
                        )}
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-4 mb-8 flex-1">
                      {plan.features.map((feature, featureIndex) => (
                        <div
                          key={featureIndex}
                          className="flex items-start gap-3"
                          style={{
                            animationDelay: `${index * 0.1 + featureIndex * 0.05}s`,
                          }}
                        >
                          <Check className="h-5 w-5 text-green-600 flex-shrink-0 mt-0.5" />
                          <span className="text-gray-700 font-body">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <Button
                      size="lg"
                      variant={plan.highlighted ? 'default' : plan.buttonVariant}
                      className={`
                        w-full font-semibold transition-all hover-lift
                        ${
                          plan.highlighted
                            ? 'bg-purple-600 hover:bg-purple-700 text-white'
                            : 'border-purple-200 text-purple-700 hover:bg-purple-50'
                        }
                      `}
                      asChild
                    >
                      <Link href="/sign-up">{plan.buttonText}</Link>
                    </Button>

                    {/* Additional Info */}
                    <div className="text-center mt-4">
                      <p className="text-xs text-gray-500">
                        {plan.price === '0'
                          ? 'Nie potrzebujesz karty kredytowej'
                          : 'Anuluj w każdej chwili'}
                      </p>
                    </div>
                  </div>
                </Card>
              </GridItem>
            ))}
          </Grid>

          {/* Trust Indicators */}
          <div className="text-center space-y-8">
            <div className="flex items-center justify-center gap-8 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4 text-green-500" />
                <span>14-dniowy trial</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-green-500" />
                <span>Bezpieczne płatności</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-green-500" />
                <span>1,200+ zadowolonych rodzin</span>
              </div>
            </div>

            {/* FAQ Link */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Masz pytania?</h3>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="outline" asChild>
                  <Link href="/faq">Sprawdź FAQ</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/contact">Skontaktuj się z nami</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
}
