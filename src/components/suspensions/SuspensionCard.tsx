'use client';

import { useState } from 'react';
import {
  TaskSuspension,
  SuspensionStatus,
  SuspensionReason,
  Task,
  FamilyMember,
  User,
} from '@prisma/client';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  CalendarDays,
  Clock,
  Edit,
  MoreVertical,
  Pause,
  Play,
  Trash2,
  Users,
  CheckSquare,
  XCircle,
  Calendar,
} from 'lucide-react';
import { getSuspensionReasonLabel, getSuspensionStatusLabel } from '@/lib/validations/suspension';

interface SuspensionWithDetails extends TaskSuspension {
  createdBy: {
    id: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  };
  suspendedTasks: {
    task: {
      id: string;
      title: string;
      description?: string | null;
      points: number;
    };
  }[];
  suspendedMembers: {
    member: {
      id: string;
      user: {
        firstName: string | null;
        lastName: string | null;
      };
    };
  }[];
}

interface SuspensionCardProps {
  suspension: SuspensionWithDetails;
  canManage: boolean;
  onEdit?: () => void;
  onCancel?: () => void;
  onExtend?: () => void;
  onActivate?: () => void;
  onDeactivate?: () => void;
  onDelete?: () => void;
}

export function SuspensionCard({
  suspension,
  canManage,
  onEdit,
  onCancel,
  onExtend,
  onActivate,
  onDeactivate,
  onDelete,
}: SuspensionCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const getStatusColor = (status: SuspensionStatus) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'ENDED':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getReasonColor = (reason: SuspensionReason) => {
    switch (reason) {
      case 'HOLIDAY_VACATION':
        return 'bg-green-100 text-green-800';
      case 'TRAVEL':
        return 'bg-blue-100 text-blue-800';
      case 'ILLNESS':
        return 'bg-red-100 text-red-800';
      case 'SCHOOL_BREAK':
        return 'bg-yellow-100 text-yellow-800';
      case 'MAINTENANCE':
        return 'bg-purple-100 text-purple-800';
      case 'OTHER':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getMemberDisplayName = (member: {
    user: { firstName: string | null; lastName: string | null };
  }) => {
    if (member.user.firstName && member.user.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    return 'Nieznany użytkownik';
  };

  const isActive = suspension.status === 'ACTIVE';
  const isExpired = new Date(suspension.endDate) < new Date();
  const daysRemaining = Math.ceil(
    (new Date(suspension.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
  );

  const handleAction = async (action: () => void) => {
    setIsLoading(true);
    try {
      await action();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <TooltipProvider>
      <Card
        className={`transition-all duration-200 hover:shadow-md ${
          isActive ? 'border-orange-200 bg-orange-50' : 'border-gray-200'
        }`}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <h3 className="font-semibold text-gray-900 truncate">{suspension.title}</h3>
                <Badge className={getStatusColor(suspension.status)}>
                  {isActive && <Pause className="h-3 w-3 mr-1" />}
                  {suspension.status === 'ENDED' && <CheckSquare className="h-3 w-3 mr-1" />}
                  {suspension.status === 'CANCELLED' && <XCircle className="h-3 w-3 mr-1" />}
                  {getSuspensionStatusLabel(suspension.status)}
                </Badge>
                <Badge variant="outline" className={getReasonColor(suspension.reason)}>
                  {getSuspensionReasonLabel(suspension.reason)}
                </Badge>
              </div>

              {suspension.description && (
                <p className="text-sm text-gray-600 mb-2 line-clamp-2">{suspension.description}</p>
              )}

              {/* Timeline */}
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(suspension.startDate).toLocaleDateString('pl-PL')}</span>
                  <span>-</span>
                  <span>{new Date(suspension.endDate).toLocaleDateString('pl-PL')}</span>
                </div>
                {isActive && !isExpired && (
                  <div className="flex items-center gap-1 text-orange-600">
                    <Clock className="h-4 w-4" />
                    <span>
                      {daysRemaining === 1
                        ? '1 dzień'
                        : daysRemaining > 0
                          ? `${daysRemaining} dni`
                          : 'Kończy się dziś'}
                    </span>
                  </div>
                )}
                {isExpired && isActive && (
                  <Badge variant="destructive" className="text-xs">
                    Wygasłe
                  </Badge>
                )}
              </div>
            </div>

            {/* Actions Menu */}
            {canManage && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0" disabled={isLoading}>
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem onClick={() => handleAction(onEdit)} disabled={isLoading}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edytuj
                    </DropdownMenuItem>
                  )}

                  {isActive && onDeactivate && (
                    <DropdownMenuItem
                      onClick={() => handleAction(onDeactivate)}
                      disabled={isLoading}
                      className="text-orange-600"
                    >
                      <Pause className="mr-2 h-4 w-4" />
                      Anuluj zawieszenie
                    </DropdownMenuItem>
                  )}

                  {!isActive && suspension.status === 'CANCELLED' && onActivate && !isExpired && (
                    <DropdownMenuItem
                      onClick={() => handleAction(onActivate)}
                      disabled={isLoading}
                      className="text-green-600"
                    >
                      <Play className="mr-2 h-4 w-4" />
                      Aktywuj ponownie
                    </DropdownMenuItem>
                  )}

                  {onExtend && isActive && (
                    <DropdownMenuItem onClick={() => handleAction(onExtend)} disabled={isLoading}>
                      <CalendarDays className="mr-2 h-4 w-4" />
                      Przedłuż
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuSeparator />

                  {onDelete && (
                    <DropdownMenuItem
                      onClick={() => handleAction(onDelete)}
                      disabled={isLoading}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Usuń
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Suspended Items */}
          <div className="space-y-3">
            {/* Suspended Tasks */}
            {suspension.suspendedTasks.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <CheckSquare className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Zawieszone zadania ({suspension.suspendedTasks.length})
                  </span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {suspension.suspendedTasks.slice(0, 3).map(suspendedTask => (
                    <Tooltip key={suspendedTask.task.id}>
                      <TooltipTrigger>
                        <Badge variant="secondary" className="text-xs">
                          {suspendedTask.task.title}
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="max-w-xs">
                          <p className="font-semibold">{suspendedTask.task.title}</p>
                          {suspendedTask.task.description && (
                            <p className="text-sm">{suspendedTask.task.description}</p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            {suspendedTask.task.points} pkt
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                  {suspension.suspendedTasks.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{suspension.suspendedTasks.length - 3} więcej
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Suspended Members */}
            {suspension.suspendedMembers.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">
                    Zawieszeni członkowie ({suspension.suspendedMembers.length})
                  </span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {suspension.suspendedMembers.slice(0, 3).map(suspendedMember => (
                    <Badge key={suspendedMember.member.id} variant="secondary" className="text-xs">
                      {getMemberDisplayName(suspendedMember.member)}
                    </Badge>
                  ))}
                  {suspension.suspendedMembers.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{suspension.suspendedMembers.length - 3} więcej
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Created By */}
            <div className="text-xs text-gray-400 mt-2">
              Utworzone przez {getMemberDisplayName(suspension.createdBy)} •{' '}
              {new Date(suspension.createdAt).toLocaleDateString('pl-PL')}
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
