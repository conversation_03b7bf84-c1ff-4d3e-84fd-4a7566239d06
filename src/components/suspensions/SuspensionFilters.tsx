'use client';

import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Filter, X, CalendarDays, Search, RotateCcw } from 'lucide-react';
import { SuspensionFilters } from '@/lib/validations/suspension';

interface SuspensionFiltersProps {
  filters: SuspensionFilters;
  onFiltersChange: (filters: SuspensionFilters) => void;
  className?: string;
}

const statusOptions = [
  { value: 'ACTIVE', label: 'Aktywne' },
  { value: 'ENDED', label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: 'CANCELLED', label: 'Anulowane' },
];

const reasonOptions = [
  { value: 'HOLIDAY_VACATION', label: 'Święta/Urlop' },
  { value: 'TRAVEL', label: 'Wyjazd' },
  { value: 'ILLNESS', label: 'Choroba' },
  { value: 'SCHOOL_BREAK', label: 'Przerwa szkolna' },
  { value: 'MAINTENANCE', label: 'Prace remontowe' },
  { value: 'OTHER', label: 'Inny powód' },
];

export function SuspensionFiltersComponent({
  filters,
  onFiltersChange,
  className,
}: SuspensionFiltersProps) {
  const [statusOpen, setStatusOpen] = useState(false);
  const [reasonOpen, setReasonOpen] = useState(false);
  const [dateRangeOpen, setDateRangeOpen] = useState(false);

  const updateFilters = (updates: Partial<SuspensionFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const clearFilters = () => {
    onFiltersChange({ includeExpired: false });
  };

  const hasActiveFilters =
    filters.status?.length ||
    filters.reason?.length ||
    filters.search ||
    filters.startDate ||
    filters.endDate ||
    filters.includeExpired;

  const activeFiltersCount = [
    filters.status?.length,
    filters.reason?.length,
    filters.search ? 1 : 0,
    filters.startDate ? 1 : 0,
    filters.endDate ? 1 : 0,
    filters.includeExpired ? 1 : 0,
  ].filter(Boolean).length;

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Szukaj zawieszeń..."
          value={filters.search || ''}
          onChange={e => updateFilters({ search: e.target.value || undefined })}
          className="pl-9 w-64"
        />
      </div>

      {/* Status Filter */}
      <Popover open={statusOpen} onOpenChange={setStatusOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Filter className="mr-2 h-4 w-4" />
            Status
            {filters.status?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.status.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj statusu..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono statusu.</CommandEmpty>
              <CommandGroup>
                {statusOptions.map(option => (
                  <CommandItem key={option.value}>
                    <Checkbox
                      checked={filters.status?.includes(option.value as any) || false}
                      onCheckedChange={checked => {
                        const currentStatus = filters.status || [];
                        if (checked) {
                          updateFilters({
                            status: [...currentStatus, option.value as any],
                          });
                        } else {
                          updateFilters({
                            status: currentStatus.filter(s => s !== option.value),
                          });
                        }
                      }}
                    />
                    <span className="ml-2">{option.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Reason Filter */}
      <Popover open={reasonOpen} onOpenChange={setReasonOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Filter className="mr-2 h-4 w-4" />
            Powód
            {filters.reason?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.reason.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj powodu..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono powodu.</CommandEmpty>
              <CommandGroup>
                {reasonOptions.map(option => (
                  <CommandItem key={option.value}>
                    <Checkbox
                      checked={filters.reason?.includes(option.value as any) || false}
                      onCheckedChange={checked => {
                        const currentReason = filters.reason || [];
                        if (checked) {
                          updateFilters({
                            reason: [...currentReason, option.value as any],
                          });
                        } else {
                          updateFilters({
                            reason: currentReason.filter(r => r !== option.value),
                          });
                        }
                      }}
                    />
                    <span className="ml-2">{option.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Date Range Filter */}
      <Popover open={dateRangeOpen} onOpenChange={setDateRangeOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <CalendarDays className="mr-2 h-4 w-4" />
            Okres
            {filters.startDate || filters.endDate ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                1
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-4" align="start">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Data rozpoczęcia</label>
              <Calendar
                mode="single"
                selected={filters.startDate}
                onSelect={date => updateFilters({ startDate: date })}
                className="rounded-md border"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Data zakończenia</label>
              <Calendar
                mode="single"
                selected={filters.endDate}
                onSelect={date => updateFilters({ endDate: date })}
                className="rounded-md border"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateFilters({ startDate: undefined, endDate: undefined })}
              >
                Wyczyść
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Include Expired Toggle */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="includeExpired"
          checked={filters.includeExpired}
          onCheckedChange={checked => updateFilters({ includeExpired: !!checked })}
        />
        <label htmlFor="includeExpired" className="text-sm">
          Pokaż wygasłe
        </label>
      </div>

      {/* Clear All Filters */}
      {hasActiveFilters && (
        <Button variant="ghost" size="sm" onClick={clearFilters} className="h-8 px-2 lg:px-3">
          <RotateCcw className="mr-2 h-4 w-4" />
          Wyczyść
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      )}
    </div>
  );
}
