'use client';

import { useState, useMemo } from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Plus, AlertCircle } from 'lucide-react';
import { SuspensionCard } from './SuspensionCard';
import { SuspensionFiltersComponent } from './SuspensionFilters';
import { SuspensionFilters } from '@/lib/validations/suspension';
import { useToast } from '@/hooks/use-toast';
import { useConfirmDialog } from '@/components/ui/confirm-dialog';

interface SuspensionWithDetails {
  id: string;
  familyId: string;
  title: string;
  description?: string | null;
  reason: string;
  startDate: Date;
  endDate: Date;
  status: string;
  createdById: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: {
    id: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  };
  suspendedTasks: {
    task: {
      id: string;
      title: string;
      description?: string | null;
      points: number;
    };
  }[];
  suspendedMembers: {
    member: {
      id: string;
      user: {
        firstName: string | null;
        lastName: string | null;
      };
    };
  }[];
}

interface SuspensionListProps {
  familyId: string;
  suspensions: SuspensionWithDetails[];
  isLoading?: boolean;
  canManage: boolean;
  onCreateSuspension?: () => void;
  onEditSuspension?: (suspension: SuspensionWithDetails) => void;
  onDeleteSuspension?: (suspensionId: string) => void;
  onActivateSuspension?: (suspensionId: string) => void;
  onDeactivateSuspension?: (suspensionId: string) => void;
  onRefresh?: () => void;
}

export function SuspensionList({
  familyId,
  suspensions,
  isLoading = false,
  canManage,
  onCreateSuspension,
  onEditSuspension,
  onDeleteSuspension,
  onActivateSuspension,
  onDeactivateSuspension,
  onRefresh,
}: SuspensionListProps) {
  const [filters, setFilters] = useState<SuspensionFilters>({
    includeExpired: false,
  });
  const { toast } = useToast();
  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  const filteredSuspensions = useMemo(() => {
    let filtered = [...suspensions];

    // Apply status filter
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(s => filters.status!.includes(s.status as any));
    }

    // Apply reason filter
    if (filters.reason && filters.reason.length > 0) {
      filtered = filtered.filter(s => filters.reason!.includes(s.reason as any));
    }

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        s =>
          s.title.toLowerCase().includes(searchLower) ||
          s.description?.toLowerCase().includes(searchLower)
      );
    }

    // Apply date filters
    if (filters.startDate) {
      filtered = filtered.filter(s => new Date(s.startDate) >= filters.startDate!);
    }
    if (filters.endDate) {
      filtered = filtered.filter(s => new Date(s.endDate) <= filters.endDate!);
    }

    // Exclude expired unless requested
    if (!filters.includeExpired) {
      filtered = filtered.filter(
        s => s.status !== 'ENDED' && (new Date(s.endDate) >= new Date() || s.status === 'ACTIVE')
      );
    }

    // Sort by creation date (newest first)
    filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return filtered;
  }, [suspensions, filters]);

  const suspensionStats = useMemo(() => {
    const active = suspensions.filter(s => s.status === 'ACTIVE').length;
    const ended = suspensions.filter(s => s.status === 'ENDED').length;
    const cancelled = suspensions.filter(s => s.status === 'CANCELLED').length;
    const expired = suspensions.filter(
      s => s.status === 'ACTIVE' && new Date(s.endDate) < new Date()
    ).length;

    return { active, ended, cancelled, expired, total: suspensions.length };
  }, [suspensions]);

  const handleActivate = async (suspensionId: string) => {
    try {
      const response = await fetch(
        `/api/families/${familyId}/suspensions/${suspensionId}/activate`,
        {
          method: 'POST',
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się aktywować zawieszenia');
      }

      onActivateSuspension?.(suspensionId);
      onRefresh?.();
    } catch (error: any) {
      console.error('Error activating suspension:', error);
      toast({
        title: 'Błąd podczas aktywacji zawieszenia',
        description: error.message || 'Wystąpił błąd podczas aktywacji zawieszenia',
        variant: 'destructive',
      });
    }
  };

  const handleDeactivate = async (suspensionId: string) => {
    try {
      const response = await fetch(
        `/api/families/${familyId}/suspensions/${suspensionId}/activate`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się anulować zawieszenia');
      }

      onDeactivateSuspension?.(suspensionId);
      onRefresh?.();
    } catch (error: any) {
      console.error('Error deactivating suspension:', error);
      toast({
        title: 'Błąd podczas anulowania zawieszenia',
        description: error.message || 'Wystąpił błąd podczas anulowania zawieszenia',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async (suspensionId: string) => {
    showConfirm({
      title: 'Usuwanie zawieszenia',
      message: 'Czy na pewno chcesz usunąć to zawieszenie? Ta operacja jest nieodwracalna.',
      variant: 'destructive',
      confirmText: 'Usuń',
      cancelText: 'Anuluj',
      onConfirm: () => performDelete(suspensionId),
    });
  };

  const performDelete = async (suspensionId: string) => {
    try {
      const response = await fetch(`/api/families/${familyId}/suspensions/${suspensionId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się usunąć zawieszenia');
      }

      onDeleteSuspension?.(suspensionId);
      onRefresh?.();
    } catch (error: any) {
      console.error('Error deleting suspension:', error);
      toast({
        title: 'Błąd podczas usuwania zawieszenia',
        description: error.message || 'Wystąpił błąd podczas usuwania zawieszenia',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-9 w-32" />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {[1, 2, 3].map(i => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <ConfirmDialog />
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h2 className="text-2xl font-semibold tracking-tight">Zawieszenia zadań</h2>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>Aktywne: {suspensionStats.active}</span>
            <span>Zakończone: {suspensionStats.ended}</span>
            <span>Anulowane: {suspensionStats.cancelled}</span>
            {suspensionStats.expired > 0 && (
              <span className="text-orange-600">Wygasłe: {suspensionStats.expired}</span>
            )}
          </div>
        </div>

        {canManage && onCreateSuspension && (
          <Button onClick={onCreateSuspension}>
            <Plus className="mr-2 h-4 w-4" />
            Nowe zawieszenie
          </Button>
        )}
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Aktywne zawieszenia</p>
                <p className="text-2xl font-bold text-orange-600">{suspensionStats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Zakończone</p>
                <p className="text-2xl font-bold text-green-600">{suspensionStats.ended}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Anulowane</p>
                <p className="text-2xl font-bold text-red-600">{suspensionStats.cancelled}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Łącznie</p>
                <p className="text-2xl font-bold">{suspensionStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <SuspensionFiltersComponent filters={filters} onFiltersChange={setFilters} />
        </CardContent>
      </Card>

      {/* Suspensions List */}
      <div className="space-y-4">
        {filteredSuspensions.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {suspensions.length === 0 ? 'Brak zawieszeń' : 'Brak wyników'}
              </h3>
              <p className="text-muted-foreground text-center">
                {suspensions.length === 0
                  ? 'Nie utworzono jeszcze żadnych zawieszeń zadań.'
                  : 'Nie znaleziono zawieszeń pasujących do wybranych filtrów.'}
              </p>
              {canManage && onCreateSuspension && suspensions.length === 0 && (
                <Button onClick={onCreateSuspension} className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Utwórz pierwsze zawieszenie
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          filteredSuspensions.map(suspension => (
            <SuspensionCard
              key={suspension.id}
              suspension={suspension as any}
              canManage={canManage}
              onEdit={() => onEditSuspension?.(suspension)}
              onActivate={() => handleActivate(suspension.id)}
              onDeactivate={() => handleDeactivate(suspension.id)}
              onDelete={() => handleDelete(suspension.id)}
            />
          ))
        )}
      </div>
    </div>
  );
}
