'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarIcon, X, Users, CheckSquare } from 'lucide-react';
import { format } from 'date-fns';
import { pl } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { getSuspensionReasonLabel } from '@/lib/validations/suspension';
import { useToast } from '@/hooks/use-toast';

type CreateSuspensionFormData = {
  title: string;
  description?: string;
  reason: 'HOLIDAY_VACATION' | 'TRAVEL' | 'ILLNESS' | 'SCHOOL_BREAK' | 'MAINTENANCE' | 'OTHER';
  startDate: Date;
  endDate: Date;
  taskIds: string[];
  memberIds: string[];
};

interface Task {
  id: string;
  title: string;
  description?: string | null;
  points: number;
}

interface FamilyMember {
  id: string;
  user: {
    firstName: string | null;
    lastName: string | null;
    email: string;
  };
}

interface CreateSuspensionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  familyId: string;
  tasks: Task[];
  members: FamilyMember[];
  onSuspensionCreated?: () => void;
}

const reasonOptions = [
  { value: 'HOLIDAY_VACATION', label: 'Święta/Urlop' },
  { value: 'TRAVEL', label: 'Wyjazd' },
  { value: 'ILLNESS', label: 'Choroba' },
  { value: 'SCHOOL_BREAK', label: 'Przerwa szkolna' },
  { value: 'MAINTENANCE', label: 'Prace remontowe' },
  { value: 'OTHER', label: 'Inny powód' },
];

export function CreateSuspensionDialog({
  open,
  onOpenChange,
  familyId,
  tasks,
  members,
  onSuspensionCreated,
}: CreateSuspensionDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<CreateSuspensionFormData>({
    defaultValues: {
      title: '',
      description: '',
      reason: 'OTHER' as const,
      startDate: new Date(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      taskIds: [] as string[],
      memberIds: [] as string[],
    },
  });

  const getMemberDisplayName = (member: FamilyMember) => {
    if (member?.user?.firstName && member?.user?.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    if (member?.user?.email) {
      return member.user.email.split('@')[0];
    }
    return 'Nieznany użytkownik';
  };

  const selectedTasks = tasks.filter(task => form.watch('taskIds').includes(task.id));
  const selectedMembers = members.filter(member => form.watch('memberIds').includes(member.id));

  const onSubmit = async (data: CreateSuspensionFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/families/${familyId}/suspensions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          startDate: data.startDate.toISOString(),
          endDate: data.endDate.toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się utworzyć zawieszenia');
      }

      onSuspensionCreated?.();
      onOpenChange(false);
      form.reset();
    } catch (error: any) {
      console.error('Error creating suspension:', error);
      toast({
        title: 'Błąd podczas tworzenia zawieszenia',
        description: error.message || 'Wystąpił błąd podczas tworzenia zawieszenia',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTask = (taskId: string) => {
    const currentTasks = form.getValues('taskIds');
    if (currentTasks.includes(taskId)) {
      form.setValue(
        'taskIds',
        currentTasks.filter(id => id !== taskId)
      );
    } else {
      form.setValue('taskIds', [...currentTasks, taskId]);
    }
  };

  const toggleMember = (memberId: string) => {
    const currentMembers = form.getValues('memberIds');
    if (currentMembers.includes(memberId)) {
      form.setValue(
        'memberIds',
        currentMembers.filter(id => id !== memberId)
      );
    } else {
      form.setValue('memberIds', [...currentMembers, memberId]);
    }
  };

  const removeTask = (taskId: string) => {
    const currentTasks = form.getValues('taskIds');
    form.setValue(
      'taskIds',
      currentTasks.filter(id => id !== taskId)
    );
  };

  const removeMember = (memberId: string) => {
    const currentMembers = form.getValues('memberIds');
    form.setValue(
      'memberIds',
      currentMembers.filter(id => id !== memberId)
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Utwórz nowe zawieszenie</DialogTitle>
          <DialogDescription>
            Zawieś zadania czasowo w określonych okolicznościach (wakacje, choroba, wyjazdy).
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tytuł zawieszenia</FormLabel>
                    <FormControl>
                      <Input placeholder="np. Wakacje letnie 2024" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Opis (opcjonalnie)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Dodatkowe informacje o zawieszeniu..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Powód zawieszenia</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Wybierz powód" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {reasonOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Data rozpoczęcia</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP', { locale: pl })
                            ) : (
                              <span>Wybierz datę</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={date => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Data zakończenia</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              'pl-3 text-left font-normal',
                              !field.value && 'text-muted-foreground'
                            )}
                          >
                            {field.value ? (
                              format(field.value, 'PPP', { locale: pl })
                            ) : (
                              <span>Wybierz datę</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={date => date < (form.getValues('startDate') || new Date())}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Task Selection */}
            <div className="space-y-3">
              <div>
                <FormLabel className="text-base">Zadania do zawieszenia</FormLabel>
                <FormDescription>
                  Wybierz zadania, które mają być zawieszone. Można również zawiesić wszystkie
                  zadania dla wybranych członków.
                </FormDescription>
              </div>

              {selectedTasks.length > 0 && (
                <div className="space-y-2">
                  <FormLabel className="text-sm">Wybrane zadania:</FormLabel>
                  <div className="flex flex-wrap gap-2">
                    {selectedTasks.map(task => (
                      <Badge key={task.id} variant="secondary" className="pr-1">
                        <CheckSquare className="h-3 w-3 mr-1" />
                        {task.title}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 ml-1"
                          onClick={() => removeTask(task.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <ScrollArea className="h-32 w-full border rounded-md p-3">
                <div className="space-y-2">
                  {tasks.map(task => (
                    <div key={task.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`task-${task.id}`}
                        checked={form.watch('taskIds').includes(task.id)}
                        onCheckedChange={() => toggleTask(task.id)}
                      />
                      <label
                        htmlFor={`task-${task.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1 cursor-pointer"
                      >
                        {task.title} ({task.points} pkt)
                      </label>
                    </div>
                  ))}
                  {tasks.length === 0 && (
                    <p className="text-sm text-muted-foreground">Brak dostępnych zadań</p>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Member Selection */}
            <div className="space-y-3">
              <div>
                <FormLabel className="text-base">Członkowie do zawieszenia</FormLabel>
                <FormDescription>
                  Wybierz członków rodziny, dla których wszystkie zadania mają być zawieszone.
                </FormDescription>
              </div>

              {selectedMembers.length > 0 && (
                <div className="space-y-2">
                  <FormLabel className="text-sm">Wybrani członkowie:</FormLabel>
                  <div className="flex flex-wrap gap-2">
                    {selectedMembers.map(member => (
                      <Badge key={member.id} variant="secondary" className="pr-1">
                        <Users className="h-3 w-3 mr-1" />
                        {getMemberDisplayName(member)}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0 ml-1"
                          onClick={() => removeMember(member.id)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <ScrollArea className="h-24 w-full border rounded-md p-3">
                <div className="space-y-2">
                  {members.map(member => (
                    <div key={member.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`member-${member.id}`}
                        checked={form.watch('memberIds').includes(member.id)}
                        onCheckedChange={() => toggleMember(member.id)}
                      />
                      <label
                        htmlFor={`member-${member.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1 cursor-pointer"
                      >
                        {getMemberDisplayName(member)}
                      </label>
                    </div>
                  ))}
                  {members.length === 0 && (
                    <p className="text-sm text-muted-foreground">Brak dostępnych członków</p>
                  )}
                </div>
              </ScrollArea>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Anuluj
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Tworzenie...' : 'Utwórz zawieszenie'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
