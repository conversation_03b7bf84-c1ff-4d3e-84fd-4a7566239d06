'use client';

import { SignInButton as ClerkSignInButton, SignOutButton } from '@clerk/nextjs';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';

interface SignInButtonProps {
  mode?: 'modal' | 'redirect';
  className?: string;
}

export function SignInButton({ mode = 'redirect', className }: SignInButtonProps) {
  const { isSignedIn } = useAuth();

  if (isSignedIn) {
    return (
      <SignOutButton>
        <Button variant="outline" className={className}>
          Wyloguj się
        </Button>
      </SignOutButton>
    );
  }

  return (
    <ClerkSignInButton mode={mode}>
      <Button className={className}>Zaloguj się</Button>
    </ClerkSignInButton>
  );
}
