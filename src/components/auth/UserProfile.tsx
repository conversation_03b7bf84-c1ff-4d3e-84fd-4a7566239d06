'use client';

import { UserButton } from '@clerk/nextjs';
import { useAuth } from '@/hooks/useAuth';

interface UserProfileProps {
  className?: string;
  showName?: boolean;
}

export function UserProfile({ className, showName = false }: UserProfileProps) {
  const { user, firstName, lastName } = useAuth();

  if (!user) return null;

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {showName && (
        <div className="text-sm">
          <div className="font-medium text-gray-900">
            {firstName || lastName ? `${firstName} ${lastName}`.trim() : 'Użytkownik'}
          </div>
          <div className="text-gray-500 text-xs">{user.emailAddresses[0]?.emailAddress}</div>
        </div>
      )}
      <UserButton
        appearance={{
          elements: {
            avatarBox: 'w-8 h-8',
          },
        }}
        userProfileProps={{
          appearance: {
            elements: {
              card: 'shadow-lg',
            },
          },
        }}
      />
    </div>
  );
}
