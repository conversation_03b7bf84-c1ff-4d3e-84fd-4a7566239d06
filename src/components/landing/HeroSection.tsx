'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { Check, Clock, Star, ArrowRight } from 'lucide-react';

interface HeroSectionProps {
  className?: string;
}

export function HeroSection({ className }: HeroSectionProps) {
  // Przykładowe dane jak w prototypie
  const sampleTasks = [
    {
      id: '1',
      title: 'Wymyć naczynia po kolacji',
      assignee: 'Anna',
      status: 'completed',
      time: '15 min temu',
    },
    {
      id: '2',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      assignee: 'Tomek',
      status: 'completed',
      time: '1 godz. temu',
    },
    {
      id: '3',
      title: 'Odku<PERSON>yć salon',
      assignee: 'Mama',
      status: 'in_progress',
      time: 'W trakcie',
    },
    {
      id: '4',
      title: '<PERSON><PERSON><PERSON><PERSON> zakupy na kolację',
      assignee: 'Tata',
      status: 'pending',
      time: 'Do 18:00',
    },
  ];

  const getTaskStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Check className="h-5 w-5 text-green-600 success-checkmark" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-orange-500" />;
      default:
        return <div className="h-5 w-5 rounded-full border-2 border-gray-300" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'border-l-green-500';
      case 'in_progress':
        return 'border-l-orange-500';
      default:
        return 'border-l-gray-300';
    }
  };

  return (
    <section className={`py-20 lg:py-32 bg-gradient-to-br from-purple-50 to-white ${className}`}>
      <Container size="xl">
        <Grid cols={1} lgCols={2} gap="xl" className="items-center">
          {/* Left Side - Text Content */}
          <GridItem className="space-y-8">
            <div className="space-y-6">
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-700 hover:bg-purple-200"
              >
                <Star className="h-4 w-4 mr-2" />
                Nowa wersja już dostępna!
              </Badge>

              <div className="space-y-4">
                <h1 className="text-display font-display text-gray-900 leading-tight">
                  Domowe obowiązki
                  <span className="text-purple-600 block">bez kłótni i stresu</span>
                </h1>

                <p className="text-xl text-gray-600 font-body leading-relaxed max-w-2xl">
                  Zorganizuj swoją rodzinę z FamilyTasks. Przydzielaj zadania, śledź postępy i
                  nagradzaj za wykonane obowiązki. Wszystko w jednym miejscu.
                </p>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                size="lg"
                className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-semibold transition-all hover-lift"
                asChild
              >
                <Link href="/sign-up">
                  Rozpocznij za darmo
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 text-lg font-semibold transition-all hover-lift"
                asChild
              >
                <Link href="/features">Dowiedz się więcej</Link>
              </Button>
            </div>

            {/* Social Proof */}
            <div className="flex items-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  <Avatar className="h-8 w-8 border-2 border-white">
                    <AvatarFallback className="bg-blue-500 text-white text-xs">A</AvatarFallback>
                  </Avatar>
                  <Avatar className="h-8 w-8 border-2 border-white">
                    <AvatarFallback className="bg-green-500 text-white text-xs">M</AvatarFallback>
                  </Avatar>
                  <Avatar className="h-8 w-8 border-2 border-white">
                    <AvatarFallback className="bg-purple-500 text-white text-xs">K</AvatarFallback>
                  </Avatar>
                </div>
                <span>
                  Zaufało nam już <strong>1,200+</strong> rodzin
                </span>
              </div>
            </div>
          </GridItem>

          {/* Right Side - Task Card Demo */}
          <GridItem className="relative">
            <div className="relative z-10">
              <Card className="p-6 bg-white card-shadow hover-lift transition-all">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-h3 font-display text-gray-900">Zadania rodziny na dziś</h3>
                    <Badge variant="secondary" className="bg-green-100 text-green-700">
                      3/4 ukończone
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    {sampleTasks.map((task, index) => (
                      <div
                        key={task.id}
                        className={`
                          flex items-center gap-4 p-4 rounded-lg 
                          border-l-4 bg-gray-50 transition-all hover:bg-gray-100
                          ${getStatusColor(task.status)}
                        `}
                        style={{
                          animationDelay: `${index * 0.1}s`,
                          animation: 'slideInRight 0.5s ease-out forwards',
                        }}
                      >
                        <div className="flex-shrink-0">{getTaskStatusIcon(task.status)}</div>

                        <div className="flex-1 min-w-0">
                          <div
                            className={`
                            font-medium text-gray-900 truncate
                            ${task.status === 'completed' ? 'line-through text-gray-600' : ''}
                          `}
                          >
                            {task.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            Przypisane: <span className="font-medium">{task.assignee}</span>
                          </div>
                        </div>

                        <div className="text-xs text-gray-500 text-right">{task.time}</div>
                      </div>
                    ))}
                  </div>

                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Dzienny postęp</span>
                      <span className="font-semibold text-green-600">75% ukończone</span>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: '75%' }}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            </div>

            {/* Background decorations */}
            <div className="absolute -top-4 -right-4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" />
            <div
              className="absolute -bottom-8 -left-4 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"
              style={{ animationDelay: '2s' }}
            />
          </GridItem>
        </Grid>
      </Container>
    </section>
  );
}

// Animation keyframes (add to globals.css if not present)
const styles = `
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
`;
