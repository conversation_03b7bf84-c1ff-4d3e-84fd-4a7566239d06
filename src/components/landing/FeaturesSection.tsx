'use client';

import { Card } from '@/components/ui/card';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { Calendar, Users, Bell, Check, Clock, Star, BarChart3, Gift } from 'lucide-react';

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: any;
  color: string;
  gradient: string;
}

interface FeaturesSectionProps {
  className?: string;
}

export function FeaturesSection({ className }: FeaturesSectionProps) {
  const features: Feature[] = [
    {
      id: 'schedule',
      title: 'Harmonogram zadań',
      description:
        'Planuj zadania domowe z kalendarza. Automatyczne przypomnienia zapewnią, że nic nie zostanie zapomniane.',
      icon: Calendar,
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-blue-600',
    },
    {
      id: 'accounts',
      title: 'Konta rodzinne',
      description:
        'Ka<PERSON><PERSON> członek rodziny ma swoje konto. Przydzielaj zadania, śledź postępy i zarządzaj uprawnieniami.',
      icon: Users,
      color: 'text-green-600',
      gradient: 'from-green-500 to-green-600',
    },
    {
      id: 'notifications',
      title: 'Powiadomienia',
      description:
        'Inteligentne powiadomienia przypomną o nadchodzących zadaniach i poinformują o ukończonych obowiązkach.',
      icon: Bell,
      color: 'text-orange-600',
      gradient: 'from-orange-500 to-orange-600',
    },
    {
      id: 'points',
      title: 'System punktów',
      description:
        'Motywuj do działania dzięki systemowi punktów i nagród. Zamień ukończone zadania na przyjemności.',
      icon: Check,
      color: 'text-purple-600',
      gradient: 'from-purple-500 to-purple-600',
    },
  ];

  const additionalFeatures = [
    {
      icon: Clock,
      title: 'Śledzenie czasu',
      description: 'Monitoruj ile czasu zajmują poszczególne zadania',
    },
    {
      icon: BarChart3,
      title: 'Statystyki i raporty',
      description: 'Analizuj produktywność całej rodziny',
    },
    {
      icon: Star,
      title: 'Oceny i feedback',
      description: 'Oceniaj jakość wykonanych zadań',
    },
    {
      icon: Gift,
      title: 'Nagrody i motywacja',
      description: 'System nagród za systematyczne wykonywanie obowiązków',
    },
  ];

  return (
    <section className={`py-20 lg:py-32 bg-white ${className}`}>
      <Container size="xl">
        <div className="space-y-16">
          {/* Section Header */}
          <div className="text-center space-y-4">
            <h2 className="text-h1 font-display text-gray-900">Funkcje dla całej rodziny</h2>
            <p className="text-xl text-gray-600 font-body max-w-3xl mx-auto">
              Wszystkie narzędzia potrzebne do zorganizowania domowych obowiązków w jednej, prostej
              aplikacji.
            </p>
          </div>

          {/* Main Features Grid */}
          <Grid cols={1} mdCols={2} gap="lg">
            {features.map((feature, index) => (
              <GridItem key={feature.id}>
                <Card
                  className="
                    p-8 h-full card-shadow hover-lift transition-all duration-300
                    border border-gray-100 hover:border-purple-200
                  "
                  style={{
                    animationDelay: `${index * 0.1}s`,
                  }}
                >
                  <div className="space-y-6">
                    {/* Icon */}
                    <div className="relative">
                      <div
                        className={`
                        w-16 h-16 rounded-xl 
                        bg-gradient-to-br ${feature.gradient}
                        flex items-center justify-center
                        shadow-lg
                      `}
                      >
                        <feature.icon className="h-8 w-8 text-white" />
                      </div>
                      <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                        <Star className="h-3 w-3 text-yellow-700" />
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-3">
                      <h3 className="text-h3 font-display text-gray-900">{feature.title}</h3>
                      <p className="text-gray-600 font-body leading-relaxed">
                        {feature.description}
                      </p>
                    </div>

                    {/* Feature highlight */}
                    <div
                      className={`
                      inline-flex items-center gap-2 text-sm font-medium
                      ${feature.color}
                    `}
                    >
                      <Check className="h-4 w-4" />
                      <span>Dostępne we wszystkich planach</span>
                    </div>
                  </div>
                </Card>
              </GridItem>
            ))}
          </Grid>

          {/* Additional Features */}
          <div className="space-y-8">
            <div className="text-center">
              <h3 className="text-h2 font-display text-gray-900 mb-4">I wiele więcej...</h3>
              <p className="text-gray-600 font-body">
                Odkryj dodatkowe funkcje, które uczynią zarządzanie rodziną jeszcze łatwiejszym
              </p>
            </div>

            <Grid cols={1} mdCols={2} lgCols={4} gap="md">
              {additionalFeatures.map((feature, index) => (
                <GridItem key={feature.title}>
                  <div
                    className="
                      text-center p-6 rounded-lg 
                      bg-gray-50 hover:bg-purple-50 
                      transition-all duration-300 hover-scale
                    "
                    style={{
                      animationDelay: `${0.5 + index * 0.1}s`,
                    }}
                  >
                    <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="h-6 w-6 text-purple-600" />
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-2">{feature.title}</h4>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </GridItem>
              ))}
            </Grid>
          </div>

          {/* Call to Action */}
          <div className="text-center space-y-6 py-16">
            <div className="space-y-4">
              <h3 className="text-h2 font-display text-gray-900">
                Gotowy na zorganizowaną rodzinę?
              </h3>
              <p className="text-gray-600 font-body max-w-2xl mx-auto">
                Dołącz do tysięcy rodzin, które już korzystają z FamilyTasks i odkryj jak proste
                może być zarządzanie domowymi obowiązkami.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="
                bg-purple-600 hover:bg-purple-700 text-white 
                px-8 py-4 rounded-lg font-semibold 
                transition-all hover-lift
              "
              >
                Rozpocznij 14-dniowy trial
              </button>
              <button
                className="
                border border-purple-200 text-purple-700 hover:bg-purple-50 
                px-8 py-4 rounded-lg font-semibold 
                transition-all hover-lift
              "
              >
                Zobacz demo
              </button>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
}
