'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Container, Grid, GridItem } from '@/components/ui/Grid';
import { UserPlus, Plus, BarChart3, Gift, ArrowRight, CheckCircle } from 'lucide-react';

interface Step {
  number: string;
  title: string;
  description: string;
  details: string[];
  icon: any;
  color: string;
  bgColor: string;
}

interface HowItWorksSectionProps {
  className?: string;
}

export function HowItWorksSection({ className }: HowItWorksSectionProps) {
  const steps: Step[] = [
    {
      number: '01',
      title: 'Stw<PERSON>rz konto rodzinne',
      description:
        'Zał<PERSON>ż darmowe konto i zaproś członków swojej rodziny. Każdy otrzyma własny profil z odpowiednimi uprawnieniami.',
      details: [
        'Rejestracja w 2 minuty',
        'Dodaj członków rodziny',
        'Ustaw role i uprawnienia',
        'Personalizuj profile',
      ],
      icon: UserPlus,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      number: '02',
      title: 'Dodaj zadania',
      description:
        'Twórz zadania domowe, przypisuj je członkom rodziny i ustal terminy. Skorzystaj z szablonów lub stwórz własne.',
      details: [
        'Gotowe szablony zadań',
        'Kalendarz i terminy',
        'Zadania cykliczne',
        'Poziomy trudności',
      ],
      icon: Plus,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      number: '03',
      title: 'Śledź postępy',
      description:
        'Monitoruj wykonane zadania w czasie rzeczywistym. Otrzymuj powiadomienia o zakończonych obowiązkach.',
      details: [
        'Dashboard z postępami',
        'Powiadomienia w czasie rzeczywistym',
        'Raporty tygodniowe',
        'Historia zadań',
      ],
      icon: BarChart3,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      number: '04',
      title: 'Wymieniaj punkty na nagrody',
      description:
        'Ustaw system nagród za ukończone zadania. Motywuj rodzinę do systematycznego wykonywania obowiązków.',
      details: [
        'System punktów',
        'Katalog nagród',
        'Automatyczne naliczanie',
        'Motywacja dla całej rodziny',
      ],
      icon: Gift,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
  ];

  return (
    <section className={`py-20 lg:py-32 bg-gray-50 ${className}`}>
      <Container size="xl">
        <div className="space-y-16">
          {/* Section Header */}
          <div className="text-center space-y-4">
            <Badge variant="secondary" className="bg-purple-100 text-purple-700 mb-4">
              Jak to działa?
            </Badge>
            <h2 className="text-h1 font-display text-gray-900">
              4 proste kroki do zorganizowanej rodziny
            </h2>
            <p className="text-xl text-gray-600 font-body max-w-3xl mx-auto">
              Poznaj jak łatwo możesz zorganizować swoją rodzinę i wprowadzić harmonię w domowe
              obowiązki.
            </p>
          </div>

          {/* Steps */}
          <div className="space-y-12">
            {steps.map((step, index) => (
              <div key={step.number} className="relative">
                {/* Connector Arrow (except for last item) */}
                {index < steps.length - 1 && (
                  <div className="absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-full z-10 hidden lg:block">
                    <div className="w-8 h-16 flex items-center justify-center">
                      <ArrowRight
                        className="h-8 w-8 text-purple-400 transform rotate-90"
                        style={{
                          filter: 'drop-shadow(0 2px 4px rgba(139, 92, 246, 0.3))',
                        }}
                      />
                    </div>
                  </div>
                )}

                <Grid
                  cols={1}
                  lgCols={2}
                  gap="lg"
                  className={`items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}
                >
                  {/* Content */}
                  <GridItem className={index % 2 === 1 ? 'lg:order-2' : ''}>
                    <div className="space-y-6">
                      {/* Step Number and Title */}
                      <div className="space-y-3">
                        <div className="flex items-center gap-4">
                          <div
                            className={`
                            w-12 h-12 rounded-full ${step.bgColor} 
                            flex items-center justify-center
                          `}
                          >
                            <span className={`text-lg font-bold ${step.color}`}>{step.number}</span>
                          </div>
                          <h3 className="text-h2 font-display text-gray-900">{step.title}</h3>
                        </div>
                      </div>

                      {/* Description */}
                      <p className="text-lg text-gray-600 font-body leading-relaxed">
                        {step.description}
                      </p>

                      {/* Details List */}
                      <div className="space-y-3">
                        {step.details.map((detail, detailIndex) => (
                          <div
                            key={detailIndex}
                            className="flex items-center gap-3"
                            style={{
                              animationDelay: `${index * 0.2 + detailIndex * 0.1}s`,
                            }}
                          >
                            <CheckCircle className={`h-5 w-5 ${step.color} flex-shrink-0`} />
                            <span className="text-gray-700 font-medium">{detail}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </GridItem>

                  {/* Visual */}
                  <GridItem className={index % 2 === 1 ? 'lg:order-1' : ''}>
                    <Card className="p-8 card-shadow hover-lift transition-all bg-white">
                      <div className="text-center space-y-6">
                        {/* Large Icon */}
                        <div className="relative mx-auto">
                          <div
                            className={`
                            w-24 h-24 rounded-2xl ${step.bgColor}
                            flex items-center justify-center mx-auto
                            shadow-lg
                          `}
                          >
                            <step.icon className={`h-12 w-12 ${step.color}`} />
                          </div>

                          {/* Step number badge */}
                          <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full border-2 border-purple-200 flex items-center justify-center">
                            <span className="text-sm font-bold text-purple-600">{step.number}</span>
                          </div>
                        </div>

                        {/* Status indicator */}
                        <div className="space-y-2">
                          <div
                            className={`
                            inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium
                            ${step.bgColor} ${step.color}
                          `}
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span>Krok {step.number}</span>
                          </div>
                          <p className="text-gray-600 font-medium">{step.title}</p>
                        </div>
                      </div>
                    </Card>
                  </GridItem>
                </Grid>
              </div>
            ))}
          </div>

          {/* Bottom CTA */}
          <div className="text-center space-y-6 pt-16">
            <div className="space-y-4">
              <h3 className="text-h2 font-display text-gray-900">Zacznij już dziś!</h3>
              <p className="text-gray-600 font-body max-w-2xl mx-auto">
                Dołącz do tysięcy zadowolonych rodzin i odkryj jak proste może być zarządzanie
                domowymi obowiązkami.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="
                bg-purple-600 hover:bg-purple-700 text-white 
                px-8 py-4 rounded-lg font-semibold text-lg
                transition-all hover-lift
                flex items-center justify-center gap-2
              "
              >
                Rozpocznij za darmo
                <ArrowRight className="h-5 w-5" />
              </button>
              <button
                className="
                border border-purple-200 text-purple-700 hover:bg-purple-50 
                px-8 py-4 rounded-lg font-semibold text-lg
                transition-all hover-lift
              "
              >
                Obejrzyj demo
              </button>
            </div>

            {/* Trust indicators */}
            <div className="pt-8 flex items-center justify-center gap-8 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Darmowy 14-dniowy trial</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Bez zobowiązań</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Anuluj w każdej chwili</span>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
}
