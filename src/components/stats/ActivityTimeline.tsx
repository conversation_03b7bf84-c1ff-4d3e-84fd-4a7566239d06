'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Activity,
  CheckCircle,
  UserPlus,
  Award,
  Clock,
  Calendar,
  Pause,
  RefreshCw,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { pl } from 'date-fns/locale';

interface ActivityTimelineProps {
  familyId: string;
  activities?: any[];
  maxItems?: number;
  showLoadMore?: boolean;
}

interface ActivityItem {
  id: string;
  action: string;
  entityType: string;
  entityId?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  user?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  member?: {
    id: string;
    user: {
      firstName: string;
      lastName: string;
    };
  };
}

export default function ActivityTimeline({
  familyId,
  activities = [],
  maxItems = 10,
  showLoadMore = true,
}: ActivityTimelineProps) {
  const [loading, setLoading] = useState(false);
  const [allActivities, setAllActivities] = useState<ActivityItem[]>(activities);
  const [expanded, setExpanded] = useState(false);

  const loadMoreActivities = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `/api/families/${familyId}/stats/activity?limit=20&offset=${allActivities?.length || 0}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch activities');
      }

      const newActivities = await response.json();
      setAllActivities(prev => [...prev, ...newActivities]);
    } catch (error) {
      console.error('Error loading more activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (action: string) => {
    switch (action) {
      case 'TASK_COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'TASK_VERIFIED':
        return <Award className="h-4 w-4 text-blue-500" />;
      case 'TASK_CREATED':
        return <Calendar className="h-4 w-4 text-purple-500" />;
      case 'TASK_ASSIGNED':
        return <Clock className="h-4 w-4 text-orange-500" />;
      case 'TASK_SUSPENDED':
        return <Pause className="h-4 w-4 text-red-500" />;
      case 'MEMBER_JOINED':
        return <UserPlus className="h-4 w-4 text-green-500" />;
      case 'MEMBER_LEFT':
        return <UserPlus className="h-4 w-4 text-red-500" />;
      case 'POINTS_AWARDED':
        return <Award className="h-4 w-4 text-yellow-500" />;
      case 'FAMILY_CREATED':
        return <Activity className="h-4 w-4 text-blue-500" />;
      default:
        return <Activity className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getActivityBadgeVariant = (
    action: string
  ): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (action) {
      case 'TASK_COMPLETED':
      case 'TASK_VERIFIED':
      case 'MEMBER_JOINED':
      case 'POINTS_AWARDED':
        return 'default';
      case 'TASK_SUSPENDED':
      case 'MEMBER_LEFT':
        return 'destructive';
      case 'TASK_CREATED':
      case 'TASK_ASSIGNED':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getActivityText = (activity: ActivityItem) => {
    const actorName = activity.member?.user
      ? `${activity.member.user.firstName || ''} ${activity.member.user.lastName || ''}`.trim()
      : activity.user
        ? `${activity.user.firstName || ''} ${activity.user.lastName || ''}`.trim()
        : 'Nieznany użytkownik';

    switch (activity.action) {
      case 'TASK_COMPLETED':
        return `${actorName} ukończył(a) zadanie`;
      case 'TASK_VERIFIED':
        return `${actorName} zweryfikował(a) zadanie`;
      case 'TASK_CREATED':
        return `${actorName} utworzył(a) nowe zadanie`;
      case 'TASK_ASSIGNED':
        return `${actorName} przypisał(a) zadanie`;
      case 'TASK_SUSPENDED':
        return `${actorName} zawiesił(a) zadanie`;
      case 'MEMBER_JOINED':
        return `${actorName} dołączył(a) do rodziny`;
      case 'MEMBER_LEFT':
        return `${actorName} opuścił(a) rodzinę`;
      case 'POINTS_AWARDED':
        const points = activity.metadata?.points || 0;
        return `${actorName} otrzymał(a) ${points} punktów`;
      case 'FAMILY_CREATED':
        return `${actorName} utworzył(a) rodzinę`;
      default:
        return `${actorName} wykonał(a) akcję: ${activity.action}`;
    }
  };

  const getActivityDetails = (activity: ActivityItem) => {
    if (activity.metadata) {
      const details = [];

      if (activity.metadata.points) {
        details.push(`${activity.metadata.points} punktów`);
      }

      if (activity.metadata.taskTitle) {
        details.push(`"${activity.metadata.taskTitle}"`);
      }

      if (activity.metadata.reason) {
        details.push(activity.metadata.reason);
      }

      return details.join(' • ');
    }

    return null;
  };

  const getInitials = (name: string) => {
    if (!name || typeof name !== 'string') return 'UN';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatRelativeTime = (date: Date) => {
    try {
      return formatDistanceToNow(new Date(date), {
        addSuffix: true,
        locale: pl,
      });
    } catch {
      return 'Nieznana data';
    }
  };

  const displayedActivities = expanded ? allActivities : allActivities.slice(0, maxItems);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Ostatnia aktywność
            </CardTitle>
            <CardDescription>Najnowsze akcje członków rodziny</CardDescription>
          </div>

          {allActivities.length > maxItems && (
            <Button variant="outline" size="sm" onClick={() => setExpanded(!expanded)}>
              {expanded ? 'Pokaż mniej' : `Pokaż wszystkie (${allActivities.length})`}
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent>
        {displayedActivities.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            <div className="text-center">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>Brak aktywności do wyświetlenia</p>
            </div>
          </div>
        ) : (
          <ScrollArea className={expanded ? 'h-96' : 'h-auto'}>
            <div className="space-y-4">
              {displayedActivities.map((activity, index) => {
                const actorName = activity.member?.user
                  ? `${activity.member.user.firstName || ''} ${activity.member.user.lastName || ''}`.trim()
                  : activity.user
                    ? `${activity.user.firstName || ''} ${activity.user.lastName || ''}`.trim()
                    : 'Nieznany użytkownik';

                return (
                  <div
                    key={activity.id}
                    className="flex items-start space-x-3 pb-4 last:pb-0 border-b last:border-b-0"
                  >
                    <Avatar className="h-8 w-8 mt-1">
                      <AvatarFallback className="text-xs">{getInitials(actorName)}</AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {getActivityIcon(activity.action)}
                        <Badge
                          variant={getActivityBadgeVariant(activity.action)}
                          className="text-xs"
                        >
                          {activity.action.replace('_', ' ').toLowerCase()}
                        </Badge>
                      </div>

                      <p className="text-sm font-medium">{getActivityText(activity)}</p>

                      {getActivityDetails(activity) && (
                        <p className="text-xs text-muted-foreground mt-1">
                          {getActivityDetails(activity)}
                        </p>
                      )}

                      <p className="text-xs text-muted-foreground mt-2">
                        {formatRelativeTime(activity.timestamp)}
                      </p>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleTimeString('pl-PL', {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        )}

        {showLoadMore && !expanded && allActivities.length >= maxItems && (
          <div className="mt-4 text-center">
            <Button variant="outline" size="sm" onClick={loadMoreActivities} disabled={loading}>
              {loading ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Ładowanie...
                </>
              ) : (
                'Załaduj więcej'
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
