'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { StatsPeriod } from '@prisma/client';
import { Trophy, Medal, Award, Star, Users, TrendingUp } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface PointsLeaderboardProps {
  familyId: string;
  period: StatsPeriod;
  showAll?: boolean;
  maxItems?: number;
}

interface LeaderboardData {
  rankings: Array<{
    memberId: string;
    memberName: string;
    value: number;
    rank: number;
    member: {
      id: string;
      name: string;
      email: string;
      role: string;
      totalPoints: number;
    };
    stats: {
      tasksCompleted: number;
      tasksAssigned: number;
      completionRate: number;
      pointsEarned: number;
      streakDays: number;
      activeDays: number;
    };
    badge: string;
    achievement: string | null;
  }>;
  summary: {
    totalMembers: number;
    totalPoints: number;
    totalTasks: number;
    averageCompletionRate: number;
    period: StatsPeriod;
    metric: string;
  };
}

export default function PointsLeaderboard({
  familyId,
  period,
  showAll = false,
  maxItems = 5,
}: PointsLeaderboardProps) {
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [metric, setMetric] = useState<'points' | 'completion_rate'>('points');
  const [expanded, setExpanded] = useState(showAll);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(
          `/api/families/${familyId}/stats/leaderboard?period=${period}&metric=${metric}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch leaderboard data');
        }

        const data = await response.json();
        setLeaderboardData(data);
      } catch (err) {
        console.error('Error fetching leaderboard:', err);
        setError('Nie udało się załadować rankingu');
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
  }, [familyId, period, metric]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <Star className="h-5 w-5 text-muted-foreground" />;
    }
  };

  const getRankBadgeVariant = (
    rank: number
  ): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (rank) {
      case 1:
        return 'default';
      case 2:
      case 3:
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getInitials = (name: string) => {
    if (!name || typeof name !== 'string') return 'UN';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatValue = (value: number, metric: string) => {
    if (metric === 'completion_rate') {
      return `${Math.round(value)}%`;
    }
    return value.toString();
  };

  const getProgressValue = (value: number, maxValue: number, metric: string) => {
    if (metric === 'completion_rate') {
      return value;
    }
    return maxValue > 0 ? (value / maxValue) * 100 : 0;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-2 w-full" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Ranking punktów</CardTitle>
          <CardDescription>Błąd ładowania danych</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32 text-muted-foreground">{error}</div>
        </CardContent>
      </Card>
    );
  }

  const displayedRankings = expanded
    ? leaderboardData?.rankings || []
    : (leaderboardData?.rankings || []).slice(0, maxItems);

  const maxValue = leaderboardData?.rankings[0]?.value || 1;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Ranking {metric === 'points' ? 'punktów' : 'ukończenia zadań'}
            </CardTitle>
            <CardDescription>Najlepsi wykonawcy w okresie: {period.toLowerCase()}</CardDescription>
          </div>

          <div className="flex gap-2">
            <Button
              variant={metric === 'points' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setMetric('points')}
            >
              Punkty
            </Button>
            <Button
              variant={metric === 'completion_rate' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setMetric('completion_rate')}
            >
              Ukończenia
            </Button>
          </div>
        </div>

        {leaderboardData?.summary && (
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              {leaderboardData.summary.totalMembers} członków
            </div>
            <div className="flex items-center gap-1">
              <TrendingUp className="h-4 w-4" />
              Średnia: {leaderboardData.summary.averageCompletionRate}%
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {displayedRankings.length === 0 ? (
          <div className="flex items-center justify-center h-32 text-muted-foreground">
            Brak danych do wyświetlenia
          </div>
        ) : (
          <div className="space-y-3">
            {displayedRankings.map(ranking => (
              <div
                key={ranking.memberId}
                className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-2">
                  {getRankIcon(ranking.rank)}
                  <Badge variant={getRankBadgeVariant(ranking.rank)}>#{ranking.rank}</Badge>
                </div>

                <Avatar className="h-10 w-10">
                  <AvatarFallback className="text-sm">
                    {getInitials(ranking.memberName)}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-sm truncate">{ranking.memberName}</p>
                    {ranking.achievement && (
                      <Badge variant="secondary" className="text-xs">
                        {ranking.achievement}
                      </Badge>
                    )}
                  </div>

                  <div className="mt-1">
                    <Progress
                      value={getProgressValue(ranking.value, maxValue, metric)}
                      className="h-2"
                    />
                  </div>

                  <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                    <span>{ranking.stats.tasksCompleted} zadań</span>
                    <span>{ranking.stats.completionRate?.toFixed(0) || '0'}% ukończenia</span>
                    {ranking.stats.streakDays > 0 && (
                      <span className="text-orange-600">🔥 {ranking.stats.streakDays} dni</span>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <div className="font-bold text-lg">{formatValue(ranking.value, metric)}</div>
                  <div className="text-xs text-muted-foreground">
                    {metric === 'points' ? 'punktów' : 'wskaźnik'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {!showAll && leaderboardData && leaderboardData.rankings.length > maxItems && (
          <div className="mt-4 text-center">
            <Button variant="outline" size="sm" onClick={() => setExpanded(!expanded)}>
              {expanded ? 'Pokaż mniej' : `Pokaż wszystkich (${leaderboardData.rankings.length})`}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
