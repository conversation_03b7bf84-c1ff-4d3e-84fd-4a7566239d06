'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { format, subDays, eachDayOfInterval } from 'date-fns';
import { pl } from 'date-fns/locale';
import { useMemo } from 'react';

interface TaskTrendsData {
  date: string;
  completed: number;
  assigned: number;
  overdue: number;
}

interface TaskTrendsChartProps {
  familyId: string;
  period?: number; // days
  className?: string;
}

export function TaskTrendsChart({ familyId, period = 30, className }: TaskTrendsChartProps) {
  // Mock data - w rzeczywistej aplikacji pobierałoby z API
  const data = useMemo(() => {
    const endDate = new Date();
    const startDate = subDays(endDate, period - 1);
    const dateRange = eachDayOfInterval({ start: startDate, end: endDate });

    return dateRange.map(date => ({
      date: format(date, 'dd.MM', { locale: pl }),
      completed: Math.floor(Math.random() * 10) + 2,
      assigned: Math.floor(Math.random() * 15) + 5,
      overdue: Math.floor(Math.random() * 3),
    }));
  }, [period]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium text-sm mb-2">{`Data: ${label}`}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-xs" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Trendy zadań</CardTitle>
        <CardDescription>Analiza wykonania zadań w ostatnich {period} dniach</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis dataKey="date" fontSize={12} tick={{ fill: 'hsl(var(--muted-foreground))' }} />
              <YAxis fontSize={12} tick={{ fill: 'hsl(var(--muted-foreground))' }} />
              <Tooltip content={<CustomTooltip />} />
              <Legend wrapperStyle={{ fontSize: '12px' }} iconType="line" />
              <Line
                type="monotone"
                dataKey="completed"
                stroke="hsl(var(--chart-1))"
                strokeWidth={2}
                name="Ukończone"
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
              />
              <Line
                type="monotone"
                dataKey="assigned"
                stroke="hsl(var(--chart-2))"
                strokeWidth={2}
                name="Przypisane"
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
              />
              <Line
                type="monotone"
                dataKey="overdue"
                stroke="hsl(var(--destructive))"
                strokeWidth={2}
                name="Przeterminowane"
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        <div className="flex justify-center gap-6 mt-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-chart-1 rounded"></div>
            <span>Ukończone</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-chart-2 rounded"></div>
            <span>Przypisane</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-0.5 bg-destructive rounded"></div>
            <span>Przeterminowane</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
