'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { StatsPeriod } from '@prisma/client';
import {
  TrendingUp,
  TrendingDown,
  Users,
  Trophy,
  Target,
  Award,
  Calendar,
  Activity,
  RefreshCw,
} from 'lucide-react';
import CompletionRateChart from './CompletionRateChart';
import PointsLeaderboard from './PointsLeaderboard';
import ActivityTimeline from './ActivityTimeline';

interface StatsDashboardProps {
  familyId: string;
  period?: StatsPeriod;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

interface StatsOverview {
  summary: {
    monthlyCompletionRate: number;
    weeklyCompletionRate: number;
    thisWeek: {
      tasksCompleted: number;
      totalPoints: number;
      activeMembersCount: number;
    };
    totalMembers: number;
  };
  rankings: {
    points: Array<{
      memberId: string;
      memberName: string;
      value: number;
      rank: number;
    }>;
    completion: Array<{
      memberId: string;
      memberName: string;
      value: number;
      rank: number;
    }>;
  };
  recentActivity: any[];
  insights: {
    familyId: string;
    totalTasks: number;
    completionRate: number;
    totalPointsAwarded: number;
    mostActiveDay: string;
    topPerformer: {
      memberId: string;
      memberName: string;
      points: number;
    };
    trends: {
      tasksCreated: 'up' | 'down' | 'stable';
      completionRate: 'up' | 'down' | 'stable';
      memberActivity: 'up' | 'down' | 'stable';
    };
  };
  trends: {
    completionRateChange: number;
    isImproving: boolean;
  };
}

export default function StatsDashboard({
  familyId,
  period = 'DAILY',
  dateRange,
}: StatsDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<StatsPeriod>(period);
  const [overview, setOverview] = useState<StatsOverview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchOverview = useCallback(async () => {
    try {
      setError(null);
      const response = await fetch(`/api/families/${familyId}/stats/overview`);

      if (!response.ok) {
        throw new Error('Failed to fetch stats overview');
      }

      const data = await response.json();
      setOverview(data);
    } catch (err) {
      console.error('Error fetching stats overview:', err);
      setError('Nie udało się załadować statystyk');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [familyId]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchOverview();
  }, [fetchOverview]);

  useEffect(() => {
    fetchOverview();
  }, [fetchOverview]);

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendBadgeVariant = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return 'default' as const;
      case 'down':
        return 'destructive' as const;
      default:
        return 'secondary' as const;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>

        <div className="grid gap-4 grid-cols-2 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-20 mt-2" />
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">Błąd ładowania</CardTitle>
            <CardDescription className="text-center">{error}</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Spróbuj ponownie
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!overview) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard Statystyk</h1>
          <p className="text-muted-foreground">Przegląd aktywności i wydajności rodziny</p>
        </div>

        <div className="flex items-center gap-2">
          <Select
            value={selectedPeriod}
            onValueChange={value => setSelectedPeriod(value as StatsPeriod)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DAILY">Dziennie</SelectItem>
              <SelectItem value="WEEKLY">Tygodniowo</SelectItem>
              <SelectItem value="MONTHLY">Miesięcznie</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Odśwież
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-4 grid-cols-2 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ukończenie zadań</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.summary.weeklyCompletionRate}%</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {overview.trends.isImproving ? (
                <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              ) : (
                <TrendingDown className="mr-1 h-3 w-3 text-red-500" />
              )}
              {Math.abs(overview.trends.completionRateChange).toFixed(1)}% od ostatniego miesiąca
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Zadania w tym tygodniu</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.summary.thisWeek.tasksCompleted}</div>
            <p className="text-xs text-muted-foreground">Ukończonych zadań</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Punkty w tym tygodniu</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.summary.thisWeek.totalPoints}</div>
            <p className="text-xs text-muted-foreground">Zdobytych punktów</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktywni członkowie</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.summary.thisWeek.activeMembersCount}</div>
            <p className="text-xs text-muted-foreground">
              z {overview.summary.totalMembers} członków
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Insights and Trends */}
      {overview.insights && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Insights i trendy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Tworzenie zadań:</span>
                <Badge variant={getTrendBadgeVariant(overview.insights.trends.tasksCreated)}>
                  {getTrendIcon(overview.insights.trends.tasksCreated)}
                  <span className="ml-1 capitalize">
                    {overview.insights.trends.tasksCreated === 'stable'
                      ? 'stabilne'
                      : overview.insights.trends.tasksCreated === 'up'
                        ? 'wzrost'
                        : 'spadek'}
                  </span>
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Ukończenia:</span>
                <Badge variant={getTrendBadgeVariant(overview.insights.trends.completionRate)}>
                  {getTrendIcon(overview.insights.trends.completionRate)}
                  <span className="ml-1 capitalize">
                    {overview.insights.trends.completionRate === 'stable'
                      ? 'stabilne'
                      : overview.insights.trends.completionRate === 'up'
                        ? 'wzrost'
                        : 'spadek'}
                  </span>
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Aktywność:</span>
                <Badge variant={getTrendBadgeVariant(overview.insights.trends.memberActivity)}>
                  {getTrendIcon(overview.insights.trends.memberActivity)}
                  <span className="ml-1 capitalize">
                    {overview.insights.trends.memberActivity === 'stable'
                      ? 'stabilna'
                      : overview.insights.trends.memberActivity === 'up'
                        ? 'wzrost'
                        : 'spadek'}
                  </span>
                </Badge>
              </div>
            </div>

            {overview.insights?.topPerformer?.memberName && (
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <p className="text-sm">
                  <strong>Najlepszy wykonawca:</strong> {overview.insights.topPerformer.memberName}{' '}
                  z {overview.insights.topPerformer.points || 0} punktami
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Charts and Data */}
      <div className="grid gap-6 lg:grid-cols-2">
        <CompletionRateChart familyId={familyId} period={selectedPeriod} />
        <PointsLeaderboard familyId={familyId} period={selectedPeriod} />
      </div>

      {/* Activity Timeline */}
      <ActivityTimeline familyId={familyId} activities={overview.recentActivity} />
    </div>
  );
}
