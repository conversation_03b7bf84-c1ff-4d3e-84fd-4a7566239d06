'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from 'recharts';
import { useMemo, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface MemberPerformance {
  metric: string;
  [memberName: string]: number | string;
}

interface MemberData {
  id: string;
  name: string;
  avatar?: string;
  color: string;
}

interface MemberPerformanceChartProps {
  familyId: string;
  members?: MemberData[];
  className?: string;
}

const MEMBER_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
];

export function MemberPerformanceChart({
  familyId,
  members = [],
  className,
}: MemberPerformanceChartProps) {
  const [selectedMembers, setSelectedMembers] = useState<string[]>(
    (members || []).slice(0, 3).map(m => m.name) // Domyślnie pierwsze 3 członków
  );

  // Mock data - w rzeczywistej aplikacji pobierałoby z API
  const mockMembers: MemberData[] =
    members.length > 0
      ? members
      : [
          { id: '1', name: 'Anna', color: MEMBER_COLORS[0] },
          { id: '2', name: 'Paweł', color: MEMBER_COLORS[1] },
          { id: '3', name: 'Kasia', color: MEMBER_COLORS[2] },
          { id: '4', name: 'Tomek', color: MEMBER_COLORS[3] },
        ];

  const data = useMemo(() => {
    // Metryki wydajności (0-100)
    const metrics = [
      'Terminowość',
      'Jakość wykonania',
      'Konsekwencja',
      'Aktywność',
      'Współpraca',
      'Punkty',
    ];

    return metrics.map(metric => {
      const dataPoint: MemberPerformance = { metric };

      mockMembers.forEach(member => {
        // Mock values - w rzeczywistości z API
        dataPoint[member.name] = Math.floor(Math.random() * 40) + 60; // 60-100
      });

      return dataPoint;
    });
  }, [mockMembers]);

  const toggleMember = (memberName: string) => {
    setSelectedMembers(prev =>
      prev.includes(memberName) ? prev.filter(name => name !== memberName) : [...prev, memberName]
    );
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg min-w-[200px]">
          <p className="font-medium text-sm mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center justify-between gap-2 text-xs">
              <span style={{ color: entry.color }}>{entry.name}:</span>
              <span className="font-medium">{entry.value}%</span>
            </div>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Wydajność członków rodziny</CardTitle>
        <CardDescription>Porównanie wydajności w różnych kategoriach</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Member Selection */}
        <div className="flex flex-wrap gap-2 mb-6">
          {mockMembers.map(member => (
            <Badge
              key={member.id}
              variant={selectedMembers.includes(member.name) ? 'default' : 'outline'}
              className="cursor-pointer hover:bg-accent transition-colors"
              onClick={() => toggleMember(member.name)}
            >
              <div className="flex items-center gap-2">
                <Avatar className="w-4 h-4">
                  <AvatarImage src={member.avatar} />
                  <AvatarFallback className="text-xs">{member.name.charAt(0)}</AvatarFallback>
                </Avatar>
                {member.name}
              </div>
            </Badge>
          ))}
        </div>

        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={data} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
              <PolarGrid gridType="polygon" className="opacity-30" />
              <PolarAngleAxis
                dataKey="metric"
                tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
              />
              <PolarRadiusAxis
                angle={90}
                domain={[0, 100]}
                tick={{ fontSize: 10, fill: 'hsl(var(--muted-foreground))' }}
                tickCount={5}
              />

              {mockMembers
                .filter(member => selectedMembers.includes(member.name))
                .map((member, index) => (
                  <Radar
                    key={member.id}
                    name={member.name}
                    dataKey={member.name}
                    stroke={member.color}
                    fill={member.color}
                    fillOpacity={0.1}
                    strokeWidth={2}
                    dot={{ r: 4, strokeWidth: 2 }}
                  />
                ))}

              <Tooltip content={<CustomTooltip />} />
              <Legend wrapperStyle={{ fontSize: '12px' }} iconType="line" />
            </RadarChart>
          </ResponsiveContainer>
        </div>

        {/* Performance Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          {mockMembers
            .filter(member => selectedMembers.includes(member.name))
            .map(member => {
              const avgScore =
                data && data.length > 0
                  ? data.reduce((sum, metric) => sum + (metric[member.name] as number), 0) /
                    data.length
                  : 0;

              return (
                <div key={member.id} className="text-center p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={member.avatar} />
                      <AvatarFallback className="text-xs">{member.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span className="font-medium text-sm">{member.name}</span>
                  </div>
                  <div className="text-2xl font-bold" style={{ color: member.color }}>
                    {avgScore.toFixed(0)}%
                  </div>
                  <div className="text-xs text-muted-foreground">Średnia wydajność</div>
                </div>
              );
            })}
        </div>
      </CardContent>
    </Card>
  );
}
