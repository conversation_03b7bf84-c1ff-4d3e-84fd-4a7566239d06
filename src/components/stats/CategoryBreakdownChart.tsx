'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON>, Legend } from 'recharts';
import { useMemo, useState } from 'react';

interface CategoryData {
  name: string;
  value: number;
  color: string;
}

interface CategoryBreakdownChartProps {
  familyId: string;
  className?: string;
}

const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
];

const CATEGORY_COLORS = {
  Sprzątanie: COLORS[0],
  Nauka: COLORS[1],
  Pomoc: COLORS[2],
  Inne: COLORS[3],
  Sport: COLORS[4],
};

export function CategoryBreakdownChart({ familyId, className }: CategoryBreakdownChartProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  // Mock data - w rzeczywistej aplikacji pobierałoby z API
  const data = useMemo(() => {
    const categories = [
      { name: 'Sprzątanie', value: 45, color: CATEGORY_COLORS['Sprzątanie'] },
      { name: 'Nauka', value: 30, color: CATEGORY_COLORS['Nauka'] },
      { name: 'Pomoc', value: 15, color: CATEGORY_COLORS['Pomoc'] },
      { name: 'Sport', value: 7, color: CATEGORY_COLORS['Sport'] },
      { name: 'Inne', value: 3, color: CATEGORY_COLORS['Inne'] },
    ];

    return categories.filter(cat => cat.value > 0);
  }, []);

  const totalTasks = data.reduce((sum, item) => sum + item.value, 0);

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const percentage = ((data.value / totalTasks) * 100).toFixed(1);
      return (
        <div className="bg-white p-3 border rounded-lg shadow-lg">
          <p className="font-medium text-sm">{data.name}</p>
          <p className="text-xs text-muted-foreground">
            {data.value} zadań ({percentage}%)
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomLegend = (props: any) => {
    const { payload } = props;
    return (
      <div className="flex flex-wrap gap-4 justify-center mt-4">
        {payload.map((entry: any, index: number) => (
          <div
            key={index}
            className="flex items-center gap-2 text-sm cursor-pointer hover:opacity-80"
            onClick={() => setActiveIndex(activeIndex === index ? null : index)}
          >
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
            <span className={activeIndex === index ? 'font-medium' : ''}>{entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const onPieLeave = () => {
    setActiveIndex(null);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg">Podział zadań według kategorii</CardTitle>
        <CardDescription>Rozkład {totalTasks} zadań w ostatnim miesiącu</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                dataKey="value"
                onMouseEnter={onPieEnter}
                onMouseLeave={onPieLeave}
              >
                {data.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.color}
                    stroke={activeIndex === index ? 'hsl(var(--border))' : 'none'}
                    strokeWidth={activeIndex === index ? 2 : 0}
                    style={{
                      filter:
                        activeIndex !== null && activeIndex !== index ? 'opacity(0.6)' : 'none',
                    }}
                  />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={<CustomLegend />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Statystyki szczegółowe */}
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">{totalTasks}</div>
            <div className="text-sm text-muted-foreground">Całkowita liczba zadań</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{data.length}</div>
            <div className="text-sm text-muted-foreground">Aktywne kategorie</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
