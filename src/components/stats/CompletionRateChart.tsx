'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { StatsPeriod } from '@prisma/client';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import { TrendingUp, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CompletionRateChartProps {
  familyId: string;
  period: StatsPeriod;
}

interface TrendData {
  data: Array<{
    date: Date;
    value: number;
    period: StatsPeriod;
  }>;
  trend: {
    direction: 'up' | 'down' | 'stable';
    percentage: number;
    isPositive: boolean;
  };
  summary: {
    total: number;
    average: number;
    peak: number;
    lowest: number;
  };
}

export default function CompletionRateChart({ familyId, period }: CompletionRateChartProps) {
  const [trendData, setTrendData] = useState<TrendData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');

  useEffect(() => {
    const fetchTrendData = async () => {
      try {
        setLoading(true);
        setError(null);

        const days = period === 'DAILY' ? 30 : period === 'WEEKLY' ? 12 * 7 : 12 * 30; // 30 days, 12 weeks, or 12 months
        const response = await fetch(
          `/api/families/${familyId}/stats/trends?metric=completion_rate&period=${period}&days=${days}`
        );

        if (!response.ok) {
          throw new Error('Failed to fetch trend data');
        }

        const data = await response.json();
        setTrendData(data);
      } catch (err) {
        console.error('Error fetching trend data:', err);
        setError('Nie udało się załadować danych wykresu');
      } finally {
        setLoading(false);
      }
    };

    fetchTrendData();
  }, [familyId, period]);

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);

    switch (period) {
      case 'DAILY':
        return date.toLocaleDateString('pl-PL', {
          month: 'short',
          day: 'numeric',
        });
      case 'WEEKLY':
        return `Tydz. ${date.toLocaleDateString('pl-PL', {
          month: 'short',
          day: 'numeric',
        })}`;
      case 'MONTHLY':
        return date.toLocaleDateString('pl-PL', {
          month: 'short',
          year: '2-digit',
        });
      default:
        return date.toLocaleDateString('pl-PL');
    }
  };

  const chartData =
    trendData?.data.map(item => ({
      date: item.date.toString(),
      formattedDate: formatDate(item.date.toString()),
      completionRate: Math.round(item.value),
      value: item.value,
    })) || [];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{formatDate(label)}</p>
          <p className="text-sm text-primary">
            Wskaźnik ukończenia: <span className="font-semibold">{payload[0].value}%</span>
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Wskaźnik ukończenia zadań</CardTitle>
          <CardDescription>Błąd ładowania danych</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64 text-muted-foreground">{error}</div>
        </CardContent>
      </Card>
    );
  }

  const getTrendColor = () => {
    if (!trendData?.trend) return 'text-muted-foreground';
    return trendData.trend.direction === 'up'
      ? 'text-green-600'
      : trendData.trend.direction === 'down'
        ? 'text-red-600'
        : 'text-muted-foreground';
  };

  const getTrendText = () => {
    if (!trendData?.trend) return 'Brak danych';
    const { direction, percentage } = trendData.trend;

    switch (direction) {
      case 'up':
        return `+${percentage}% wzrost`;
      case 'down':
        return `-${percentage}% spadek`;
      default:
        return 'Stabilny';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Wskaźnik ukończenia zadań
            </CardTitle>
            <CardDescription>
              Trend ukończonych zadań w czasie ({period.toLowerCase()})
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setChartType(chartType === 'line' ? 'bar' : 'line')}
          >
            <BarChart3 className="h-4 w-4 mr-2" />
            {chartType === 'line' ? 'Słupki' : 'Linia'}
          </Button>
        </div>

        {trendData?.trend && (
          <div className="flex items-center gap-4 text-sm">
            <div className={getTrendColor()}>
              <span className="font-medium">{getTrendText()}</span>
            </div>
            <div className="text-muted-foreground">
              Średnia: {trendData.summary.average?.toFixed(1) || '0'}%
            </div>
            <div className="text-muted-foreground">
              Najwyższy: {trendData.summary.peak?.toFixed(1) || '0'}%
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        {chartData.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            Brak danych do wyświetlenia
          </div>
        ) : (
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'line' ? (
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis dataKey="formattedDate" className="text-xs" tick={{ fontSize: 12 }} />
                  <YAxis
                    domain={[0, 100]}
                    className="text-xs"
                    tick={{ fontSize: 12 }}
                    label={{
                      value: 'Procent (%)',
                      angle: -90,
                      position: 'insideLeft',
                      style: { textAnchor: 'middle', fontSize: 12 },
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="completionRate"
                    stroke="hsl(var(--primary))"
                    strokeWidth={2}
                    dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: 'hsl(var(--primary))', strokeWidth: 2 }}
                  />
                </LineChart>
              ) : (
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis dataKey="formattedDate" className="text-xs" tick={{ fontSize: 12 }} />
                  <YAxis
                    domain={[0, 100]}
                    className="text-xs"
                    tick={{ fontSize: 12 }}
                    label={{
                      value: 'Procent (%)',
                      angle: -90,
                      position: 'insideLeft',
                      style: { textAnchor: 'middle', fontSize: 12 },
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="completionRate" fill="hsl(var(--primary))" radius={[2, 2, 0, 0]} />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
