'use client';

import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Trash2, Edit, Check, X, MoreHorizontal } from 'lucide-react';

interface SwipeAction {
  key: string;
  label: string;
  icon: any;
  color: 'red' | 'green' | 'blue' | 'gray';
  onClick: () => void;
}

interface SwipeableCardProps {
  children: React.ReactNode;
  actions?: SwipeAction[];
  className?: string;
  disabled?: boolean;
  onSwipeStart?: () => void;
  onSwipeEnd?: () => void;
}

export function SwipeableCard({
  children,
  actions = [],
  className,
  disabled = false,
  onSwipeStart,
  onSwipeEnd,
}: SwipeableCardProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [startX, setStartX] = useState(0);
  const [currentX, setCurrentX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const swipeThreshold = 60; // Minimum swipe distance to trigger action
  const maxSwipeDistance = 200; // Maximum swipe distance

  const handleTouchStart = (e: React.TouchEvent) => {
    if (disabled || actions.length === 0) return;

    setStartX(e.touches[0].clientX);
    setIsDragging(true);
    onSwipeStart?.();
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || disabled) return;

    const touchX = e.touches[0].clientX;
    const deltaX = startX - touchX;

    // Only allow swipe to the left (revealing actions on the right)
    if (deltaX > 0) {
      const clampedDelta = Math.min(deltaX, maxSwipeDistance);
      setCurrentX(clampedDelta);
    } else {
      setCurrentX(0);
    }
  };

  const handleTouchEnd = () => {
    if (!isDragging || disabled) return;

    setIsDragging(false);

    if (currentX > swipeThreshold) {
      setIsOpen(true);
      setCurrentX(maxSwipeDistance);
    } else {
      setIsOpen(false);
      setCurrentX(0);
    }

    onSwipeEnd?.();
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled || actions.length === 0) return;

    setStartX(e.clientX);
    setIsDragging(true);
    onSwipeStart?.();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || disabled) return;

    const deltaX = startX - e.clientX;

    if (deltaX > 0) {
      const clampedDelta = Math.min(deltaX, maxSwipeDistance);
      setCurrentX(clampedDelta);
    } else {
      setCurrentX(0);
    }
  };

  const handleMouseUp = () => {
    if (!isDragging || disabled) return;

    setIsDragging(false);

    if (currentX > swipeThreshold) {
      setIsOpen(true);
      setCurrentX(maxSwipeDistance);
    } else {
      setIsOpen(false);
      setCurrentX(0);
    }

    onSwipeEnd?.();
  };

  const closeSwipe = () => {
    setIsOpen(false);
    setCurrentX(0);
  };

  const getActionColor = (color: string) => {
    const colors = {
      red: 'bg-red-500 hover:bg-red-600 text-white',
      green: 'bg-green-500 hover:bg-green-600 text-white',
      blue: 'bg-blue-500 hover:bg-blue-600 text-white',
      gray: 'bg-gray-500 hover:bg-gray-600 text-white',
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
        closeSwipe();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen]);

  if (actions.length === 0) {
    return <div className={cn('relative', className)}>{children}</div>;
  }

  return (
    <div ref={cardRef} className={cn('relative overflow-hidden', className)}>
      {/* Main Card Content */}
      <div
        className={cn(
          'relative transition-transform duration-200 ease-out',
          isDragging && 'transition-none',
          'touch-pan-y' // Allow vertical scrolling
        )}
        style={{
          transform: `translateX(-${currentX}px)`,
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {children}
      </div>

      {/* Action Buttons */}
      <div
        className={cn(
          'absolute right-0 top-0 bottom-0 flex items-center',
          'transition-opacity duration-200',
          currentX > 0 ? 'opacity-100' : 'opacity-0'
        )}
        style={{
          width: `${maxSwipeDistance}px`,
          transform: `translateX(${maxSwipeDistance - currentX}px)`,
        }}
      >
        {actions.map((action, index) => (
          <Button
            key={action.key}
            variant="ghost"
            size="sm"
            className={cn(
              'h-full rounded-none flex-1 flex flex-col items-center justify-center gap-1',
              'min-h-[60px] border-r border-white/20 last:border-r-0',
              getActionColor(action.color),
              'active:scale-95 transition-transform'
            )}
            onClick={() => {
              action.onClick();
              closeSwipe();
            }}
          >
            <action.icon className="h-5 w-5" />
            <span className="text-xs font-medium">{action.label}</span>
          </Button>
        ))}
      </div>

      {/* Overlay for desktop fallback */}
      {isOpen && (
        <div className="absolute inset-0 bg-black bg-opacity-10 flex items-center justify-end pr-4 md:hidden">
          <div className="flex gap-2">
            {actions.map(action => (
              <Button
                key={action.key}
                variant="ghost"
                size="sm"
                className={cn('h-10 w-10 rounded-full', getActionColor(action.color))}
                onClick={() => {
                  action.onClick();
                  closeSwipe();
                }}
              >
                <action.icon className="h-4 w-4" />
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Common action presets
export const swipeActions = {
  delete: (onDelete: () => void): SwipeAction => ({
    key: 'delete',
    label: 'Usuń',
    icon: Trash2,
    color: 'red',
    onClick: onDelete,
  }),

  edit: (onEdit: () => void): SwipeAction => ({
    key: 'edit',
    label: 'Edytuj',
    icon: Edit,
    color: 'blue',
    onClick: onEdit,
  }),

  complete: (onComplete: () => void): SwipeAction => ({
    key: 'complete',
    label: 'Ukończ',
    icon: Check,
    color: 'green',
    onClick: onComplete,
  }),

  cancel: (onCancel: () => void): SwipeAction => ({
    key: 'cancel',
    label: 'Anuluj',
    icon: X,
    color: 'gray',
    onClick: onCancel,
  }),

  more: (onMore: () => void): SwipeAction => ({
    key: 'more',
    label: 'Więcej',
    icon: MoreHorizontal,
    color: 'gray',
    onClick: onMore,
  }),
};
