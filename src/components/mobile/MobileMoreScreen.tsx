'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { getMobileExtendedNavigation } from '@/components/layout/Navigation';
import { X, ArrowLeft } from 'lucide-react';

interface MobileMoreScreenProps {
    familyId?: string;
    isOpen: boolean;
    onClose: () => void;
    className?: string;
}

/**
 * Mobile More Screen Component
 *
 * Full-screen overlay that displays extended navigation options
 * when user taps "More" in the bottom tab bar.
 *
 * Features:
 * - Full-screen modal overlay
 * - Grid layout for navigation items
 * - Large touch targets (min 48dp)
 * - Smooth animations
 * - iOS/Android native feel
 */
export function MobileMoreScreen({ familyId, isOpen, onClose, className }: MobileMoreScreenProps) {
    const router = useRouter();

    const extendedNavigation = getMobileExtendedNavigation(familyId);

    const handleNavigation = (href: string) => {
        onClose();
        router.push(href);
    };

    if (!isOpen || !familyId) return null;

    return (
        <>
            {/* Backdrop with smooth fade */}
            <div
                className={cn(
                    'fixed inset-0 bg-black z-50 md:hidden',
                    'transition-all duration-300 ease-out',
                    isOpen ? 'bg-opacity-50' : 'bg-opacity-0 pointer-events-none'
                )}
                onClick={onClose}
            />

            {/* Content with spring animation */}
            <div
                className={cn(
                    'fixed inset-0 z-50 md:hidden',
                    'bg-white',
                    'transform transition-all duration-300 ease-out',
                    isOpen ? 'translate-y-0 scale-100 opacity-100' : 'translate-y-full scale-95 opacity-0',
                    className
                )}
                style={{
                    transitionTimingFunction: isOpen
                        ? 'cubic-bezier(0.34, 1.56, 0.64, 1)' // Spring effect on open
                        : 'cubic-bezier(0.25, 0.46, 0.45, 0.94)', // Smooth on close
                }}
            >
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Więcej opcji</h2>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={onClose}
                        className="p-3 min-h-[44px] min-w-[44px]"
                    >
                        <X className="h-5 w-5" />
                    </Button>
                </div>

                {/* Navigation Grid with staggered animation */}
                <div className="flex-1 p-6">
                    <div className="grid grid-cols-2 gap-4">
                        {extendedNavigation.map((item, index) => (
                            <button
                                key={item.name}
                                onClick={() => handleNavigation(item.href)}
                                className={cn(
                                    'flex flex-col items-center justify-center',
                                    'p-6 min-h-[120px] rounded-xl',
                                    'bg-gray-50 hover:bg-gray-100 active:bg-gray-200',
                                    'border border-gray-200 hover:border-gray-300',
                                    'transition-all duration-200 ease-out',
                                    'touch-manipulation', // Optimizes for touch
                                    'focus:ring-2 focus:ring-purple-500 focus:ring-offset-2',
                                    'hover:scale-105 hover:shadow-md',
                                    'transform-gpu' // Better performance
                                )}
                                style={{
                                    animationDelay: isOpen ? `${index * 50}ms` : '0ms',
                                    animation: isOpen ? 'slideInUp 0.4s ease-out forwards' : 'none',
                                }}
                            >
                                <div className="mb-3 p-3 rounded-full bg-white shadow-sm">
                                    <item.icon className="h-6 w-6 text-purple-600" />
                                </div>
                                <span className="text-sm font-medium text-gray-900 text-center">{item.name}</span>
                                {item.description && (
                                    <span className="text-xs text-gray-500 text-center mt-1 leading-tight">
                                        {item.description}
                                    </span>
                                )}
                            </button>
                        ))}
                    </div>
                </div>

                {/* Safe area at bottom for iPhone home indicator */}
                <div className="h-safe-area-inset-bottom bg-white" />
            </div>
        </>
    );
}
