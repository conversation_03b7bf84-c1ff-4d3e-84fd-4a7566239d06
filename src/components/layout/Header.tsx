'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Family, User } from '@/types';
import { Button } from '@/components/ui/button';
import { UserProfile } from '@/components/auth/UserProfile';
import { Menu, X, Home, Star, Users } from 'lucide-react';

interface HeaderProps {
  currentFamily?: Family;
  user?: User;
  families?: any[];
  currentFamilyId?: string;
  className?: string;
}

export function Header({
  currentFamily,
  user,
  families = [],
  currentFamilyId,
  className,
}: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);

  // Simplified navigation for mobile menu only
  const landingNavigation = [
    { name: 'Strona główna', href: '/', icon: Home },
    { name: '<PERSON><PERSON><PERSON>', href: '/features', icon: Star },
    { name: 'O nas', href: '/about', icon: Users },
  ];

  return (
    <header
      className={`
                h-18
                sticky top-0 z-50 w-full 
                bg-white border-b border-gray-200 
                transition-colors
                ${className}
            `}
      //   style={{ height: '72px' }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-18">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="text-xl font-bold font-display">
                <span style={{ color: 'var(--family-purple)' }}>FT</span>
                <span className="text-gray-900 ml-1">FamilyTasks</span>
              </div>
            </Link>
          </div>

          {/* Spacer - navigation is now in sidebar */}
          <div className="flex-1" />

          {/* Right Side - User Controls */}
          <div className="flex items-center gap-4">
            {/* User Profile */}
            {user ? (
              <UserProfile showName className="hidden sm:flex" />
            ) : (
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/sign-in">Zaloguj się</Link>
                </Button>
                <Button size="sm" asChild>
                  <Link href="/sign-up">Rozpocznij za darmo</Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu Button */}
            <Button variant="ghost" size="sm" className="md:hidden" onClick={toggleMobileMenu}>
              {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <div className="px-4 py-6 space-y-4">
            {/* Navigation Links - only for landing page */}
            {!currentFamily && (
              <nav className="space-y-2">
                {landingNavigation.map(item => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="
                      flex items-center gap-3 px-3 py-2 
                      text-gray-700 hover:text-purple-600 
                      hover:bg-purple-50 rounded-md
                      font-medium transition-colors
                    "
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <item.icon className="h-5 w-5" />
                    {item.name}
                  </Link>
                ))}
              </nav>
            )}

            {/* For dashboard pages, navigation is in sidebar */}
            {currentFamily && (
              <div className="text-center py-8">
                <p className="text-gray-500 text-sm">Nawigacja dostępna w menu bocznym</p>
              </div>
            )}

            {/* User Profile Mobile */}
            {user && (
              <div className="pt-4 border-t border-gray-200">
                <UserProfile showName />
              </div>
            )}

            {/* Auth Buttons Mobile */}
            {!user && (
              <div className="pt-4 border-t border-gray-200 space-y-2">
                <Button variant="ghost" className="w-full" asChild>
                  <Link href="/sign-in">Zaloguj się</Link>
                </Button>
                <Button className="w-full" asChild>
                  <Link href="/sign-up">Rozpocznij za darmo</Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
}
