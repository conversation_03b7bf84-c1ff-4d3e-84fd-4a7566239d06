'use client';

import { useState } from 'react';
import { CompletionStatus } from '@prisma/client';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Clock,
  CheckCircle,
  XCircle,
  Search,
  Calendar,
  ArrowUpDown,
  SlidersHorizontal,
} from 'lucide-react';

interface VerificationFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  completionStatusFilter: CompletionStatus | 'all';
  onCompletionStatusChange: (status: CompletionStatus | 'all') => void;
  sortBy: 'completedAt' | 'points' | 'title' | 'memberName';
  onSortChange: (sort: 'completedAt' | 'points' | 'title' | 'memberName') => void;
  sortOrder: 'asc' | 'desc';
  onSortOrderChange: (order: 'asc' | 'desc') => void;
  dateFilter: 'today' | 'week' | 'month' | 'all';
  onDateFilterChange: (filter: 'today' | 'week' | 'month' | 'all') => void;
  stats: {
    pending: number;
    approved: number;
    rejected: number;
    total: number;
  };
}

export function VerificationFilters({
  searchTerm,
  onSearchChange,
  completionStatusFilter,
  onCompletionStatusChange,
  sortBy,
  onSortChange,
  sortOrder,
  onSortOrderChange,
  dateFilter,
  onDateFilterChange,
  stats,
}: VerificationFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = (status: CompletionStatus | 'all') => {
    switch (status) {
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: CompletionStatus | 'all') => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-300';
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header with stats */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <SlidersHorizontal className="h-5 w-5 text-gray-600" />
              <h3 className="font-medium text-gray-900">Filtry weryfikacji</h3>
              <Button variant="ghost" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
                {isExpanded ? 'Zwiń' : 'Rozwiń'}
              </Button>
            </div>

            {/* Stats badges */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-300">
                <Clock className="h-3 w-3 mr-1" />
                {stats.pending} oczekujące
              </Badge>
              <Badge variant="outline" className="bg-green-50 text-green-800 border-green-300">
                <CheckCircle className="h-3 w-3 mr-1" />
                {stats.approved} zatwierdzone
              </Badge>
              <Badge variant="outline" className="bg-red-50 text-red-800 border-red-300">
                <XCircle className="h-3 w-3 mr-1" />
                {stats.rejected} odrzucone
              </Badge>
            </div>
          </div>

          {/* Quick status filters */}
          <div className="flex flex-wrap gap-2">
            {[
              { value: 'all', label: 'Wszystkie', count: stats.total },
              { value: 'PENDING', label: 'Oczekujące', count: stats.pending },
              { value: 'APPROVED', label: 'Zatwierdzone', count: stats.approved },
              { value: 'REJECTED', label: 'Odrzucone', count: stats.rejected },
            ].map(status => (
              <Button
                key={status.value}
                variant={completionStatusFilter === status.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => onCompletionStatusChange(status.value as CompletionStatus | 'all')}
                className={
                  completionStatusFilter === status.value
                    ? getStatusColor(status.value as CompletionStatus | 'all')
                    : ''
                }
              >
                {getStatusIcon(status.value as CompletionStatus | 'all')}
                <span className="ml-1">{status.label}</span>
                <Badge variant="secondary" className="ml-2">
                  {status.count}
                </Badge>
              </Button>
            ))}
          </div>

          {/* Expanded filters */}
          {isExpanded && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
              {/* Search */}
              <div className="space-y-2">
                <Label htmlFor="search">Szukaj</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Nazwa zadania, członek..."
                    value={searchTerm}
                    onChange={e => onSearchChange(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Date filter */}
              <div className="space-y-2">
                <Label htmlFor="date-filter">Okres</Label>
                <Select value={dateFilter} onValueChange={onDateFilterChange}>
                  <SelectTrigger>
                    <Calendar className="h-4 w-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="today">Dzisiaj</SelectItem>
                    <SelectItem value="week">Ten tydzień</SelectItem>
                    <SelectItem value="month">Ten miesiąc</SelectItem>
                    <SelectItem value="all">Wszystkie</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort by */}
              <div className="space-y-2">
                <Label htmlFor="sort-by">Sortuj według</Label>
                <Select value={sortBy} onValueChange={onSortChange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="completedAt">Data ukończenia</SelectItem>
                    <SelectItem value="points">Liczba punktów</SelectItem>
                    <SelectItem value="title">Nazwa zadania</SelectItem>
                    <SelectItem value="memberName">Członek</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort order */}
              <div className="space-y-2">
                <Label htmlFor="sort-order">Kolejność</Label>
                <Button
                  variant="outline"
                  onClick={() => onSortOrderChange(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="w-full justify-start"
                >
                  <ArrowUpDown className="h-4 w-4 mr-2" />
                  {sortOrder === 'asc' ? 'Rosnąco' : 'Malejąco'}
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
