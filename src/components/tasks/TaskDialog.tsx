'use client';

import { useState } from 'react';
import { TaskWeight, TaskFrequency, FamilyMember, User, Task } from '@prisma/client';
import { TaskWithRelations } from '@/hooks/useTasks';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import TaskForm from './TaskForm';

interface TaskDialogProps {
  familyId: string;
  familyMembers: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  mode?: 'create' | 'edit';
  task?: TaskWithRelations;
  trigger?: React.ReactNode | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onTaskSuccess?: () => void;
  initialDate?: Date;
  // Dla wystąpień zadań cyklicznych - ID oryginalnego zadania
  originalTaskId?: string;
  // Flaga określająca czy edytujemy wystąpienie zadania cyklicznego
  isEditingOccurrence?: boolean;
}

export function TaskDialog({
  familyId,
  familyMembers,
  mode = 'create',
  task,
  trigger,
  open: controlledOpen,
  onOpenChange,
  onTaskSuccess,
  initialDate,
  originalTaskId,
  isEditingOccurrence,
}: TaskDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false);

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const setOpen = isControlled ? onOpenChange || (() => {}) : setInternalOpen;

  const title = mode === 'create' ? 'Utwórz nowe zadanie' : 'Edytuj zadanie';
  const description =
    mode === 'create'
      ? 'Dodaj nowe zadanie dla członków rodziny. Wypełnij wszystkie wymagane pola.'
      : 'Edytuj istniejące zadanie. Możesz zmienić wszystkie właściwości zadania.';

  const handleSuccess = () => {
    setOpen(false);
    onTaskSuccess?.();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <TaskForm
          familyId={familyId}
          familyMembers={familyMembers}
          mode={mode}
          task={task}
          initialDate={initialDate}
          isMobile={false}
          onClose={() => setOpen(false)}
          onSuccess={handleSuccess}
          originalTaskId={originalTaskId}
          isEditingOccurrence={isEditingOccurrence}
        />
      </DialogContent>
    </Dialog>
  );
}
