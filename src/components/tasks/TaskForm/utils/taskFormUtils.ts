import { TaskWeight, TaskFrequency, FamilyMember, User } from '@prisma/client';
import { TASK_WEIGHT_POINTS } from '@/lib/validations/task';
import { TaskFormData } from './hooks/useTaskFormState';

export const getWeightLabel = (weight: TaskWeight) => {
  const labels: Record<TaskWeight, string> = {
    [TaskWeight.LIGHT]: 'Lek<PERSON>',
    [TaskWeight.NORMAL]: 'Normalne',
    [TaskWeight.HEAVY]: 'Ciężkie',
    [TaskWeight.CRITICAL]: 'Krytyczne',
  };
  return labels[weight] || 'Normalne';
};

export const getWeightColor = (weight: TaskWeight) => {
  const colors: Record<TaskWeight, string> = {
    [TaskWeight.LIGHT]: 'bg-green-100 text-green-800',
    [TaskWeight.NORMAL]: 'bg-blue-100 text-blue-800',
    [TaskWeight.HEAVY]: 'bg-orange-100 text-orange-800',
    [TaskWeight.CRITICAL]: 'bg-red-100 text-red-800',
  };
  return colors[weight] || 'bg-blue-100 text-blue-800';
};

export const getFrequencyLabel = (frequency: TaskFrequency) => {
  const labels: Record<TaskFrequency, string> = {
    [TaskFrequency.ONCE]: 'Jednorazowe',
    [TaskFrequency.DAILY]: 'Codziennie',
    [TaskFrequency.WEEKLY]: 'Tygodniowo',
    [TaskFrequency.MONTHLY]: 'Miesięcznie',
    [TaskFrequency.YEARLY]: 'Rocznie',
    [TaskFrequency.CUSTOM]: 'Niestandardowe',
  };
  return labels[frequency] || 'Jednorazowe';
};

export const getMemberDisplayName = (
  member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
) => {
  if (member?.user?.firstName && member?.user?.lastName) {
    return `${member.user.firstName} ${member.user.lastName}`;
  }
  if (member?.user?.email) {
    return member.user.email.split('@')[0];
  }
  return 'Nieznany użytkownik';
};

export const getMemberInitials = (
  member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
) => {
  if (member?.user?.firstName && member?.user?.lastName) {
    return `${member.user.firstName[0]}${member.user.lastName[0]}`.toUpperCase();
  }
  if (member?.user?.email) {
    return member.user.email[0].toUpperCase();
  }
  return 'U';
};

export const calculateMaxRepetitions = (formData: TaskFormData) => {
  if (!formData.dueDate || !formData.endDate || !formData.rruleFreq) {
    return 999; // Default max
  }

  const startDate = new Date(formData.dueDate);
  const endDate = new Date(formData.endDate);
  const diffTime = endDate.getTime() - startDate.getTime();

  const calculations: Record<string, number> = {
    DAILY: Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1,
    WEEKLY: Math.floor(diffTime / (1000 * 60 * 60 * 24 * 7)) + 1,
    MONTHLY: Math.floor(diffTime / (1000 * 60 * 60 * 24 * 30)) + 1,
    YEARLY: Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365)) + 1,
    HOURLY: Math.floor(diffTime / (1000 * 60 * 60)) + 1,
    MINUTELY: Math.floor(diffTime / (1000 * 60)) + 1,
  };

  return calculations[formData.rruleFreq] || 999;
};

export const buildRRule = (formData: TaskFormData) => {
  // Handle simple recurring tasks (DAILY, WEEKLY, MONTHLY with intervals)
  if (
    formData.frequency === TaskFrequency.DAILY ||
    formData.frequency === TaskFrequency.WEEKLY ||
    formData.frequency === TaskFrequency.MONTHLY
  ) {
    let rule = `FREQ=${formData.frequency}`;

    // Add interval if greater than 1
    if (formData.rruleInterval && formData.rruleInterval > 1) {
      rule += `;INTERVAL=${formData.rruleInterval}`;
    }

    // Add end date if specified
    if (formData.endDate) {
      const untilDate = new Date(formData.endDate);
      untilDate.setHours(23, 59, 59); // End of day
      rule += `;UNTIL=${untilDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`;
    }

    return rule;
  }

  // Handle YEARLY (simple case)
  if (formData.frequency === TaskFrequency.YEARLY) {
    let rule = 'FREQ=YEARLY';

    if (formData.endDate) {
      const untilDate = new Date(formData.endDate);
      untilDate.setHours(23, 59, 59);
      rule += `;UNTIL=${untilDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`;
    }

    return rule;
  }

  // Handle CUSTOM frequency with advanced rules
  if (formData.frequency === TaskFrequency.CUSTOM) {
    if (formData.rruleMode === 'advanced') {
      return formData.recurrenceRule;
    }

    if (formData.rruleMode === 'simple' && formData.rruleFreq) {
      let rule = `FREQ=${formData.rruleFreq}`;

      if (formData.rruleInterval > 1) {
        rule += `;INTERVAL=${formData.rruleInterval}`;
      }

      if (formData.endDate) {
        const untilDate = new Date(formData.endDate);
        untilDate.setHours(23, 59, 59); // End of day
        rule += `;UNTIL=${untilDate.toISOString().replace(/[-:]/g, '').split('.')[0]}Z`;
      } else if (formData.rruleCount > 0) {
        const maxReps = calculateMaxRepetitions(formData);
        const count = Math.min(formData.rruleCount, maxReps);
        rule += `;COUNT=${count}`;
      }

      return rule;
    }
  }

  // ONCE or no recurrence
  return undefined;
};
