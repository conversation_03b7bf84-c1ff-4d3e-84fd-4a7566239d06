import { useReducer, useEffect } from 'react';
import { TaskWeight, TaskFrequency, TaskAssignmentType } from '@prisma/client';
import { TaskWithRelations } from '@/hooks/useTasks';

// Funkcja pomocnicza do formatowania czasu lokalnego
function getLocalTime(date: Date): string {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
}

// Helper function to parse RRULE into simple components
function parseRRuleToComponents(rrule: string): {
  rruleFreq: 'MINUTELY' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | '';
  rruleInterval: number;
  rruleCount: number;
} {
  const defaultValues = {
    rruleFreq: '' as const,
    rruleInterval: 1,
    rruleCount: 1,
  };

  if (!rrule) return defaultValues;

  try {
    const parts = rrule.split(';');
    const ruleMap: Record<string, string> = {};

    parts.forEach(part => {
      const [key, value] = part.split('=');
      if (key && value) {
        ruleMap[key] = value;
      }
    });

    return {
      rruleFreq: (ruleMap['FREQ'] as any) || '',
      rruleInterval: parseInt(ruleMap['INTERVAL'] || '1'),
      rruleCount: parseInt(ruleMap['COUNT'] || '1'),
    };
  } catch {
    return defaultValues;
  }
}

export interface TaskFormData {
  title: string;
  description: string;
  points: number;
  weight: TaskWeight;
  frequency: TaskFrequency;
  // Nowe semantyczne pola czasowe
  startDateTime: string; // Data/czas rozpoczęcia zadania
  endDateTime: string; // Data/czas zakończenia wystąpienia
  cycleEndDate: string; // Data końca cyklu (tylko data)
  startTime: string; // Czas rozpoczęcia
  endTime: string; // Czas zakończenia
  assignedMemberIds: string[];
  assignmentType: TaskAssignmentType;
  allDay: boolean;
  color: string;
  recurrenceRule: string;
  rruleFreq: 'MINUTELY' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY' | '';
  rruleInterval: number;
  rruleCount: number;
  rruleMode: 'simple' | 'advanced';
  // Przestarzałe pola - zachowane dla kompatybilności
  dueDate: string; // @deprecated Użyj startDateTime
  dueTime: string; // @deprecated Użyj startTime
  endDate: string; // @deprecated Użyj endDateTime lub cycleEndDate
}

type Action =
  | { type: 'SET_FIELD'; field: keyof TaskFormData; value: any }
  | { type: 'SET_FORM_DATA'; data: Partial<TaskFormData> }
  | { type: 'RESET_FORM'; initialDate?: Date };

const initialState: TaskFormData = {
  title: '',
  description: '',
  points: 3,
  weight: TaskWeight.NORMAL,
  frequency: TaskFrequency.ONCE,
  // Nowe semantyczne pola czasowe
  startDateTime: new Date().toLocaleDateString('sv-SE'),
  endDateTime: '',
  cycleEndDate: '',
  startTime: '',
  endTime: '',
  assignedMemberIds: [],
  assignmentType: TaskAssignmentType.ANY,
  allDay: true,
  color: '#3b82f6',
  recurrenceRule: '',
  rruleFreq: '',
  rruleInterval: 1,
  rruleCount: 1,
  rruleMode: 'simple',
  // Przestarzałe pola - zachowane dla kompatybilności
  dueDate: new Date().toLocaleDateString('sv-SE'),
  dueTime: '',
  endDate: '',
};

function formReducer(state: TaskFormData, action: Action): TaskFormData {
  switch (action.type) {
    case 'SET_FIELD':
      // Walidacja dla pola points
      if (action.field === 'points') {
        const numValue = typeof action.value === 'number' ? action.value : parseInt(action.value);
        const validatedValue = !isNaN(numValue) && numValue >= 1 && numValue <= 10 ? numValue : 3;
        return { ...state, [action.field]: validatedValue };
      }

      // Walidacja dla pola weight
      if (action.field === 'weight') {
        const validatedValue = Object.values(TaskWeight).includes(action.value)
          ? action.value
          : TaskWeight.NORMAL;
        console.log('SET_FIELD weight validation:', {
          original: action.value,
          validated: validatedValue,
        });
        return { ...state, [action.field]: validatedValue };
      }
      return { ...state, [action.field]: action.value };
    case 'SET_FORM_DATA':
      return { ...state, ...action.data };
    case 'RESET_FORM':
      const dateStr = action.initialDate
        ? action.initialDate.toLocaleDateString('sv-SE')
        : new Date().toLocaleDateString('sv-SE');
      return {
        ...initialState,
        // Nowe semantyczne pole
        startDateTime: dateStr,
        // Kompatybilność wsteczna
        dueDate: dateStr,
      };
    default:
      return state;
  }
}

export function useTaskFormState(
  mode: 'create' | 'edit',
  task?: TaskWithRelations,
  initialDate?: Date
) {
  const [formData, dispatch] = useReducer(formReducer, {
    ...initialState,
    // Używamy 'sv-SE' dla lokalnej daty w formacie YYYY-MM-DD
    dueDate: initialDate
      ? initialDate.toLocaleDateString('sv-SE')
      : new Date().toLocaleDateString('sv-SE'),
  });

  useEffect(() => {
    if (mode === 'edit' && task) {
      console.log(
        '🔄 useTaskFormState: Ładowanie zadania ID:',
        task.id,
        'frequency:',
        task.frequency
      );

      // DEBUG: Sprawdź dane weight z bazy
      console.log('useTaskFormState Weight DEBUG - Edit mode:', {
        taskWeight: task.weight,
        taskWeightType: typeof task.weight,
        availableWeights: Object.values(TaskWeight),
      });

      // DEBUG: Dodatkowe logowanie dla zadań cyklicznych
      if (task.frequency !== TaskFrequency.ONCE) {
        console.log('🔄 useTaskFormState - Ładowanie zadania cyklicznego:', {
          taskId: task.id,
          title: task.title,
          frequency: task.frequency,
          recurrenceRule: task.recurrenceRule,
          hasRecurrenceRule: !!task.recurrenceRule,
          ruleLength: task.recurrenceRule?.length || 0,
        });
      }

      const parsedRRule = parseRRuleToComponents(task.recurrenceRule || '');

      // Konwersja semantyczna: nowe pola mają priorytet nad starymi
      const getStartDate = () => {
        if ((task as any).startDateTime) return new Date((task as any).startDateTime);
        if (task.dueDate) return new Date(task.dueDate);
        if ((task as any).startDate) return new Date((task as any).startDate);
        return null;
      };

      const getEndDate = () => {
        if ((task as any).endDateTime) return new Date((task as any).endDateTime);
        if (task.frequency === 'ONCE' && task.endDate) return new Date(task.endDate);
        return null;
      };

      const getCycleEndDate = () => {
        if ((task as any).cycleEndDate) return new Date((task as any).cycleEndDate);
        if (task.frequency !== 'ONCE' && (task as any).recurrenceEnd)
          return new Date((task as any).recurrenceEnd);
        if (task.frequency !== 'ONCE' && task.endDate) return new Date(task.endDate);
        return null;
      };

      const startDate = getStartDate();
      const endDate = getEndDate();
      const cycleEndDate = getCycleEndDate();

      const formDataToSet = {
        title: task.title || '',
        description: task.description || '',
        points: typeof task.points === 'number' && !isNaN(task.points) ? task.points : 3,
        weight: Object.values(TaskWeight).includes(task.weight) ? task.weight : TaskWeight.NORMAL,
        frequency: task.frequency || TaskFrequency.ONCE,
        // Nowe semantyczne pola
        startDateTime: startDate ? startDate.toLocaleDateString('sv-SE') : '',
        endDateTime: endDate ? endDate.toLocaleDateString('sv-SE') : '',
        cycleEndDate: cycleEndDate ? cycleEndDate.toLocaleDateString('sv-SE') : '',
        startTime: startDate ? getLocalTime(startDate) : '',
        endTime: endDate ? getLocalTime(endDate) : '',
        assignedMemberIds: task.assignments?.map(a => a.member.id) || [],
        assignmentType: task.assignments?.[0]?.assignmentType || TaskAssignmentType.ANY,
        allDay: task.allDay ?? true,
        color: task.color || '#3b82f6',
        recurrenceRule: task.recurrenceRule || '',
        rruleMode: 'simple' as const,
        rruleFreq: parsedRRule.rruleFreq,
        rruleInterval: parsedRRule.rruleInterval,
        rruleCount: parsedRRule.rruleCount,
        // Kompatybilność wsteczna
        dueDate: startDate ? startDate.toLocaleDateString('sv-SE') : '',
        dueTime: startDate ? getLocalTime(startDate) : '',
        endDate:
          endDate || cycleEndDate ? (endDate || cycleEndDate)!.toLocaleDateString('sv-SE') : '',
      };

      // DEBUG: Logowanie właściwości cyklicznych w danych formularza
      if (task.frequency !== TaskFrequency.ONCE) {
        console.log('✅ useTaskFormState - Ustawione właściwości cykliczne:', {
          frequency: formDataToSet.frequency,
          recurrenceRule: formDataToSet.recurrenceRule,
          rruleFreq: formDataToSet.rruleFreq,
          rruleInterval: formDataToSet.rruleInterval,
        });
      }

      dispatch({
        type: 'SET_FORM_DATA',
        data: formDataToSet,
      });
    }
  }, [mode, task]);

  return { formData, dispatch };
}
