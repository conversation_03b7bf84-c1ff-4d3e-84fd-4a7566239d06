'use client';

import React from 'react';
import { FamilyMember, User, TaskFrequency } from '@prisma/client';
import { useTaskMutations, TaskWithRelations } from '@/hooks/useTasks';
import { useTaskFormState, TaskFormData } from './hooks/useTaskFormState';
import { buildRRule } from './utils/taskFormUtils';
import { BasicInfoSection } from './components/BasicInfoSection';
import { PropertiesSection } from './components/PropertiesSection';
import { DateTimeSection } from './components/DateTimeSection';
import { RecurrenceSection } from './components/RecurrenceSection';
import { AssignmentSection } from './components/AssignmentSection';
import { TaskPreview } from './components/TaskPreview';
import { FormActions } from './components/FormActions';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

interface TaskFormProps {
  familyId: string;
  familyMembers: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  mode?: 'create' | 'edit';
  task?: TaskWithRelations;
  initialDate?: Date;
  isMobile?: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  // Dla wystąpień zadań cyklicznych - ID oryginalnego zadania
  originalTaskId?: string;
  // Flaga określająca czy edytujemy wystąpienie zadania cyklicznego
  isEditingOccurrence?: boolean;
}

function TaskForm({
  familyId,
  familyMembers,
  mode = 'create',
  task,
  initialDate,
  isMobile,
  onClose,
  onSuccess,
  originalTaskId,
  isEditingOccurrence,
}: TaskFormProps) {
  const { formData, dispatch } = useTaskFormState(mode, task, initialDate);
  const { createTask, updateTask, loading, error } = useTaskMutations(familyId);
  const { toast } = useToast();

  const handleFieldChange = (field: keyof TaskFormData, value: any) => {
    dispatch({ type: 'SET_FIELD', field, value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.allDay) {
      if (!formData.startTime) {
        toast({
          title: 'Błąd walidacji',
          description: 'Godzina rozpoczęcia jest wymagana dla wydarzeń o określonym czasie.',
          variant: 'destructive',
        });
        return;
      }
      // Dla zadań z określonym czasem trwania
      if (formData.endDateTime && formData.endTime) {
        const isSameDay = formData.startDateTime === formData.endDateTime;
        if (isSameDay && formData.endTime <= formData.startTime) {
          toast({
            title: 'Błąd walidacji',
            description: 'Godzina zakończenia musi być późniejsza niż godzina rozpoczęcia.',
            variant: 'destructive',
          });
          return;
        }
      }
    }

    try {
      // Tworzenie semantycznych dat
      let startDateTime: Date | undefined;
      if (formData.startDateTime) {
        const dateTime = formData.allDay
          ? `${formData.startDateTime}T00:00`
          : `${formData.startDateTime}T${formData.startTime || '09:00'}`;
        startDateTime = new Date(dateTime);
      }

      let endDateTime: Date | undefined;
      if (formData.endDateTime) {
        const dateTime = formData.allDay
          ? `${formData.endDateTime}T23:59`
          : `${formData.endDateTime}T${formData.endTime || '17:00'}`;
        endDateTime = new Date(dateTime);
      } else if (!formData.allDay && formData.startDateTime && formData.endTime) {
        // Jeśli nie ma osobnej daty końca, ale jest czas końca - to samo dzień
        const dateTime = `${formData.startDateTime}T${formData.endTime}`;
        endDateTime = new Date(dateTime);
      }

      let cycleEndDate: Date | undefined;
      if (formData.cycleEndDate) {
        // Data końca cyklu - zawsze koniec dnia
        cycleEndDate = new Date(`${formData.cycleEndDate}T23:59`);
      }

      // Kompatybilność wsteczna - mapowanie na stare pola
      const dueDate = startDateTime;
      const endDate = endDateTime || cycleEndDate;

      const recurrenceRule = buildRRule(formData);

      const taskData = {
        title: formData.title,
        description: formData.description || undefined,
        points: formData.points,
        weight: formData.weight,
        frequency: formData.frequency,
        // Nowe semantyczne pola czasowe
        startDateTime,
        endDateTime,
        cycleEndDate,
        assignedMemberIds:
          formData.assignedMemberIds.length > 0 ? formData.assignedMemberIds : undefined,
        assignmentType: formData.assignedMemberIds.length > 1 ? formData.assignmentType : undefined,
        allDay: formData.allDay,
        color: formData.color || undefined,
        recurrenceRule: recurrenceRule || undefined,
        // Kompatybilność wsteczna
        dueDate,
        endDate,
        startDate: startDateTime,
      };

      // Walidacja points przed wysłaniem
      if (isNaN(taskData.points) || taskData.points < 1 || taskData.points > 100) {
        toast({
          title: 'Błąd walidacji',
          description: 'Nieprawidłowa wartość punktów. Musi być liczbą z zakresu 1-100.',
          variant: 'destructive',
        });
        return;
      }

      if (mode === 'create') {
        await createTask(taskData);
        dispatch({ type: 'RESET_FORM', initialDate });
      } else if (mode === 'edit' && task) {
        if (isEditingOccurrence) {
          // Edycja wystąpienia - utwórz nowe zadanie jednorazowe
          const occurrenceTaskData = {
            ...taskData,
            frequency: TaskFrequency.ONCE, // Wymuszamy jednorazowość
            recurrenceRule: undefined, // Usuwamy regułę powtarzania
          };

          console.log('📤 TaskForm - Tworzenie zadania dla wystąpienia:', {
            originalTaskId: task.originalTaskId || task.id,
            taskData: occurrenceTaskData,
          });

          await createTask(occurrenceTaskData);

          // TODO: Dodaj datę wyjątku do oryginalnego zadania cyklicznego
          // (wymaga rozszerzenia modelu Task o pole excludedDates)
        } else {
          // Zwykła edycja zadania
          const taskIdToUpdate = originalTaskId || task.id;

          // DEBUG: Logowanie danych przed aktualizacją zadania
          console.log('📤 TaskForm - Aktualizacja zadania:', {
            taskId: taskIdToUpdate,
            originalTaskId,
            isRecurring: task.frequency !== TaskFrequency.ONCE,
            taskData: {
              title: taskData.title,
              frequency: taskData.frequency,
              recurrenceRule: taskData.recurrenceRule,
              hasRecurrenceRule: !!taskData.recurrenceRule,
            },
          });

          await updateTask(taskIdToUpdate, taskData);
        }
      }

      onSuccess?.();
      onClose();
    } catch (err) {
      console.error(`Error ${mode === 'create' ? 'creating' : 'updating'} task:`, err);
    }
  };

  const formContent = (
    <div className="space-y-4 p-3">
      <BasicInfoSection formData={formData} onFieldChange={handleFieldChange} />
      <PropertiesSection formData={formData} onFieldChange={handleFieldChange} mode={mode} />
      <DateTimeSection formData={formData} onFieldChange={handleFieldChange} />
      {formData.frequency === TaskFrequency.CUSTOM && (
        <RecurrenceSection formData={formData} onFieldChange={handleFieldChange} />
      )}
      <AssignmentSection
        formData={formData}
        familyMembers={familyMembers}
        onFieldChange={handleFieldChange}
      />
      {error && (
        <Card className="p-3 bg-red-50 border-red-200">
          <p className="text-sm text-red-600">{error}</p>
        </Card>
      )}
      <TaskPreview formData={formData} familyMembers={familyMembers} />
      <div className="h-12" />
    </div>
  );

  if (isMobile) {
    return (
      <div className="fixed inset-0 bg-white z-50 overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <FormActions
            onClose={onClose}
            isMobile={isMobile}
            loading={loading}
            isTitleEmpty={!formData.title.trim()}
            mode={mode}
          />
          {formContent}
        </form>
      </div>
    );
  }

  return (
    <div className="w-full max-w-[600px] mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {formContent}
        <FormActions
          onClose={onClose}
          loading={loading}
          isTitleEmpty={!formData.title.trim()}
          mode={mode}
        />
      </form>
    </div>
  );
}

export { TaskForm };
export default TaskForm;
