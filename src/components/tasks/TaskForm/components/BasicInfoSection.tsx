import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { FormSection } from './FormSection';
import { TaskFormData } from '../hooks/useTaskFormState';

interface BasicInfoSectionProps {
  formData: TaskFormData;
  onFieldChange: (field: keyof TaskFormData, value: any) => void;
}

export function BasicInfoSection({ formData, onFieldChange }: BasicInfoSectionProps) {
  return (
    <FormSection title="Podstawowe informacje">
      <div>
        <Label htmlFor="title" className="text-sm">
          Tytuł zadania *
        </Label>
        <Input
          id="title"
          placeholder="np. Posprzątaj pokój"
          value={formData.title}
          onChange={e => onFieldChange('title', e.target.value)}
          className="text-sm"
          required
        />
      </div>

      <div>
        <Label htmlFor="description" className="text-sm">
          Opis (opcjonalny)
        </Label>
        <Textarea
          id="description"
          placeholder="Dodaj szczegóły zadania..."
          value={formData.description}
          onChange={e => onFieldChange('description', e.target.value)}
          rows={2}
          className="text-sm resize-none"
        />
      </div>
    </FormSection>
  );
}
