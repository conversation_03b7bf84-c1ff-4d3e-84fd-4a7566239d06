import React from 'react';
import { TaskFrequency } from '@prisma/client';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { FormSection } from './FormSection';
import { TaskFormData } from '../hooks/useTaskFormState';
import { calculateMaxRepetitions } from '../utils/taskFormUtils';

interface RecurrenceSectionProps {
  formData: TaskFormData;
  onFieldChange: (field: keyof TaskFormData, value: any) => void;
}

export function RecurrenceSection({ formData, onFieldChange }: RecurrenceSectionProps) {
  if (formData.frequency !== TaskFrequency.CUSTOM) {
    return null;
  }

  return (
    <FormSection title="Ustawienia powtarzania">
      <div>
        <Label>Tryb powtarzania</Label>
        <div className="flex gap-2 mt-2">
          <Button
            type="button"
            variant={formData.rruleMode === 'simple' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onFieldChange('rruleMode', 'simple')}
          >
            Prosty
          </Button>
          <Button
            type="button"
            variant={formData.rruleMode === 'advanced' ? 'default' : 'outline'}
            size="sm"
            onClick={() => onFieldChange('rruleMode', 'advanced')}
          >
            Zaawansowany
          </Button>
        </div>
      </div>

      {formData.rruleMode === 'simple' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label>Częstotliwość</Label>
            <Select
              value={formData.rruleFreq}
              onValueChange={value => onFieldChange('rruleFreq', value as any)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Wybierz" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MINUTELY">Minuty</SelectItem>
                <SelectItem value="HOURLY">Godziny</SelectItem>
                <SelectItem value="DAILY">Dni</SelectItem>
                <SelectItem value="WEEKLY">Tygodnie</SelectItem>
                <SelectItem value="MONTHLY">Miesiące</SelectItem>
                <SelectItem value="YEARLY">Lata</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label>Co ile</Label>
            <Input
              type="number"
              min="1"
              max="999"
              value={formData.rruleInterval}
              onChange={e => onFieldChange('rruleInterval', parseInt(e.target.value) || 1)}
            />
          </div>

          <div>
            <Label>Liczba powtórzeń</Label>
            <Input
              type="number"
              min="1"
              max={calculateMaxRepetitions(formData)}
              value={formData.rruleCount}
              onChange={e => onFieldChange('rruleCount', parseInt(e.target.value) || 1)}
              disabled={!!formData.endDate}
            />
            {formData.endDate && (
              <p className="text-xs text-gray-500 mt-1">
                Ograniczone datą zakończenia (maks. {calculateMaxRepetitions(formData)})
              </p>
            )}
          </div>
        </div>
      )}

      {formData.rruleMode === 'advanced' && (
        <div>
          <Label htmlFor="recurrenceRule">Reguła RRULE</Label>
          <Input
            id="recurrenceRule"
            placeholder="np. FREQ=WEEKLY;BYDAY=MO,WE,FR"
            value={formData.recurrenceRule}
            onChange={e => onFieldChange('recurrenceRule', e.target.value)}
          />
          <p className="text-xs text-gray-500 mt-1">
            Zaawansowane reguły powtarzania w formacie RFC 5545 (RRULE)
          </p>
        </div>
      )}
    </FormSection>
  );
}
