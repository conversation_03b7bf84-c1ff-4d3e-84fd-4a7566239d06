import React from 'react';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface TimeSelectProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  className?: string;
}

const generateHours = () => {
  return Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0');
    return { value: hour, label: hour };
  });
};

const generateMinutes = () => {
  return Array.from({ length: 12 }, (_, i) => {
    const minute = (i * 5).toString().padStart(2, '0');
    return { value: minute, label: minute };
  });
};

export function TimeSelect({ id, label, value, onChange, required, className }: TimeSelectProps) {
  const [hours, minutes] = value.split(':');
  const hourOptions = generateHours();
  const minuteOptions = generateMinutes();

  const handleHourChange = (newHour: string) => {
    const currentMinutes = minutes || '00';
    onChange(`${newHour}:${currentMinutes}`);
  };

  const handleMinuteChange = (newMinute: string) => {
    const currentHours = hours || '00';
    onChange(`${currentHours}:${newMinute}`);
  };

  return (
    <div>
      <Label htmlFor={id} className="text-sm">
        {label}
      </Label>
      <div className={`flex items-center gap-1 ${className}`}>
        <Select value={hours || '00'} onValueChange={handleHourChange}>
          <SelectTrigger className="w-20 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {hourOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <span className="text-sm font-mono">:</span>
        <Select value={minutes || '00'} onValueChange={handleMinuteChange}>
          <SelectTrigger className="w-20 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {minuteOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
