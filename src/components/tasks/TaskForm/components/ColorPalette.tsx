import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ColorPaletteProps {
  selectedColor: string;
  onColorSelect: (color: string) => void;
}

const PRESET_COLORS = [
  { value: '#3B82F6', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: '#EF4444', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: '#10B981', name: '<PERSON><PERSON><PERSON>' },
  { value: '#F59E0B', name: 'Pomarańczowy' },
  { value: '#8B5CF6', name: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: '#EC4899', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: '#6B7280', name: '<PERSON><PERSON><PERSON>' },
  { value: '#14B8A6', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: '#DC2626', name: 'Ciem<PERSON>czerwony' },
  { value: '#059669', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: '#D97706', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: '#7C3AED', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { value: '#BE185D', name: '<PERSON><PERSON><PERSON><PERSON>óżowy' },
  { value: '#475569', name: 'Ciemnoszary' },
  { value: '#0891B2', name: 'Cyjan' },
  { value: '#EA580C', name: 'Pomarańczowoczerwony' },
];

export function ColorPalette({ selectedColor, onColorSelect }: ColorPaletteProps) {
  return (
    <div className="grid grid-cols-8 gap-2">
      {PRESET_COLORS.map(color => (
        <Button
          key={color.value}
          type="button"
          variant="outline"
          size="sm"
          className={cn(
            'h-10 w-full border-2 transition-all duration-200',
            selectedColor === color.value
              ? 'border-primary scale-110 shadow-md'
              : 'border-muted hover:border-border hover:scale-105'
          )}
          style={{ backgroundColor: color.value }}
          onClick={() => onColorSelect(color.value)}
          title={color.name}
        >
          <span className="sr-only">{color.name}</span>
        </Button>
      ))}
    </div>
  );
}
