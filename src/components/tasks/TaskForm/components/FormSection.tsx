import React from 'react';
import { Card } from '@/components/ui/card';

interface FormSectionProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

export function FormSection({ title, children, className }: FormSectionProps) {
  return (
    <Card className={`p-2 ${className}`}>
      <h2 className="font-medium text-sm">{title}</h2>
      <div className="space-y-4">{children}</div>
    </Card>
  );
}
