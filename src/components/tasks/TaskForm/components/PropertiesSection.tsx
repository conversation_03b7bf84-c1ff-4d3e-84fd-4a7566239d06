import React from 'react';
import { TaskWeight, TaskFrequency } from '@prisma/client';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { FormSection } from './FormSection';
import { TaskFormData } from '../hooks/useTaskFormState';
import { getWeightLabel, getWeightColor, getFrequencyLabel } from '../utils/taskFormUtils';
import { TASK_WEIGHT_POINTS } from '@/lib/validations/task';

interface PropertiesSectionProps {
  formData: TaskFormData;
  onFieldChange: (field: keyof TaskFormData, value: any) => void;
  mode?: 'create' | 'edit';
}

const POINTS_OPTIONS = [
  { value: 1, label: '1 - <PERSON><PERSON><PERSON> łat<PERSON>', description: 'Zadania 1-2 minutowe' },
  { value: 2, label: '2 - <PERSON>at<PERSON>', description: '<PERSON>adania 5-10 minutowe' },
  { value: 3, label: '3 - Średnie', description: '<PERSON>adania 15-30 minutowe' },
  { value: 4, label: '4 - Średnie+', description: 'Zadania 30-45 minutowe' },
  { value: 5, label: '5 - Standardowe', description: 'Zadania 1 godzinne' },
  { value: 6, label: '6 - Wymagające', description: 'Zadania 1-2 godzinne' },
  { value: 7, label: '7 - Trudne', description: 'Zadania 2-3 godzinne' },
  { value: 8, label: '8 - Bardzo trudne', description: 'Zadania półdniowe' },
  { value: 9, label: '9 - Złożone', description: 'Zadania całodniowe' },
  { value: 10, label: '10 - Ekstremalne', description: 'Zadania wielodniowe' },
];

export function PropertiesSection({
  formData,
  onFieldChange,
  mode = 'create',
}: PropertiesSectionProps) {
  // Walidacja wartości points
  const currentPoints = formData.points || 3;
  const pointsValue = currentPoints.toString();

  // Sprawdź czy wartość jest dostępna w opcjach
  const isValidOption = POINTS_OPTIONS.some(option => option.value === currentPoints);

  // DEBUG: Sprawdź wartości weight w trybie edycji
  console.log('PropertiesSection Weight DEBUG:', {
    mode,
    weight: formData.weight,
    weightType: typeof formData.weight,
    availableWeights: Object.values(TaskWeight),
    isValidWeight: Object.values(TaskWeight).includes(formData.weight),
    stringified: JSON.stringify(formData.weight),
    taskWeightEnum: TaskWeight,
  });

  // Walidacja wartości weight
  const currentWeight = formData.weight || TaskWeight.NORMAL;
  const isValidWeight = Object.values(TaskWeight).includes(currentWeight);

  console.log('Weight validation:', {
    currentWeight,
    isValidWeight,
  });

  return (
    <FormSection title="Właściwości zadania">
      <div className="space-y-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="points" className="text-sm">
              Punkty bazowe
            </Label>
            <Select
              value={pointsValue}
              defaultValue={pointsValue}
              onValueChange={value => onFieldChange('points', parseInt(value))}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Wybierz poziom trudności" />
              </SelectTrigger>
              <SelectContent>
                {POINTS_OPTIONS.map(option => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-xs text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="weight" className="text-sm">
              Waga zadania
            </Label>
            <Select
              value={formData.weight}
              defaultValue={formData.weight}
              onValueChange={value => {
                // Block empty string updates - this is a Select component bug
                if (value === '' || value === undefined || value === null) {
                  console.log('Weight Select - blocked empty value:', value);
                  return;
                }

                console.log(
                  'Weight Select onValueChange:',
                  value,
                  'as TaskWeight:',
                  value as TaskWeight
                );
                onFieldChange('weight', value as TaskWeight);
              }}
            >
              <SelectTrigger className="text-sm">
                <SelectValue placeholder="Wybierz wagę zadania" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(TaskWeight).map(weight => {
                  console.log('Rendering weight option:', weight, 'type:', typeof weight);
                  return (
                    <SelectItem key={weight} value={weight}>
                      <Badge className={`text-xs ${getWeightColor(weight)}`}>
                        {TASK_WEIGHT_POINTS[weight]} pkt
                      </Badge>
                      <span>{getWeightLabel(weight)}</span>{' '}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="frequency" className="text-sm">
              Częstotliwość
            </Label>
            <Select
              value={formData.frequency || TaskFrequency.ONCE}
              defaultValue={formData.frequency || TaskFrequency.ONCE}
              onValueChange={value => {
                // Block empty string updates - this is a Select component bug
                if (value === '' || value === undefined || value === null) {
                  return;
                }

                onFieldChange('frequency', value as TaskFrequency);
              }}
            >
              <SelectTrigger className="text-sm">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.values(TaskFrequency).map(frequency => (
                  <SelectItem key={frequency} value={frequency}>
                    {getFrequencyLabel(frequency)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Interval field for DAILY, WEEKLY, MONTHLY */}
          {(formData.frequency === TaskFrequency.DAILY ||
            formData.frequency === TaskFrequency.WEEKLY ||
            formData.frequency === TaskFrequency.MONTHLY) && (
            <div>
              <Label htmlFor="rruleInterval" className="text-sm">
                Co ile{' '}
                {formData.frequency === TaskFrequency.DAILY
                  ? 'dni'
                  : formData.frequency === TaskFrequency.WEEKLY
                    ? 'tygodni'
                    : 'miesięcy'}
              </Label>
              <Select
                value={formData.rruleInterval?.toString() || '1'}
                defaultValue={formData.rruleInterval?.toString() || '1'}
                onValueChange={value => {
                  onFieldChange('rruleInterval', parseInt(value) || 1);
                }}
              >
                <SelectTrigger className="text-sm">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => i + 1).map(num => (
                    <SelectItem key={num} value={num.toString()}>
                      {num === 1
                        ? formData.frequency === TaskFrequency.DAILY
                          ? 'Codziennie'
                          : formData.frequency === TaskFrequency.WEEKLY
                            ? 'Co tydzień'
                            : 'Co miesiąc'
                        : `Co ${num} ${
                            formData.frequency === TaskFrequency.DAILY
                              ? 'dni'
                              : formData.frequency === TaskFrequency.WEEKLY
                                ? 'tygodnie'
                                : 'miesiące'
                          }`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {formData.frequency !== TaskFrequency.ONCE && (
          <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
            <div className="flex items-start gap-2">
              <div className="w-4 h-4 rounded-full bg-blue-500 flex-shrink-0 mt-0.5">
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="text-sm">
                <p className="text-blue-900 dark:text-blue-100 font-medium">Zadanie cykliczne</p>
                <p className="text-blue-700 dark:text-blue-300 text-xs mt-1">
                  {formData.frequency === TaskFrequency.DAILY &&
                    formData.rruleInterval === 1 &&
                    'Będzie powtarzane codziennie'}
                  {formData.frequency === TaskFrequency.DAILY &&
                    formData.rruleInterval > 1 &&
                    `Będzie powtarzane co ${formData.rruleInterval} dni`}
                  {formData.frequency === TaskFrequency.WEEKLY &&
                    formData.rruleInterval === 1 &&
                    'Będzie powtarzane co tydzień'}
                  {formData.frequency === TaskFrequency.WEEKLY &&
                    formData.rruleInterval > 1 &&
                    `Będzie powtarzane co ${formData.rruleInterval} tygodnie`}
                  {formData.frequency === TaskFrequency.MONTHLY &&
                    formData.rruleInterval === 1 &&
                    'Będzie powtarzane co miesiąc'}
                  {formData.frequency === TaskFrequency.MONTHLY &&
                    formData.rruleInterval > 1 &&
                    `Będzie powtarzane co ${formData.rruleInterval} miesiące`}
                  {formData.frequency === TaskFrequency.YEARLY && 'Będzie powtarzane co rok'}
                  {formData.frequency === TaskFrequency.CUSTOM &&
                    'Niestandardowe reguły powtarzania'}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </FormSection>
  );
}
