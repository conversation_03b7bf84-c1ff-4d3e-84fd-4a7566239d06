import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Save, ArrowLeft } from 'lucide-react';

interface FormActionsProps {
  onClose: () => void;
  isMobile?: boolean;
  loading: boolean;
  isTitleEmpty: boolean;
  mode: 'create' | 'edit';
}

export function FormActions({ onClose, isMobile, loading, isTitleEmpty, mode }: FormActionsProps) {
  const submitText = mode === 'create' ? 'Utwórz zadanie' : 'Zapisz zmiany';
  const loadingText = mode === 'create' ? 'Tworzenie...' : 'Zapisywanie...';

  if (isMobile) {
    return (
      <div className="sticky top-0 bg-white border-b px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" onClick={onClose}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-lg font-semibold">
            {mode === 'create' ? 'Nowe zadanie' : 'Edytuj zadanie'}
          </h1>
        </div>

        <Button type="submit" disabled={loading || isTitleEmpty} size="sm">
          {loading ? (
            loadingText
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Zapisz
            </>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="flex justify-end gap-3">
      <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
        Anuluj
      </Button>
      <Button type="submit" disabled={loading || isTitleEmpty}>
        {loading ? loadingText : submitText}
      </Button>
    </div>
  );
}
