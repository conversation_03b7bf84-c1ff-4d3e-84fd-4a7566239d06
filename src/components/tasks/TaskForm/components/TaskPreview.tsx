import React from 'react';
import { FamilyM<PERSON>ber, User, TaskFrequency } from '@prisma/client';
import { Badge } from '@/components/ui/badge';
import { FormSection } from './FormSection';
import { TaskFormData } from '../hooks/useTaskFormState';
import { getWeightLabel, getFrequencyLabel, getMemberDisplayName } from '../utils/taskFormUtils';
import { TASK_WEIGHT_POINTS } from '@/lib/validations/task';

interface TaskPreviewProps {
  formData: TaskFormData;
  familyMembers: (FamilyMember & { user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'> })[];
}

export function TaskPreview({ formData, familyMembers }: TaskPreviewProps) {
  return (
    <FormSection title="Podgląd" className="bg-gray-50">
      <div className="space-y-2 text-sm">
        <p>
          <strong>Tytuł:</strong> {formData.title || 'Brak tytułu'}
        </p>
        {formData.description && (
          <p>
            <strong>Opis:</strong> {formData.description}
          </p>
        )}
        <div className="flex flex-wrap items-center gap-2">
          <Badge>{formData.points} pkt</Badge>
          <Badge variant="outline">
            {getWeightLabel(formData.weight)} (+{TASK_WEIGHT_POINTS[formData.weight]} pkt)
          </Badge>
          <Badge variant="outline">{getFrequencyLabel(formData.frequency)}</Badge>
        </div>
        {formData.dueDate && (
          <p>
            <strong>Rozpoczęcie:</strong>{' '}
            {new Date(`${formData.dueDate}T${formData.dueTime || '00:00'}`).toLocaleString('pl-PL')}
          </p>
        )}
        {formData.endDate && (
          <p>
            <strong>Zakończenie:</strong>{' '}
            {new Date(`${formData.endDate}T${formData.endTime || '23:59'}`).toLocaleString('pl-PL')}
          </p>
        )}
        {formData.frequency === TaskFrequency.CUSTOM &&
          formData.rruleMode === 'simple' &&
          formData.rruleFreq && (
            <p>
              <strong>Powtarzanie:</strong> Co{' '}
              {formData.rruleInterval > 1 ? formData.rruleInterval + ' ' : ''}
              {formData.rruleFreq.toLowerCase()}
              {formData.endDate
                ? ` do ${new Date(formData.endDate).toLocaleDateString('pl-PL')}`
                : `, ${formData.rruleCount} razy`}
            </p>
          )}
        {formData.assignedMemberIds.length > 0 && (
          <p>
            <strong>Przypisane do:</strong>{' '}
            {formData.assignedMemberIds
              .map(id => {
                const member = familyMembers.find(m => m.id === id);
                return member ? getMemberDisplayName(member) : '';
              })
              .filter(Boolean)
              .join(', ')}
          </p>
        )}
      </div>
    </FormSection>
  );
}
