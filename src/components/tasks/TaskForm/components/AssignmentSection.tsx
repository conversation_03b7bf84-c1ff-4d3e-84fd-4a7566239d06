import React from 'react';
import { FamilyMember, User, TaskAssignmentType } from '@prisma/client';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { FormSection } from './FormSection';
import { TaskFormData } from '../hooks/useTaskFormState';
import { getMemberDisplayName, getMemberInitials } from '../utils/taskFormUtils';

interface AssignmentSectionProps {
  formData: TaskFormData;
  familyMembers: (FamilyMember & { user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'> })[];
  onFieldChange: (field: keyof TaskFormData, value: any) => void;
}

export function AssignmentSection({
  formData,
  familyMembers,
  onFieldChange,
}: AssignmentSectionProps) {
  const handleMemberToggle = (memberId: string, checked: boolean) => {
    const currentIds = formData.assignedMemberIds;
    const newIds = checked ? [...currentIds, memberId] : currentIds.filter(id => id !== memberId);
    onFieldChange('assignedMemberIds', newIds);
  };

  return (
    <FormSection title="Przypisanie">
      <div className="space-y-3">
        {familyMembers.map(member => (
          <div key={member.id} className="flex items-center space-x-3">
            <Checkbox
              id={`member-${member.id}`}
              checked={formData.assignedMemberIds.includes(member.id)}
              onCheckedChange={checked => handleMemberToggle(member.id, Boolean(checked))}
            />
            <div className="flex items-center gap-2 flex-1">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-blue-100 text-blue-700 text-xs">
                  {getMemberInitials(member)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <p className="text-sm font-medium">{getMemberDisplayName(member)}</p>
                <Badge variant="outline" className="text-xs">
                  {member.role}
                </Badge>
              </div>
            </div>
          </div>
        ))}
      </div>

      {formData.assignedMemberIds.length === 0 && (
        <p className="text-xs text-gray-500 mt-3">
          Zadanie można przypisać później lub pozostawić do samodzielnego podjęcia
        </p>
      )}

      {formData.assignedMemberIds.length > 1 && (
        <Card className="mt-4 p-3 bg-gray-50">
          <Label className="text-sm font-medium">Sposób wykonania</Label>
          <RadioGroup
            value={formData.assignmentType}
            onValueChange={value => onFieldChange('assignmentType', value as TaskAssignmentType)}
            className="mt-2 space-y-3"
          >
            <div className="flex items-start space-x-2">
              <RadioGroupItem value={TaskAssignmentType.ANY} id="any" className="mt-1" />
              <div className="flex-1">
                <Label htmlFor="any" className="text-sm font-medium">
                  Jeden z wybranych
                </Label>
                <p className="text-xs text-gray-600">Zadanie wykonuje jedna z przypisanych osób</p>
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <RadioGroupItem value={TaskAssignmentType.ALL} id="all" className="mt-1" />
              <div className="flex-1">
                <Label htmlFor="all" className="text-sm font-medium">
                  Wszyscy przypisani
                </Label>
                <p className="text-xs text-gray-600">
                  Zadanie muszą wykonać wszystkie przypisane osoby
                </p>
              </div>
            </div>
          </RadioGroup>
        </Card>
      )}
    </FormSection>
  );
}
