'use client';

import { useState } from 'react';
import { CompletionStatus } from '@prisma/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useConfirmDialog } from '@/components/ui/confirm-dialog';
import { CheckCircle, XCircle, Trash2, RotateCcw, Loader2, CheckSquare, X } from 'lucide-react';

interface TaskCompletion {
  id: string;
  taskId: string;
  status: CompletionStatus;
  task: {
    id: string;
    title: string;
  };
}

interface BulkTaskActionsProps {
  familyId: string;
  selectedCompletions: TaskCompletion[];
  onClearSelection: () => void;
  onActionComplete: () => void;
  canVerifyTasks: boolean;
}

export function BulkTaskActions({
  familyId,
  selectedCompletions,
  onClearSelection,
  onActionComplete,
  canVerifyTasks,
}: BulkTaskActionsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  if (selectedCompletions.length === 0 || !canVerifyTasks) {
    return null;
  }

  const pendingCompletions = selectedCompletions.filter(c => c.status === CompletionStatus.PENDING);
  const approvedCompletions = selectedCompletions.filter(
    c => c.status === CompletionStatus.APPROVED
  );
  const rejectedCompletions = selectedCompletions.filter(
    c => c.status === CompletionStatus.REJECTED
  );

  const handleBulkVerification = async (approved: boolean) => {
    setIsLoading(true);
    try {
      const promises = pendingCompletions.map(completion =>
        fetch(
          `/api/families/${familyId}/tasks/${completion.taskId}/verify?completionId=${completion.id}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ approved }),
          }
        )
      );

      const results = await Promise.allSettled(promises);
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      if (successful > 0) {
        toast({
          title: 'Sukces',
          description: `${successful} zadanie/zadań zostało ${approved ? 'zatwierdzonych' : 'odrzuconych'}${failed > 0 ? `. ${failed} zadań nie udało się przetworzyć.` : ''}`,
          variant: 'success',
        });
      }

      if (failed > 0 && successful === 0) {
        toast({
          title: 'Błąd',
          description: `Nie udało się przetworzyć żadnego zadania (${failed} błędów)`,
          variant: 'destructive',
        });
      }

      onActionComplete();
      onClearSelection();
    } catch (error) {
      console.error('Bulk verification error:', error);
      toast({
        title: 'Błąd',
        description: 'Wystąpił błąd podczas przetwarzania zadań',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkApprove = () => {
    if (pendingCompletions.length === 0) {
      toast({
        title: 'Brak zadań do zatwierdzenia',
        description: 'Wybrane zadania nie oczekują na weryfikację',
        variant: 'destructive',
      });
      return;
    }

    showConfirm({
      title: 'Zatwierdź zadania',
      message: `Czy na pewno chcesz zatwierdzić ${pendingCompletions.length} zadanie/zadań?`,
      variant: 'default',
      confirmText: 'Zatwierdź',
      cancelText: 'Anuluj',
      onConfirm: () => handleBulkVerification(true),
    });
  };

  const handleBulkReject = () => {
    if (pendingCompletions.length === 0) {
      toast({
        title: 'Brak zadań do odrzucenia',
        description: 'Wybrane zadania nie oczekują na weryfikację',
        variant: 'destructive',
      });
      return;
    }

    showConfirm({
      title: 'Odrzuć zadania',
      message: `Czy na pewno chcesz odrzucić ${pendingCompletions.length} zadanie/zadań?`,
      variant: 'destructive',
      confirmText: 'Odrzuć',
      cancelText: 'Anuluj',
      onConfirm: () => handleBulkVerification(false),
    });
  };

  return (
    <>
      <ConfirmDialog />
      <Card className="mb-4 bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CheckSquare className="h-5 w-5 text-blue-600" />
              <div className="flex items-center gap-2">
                <span className="font-medium text-blue-900">
                  Wybrano {selectedCompletions.length} zadanie/zadań
                </span>
                <div className="flex gap-1">
                  {pendingCompletions.length > 0 && (
                    <Badge
                      variant="outline"
                      className="bg-yellow-100 text-yellow-800 border-yellow-300"
                    >
                      {pendingCompletions.length} oczekujące
                    </Badge>
                  )}
                  {approvedCompletions.length > 0 && (
                    <Badge
                      variant="outline"
                      className="bg-green-100 text-green-800 border-green-300"
                    >
                      {approvedCompletions.length} zatwierdzone
                    </Badge>
                  )}
                  {rejectedCompletions.length > 0 && (
                    <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">
                      {rejectedCompletions.length} odrzucone
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {pendingCompletions.length > 0 && (
                <>
                  <Button
                    onClick={handleBulkApprove}
                    disabled={isLoading}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4" />
                    )}
                    <span className="ml-1">Zatwierdź ({pendingCompletions.length})</span>
                  </Button>
                  <Button
                    onClick={handleBulkReject}
                    disabled={isLoading}
                    size="sm"
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-50"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <XCircle className="h-4 w-4" />
                    )}
                    <span className="ml-1">Odrzuć ({pendingCompletions.length})</span>
                  </Button>
                </>
              )}
              <Button onClick={onClearSelection} disabled={isLoading} size="sm" variant="ghost">
                <X className="h-4 w-4" />
                <span className="ml-1">Anuluj</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
}
