'use client';

import { TaskWeight, TaskFrequency, FamilyMember, User, Task } from '@prisma/client';
import { TaskWithRelations } from '@/hooks/useTasks';
import { TaskForm } from './TaskForm/index';

interface TaskMobileFormProps {
  familyId: string;
  familyMembers: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  mode?: 'create' | 'edit';
  task?: TaskWithRelations;
  initialDate?: Date;
  onClose: () => void;
  onTaskSuccess?: () => void;
}

export function TaskMobileForm({
  familyId,
  familyMembers,
  mode = 'create',
  task,
  initialDate,
  onClose,
  onTaskSuccess,
}: TaskMobileFormProps) {
  return (
    <TaskForm
      familyId={familyId}
      familyMembers={familyMembers}
      mode={mode}
      task={task}
      initialDate={initialDate}
      isMobile={true}
      onClose={onClose}
      onSuccess={onTaskSuccess}
    />
  );
}
