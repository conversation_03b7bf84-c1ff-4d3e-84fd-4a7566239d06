'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { AlertCircle } from 'lucide-react';
import { TaskCard } from './TaskCard';
import { TaskFilters } from '@/hooks/useTasks';
import { TaskFilters as TaskFiltersComponent } from './TaskFilters';
import { FamilyMember, User } from '@prisma/client';
import { TaskWithRelations } from '@/hooks/useTasks';
import { UserTaskWithRelations } from '@/hooks/useUserTasks';

interface PriorityTasksSectionProps {
  priorityTasks: TaskWithRelations[] | UserTaskWithRelations[];
  priorityTaskLimit: number;
  setPriorityTaskLimit: (limit: number) => void;
  taskFilters: TaskFilters;
  setTaskFilters: (filters: TaskFilters) => void;
  familyMembers: (Family<PERSON><PERSON><PERSON> & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  currentMember: FamilyMember;
  canManageTasks: boolean;
  canAssignTasks: boolean;
  canCompleteTasks: boolean;
  onTaskUpdate: () => void;
  updateTaskLocally?: (
    taskId: string,
    updater: (task: TaskWithRelations) => TaskWithRelations
  ) => void;
  showFamilyBadge?: boolean;
  className?: string;
}

export function PriorityTasksSection({
  priorityTasks,
  priorityTaskLimit,
  setPriorityTaskLimit,
  taskFilters,
  setTaskFilters,
  familyMembers,
  currentMember,
  canManageTasks,
  canAssignTasks,
  canCompleteTasks,
  onTaskUpdate,
  updateTaskLocally,
  showFamilyBadge = false,
  className,
}: PriorityTasksSectionProps) {
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          Zadania wymagające uwagi
        </CardTitle>
        <Select
          value={String(priorityTaskLimit)}
          onValueChange={value => setPriorityTaskLimit(Number(value))}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Pokaż" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="3">Pokaż 3</SelectItem>
            <SelectItem value="6">Pokaż 6</SelectItem>
            <SelectItem value="9">Pokaż 9</SelectItem>
            <SelectItem value="12">Pokaż 12</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        {/* Task Filters */}
        <div className="mb-6">
          <TaskFiltersComponent
            filters={taskFilters}
            onFiltersChange={setTaskFilters}
            familyMembers={familyMembers}
            className="mb-4"
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {priorityTasks.length === 0 ? (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500 mb-2">
                Brak zadań spełniających wybrane kryteria filtrowania.
              </p>
              <p className="text-sm text-gray-400">
                Spróbuj zmienić filtry lub wyczyścić je powyżej.
              </p>
            </div>
          ) : (
            priorityTasks.map(task => {
              const TaskWrapper = showFamilyBadge ? 'div' : 'div';
              const wrapperProps = showFamilyBadge ? { className: 'relative' } : {};

              return (
                <TaskWrapper key={task.id} {...wrapperProps}>
                  <TaskCard
                    task={task as TaskWithRelations}
                    currentMember={currentMember}
                    canManageTasks={canManageTasks}
                    canAssignTasks={canAssignTasks}
                    canCompleteTasks={canCompleteTasks}
                    onTaskUpdate={onTaskUpdate}
                    updateTaskLocally={updateTaskLocally}
                  />
                  {/* Family badge for UserTasksDashboard */}
                  {showFamilyBadge && 'family' in task && (
                    <div className="absolute top-2 right-2">
                      <Badge variant="outline" className="bg-white/90 text-xs">
                        {task.family.name}
                      </Badge>
                    </div>
                  )}
                </TaskWrapper>
              );
            })
          )}
        </div>
      </CardContent>
    </Card>
  );
}
