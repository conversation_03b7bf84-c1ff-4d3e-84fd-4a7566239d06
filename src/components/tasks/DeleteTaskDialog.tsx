'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Trash2, Loader2, Al<PERSON><PERSON>riangle } from 'lucide-react';
import { Task } from '@prisma/client';

interface TaskLike {
  id: string;
  familyId: string;
  title: string;
}

interface DeleteTaskDialogProps {
  task: TaskLike | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onDelete?: (task: TaskLike) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
}

export function DeleteTaskDialog({
  task,
  open,
  onOpenChange,
  onDelete,
  isLoading = false,
  error = null,
}: DeleteTaskDialogProps) {
  const handleDelete = async () => {
    if (!task || !onDelete) return;
    await onDelete(task);
  };

  if (!task) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[480px]" onClick={e => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <Trash2 className="h-5 w-5" />
            Potwierdź usunięcie zadania
          </DialogTitle>
          <DialogDescription>
            Czy na pewno chcesz trwale usunąć to zadanie? Ta operacja jest nieodwracalna.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <p className="text-sm text-gray-800">
            Usuwasz zadanie: <strong className="font-semibold">{task.title}</strong>
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Spowoduje to usunięcie wszystkich powiązanych z nim danych, w tym historii wykonań.
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange?.(false)}
            disabled={isLoading}
          >
            Anuluj
          </Button>
          <Button type="button" variant="destructive" onClick={handleDelete} disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Usuwanie...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Usuń zadanie
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
