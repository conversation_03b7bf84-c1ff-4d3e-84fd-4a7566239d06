'use client';

import { useState } from 'react';
import {
  Task,
  TaskStatus,
  FamilyMember,
  User,
  TaskCompletion,
  CompletionStatus,
  TaskWeight,
  TaskFrequency,
} from '@prisma/client';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  CheckCircle,
  Clock,
  Calendar,
  User as UserIcon,
  MoreVertical,
  AlertCircle,
  Trophy,
  Edit,
  Trash2,
  UserPlus,
  CheckSquare,
  XCircle,
  Target,
  Repeat,
  AlertTriangle,
  Star,
  Pause,
  Info,
  Lock,
} from 'lucide-react';
import { TASK_WEIGHT_POINTS } from '@/lib/validations/task';
import { TaskWithRelations } from '@/hooks/useTasks';
import { getSuspensionReasonLabel } from '@/lib/validations/suspension';
import { DeleteTaskDialog } from './DeleteTaskDialog';
import { useToast } from '@/hooks/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { canModifyTask } from '@/lib/utils/task-permissions';

interface SuspensionInfo {
  isSuspended: boolean;
  reason?: string;
  title?: string;
  endDate?: Date;
  suspensionType?: 'task' | 'member';
}

interface TaskCardProps {
  task: TaskWithRelations;
  currentMember: FamilyMember;
  canManageTasks: boolean;
  canAssignTasks: boolean;
  canCompleteTasks: boolean;
  suspensionInfo?: SuspensionInfo;
  onTaskUpdate?: () => void;
  updateTaskLocally?: (
    taskId: string,
    updater: (task: TaskWithRelations) => TaskWithRelations
  ) => void;
  // Bulk selection props
  bulkSelectMode?: boolean;
  isSelected?: boolean;
  onToggleSelection?: () => void;
}

export function TaskCard({
  task,
  currentMember,
  canManageTasks,
  canAssignTasks,
  canCompleteTasks,
  suspensionInfo,
  onTaskUpdate,
  updateTaskLocally,
  bulkSelectMode = false,
  isSelected = false,
  onToggleSelection,
}: TaskCardProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [taskToDelete, setTaskToDelete] = useState<TaskWithRelations | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleDeleteTask = async (task: { id: string; familyId: string; title: string }) => {
    setIsDeleting(true);
    setDeleteError(null);

    try {
      const response = await fetch(`/api/families/${task.familyId}/tasks/${task.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nie udało się usunąć zadania');
      }

      setTaskToDelete(null);
      onTaskUpdate?.();
    } catch (err: any) {
      console.error('Error deleting task:', err);
      setDeleteError(err.message || 'Wystąpił nieznany błąd podczas usuwania zadania.');
    } finally {
      setIsDeleting(false);
    }
  };

  const getMemberDisplayName = (
    member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
  ) => {
    if (member?.user?.firstName && member?.user?.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    if (member?.user?.email) {
      return member.user.email.split('@')[0];
    }
    return 'Nieznany użytkownik';
  };

  const getMemberInitials = (
    member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
  ) => {
    if (member?.user?.firstName && member?.user?.lastName) {
      return `${member.user.firstName[0]}${member.user.lastName[0]}`.toUpperCase();
    }
    if (member?.user?.email) {
      return member.user.email[0].toUpperCase();
    }
    return 'U';
  };

  const getWeightColor = (weight: TaskWeight) => {
    switch (weight) {
      case TaskWeight.LIGHT:
        return 'bg-green-100 text-green-800 border-green-200';
      case TaskWeight.NORMAL:
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case TaskWeight.HEAVY:
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case TaskWeight.CRITICAL:
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getWeightIcon = (weight: TaskWeight) => {
    switch (weight) {
      case TaskWeight.LIGHT:
        return <Target className="h-3 w-3" />;
      case TaskWeight.NORMAL:
        return <Star className="h-3 w-3" />;
      case TaskWeight.HEAVY:
        return <AlertTriangle className="h-3 w-3" />;
      case TaskWeight.CRITICAL:
        return <AlertCircle className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const getFrequencyLabel = (frequency: TaskFrequency) => {
    switch (frequency) {
      case TaskFrequency.ONCE:
        return 'Jednorazowe';
      case TaskFrequency.DAILY:
        return 'Codziennie';
      case TaskFrequency.WEEKLY:
        return 'Tygodniowo';
      case TaskFrequency.MONTHLY:
        return 'Miesięcznie';
      case TaskFrequency.CUSTOM:
        return 'Niestandardowe';
      default:
        return 'Nieznane';
    }
  };

  // Helper function to get semantic dates from task
  const getTaskSemanticDates = () => {
    // Priorytet: nowe pola semantyczne > stare pola
    const startDate = (task as any).startDateTime || task.dueDate || (task as any).startDate;
    const endDate =
      (task as any).endDateTime || (task.frequency === 'ONCE' ? task.endDate : undefined);
    const cycleEndDate =
      (task as any).cycleEndDate ||
      (task.frequency !== 'ONCE' ? (task as any).recurrenceEnd || task.endDate : undefined);

    return {
      start: startDate ? new Date(startDate) : null,
      end: endDate ? new Date(endDate) : null,
      cycleEnd: cycleEndDate ? new Date(cycleEndDate) : null,
      isRecurring: task.frequency !== 'ONCE',
    };
  };

  const getTaskStatus = () => {
    // Check if suspended first
    if (suspensionInfo?.isSuspended) {
      return { type: 'suspended', suspensionInfo };
    }

    const approvedCompletion = task.completions.find(c => c.status === CompletionStatus.APPROVED);
    const pendingCompletion = task.completions.find(c => c.status === CompletionStatus.PENDING);
    const userCompletion = task.completions.find(c => c.completedById === currentMember.id);

    if (approvedCompletion) {
      return { type: 'completed', completion: approvedCompletion };
    }
    if (pendingCompletion) {
      return { type: 'pending', completion: pendingCompletion };
    }
    if (userCompletion) {
      return { type: 'user_completed', completion: userCompletion };
    }

    const semanticDates = getTaskSemanticDates();

    // Check if overdue (using semantic dates)
    if (
      semanticDates.start &&
      semanticDates.start < new Date() &&
      task.status === TaskStatus.ACTIVE
    ) {
      return { type: 'overdue' };
    }

    // Check if due soon (within 24 hours) - using semantic dates
    if (semanticDates.start && task.status === TaskStatus.ACTIVE) {
      const hoursUntilDue =
        (semanticDates.start.getTime() - new Date().getTime()) / (1000 * 60 * 60);
      if (hoursUntilDue <= 24 && hoursUntilDue > 0) {
        return { type: 'due_soon' };
      }
    }

    return { type: 'active' };
  };

  const getCategoryIndicator = () => {
    const status = getTaskStatus();

    switch (status.type) {
      case 'overdue':
        return {
          color: 'bg-red-500',
          label: 'Przeterminowane',
          textColor: 'text-red-700',
          bgColor: 'bg-red-50',
        };
      case 'due_soon':
        return {
          color: 'bg-orange-500',
          label: 'Wkrótce termin',
          textColor: 'text-orange-700',
          bgColor: 'bg-orange-50',
        };
      case 'pending':
        return {
          color: 'bg-yellow-500',
          label: 'Do weryfikacji',
          textColor: 'text-yellow-700',
          bgColor: 'bg-yellow-50',
        };
      case 'completed':
        return {
          color: 'bg-green-500',
          label: 'Ukończone',
          textColor: 'text-green-700',
          bgColor: 'bg-green-50',
        };
      case 'suspended':
        return {
          color: 'bg-purple-500',
          label: 'Zawieszone',
          textColor: 'text-purple-700',
          bgColor: 'bg-purple-50',
        };
      default:
        return null;
    }
  };

  const taskStatus = getTaskStatus();
  const categoryIndicator = getCategoryIndicator();

  const handleCompleteTask = async () => {
    setIsLoading(true);

    // Optimistic update - add pending completion immediately
    const optimisticCompletion = {
      id: `temp-${Date.now()}`, // Temporary ID
      taskId: task.id,
      completedById: currentMember.id,
      completedBy: {
        id: currentMember.id,
        user: currentMember.user,
      },
      status: CompletionStatus.PENDING,
      createdAt: new Date(),
      completedAt: new Date(),
      notes: null,
      verifiedAt: null,
      verifiedById: null,
    };

    if (updateTaskLocally) {
      updateTaskLocally(task.id, prevTask => ({
        ...prevTask,
        completions: [...prevTask.completions, optimisticCompletion as any],
      }));
    }

    try {
      const response = await fetch(`/api/families/${task.familyId}/tasks/${task.id}/complete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się oznaczyć zadania jako wykonane');
      }

      const realCompletion = await response.json();

      // Replace optimistic completion with real one
      if (updateTaskLocally) {
        updateTaskLocally(task.id, prevTask => ({
          ...prevTask,
          completions: [
            ...prevTask.completions.filter(c => c.id !== optimisticCompletion.id),
            realCompletion,
          ],
        }));
      }

      toast({
        title: 'Sukces',
        description: 'Zadanie zostało ukończone i oczekuje na weryfikację',
        variant: 'success',
      });

      // Only call onTaskUpdate as fallback if no optimistic update available
      if (!updateTaskLocally) {
        onTaskUpdate?.();
      }
    } catch (error: any) {
      console.error('Error completing task:', error);

      // Revert optimistic update on error
      if (updateTaskLocally) {
        updateTaskLocally(task.id, prevTask => ({
          ...prevTask,
          completions: prevTask.completions.filter(c => c.id !== optimisticCompletion.id),
        }));
      }

      toast({
        title: 'Błąd',
        description: error.message || 'Wystąpił błąd podczas oznaczania zadania jako wykonane',
        variant: 'destructive',
      });

      // Fallback to full refresh on error
      onTaskUpdate?.();
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyTask = async (approved: boolean, completionId: string) => {
    setIsLoading(true);

    // Optimistic update - update UI immediately
    if (updateTaskLocally) {
      updateTaskLocally(task.id, prevTask => ({
        ...prevTask,
        completions: prevTask.completions.map(completion =>
          completion.id === completionId
            ? {
                ...completion,
                status: approved ? CompletionStatus.APPROVED : CompletionStatus.REJECTED,
                verifiedAt: new Date(),
                verifiedById: currentMember.id,
              }
            : completion
        ),
      }));
    }

    try {
      const response = await fetch(
        `/api/families/${task.familyId}/tasks/${task.id}/verify?completionId=${completionId}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ approved }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Nie udało się zweryfikować zadania');
      }

      toast({
        title: 'Sukces',
        description: approved ? 'Zadanie zostało zatwierdzone' : 'Zadanie zostało odrzucone',
        variant: 'success',
      });

      // Only call onTaskUpdate as fallback if no optimistic update available
      if (!updateTaskLocally) {
        onTaskUpdate?.();
      }
    } catch (error: any) {
      console.error('Error verifying task:', error);

      // Revert optimistic update on error
      if (updateTaskLocally) {
        updateTaskLocally(task.id, prevTask => ({
          ...prevTask,
          completions: prevTask.completions.map(completion =>
            completion.id === completionId
              ? {
                  ...completion,
                  status: CompletionStatus.PENDING,
                  verifiedAt: undefined,
                  verifiedById: undefined,
                }
              : completion
          ),
        }));
      }

      toast({
        title: 'Błąd',
        description: error.message || 'Wystąpił błąd podczas weryfikacji zadania',
        variant: 'destructive',
      });

      // Fallback to full refresh on error
      onTaskUpdate?.();
    } finally {
      setIsLoading(false);
    }
  };

  const isAssignedToCurrentUser =
    task.assignments?.some(a => a.memberId === currentMember.id) || false;
  const isSuspended = suspensionInfo?.isSuspended || false;

  const userHasCompleted = task.completions.some(c => c.completedById === currentMember.id);

  const canComplete =
    canCompleteTasks &&
    isAssignedToCurrentUser &&
    task.status === TaskStatus.ACTIVE &&
    !isSuspended &&
    !userHasCompleted;

  const canVerify = canManageTasks && taskStatus.type === 'pending' && !isSuspended;

  // Check enhanced permissions for completed tasks
  const taskPermissions = canModifyTask(task, currentMember, canManageTasks);
  const canEditTask = taskPermissions.canEdit;
  const canDeleteTask = taskPermissions.canDelete;

  return (
    <TooltipProvider>
      <Card
        className={`
          group relative transition-all duration-200 hover-lift card-shadow
          bg-white border border-gray-100 hover:border-purple-200
          ${taskStatus.type === 'suspended' ? 'opacity-75' : ''}
          ${canComplete ? 'cursor-pointer' : ''}
          ${taskStatus.type === 'completed' ? 'task-completed' : ''}
          ${taskStatus.type === 'pending' ? 'task-in-progress' : ''}
          ${taskStatus.type === 'overdue' ? 'task-overdue' : ''}
          ${taskStatus.type === 'active' ? 'task-pending' : ''}
        `}
        onClick={canComplete && taskStatus.type === 'active' ? handleCompleteTask : undefined}
      >
        {/* Prototype Design Layout */}
        <div className="p-2">
          <div className="flex items-center justify-between">
            {/* Bulk selection checkbox */}
            {bulkSelectMode && taskStatus.type === 'pending' && (
              <div className="mr-3">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={onToggleSelection}
                  onClick={e => e.stopPropagation()}
                />
              </div>
            )}

            {/* Left side - Main content */}
            <div className="flex-1 min-w-0">
              {/* Task Title with badges */}
              <div className="flex items-center gap-2 mb-1">
                <h3
                  className={`
                                        font-bold text-gray-900 truncate
                                        ${taskStatus.type === 'completed' ? 'line-through text-gray-600' : ''}
                                    `}
                >
                  {task.title}
                </h3>

                {/* Weight indicator */}
                <Badge
                  variant="outline"
                  className={`${getWeightColor(task.weight)} px-1.5 py-0.5 text-xs flex items-center gap-1`}
                >
                  {getWeightIcon(task.weight)}
                  {task.weight.toLowerCase()}
                </Badge>

                {/* Protection indicator for completed tasks */}
                {taskStatus.type === 'completed' &&
                  !taskPermissions.canEdit &&
                  !taskPermissions.canDelete && (
                    <Tooltip>
                      <TooltipTrigger>
                        <Badge
                          variant="outline"
                          className="bg-slate-100 text-slate-600 border-slate-300 px-1.5 py-0.5 text-xs flex items-center gap-1"
                        >
                          <Lock className="h-3 w-3" />
                          Chronione
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="text-xs max-w-48">
                          To ukończone zadanie jest chronione przed modyfikacją zgodnie z zasadami
                          rodziny
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  )}

                {/* Category indicator */}
                {categoryIndicator && (
                  <Badge
                    variant="outline"
                    className={`${categoryIndicator.bgColor} ${categoryIndicator.textColor} border-0 px-1.5 py-0.5 text-xs`}
                  >
                    {categoryIndicator.label}
                  </Badge>
                )}
              </div>

              {/* Assignment info */}
              {task.assignments && task.assignments.length > 0 ? (
                <div className="text-sm text-gray-500">
                  <span>Przypisane do: </span>
                  {task.assignments.map((assignment, index) => (
                    <span key={assignment.id} className="font-medium">
                      {getMemberDisplayName(assignment.member)}
                      {assignment.memberId === currentMember.id && (
                        <span className="text-purple-600 ml-1">(Ty)</span>
                      )}
                      {index < task.assignments.length - 1 && ', '}
                    </span>
                  ))}
                  {task.assignments.length > 1 && task.assignmentType === 'ALL' && (
                    <span className="text-xs text-blue-600 ml-2">(wszyscy)</span>
                  )}
                </div>
              ) : task.assignedTo ? (
                <p className="text-sm text-gray-500">
                  Przypisane do:{' '}
                  <span className="font-medium">{getMemberDisplayName(task.assignedTo)}</span>
                  {task.assignments?.some(a => a.memberId === currentMember.id) && (
                    <span className="text-purple-600 ml-1">(Ty)</span>
                  )}
                </p>
              ) : (
                <p className="text-sm text-gray-500">Nieprzypisane</p>
              )}

              {/* Additional info for completed tasks */}
              {taskStatus.completion && (
                <p className="text-xs text-gray-400 mt-1">
                  Ukończone{' '}
                  {new Date(taskStatus.completion.completedAt).toLocaleDateString('pl-PL')}
                </p>
              )}

              {/* Status info for pending verification */}
              {taskStatus.type === 'pending' && (
                <div className="text-xs text-orange-600 mt-1 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>Oczekuje na weryfikację</span>
                </div>
              )}

              {/* Status info for user completed but not verified */}
              {taskStatus.type === 'user_completed' && taskStatus.completion && (
                <div className="text-xs text-blue-600 mt-1 flex items-center gap-1">
                  <CheckCircle className="h-3 w-3" />
                  <span>
                    Ukończone przez Ciebie{' '}
                    {new Date(taskStatus.completion.completedAt).toLocaleDateString('pl-PL')}
                    {taskStatus.completion.status === CompletionStatus.PENDING &&
                      ' - oczekuje na weryfikację'}
                    {taskStatus.completion.status === CompletionStatus.REJECTED && ' - odrzucone'}
                  </span>
                </div>
              )}

              {/* Date info for active tasks */}
              {getTaskSemanticDates().start && taskStatus.type !== 'completed' && (
                <p
                  className={`text-xs mt-1 ${
                    taskStatus.type === 'overdue'
                      ? 'text-red-600 font-medium'
                      : taskStatus.type === 'due_soon'
                        ? 'text-orange-600 font-medium'
                        : 'text-gray-400'
                  }`}
                >
                  {(() => {
                    const semanticDates = getTaskSemanticDates();
                    if (!semanticDates.start) return 'Brak terminu';

                    if (semanticDates.isRecurring) {
                      if (semanticDates.cycleEnd) {
                        return `Cykl: ${semanticDates.start.toLocaleDateString('pl-PL')} - ${semanticDates.cycleEnd.toLocaleDateString('pl-PL')}`;
                      } else {
                        return `Cykl od: ${semanticDates.start.toLocaleDateString('pl-PL')}`;
                      }
                    } else {
                      if (semanticDates.end) {
                        return `${semanticDates.start.toLocaleDateString('pl-PL')} - ${semanticDates.end.toLocaleDateString('pl-PL')}`;
                      } else {
                        return `Termin: ${semanticDates.start.toLocaleDateString('pl-PL')}`;
                      }
                    }
                  })()}
                </p>
              )}
            </div>

            {/* Right side - Status Icon */}
            <div className="flex items-center gap-2 ml-4">
              {/* Points indicator */}
              {task.points > 0 && (
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Trophy className="h-3 w-3" />
                  <span>{task.points}</span>
                </div>
              )}

              {/* Status Icon/Checkbox */}
              <div className="flex-shrink-0">
                {taskStatus.type === 'completed' && (
                  <CheckCircle className="h-6 w-6 text-green-600 success-checkmark" />
                )}
                {taskStatus.type === 'pending' && <Clock className="h-6 w-6 text-orange-500" />}
                {taskStatus.type === 'overdue' && <AlertCircle className="h-6 w-6 text-red-500" />}
                {taskStatus.type === 'active' && !isSuspended && (
                  <div
                    className={`
                                            h-6 w-6 rounded-full border-2 border-gray-300 
                                            flex items-center justify-center
                                            ${canComplete ? 'hover:border-purple-400 cursor-pointer' : ''}
                                            transition-colors
                                        `}
                  >
                    {isLoading && (
                      <div className="h-3 w-3 border border-purple-400 border-t-transparent rounded-full animate-spin" />
                    )}
                  </div>
                )}
                {isSuspended && (
                  <Tooltip>
                    <TooltipTrigger>
                      <Pause className="h-6 w-6 text-orange-500" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Zadanie zawieszone</p>
                      {suspensionInfo?.endDate && (
                        <p className="text-xs">
                          Do: {new Date(suspensionInfo.endDate).toLocaleDateString('pl-PL')}
                        </p>
                      )}
                    </TooltipContent>
                  </Tooltip>
                )}
              </div>

              {/* Actions Menu (only for desktop) */}
              {(canManageTasks || canVerify) && !isSuspended && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      disabled={isLoading}
                      onClick={e => e.stopPropagation()}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {canVerify && taskStatus.completion && (
                      <>
                        <DropdownMenuItem
                          onClick={e => {
                            e.stopPropagation();
                            handleVerifyTask(true, taskStatus.completion!.id);
                          }}
                          disabled={isLoading}
                          className="text-green-600"
                        >
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Zatwierdź
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={e => {
                            e.stopPropagation();
                            handleVerifyTask(false, taskStatus.completion!.id);
                          }}
                          disabled={isLoading}
                          className="text-red-600"
                        >
                          <XCircle className="mr-2 h-4 w-4" />
                          Odrzuć
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                      </>
                    )}

                    {canManageTasks && (
                      <>
                        {canEditTask ? (
                          <DropdownMenuItem asChild>
                            <a href={`/families/${task.familyId}/tasks/${task.id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edytuj
                            </a>
                          </DropdownMenuItem>
                        ) : (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DropdownMenuItem disabled className="text-gray-400">
                                <Edit className="mr-2 h-4 w-4" />
                                Edytuj
                              </DropdownMenuItem>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs max-w-48">
                                {taskPermissions.reason ||
                                  'Nie możesz edytować tego ukończonego zadania'}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                        {canDeleteTask ? (
                          <DropdownMenuItem
                            onClick={e => {
                              e.stopPropagation();
                              setTaskToDelete(task);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Usuń
                          </DropdownMenuItem>
                        ) : (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <DropdownMenuItem disabled className="text-gray-400">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Usuń
                              </DropdownMenuItem>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs max-w-48">
                                {taskPermissions.reason ||
                                  'Nie możesz usunąć tego ukończonego zadania'}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </div>
      </Card>
      <DeleteTaskDialog
        task={taskToDelete}
        open={!!taskToDelete}
        onOpenChange={open => !open && setTaskToDelete(null)}
        onDelete={handleDeleteTask}
        isLoading={isDeleting}
        error={deleteError}
      />
    </TooltipProvider>
  );
}
