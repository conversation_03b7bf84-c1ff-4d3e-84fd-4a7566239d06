'use client';

import { useMemo, useState } from 'react';
import { TaskStatus, CompletionStatus, FamilyMember, User } from '@prisma/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Trophy,
  Target,
  Users,
  TrendingUp,
  Home,
  Plus,
} from 'lucide-react';
import { TaskCard } from './TaskCard';
import { TaskFilters } from '@/hooks/useTasks';
import { TaskFilters as TaskFiltersComponent } from './TaskFilters';
import { TaskCategories } from './TaskCategories';
import { UserTaskWithRelations, useUserTasks, useUserTaskStats } from '@/hooks/useUserTasks';
import { PriorityTasksSection } from './PriorityTasksSection';

interface UserTasksDashboardProps {
  currentUser: {
    id: string;
    firstName?: string;
    lastName?: string;
    email: string;
  };
  families: any[]; // From useUserFamilies
}

export function UserTasksDashboard({ currentUser, families }: UserTasksDashboardProps) {
  const [priorityTaskLimit, setPriorityTaskLimit] = useState(6);
  const [taskFilters, setTaskFilters] = useState<TaskFilters>({ includeCompleted: false });

  const { tasks, loading, error, refetch } = useUserTasks(taskFilters);
  const { stats } = useUserTaskStats();

  // Get all family members for filter purposes
  const allFamilyMembers = useMemo(() => {
    return families
      .flatMap(familyMember => familyMember.family?.members || [])
      .filter(
        (member, index, self) =>
          // Remove duplicates by member ID
          index === self.findIndex(m => m.id === member.id)
      );
  }, [families]);

  // Find current user's member IDs across all families
  const currentUserMemberIds = useMemo(() => {
    return families.filter(fm => fm.userId === currentUser.id).map(fm => fm.id);
  }, [families, currentUser.id]);

  // Create a current member object for TaskCard compatibility
  const currentMember = useMemo(() => {
    const firstMembership = families.find(fm => fm.userId === currentUser.id);
    return (
      firstMembership || {
        id: '',
        userId: currentUser.id,
        user: currentUser,
        points: 0,
        role: 'MEMBER' as const,
      }
    );
  }, [families, currentUser]);

  // Function to filter tasks based on current filters
  const filterTasks = useMemo(() => {
    return (tasksToFilter: UserTaskWithRelations[]) => {
      const now = new Date();

      return tasksToFilter.filter(task => {
        // Search filter
        if (taskFilters.search) {
          const searchLower = taskFilters.search.toLowerCase();
          if (
            !task.title.toLowerCase().includes(searchLower) &&
            !task.description?.toLowerCase().includes(searchLower)
          ) {
            return false;
          }
        }

        // Status filter
        if (taskFilters.status && taskFilters.status.length > 0) {
          if (!taskFilters.status.includes(task.status)) {
            return false;
          }
        }

        // Assigned members filter
        if (taskFilters.assignedMemberIds && taskFilters.assignedMemberIds.length > 0) {
          const isAssigned =
            task.assignments?.some(a => taskFilters.assignedMemberIds!.includes(a.memberId)) ||
            false;
          if (!isAssigned) {
            return false;
          }
        }

        // Weight filter
        if (taskFilters.weight && taskFilters.weight.length > 0) {
          if (!taskFilters.weight.includes(task.weight)) {
            return false;
          }
        }

        // Frequency filter
        if (taskFilters.frequency && taskFilters.frequency.length > 0) {
          if (!taskFilters.frequency.includes(task.frequency)) {
            return false;
          }
        }

        // Completion status filter
        if (taskFilters.completionStatus && taskFilters.completionStatus.length > 0) {
          // Sort completions by date (newest first) and get the latest for each status
          const sortedCompletions = [...task.completions].sort(
            (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
          );

          const approvedCompletion = sortedCompletions.find(
            c => c.status === CompletionStatus.APPROVED
          );
          const pendingCompletion = sortedCompletions.find(
            c => c.status === CompletionStatus.PENDING
          );
          const rejectedCompletion = sortedCompletions.find(
            c => c.status === CompletionStatus.REJECTED
          );

          const isOverdue =
            task.status === TaskStatus.ACTIVE &&
            task.dueDate &&
            new Date(task.dueDate) < now &&
            !approvedCompletion;

          const isDueSoon =
            task.status === TaskStatus.ACTIVE &&
            task.dueDate &&
            !approvedCompletion &&
            (() => {
              const hoursUntilDue =
                (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
              return hoursUntilDue <= 24 && hoursUntilDue > 0;
            })();

          const hasMatchStatus = taskFilters.completionStatus.some(status => {
            switch (status) {
              case 'approved':
                return !!approvedCompletion;
              case 'pending':
                return !!pendingCompletion;
              case 'rejected':
                return !!rejectedCompletion;
              case 'overdue':
                return isOverdue;
              case 'due_soon':
                return isDueSoon;
              default:
                return false;
            }
          });

          if (!hasMatchStatus) {
            return false;
          }
        }

        // Points range filter
        if (taskFilters.pointsRange) {
          if (
            taskFilters.pointsRange.min !== undefined &&
            task.points < taskFilters.pointsRange.min
          ) {
            return false;
          }
          if (
            taskFilters.pointsRange.max !== undefined &&
            task.points > taskFilters.pointsRange.max
          ) {
            return false;
          }
        }

        // Due date filter
        if (taskFilters.dueDate) {
          if (
            taskFilters.dueDate.from &&
            task.dueDate &&
            new Date(task.dueDate) < taskFilters.dueDate.from
          ) {
            return false;
          }
          if (
            taskFilters.dueDate.to &&
            task.dueDate &&
            new Date(task.dueDate) > taskFilters.dueDate.to
          ) {
            return false;
          }
        }

        // Include completed filter
        if (!taskFilters.includeCompleted) {
          // Check if task has any approved completion (latest first)
          const sortedCompletions = [...task.completions].sort(
            (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
          );
          const hasApprovedCompletion = sortedCompletions.some(
            c => c.status === CompletionStatus.APPROVED
          );
          if (hasApprovedCompletion) {
            return false;
          }
        }

        return true;
      });
    };
  }, [taskFilters]);

  const priorityTasks = useMemo(() => {
    const now = new Date();

    // Apply filters first
    const filteredTasks = filterTasks(tasks);

    // Priority: overdue > due soon > pending verification > my tasks
    const overdue = filteredTasks.filter(task => {
      const sortedCompletions = [...task.completions].sort(
        (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
      );
      return (
        task.status === TaskStatus.ACTIVE &&
        task.dueDate &&
        new Date(task.dueDate) < now &&
        !sortedCompletions.some(c => c.status === CompletionStatus.APPROVED)
      );
    });

    const dueSoon = filteredTasks.filter(task => {
      if (!task.dueDate || task.status !== TaskStatus.ACTIVE) return false;

      const sortedCompletions = [...task.completions].sort(
        (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
      );
      if (sortedCompletions.some(c => c.status === CompletionStatus.APPROVED)) return false;

      const hoursUntilDue = (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
      return hoursUntilDue <= 24 && hoursUntilDue > 0;
    });

    const pending = filteredTasks.filter(task => {
      const sortedCompletions = [...task.completions].sort(
        (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
      );
      return sortedCompletions.some(c => c.status === CompletionStatus.PENDING);
    });

    const myActiveTasks = filteredTasks.filter(task => {
      const sortedCompletions = [...task.completions].sort(
        (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
      );
      return (
        task.assignments?.some(a => currentUserMemberIds.includes(a.memberId)) &&
        task.status === TaskStatus.ACTIVE &&
        !sortedCompletions.some(c => c.status === CompletionStatus.APPROVED) &&
        !overdue.includes(task) &&
        !dueSoon.includes(task) &&
        !pending.includes(task)
      );
    });

    // Deduplikujemy zadania między kategoriami (zadanie może być w kilku kategoriach)
    const allPriorityTasks = [...overdue, ...dueSoon, ...pending, ...myActiveTasks];
    const uniquePriorityTasks = allPriorityTasks.reduce(
      (acc, task) => {
        if (!acc.find(t => t.id === task.id)) {
          acc.push(task);
        }
        return acc;
      },
      [] as typeof allPriorityTasks
    );

    return uniquePriorityTasks.slice(0, priorityTaskLimit);
  }, [tasks, currentUserMemberIds, priorityTaskLimit, filterTasks]);

  const displayName =
    currentUser.firstName && currentUser.lastName
      ? `${currentUser.firstName} ${currentUser.lastName}`
      : currentUser.firstName || currentUser.lastName || 'Użytkownik';

  if (loading) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Ładowanie zadań...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <h3 className="text-lg font-semibold text-red-900 mb-2">Wystąpił błąd</h3>
          <p className="text-red-700 mb-4">{error}</p>
          <Button onClick={refetch} variant="outline">
            Spróbuj ponownie
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Moje zadania</h1>
          <p className="text-gray-600">Przegląd zadań ze wszystkich Twoich rodzin</p>
        </div>
      </div>

      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">Aktywne zadania</p>
                  <p className="text-2xl font-bold">{stats.activeTasks}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600">Ukończone</p>
                  <p className="text-2xl font-bold">{stats.completedTasks}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm text-gray-600">Przeterminowane</p>
                  <p className="text-2xl font-bold">{stats.overdueTasks}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Home className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-600">Rodziny</p>
                  <p className="text-2xl font-bold">{stats.familiesCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Progress Bar */}
      {stats && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Postęp ogólny</span>
                <span>
                  {stats.completedTasks} z {stats.totalTasks} zadań
                </span>
              </div>
              <Progress
                value={stats.totalTasks > 0 ? (stats.completedTasks / stats.totalTasks) * 100 : 0}
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Task Categories */}
      <TaskCategories
        familyId="" // Not applicable for user dashboard
        tasks={tasks}
        currentMember={currentMember}
        onFilterChange={setTaskFilters}
        className="mb-6"
      />

      {/* Priority Tasks */}
      <PriorityTasksSection
        priorityTasks={priorityTasks}
        priorityTaskLimit={priorityTaskLimit}
        setPriorityTaskLimit={setPriorityTaskLimit}
        taskFilters={taskFilters}
        setTaskFilters={setTaskFilters}
        familyMembers={allFamilyMembers}
        currentMember={currentMember}
        canManageTasks={true}
        canAssignTasks={true}
        canCompleteTasks={true}
        onTaskUpdate={refetch}
        showFamilyBadge={true}
      />

      {/* niepotrzebne */}
      {/* When no tasks */}
      {/* {tasks.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <CheckCircle className="h-16 w-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Brak zadań</h3>
            <p className="text-gray-600 mb-4">Nie masz jeszcze żadnych zadań w swoich rodzinach.</p>
            {families.length > 0 && (
              <Button asChild>
                <a href={`/dashboard/${families[0].family.id}/tasks`}>
                  <Plus className="mr-2 h-4 w-4" />
                  Przejdź do zadań rodziny
                </a>
              </Button>
            )}
          </CardContent>
        </Card>
      )} */}
    </div>
  );
}
