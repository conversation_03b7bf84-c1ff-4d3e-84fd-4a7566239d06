'use client';

import { useMemo } from 'react';
import { TaskStatus, CompletionStatus, FamilyMember, User } from '@prisma/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, Clock, AlertCircle, Target, Users, Pause, ArrowRight } from 'lucide-react';
import { TaskWithRelations, TaskFilters } from '@/hooks/useTasks';

interface TaskCategoriesProps {
  familyId: string;
  tasks: TaskWithRelations[];
  currentMember: FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  };
  onFilterChange?: (filters: TaskFilters) => void;
  className?: string;
}

interface TaskCategory {
  id: string;
  title: string;
  description: string;
  count: number;
  color: string;
  bgColor: string;
  borderColor: string;
  icon: React.ReactNode;
  filter: TaskFilters;
  priority: number; // Lower number = higher priority
}

export function TaskCategories({
  familyId,
  tasks,
  currentMember,
  onFilterChange,
  className,
}: TaskCategoriesProps) {
  const categories = useMemo((): TaskCategory[] => {
    const now = new Date();

    // Przeterminowane zadania (najwyższy priorytet)
    const overdueTasks = tasks.filter(
      task =>
        task.status === TaskStatus.ACTIVE &&
        task.dueDate &&
        new Date(task.dueDate) < now &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );

    // Zadania oczekujące na weryfikację
    const pendingVerificationTasks = tasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.PENDING)
    );

    // Zadania wkrótce do wykonania (w ciągu 24 godzin)
    const dueSoonTasks = tasks.filter(task => {
      if (!task.dueDate || task.status !== TaskStatus.ACTIVE) return false;
      if (task.completions.some(c => c.status === CompletionStatus.APPROVED)) return false;
      if (overdueTasks.includes(task)) return false; // Już policzone jako przeterminowane

      const hoursUntilDue = (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
      return hoursUntilDue <= 24 && hoursUntilDue > 0;
    });

    // Moje aktywne zadania
    const myActiveTasks = tasks.filter(
      task =>
        task.assignments?.some(a => a.memberId === currentMember.id) &&
        task.status === TaskStatus.ACTIVE &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED) &&
        !overdueTasks.includes(task) &&
        !dueSoonTasks.includes(task)
    );

    // Zadania ukończone (zatwierdzone)
    const completedTasks = tasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );

    // Zadania zawieszone
    const suspendedTasks = tasks.filter(task => task.status === TaskStatus.SUSPENDED);

    // Zadania nieprzypisane
    const unassignedTasks = tasks.filter(
      task =>
        task.status === TaskStatus.ACTIVE &&
        (!task.assignments || task.assignments.length === 0) &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED) &&
        !overdueTasks.includes(task) &&
        !dueSoonTasks.includes(task)
    );

    return [
      {
        id: 'overdue',
        title: 'Pilne',
        description: 'Przeterminowane zadania',
        count: overdueTasks.length,
        color: 'text-red-700',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
        icon: <AlertCircle className="h-5 w-5 text-red-600" />,
        filter: { completionStatus: ['overdue'] },
        priority: 1,
      },
      {
        id: 'due_soon',
        title: 'Wkrótce',
        description: 'Kończą się dziś',
        count: dueSoonTasks.length,
        color: 'text-orange-700',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
        icon: <Clock className="h-5 w-5 text-orange-600" />,
        filter: { completionStatus: ['due_soon'] },
        priority: 2,
      },
      {
        id: 'pending',
        title: 'Do weryfikacji',
        description: 'Oczekują zatwierdzenia',
        count: pendingVerificationTasks.length,
        color: 'text-yellow-700',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
        icon: <Clock className="h-5 w-5 text-yellow-600" />,
        filter: { completionStatus: ['pending'] },
        priority: 3,
      },
      {
        id: 'my_tasks',
        title: 'Moje zadania',
        description: 'Przypisane do mnie',
        count: myActiveTasks.length,
        color: 'text-blue-700',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
        icon: <Users className="h-5 w-5 text-blue-600" />,
        filter: {
          assignedMemberIds: [currentMember.id],
          status: [TaskStatus.ACTIVE],
          includeCompleted: false,
        },
        priority: 4,
      },
      {
        id: 'unassigned',
        title: 'Nieprzypisane',
        description: 'Wymagają przypisania',
        count: unassignedTasks.length,
        color: 'text-gray-700',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        icon: <Target className="h-5 w-5 text-gray-600" />,
        filter: {
          assignedMemberIds: [], // Puste oznacza nieprzypisane
          status: [TaskStatus.ACTIVE],
          includeCompleted: false,
        },
        priority: 5,
      },
      {
        id: 'completed',
        title: 'Ukończone',
        description: 'Zatwierdzone zadania',
        count: completedTasks.length,
        color: 'text-green-700',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        icon: <CheckCircle className="h-5 w-5 text-green-600" />,
        filter: { completionStatus: ['approved'], includeCompleted: true },
        priority: 6,
      },
      {
        id: 'suspended',
        title: 'Zawieszone',
        description: 'Tymczasowo wstrzymane',
        count: suspendedTasks.length,
        color: 'text-purple-700',
        bgColor: 'bg-purple-50',
        borderColor: 'border-purple-200',
        icon: <Pause className="h-5 w-5 text-purple-600" />,
        filter: { status: [TaskStatus.SUSPENDED] },
        priority: 7,
      },
    ]
      .filter(category => category.count > 0) // Pokaż tylko kategorie z zadaniami
      .sort((a, b) => a.priority - b.priority); // Sortuj według priorytetu
  }, [tasks, currentMember.id]);

  const handleCategoryClick = (category: TaskCategory) => {
    if (onFilterChange) {
      onFilterChange(category.filter);
    }
  };

  const totalTasks = tasks.length;
  const activeTasks = tasks.filter(
    task =>
      task.status === TaskStatus.ACTIVE &&
      !task.completions.some(c => c.status === CompletionStatus.APPROVED)
  ).length;

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Kategorie zadań
          </CardTitle>
          <p className="text-sm text-gray-600">
            {activeTasks} aktywnych z {totalTasks} zadań
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
            {categories.map(category => (
              <Button
                key={category.id}
                variant="outline"
                className={`
                  h-auto p-4 flex flex-col items-start gap-2 text-left transition-all
                  ${category.bgColor} ${category.borderColor} ${category.color}
                  hover:shadow-md hover:scale-[1.02] border-2
                `}
                onClick={() => handleCategoryClick(category)}
              >
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-2">
                    {category.icon}
                    <span className="font-medium">{category.title}</span>
                  </div>
                  <Badge
                    variant="secondary"
                    className={`${category.bgColor} ${category.color} border-0`}
                  >
                    {category.count}
                  </Badge>
                </div>
                <div className="flex items-center justify-between w-full">
                  <span className="text-xs opacity-80">{category.description}</span>
                  <ArrowRight className="h-3 w-3 opacity-50" />
                </div>
              </Button>
            ))}
          </div>

          {categories.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Wszystkie zadania pod kontrolą!</p>
              <p className="text-sm">Nie ma zadań wymagających natychmiastowej uwagi.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
