'use client';

import { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import {
  Filter,
  X,
  CalendarDays,
  Search,
  RotateCcw,
  CheckCircle,
  Clock,
  AlertCircle,
  Target,
  Star,
  AlertTriangle,
  Trophy,
  Users,
  Repeat,
} from 'lucide-react';
import { TaskStatus, TaskWeight, TaskFrequency, CompletionStatus } from '@prisma/client';
import { FamilyMember, User } from '@prisma/client';

export interface TaskFilters {
  status?: TaskStatus[];
  assignedMemberIds?: string[];
  dueDate?: {
    from?: Date;
    to?: Date;
  };
  weight?: TaskWeight[];
  frequency?: TaskFrequency[];
  search?: string;
  // Nowe filtry dla kategorii
  completionStatus?: ('pending' | 'approved' | 'rejected' | 'overdue' | 'due_soon')[];
  pointsRange?: {
    min?: number;
    max?: number;
  };
  includeCompleted?: boolean;
}

interface TaskFiltersProps {
  filters: TaskFilters;
  onFiltersChange: (filters: TaskFilters) => void;
  familyMembers: (FamilyMember & { user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'> })[];
  className?: string;
}

const statusOptions = [
  { value: 'ACTIVE', label: 'Aktywne', icon: <CheckCircle className="h-4 w-4" /> },
  { value: 'COMPLETED', label: 'Ukończone', icon: <CheckCircle className="h-4 w-4" /> },
  { value: 'SUSPENDED', label: 'Zawieszone', icon: <Clock className="h-4 w-4" /> },
  { value: 'ARCHIVED', label: 'Zarchiwizowane', icon: <X className="h-4 w-4" /> },
];

const completionStatusOptions = [
  {
    value: 'pending',
    label: 'Oczekujące weryfikację',
    icon: <Clock className="h-4 w-4" />,
    color: 'text-orange-600',
  },
  {
    value: 'approved',
    label: 'Zatwierdzone',
    icon: <CheckCircle className="h-4 w-4" />,
    color: 'text-green-600',
  },
  { value: 'rejected', label: 'Odrzucone', icon: <X className="h-4 w-4" />, color: 'text-red-600' },
  {
    value: 'overdue',
    label: 'Przeterminowane',
    icon: <AlertCircle className="h-4 w-4" />,
    color: 'text-red-600',
  },
  {
    value: 'due_soon',
    label: 'Wkrótce termin',
    icon: <AlertCircle className="h-4 w-4" />,
    color: 'text-orange-600',
  },
];

const weightOptions = [
  {
    value: 'LIGHT',
    label: 'Lekkie',
    icon: <Target className="h-4 w-4" />,
    color: 'text-green-600',
  },
  {
    value: 'NORMAL',
    label: 'Normalne',
    icon: <Star className="h-4 w-4" />,
    color: 'text-blue-600',
  },
  {
    value: 'HEAVY',
    label: 'Ciężkie',
    icon: <AlertTriangle className="h-4 w-4" />,
    color: 'text-orange-600',
  },
  {
    value: 'CRITICAL',
    label: 'Krytyczne',
    icon: <AlertCircle className="h-4 w-4" />,
    color: 'text-red-600',
  },
];

const frequencyOptions = [
  { value: 'ONCE', label: 'Jednorazowe', icon: <Target className="h-4 w-4" /> },
  { value: 'DAILY', label: 'Codziennie', icon: <Repeat className="h-4 w-4" /> },
  { value: 'WEEKLY', label: 'Tygodniowo', icon: <Repeat className="h-4 w-4" /> },
  { value: 'MONTHLY', label: 'Miesięcznie', icon: <Repeat className="h-4 w-4" /> },
  { value: 'YEARLY', label: 'Rocznie', icon: <Repeat className="h-4 w-4" /> },
  { value: 'CUSTOM', label: 'Niestandardowe', icon: <Repeat className="h-4 w-4" /> },
];

export function TaskFilters({
  filters,
  onFiltersChange,
  familyMembers,
  className,
}: TaskFiltersProps) {
  const [statusOpen, setStatusOpen] = useState(false);
  const [completionStatusOpen, setCompletionStatusOpen] = useState(false);
  const [memberOpen, setMemberOpen] = useState(false);
  const [weightOpen, setWeightOpen] = useState(false);
  const [frequencyOpen, setFrequencyOpen] = useState(false);
  const [dateRangeOpen, setDateRangeOpen] = useState(false);
  const [pointsRangeOpen, setPointsRangeOpen] = useState(false);

  const updateFilters = (updates: Partial<TaskFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const clearFilters = () => {
    onFiltersChange({ includeCompleted: false });
  };

  const getMemberDisplayName = (
    member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
  ) => {
    if (member?.user?.firstName && member?.user?.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    if (member?.user?.email) {
      return member.user.email.split('@')[0];
    }
    return 'Nieznany użytkownik';
  };

  const hasActiveFilters =
    filters.status?.length ||
    filters.completionStatus?.length ||
    filters.assignedMemberIds?.length ||
    filters.weight?.length ||
    filters.frequency?.length ||
    filters.search ||
    filters.dueDate?.from ||
    filters.dueDate?.to ||
    filters.pointsRange?.min ||
    filters.pointsRange?.max ||
    filters.includeCompleted;

  const activeFiltersCount = [
    filters.status?.length,
    filters.completionStatus?.length,
    filters.assignedMemberIds?.length,
    filters.weight?.length,
    filters.frequency?.length,
    filters.search ? 1 : 0,
    filters.dueDate?.from ? 1 : 0,
    filters.dueDate?.to ? 1 : 0,
    filters.pointsRange?.min || filters.pointsRange?.max ? 1 : 0,
    filters.includeCompleted ? 1 : 0,
  ]
    .filter(Boolean)
    .reduce((sum, count) => sum + count, 0);

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Szukaj zadań..."
          value={filters.search || ''}
          onChange={e => updateFilters({ search: e.target.value || undefined })}
          className="pl-9 w-64"
        />
      </div>

      {/* Status Filter */}
      <Popover open={statusOpen} onOpenChange={setStatusOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Filter className="mr-2 h-4 w-4" />
            Status
            {filters.status?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.status.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj statusu..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono statusu.</CommandEmpty>
              <CommandGroup>
                {statusOptions.map(option => (
                  <div
                    key={option.value}
                    className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  >
                    <Checkbox
                      checked={filters.status?.includes(option.value as TaskStatus) || false}
                      onCheckedChange={checked => {
                        const currentStatus = filters.status || [];
                        if (checked) {
                          updateFilters({
                            status: [...currentStatus, option.value as TaskStatus],
                          });
                        } else {
                          updateFilters({
                            status: currentStatus.filter(s => s !== option.value),
                          });
                        }
                      }}
                    />
                    <div className="ml-2 flex items-center gap-2">
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                  </div>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Completion Status Filter */}
      <Popover open={completionStatusOpen} onOpenChange={setCompletionStatusOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Clock className="mr-2 h-4 w-4" />
            Stan realizacji
            {filters.completionStatus?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.completionStatus.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj stanu..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono stanu.</CommandEmpty>
              <CommandGroup>
                {completionStatusOptions.map(option => (
                  <div
                    key={option.value}
                    className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  >
                    <Checkbox
                      checked={filters.completionStatus?.includes(option.value as any) || false}
                      onCheckedChange={checked => {
                        const currentStatus = filters.completionStatus || [];
                        if (checked) {
                          updateFilters({
                            completionStatus: [...currentStatus, option.value as any],
                          });
                        } else {
                          updateFilters({
                            completionStatus: currentStatus.filter(s => s !== option.value),
                          });
                        }
                      }}
                    />
                    <div className={`ml-2 flex items-center gap-2 ${option.color}`}>
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                  </div>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Member Filter */}
      <Popover open={memberOpen} onOpenChange={setMemberOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Users className="mr-2 h-4 w-4" />
            Przypisane do
            {filters.assignedMemberIds?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.assignedMemberIds.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj członka..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono członka.</CommandEmpty>
              <CommandGroup>
                {familyMembers.map(member => (
                  <div
                    key={member.id}
                    className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  >
                    <Checkbox
                      checked={filters.assignedMemberIds?.includes(member.id) || false}
                      onCheckedChange={checked => {
                        const currentMembers = filters.assignedMemberIds || [];
                        if (checked) {
                          updateFilters({
                            assignedMemberIds: [...currentMembers, member.id],
                          });
                        } else {
                          updateFilters({
                            assignedMemberIds: currentMembers.filter(m => m !== member.id),
                          });
                        }
                      }}
                    />
                    <span className="ml-2">{getMemberDisplayName(member)}</span>
                  </div>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Weight Filter */}
      <Popover open={weightOpen} onOpenChange={setWeightOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Target className="mr-2 h-4 w-4" />
            Waga
            {filters.weight?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.weight.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-48 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj wagi..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono wagi.</CommandEmpty>
              <CommandGroup>
                {weightOptions.map(option => (
                  <div
                    key={option.value}
                    className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  >
                    <Checkbox
                      checked={filters.weight?.includes(option.value as TaskWeight) || false}
                      onCheckedChange={checked => {
                        const currentWeight = filters.weight || [];
                        if (checked) {
                          updateFilters({
                            weight: [...currentWeight, option.value as TaskWeight],
                          });
                        } else {
                          updateFilters({
                            weight: currentWeight.filter(w => w !== option.value),
                          });
                        }
                      }}
                    />
                    <div className={`ml-2 flex items-center gap-2 ${option.color}`}>
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                  </div>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Frequency Filter */}
      <Popover open={frequencyOpen} onOpenChange={setFrequencyOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Repeat className="mr-2 h-4 w-4" />
            Częstotliwość
            {filters.frequency?.length ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                {filters.frequency.length}
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-56 p-0" align="start">
          <Command>
            <CommandInput placeholder="Szukaj częstotliwości..." />
            <CommandList>
              <CommandEmpty>Nie znaleziono częstotliwości.</CommandEmpty>
              <CommandGroup>
                {frequencyOptions.map(option => (
                  <div
                    key={option.value}
                    className="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-accent hover:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
                  >
                    <Checkbox
                      checked={filters.frequency?.includes(option.value as TaskFrequency) || false}
                      onCheckedChange={checked => {
                        const currentFrequency = filters.frequency || [];
                        if (checked) {
                          updateFilters({
                            frequency: [...currentFrequency, option.value as TaskFrequency],
                          });
                        } else {
                          updateFilters({
                            frequency: currentFrequency.filter(f => f !== option.value),
                          });
                        }
                      }}
                    />
                    <div className="ml-2 flex items-center gap-2">
                      {option.icon}
                      <span>{option.label}</span>
                    </div>
                  </div>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Date Range Filter */}
      <Popover open={dateRangeOpen} onOpenChange={setDateRangeOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <CalendarDays className="mr-2 h-4 w-4" />
            Termin
            {filters.dueDate?.from || filters.dueDate?.to ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                1
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-4" align="start">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Data początkowa</label>
              <Calendar
                mode="single"
                selected={filters.dueDate?.from}
                onSelect={date =>
                  updateFilters({
                    dueDate: { ...filters.dueDate, from: date },
                  })
                }
                className="rounded-md border"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Data końcowa</label>
              <Calendar
                mode="single"
                selected={filters.dueDate?.to}
                onSelect={date =>
                  updateFilters({
                    dueDate: { ...filters.dueDate, to: date },
                  })
                }
                className="rounded-md border"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateFilters({ dueDate: undefined })}
              >
                Wyczyść
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Points Range Filter */}
      <Popover open={pointsRangeOpen} onOpenChange={setPointsRangeOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" className="border-dashed">
            <Trophy className="mr-2 h-4 w-4" />
            Punkty
            {filters.pointsRange?.min || filters.pointsRange?.max ? (
              <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
                1
              </Badge>
            ) : null}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-64 p-4" align="start">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Minimum punktów</label>
              <Input
                type="number"
                placeholder="0"
                value={filters.pointsRange?.min || ''}
                onChange={e =>
                  updateFilters({
                    pointsRange: {
                      ...filters.pointsRange,
                      min: e.target.value ? parseInt(e.target.value) : undefined,
                    },
                  })
                }
              />
            </div>
            <div>
              <label className="text-sm font-medium">Maksimum punktów</label>
              <Input
                type="number"
                placeholder="100"
                value={filters.pointsRange?.max || ''}
                onChange={e =>
                  updateFilters({
                    pointsRange: {
                      ...filters.pointsRange,
                      max: e.target.value ? parseInt(e.target.value) : undefined,
                    },
                  })
                }
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => updateFilters({ pointsRange: undefined })}
              >
                Wyczyść
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Include Completed Toggle */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="includeCompleted"
          checked={filters.includeCompleted}
          onCheckedChange={checked => updateFilters({ includeCompleted: !!checked })}
        />
        <label htmlFor="includeCompleted" className="text-sm">
          Pokaż ukończone
        </label>
      </div>

      {/* Clear All Filters */}
      {hasActiveFilters && (
        <Button variant="ghost" size="sm" onClick={clearFilters} className="h-8 px-2 lg:px-3">
          <RotateCcw className="mr-2 h-4 w-4" />
          Wyczyść
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 px-1 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      )}
    </div>
  );
}
