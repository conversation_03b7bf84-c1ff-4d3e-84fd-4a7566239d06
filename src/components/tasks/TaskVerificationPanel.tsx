'use client';

import { useState, useMemo } from 'react';
import { CompletionStatus, FamilyMember, User } from '@prisma/client';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { TaskCard } from './TaskCard';
import { VerificationFilters } from './VerificationFilters';
import { BulkTaskActions } from './BulkTaskActions';
import { TaskWithRelations } from '@/hooks/useTasks';
import { useSuspensions } from '@/hooks/useSuspensions';
import { Clock, CheckCircle, AlertTriangle, Target } from 'lucide-react';

interface TaskVerificationPanelProps {
  familyId: string;
  tasks: TaskWithRelations[];
  currentMember: FamilyMember;
  familyMembers: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  canManageTasks: boolean;
  canAssignTasks: boolean;
  canCompleteTasks: boolean;
  onTaskUpdate?: () => void;
  updateTaskLocally?: (
    taskId: string,
    updater: (task: TaskWithRelations) => TaskWithRelations
  ) => void;
}

interface TaskCompletion {
  id: string;
  taskId: string;
  status: CompletionStatus;
  task: {
    id: string;
    title: string;
  };
}

export function TaskVerificationPanel({
  familyId,
  tasks,
  currentMember,
  familyMembers,
  canManageTasks,
  canAssignTasks,
  canCompleteTasks,
  onTaskUpdate,
  updateTaskLocally,
}: TaskVerificationPanelProps) {
  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [completionStatusFilter, setCompletionStatusFilter] = useState<CompletionStatus | 'all'>(
    'all'
  );
  const [sortBy, setSortBy] = useState<'completedAt' | 'points' | 'title' | 'memberName'>(
    'completedAt'
  );
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [dateFilter, setDateFilter] = useState<'today' | 'week' | 'month' | 'all'>('all');

  // Bulk selection states
  const [selectedCompletions, setSelectedCompletions] = useState<TaskCompletion[]>([]);
  const [bulkSelectMode, setBulkSelectMode] = useState(false);

  const { getTaskSuspensionStatus } = useSuspensions(familyId);

  // Get all tasks that have at least one completion
  const tasksWithCompletions = useMemo(() => {
    return tasks.filter(task => task.completions && task.completions.length > 0);
  }, [tasks]);

  // Filter and sort tasks
  const filteredAndSortedTasks = useMemo(() => {
    let filtered = [...tasksWithCompletions];

    // Filter by completion status
    if (completionStatusFilter !== 'all') {
      filtered = filtered.filter(task =>
        task.completions.some(c => c.status === completionStatusFilter)
      );
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(task => {
        const taskMatches =
          task.title.toLowerCase().includes(searchLower) ||
          task.description?.toLowerCase().includes(searchLower);

        const assignedMember = task.assignments?.[0]?.member;
        const memberMatches =
          assignedMember &&
          (assignedMember.user.firstName?.toLowerCase().includes(searchLower) ||
            assignedMember.user.lastName?.toLowerCase().includes(searchLower) ||
            assignedMember.user.email.toLowerCase().includes(searchLower));

        return taskMatches || memberMatches;
      });
    }

    // Filter by date
    if (dateFilter !== 'all') {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);

      filtered = filtered.filter(task => {
        const latestCompletion = task.completions.sort(
          (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )[0];

        if (!latestCompletion) return false;

        const completionDate = new Date(latestCompletion.createdAt);

        switch (dateFilter) {
          case 'today':
            return completionDate >= today;
          case 'week':
            return completionDate >= weekStart;
          case 'month':
            return completionDate >= monthStart;
          default:
            return true;
        }
      });
    }

    // Sort tasks
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'completedAt':
          const aCompletion = a.completions.sort(
            (x, y) => new Date(y.createdAt).getTime() - new Date(x.createdAt).getTime()
          )[0];
          const bCompletion = b.completions.sort(
            (x, y) => new Date(y.createdAt).getTime() - new Date(x.createdAt).getTime()
          )[0];

          if (aCompletion && bCompletion) {
            comparison =
              new Date(aCompletion.createdAt).getTime() - new Date(bCompletion.createdAt).getTime();
          }
          break;

        case 'points':
          comparison = a.points - b.points;
          break;

        case 'title':
          comparison = a.title.localeCompare(b.title, 'pl');
          break;

        case 'memberName':
          const aMember = a.assignments?.[0]?.member;
          const bMember = b.assignments?.[0]?.member;
          const aName = aMember
            ? `${aMember.user.firstName || ''} ${aMember.user.lastName || ''}`.trim() ||
              aMember.user.email
            : '';
          const bName = bMember
            ? `${bMember.user.firstName || ''} ${bMember.user.lastName || ''}`.trim() ||
              bMember.user.email
            : '';
          comparison = aName.localeCompare(bName, 'pl');
          break;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [tasksWithCompletions, searchTerm, completionStatusFilter, sortBy, sortOrder, dateFilter]);

  // Calculate stats
  const stats = useMemo(() => {
    const allCompletions = tasksWithCompletions.flatMap(task => task.completions);
    return {
      pending: allCompletions.filter(c => c.status === CompletionStatus.PENDING).length,
      approved: allCompletions.filter(c => c.status === CompletionStatus.APPROVED).length,
      rejected: allCompletions.filter(c => c.status === CompletionStatus.REJECTED).length,
      total: allCompletions.length,
    };
  }, [tasksWithCompletions]);

  // Bulk selection functions
  const allPendingCompletions = useMemo(() => {
    return filteredAndSortedTasks.flatMap(task =>
      task.completions
        .filter(c => c.status === CompletionStatus.PENDING)
        .map(c => ({
          id: c.id,
          taskId: task.id,
          status: c.status,
          task: { id: task.id, title: task.title },
        }))
    );
  }, [filteredAndSortedTasks]);

  const handleToggleCompletion = (completionId: string, taskId: string, title: string) => {
    setSelectedCompletions(prev => {
      const exists = prev.find(c => c.id === completionId);
      if (exists) {
        return prev.filter(c => c.id !== completionId);
      } else {
        return [
          ...prev,
          {
            id: completionId,
            taskId,
            status: CompletionStatus.PENDING,
            task: { id: taskId, title },
          },
        ];
      }
    });
  };

  const handleSelectAllPending = () => {
    if (selectedCompletions.length === allPendingCompletions.length) {
      setSelectedCompletions([]);
    } else {
      setSelectedCompletions(allPendingCompletions);
    }
  };

  const handleClearSelection = () => {
    setSelectedCompletions([]);
    setBulkSelectMode(false);
  };

  if (tasksWithCompletions.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Clock className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Brak zadań do weryfikacji</h3>
          <p className="text-gray-600 text-center">
            Nie ma jeszcze żadnych ukończonych zadań oczekujących na weryfikację.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Panel weryfikacji zadań</h2>
          <p className="text-gray-600 mt-1">
            Zatwierdź lub odrzuć ukończone zadania członków rodziny
          </p>
        </div>

        {stats.pending > 0 && canManageTasks && (
          <Button
            onClick={() => setBulkSelectMode(!bulkSelectMode)}
            variant={bulkSelectMode ? 'default' : 'outline'}
          >
            {bulkSelectMode ? 'Anuluj zaznaczanie' : 'Zaznacz do weryfikacji'}
          </Button>
        )}
      </div>

      {/* Filters */}
      <VerificationFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        completionStatusFilter={completionStatusFilter}
        onCompletionStatusChange={setCompletionStatusFilter}
        sortBy={sortBy}
        onSortChange={setSortBy}
        sortOrder={sortOrder}
        onSortOrderChange={setSortOrder}
        dateFilter={dateFilter}
        onDateFilterChange={setDateFilter}
        stats={stats}
      />

      {/* Bulk selection header */}
      {bulkSelectMode && allPendingCompletions.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Checkbox
                checked={
                  selectedCompletions.length === allPendingCompletions.length &&
                  allPendingCompletions.length > 0
                }
                onCheckedChange={handleSelectAllPending}
                id="select-all-verification"
              />
              <label
                htmlFor="select-all-verification"
                className="text-sm font-medium text-blue-900 cursor-pointer"
              >
                Zaznacz wszystkie oczekujące zadania ({allPendingCompletions.length})
              </label>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk actions */}
      <BulkTaskActions
        familyId={familyId}
        selectedCompletions={selectedCompletions}
        onClearSelection={handleClearSelection}
        onActionComplete={onTaskUpdate || (() => {})}
        canVerifyTasks={canManageTasks}
      />

      {/* Tasks grid */}
      <div className="space-y-4">
        {filteredAndSortedTasks.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Target className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Brak wyników</h3>
              <p className="text-gray-600 text-center">
                Nie znaleziono zadań spełniających wybrane kryteria.
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredAndSortedTasks.map((task, index) => {
            const suspensionInfo = getTaskSuspensionStatus(
              task.id,
              task.assignments?.[0]?.memberId
            );

            const pendingCompletion = task.completions.find(
              c => c.status === CompletionStatus.PENDING
            );
            const isTaskSelected = pendingCompletion
              ? selectedCompletions.some(sc => sc.id === pendingCompletion.id)
              : false;

            return (
              <TaskCard
                key={task.id}
                task={task}
                currentMember={currentMember}
                canManageTasks={canManageTasks}
                canAssignTasks={canAssignTasks}
                canCompleteTasks={canCompleteTasks}
                suspensionInfo={suspensionInfo}
                onTaskUpdate={onTaskUpdate}
                updateTaskLocally={updateTaskLocally}
                bulkSelectMode={bulkSelectMode}
                isSelected={isTaskSelected}
                onToggleSelection={
                  pendingCompletion
                    ? () => handleToggleCompletion(pendingCompletion.id, task.id, task.title)
                    : undefined
                }
              />
            );
          })
        )}
      </div>
    </div>
  );
}
