/**
 * Dialog do edycji zadań cyklicznych z opcjami:
 * - <PERSON><PERSON><PERSON><PERSON> tylko to wystąpienie
 * - Edytuj całą serię
 */

import React, { useState } from 'react';
import { CalendarTask } from '@/lib/validations/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, CalendarCheck, Edit } from 'lucide-react';
import { format } from 'date-fns';
import { pl } from 'date-fns/locale';
import RecurringPatternIndicator from '../calendar/RecurringPatternIndicator';

interface EditRecurringTaskDialogProps {
  isOpen: boolean;
  onClose: () => void;
  task: CalendarTask;
  onEditOccurrence: (task: CalendarTask) => void;
  onEditSeries: (originalTaskId: string) => void;
}

export default function EditRecurringTaskDialog({
  isOpen,
  onClose,
  task,
  onEditOccurrence,
  onEditSeries,
}: EditRecurringTaskDialogProps) {
  const [selectedOption, setSelectedOption] = useState<'occurrence' | 'series' | null>(null);

  const handleConfirm = () => {
    if (selectedOption === 'occurrence') {
      onEditOccurrence(task);
    } else if (selectedOption === 'series') {
      onEditSeries(task.originalTaskId || task.id);
    }
    onClose();
  };

  const formatTaskDate = (date: Date) => {
    return format(date, 'PPP', { locale: pl });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Edycja zadania cyklicznego
          </DialogTitle>
          <DialogDescription>
            To zadanie jest częścią serii cyklicznej. Wybierz zakres edycji.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Task info */}
          <Card className="p-3 bg-muted/30">
            <div className="space-y-2">
              <div className="font-medium text-sm">{task.title}</div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {formatTaskDate(task.start)}
                </Badge>
                {task.frequency && (
                  <RecurringPatternIndicator
                    frequency={task.frequency}
                    interval={task.recurringInterval}
                    size="sm"
                    showLabel={false}
                  />
                )}
              </div>
            </div>
          </Card>

          {/* Edit options */}
          <div className="space-y-3">
            <Card
              className={`p-4 cursor-pointer transition-all border-2 ${
                selectedOption === 'occurrence'
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50'
              }`}
              onClick={() => setSelectedOption('occurrence')}
            >
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  <CalendarCheck className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">Edytuj tylko to wystąpienie</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Zmieni tylko zadanie na dzień {formatTaskDate(task.start)}. Pozostałe
                    wystąpienia pozostaną bez zmian.
                  </div>
                </div>
              </div>
            </Card>

            <Card
              className={`p-4 cursor-pointer transition-all border-2 ${
                selectedOption === 'series'
                  ? 'border-primary bg-primary/5'
                  : 'border-border hover:border-primary/50'
              }`}
              onClick={() => setSelectedOption('series')}
            >
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  <Calendar className="w-5 h-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="font-medium text-sm">Edytuj całą serię</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    Zmieni wszystkie przyszłe wystąpienia tego zadania. Przeszłe wystąpienia
                    pozostaną bez zmian.
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Warning for series edit */}
          {selectedOption === 'series' && (
            <Card className="p-3 bg-orange-50 dark:bg-orange-950/20 border-orange-200">
              <div className="flex items-start gap-2">
                <div className="w-4 h-4 rounded-full bg-orange-500 flex-shrink-0 mt-0.5">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="text-sm text-orange-900 dark:text-orange-100">
                  <div className="font-medium">Uwaga</div>
                  <div className="text-xs mt-1">
                    Edycja całej serii wpłynie na wszystkie przyszłe wystąpienia tego zadania.
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={onClose}>
            Anuluj
          </Button>
          <Button onClick={handleConfirm} disabled={!selectedOption}>
            {selectedOption === 'occurrence'
              ? 'Edytuj wystąpienie'
              : selectedOption === 'series'
                ? 'Edytuj serię'
                : 'Wybierz opcję'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
