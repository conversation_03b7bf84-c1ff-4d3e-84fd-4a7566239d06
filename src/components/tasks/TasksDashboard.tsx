'use client';

import { useMemo, useState } from 'react';
import {
  Task,
  TaskStatus,
  FamilyMember,
  User,
  TaskCompletion,
  CompletionStatus,
} from '@prisma/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Trophy,
  Target,
  Users,
  TrendingUp,
  // Calendar,
  Plus,
} from 'lucide-react';
import { TaskCard } from './TaskCard';
import { TaskDialog } from './TaskDialog';
import { TaskWithRelations, TaskFilters } from '@/hooks/useTasks';
import { TaskFilters as TaskFiltersComponent } from './TaskFilters';
import { TaskCategories } from './TaskCategories';
import { PriorityTasksSection } from './PriorityTasksSection';

interface TasksDashboardProps {
  familyId: string;
  tasks: TaskWithRelations[];
  currentMember: FamilyMember;
  familyMembers: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  canManageTasks: boolean;
  canAssignTasks: boolean;
  canCompleteTasks: boolean;
  onTaskUpdate?: () => void;
  updateTaskLocally?: (
    taskId: string,
    updater: (task: TaskWithRelations) => TaskWithRelations
  ) => void;
}

export function TasksDashboard({
  familyId,
  tasks,
  currentMember,
  familyMembers,
  canManageTasks,
  canAssignTasks,
  canCompleteTasks,
  onTaskUpdate,
  updateTaskLocally,
}: TasksDashboardProps) {
  const [priorityTaskLimit, setPriorityTaskLimit] = useState(6);
  const [taskFilters, setTaskFilters] = useState<TaskFilters>({ includeCompleted: false });

  // Function to filter tasks based on current filters
  const filterTasks = useMemo(() => {
    return (tasksToFilter: TaskWithRelations[]) => {
      const now = new Date();

      return tasksToFilter.filter(task => {
        // Search filter
        if (taskFilters.search) {
          const searchLower = taskFilters.search.toLowerCase();
          if (
            !task.title.toLowerCase().includes(searchLower) &&
            !task.description?.toLowerCase().includes(searchLower)
          ) {
            return false;
          }
        }

        // Status filter
        if (taskFilters.status && taskFilters.status.length > 0) {
          if (!taskFilters.status.includes(task.status)) {
            return false;
          }
        }

        // Assigned members filter
        if (taskFilters.assignedMemberIds && taskFilters.assignedMemberIds.length > 0) {
          const isAssigned =
            task.assignments?.some(a => taskFilters.assignedMemberIds!.includes(a.memberId)) ||
            false;
          if (!isAssigned) {
            return false;
          }
        }

        // Weight filter
        if (taskFilters.weight && taskFilters.weight.length > 0) {
          if (!taskFilters.weight.includes(task.weight)) {
            return false;
          }
        }

        // Frequency filter
        if (taskFilters.frequency && taskFilters.frequency.length > 0) {
          if (!taskFilters.frequency.includes(task.frequency)) {
            return false;
          }
        }

        // Completion status filter
        if (taskFilters.completionStatus && taskFilters.completionStatus.length > 0) {
          const approvedCompletion = task.completions.find(
            c => c.status === CompletionStatus.APPROVED
          );
          const pendingCompletion = task.completions.find(
            c => c.status === CompletionStatus.PENDING
          );
          const rejectedCompletion = task.completions.find(
            c => c.status === CompletionStatus.REJECTED
          );

          const isOverdue =
            task.status === TaskStatus.ACTIVE &&
            task.dueDate &&
            new Date(task.dueDate) < now &&
            !approvedCompletion;

          const isDueSoon =
            task.status === TaskStatus.ACTIVE &&
            task.dueDate &&
            !approvedCompletion &&
            (() => {
              const hoursUntilDue =
                (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
              return hoursUntilDue <= 24 && hoursUntilDue > 0;
            })();

          const hasMatchStatus = taskFilters.completionStatus.some(status => {
            switch (status) {
              case 'approved':
                return !!approvedCompletion;
              case 'pending':
                return !!pendingCompletion;
              case 'rejected':
                return !!rejectedCompletion;
              case 'overdue':
                return isOverdue;
              case 'due_soon':
                return isDueSoon;
              default:
                return false;
            }
          });

          if (!hasMatchStatus) {
            return false;
          }
        }

        // Points range filter
        if (taskFilters.pointsRange) {
          if (
            taskFilters.pointsRange.min !== undefined &&
            task.points < taskFilters.pointsRange.min
          ) {
            return false;
          }
          if (
            taskFilters.pointsRange.max !== undefined &&
            task.points > taskFilters.pointsRange.max
          ) {
            return false;
          }
        }

        // Due date filter
        if (taskFilters.dueDate) {
          if (
            taskFilters.dueDate.from &&
            task.dueDate &&
            new Date(task.dueDate) < taskFilters.dueDate.from
          ) {
            return false;
          }
          if (
            taskFilters.dueDate.to &&
            task.dueDate &&
            new Date(task.dueDate) > taskFilters.dueDate.to
          ) {
            return false;
          }
        }

        // Include completed filter
        if (!taskFilters.includeCompleted) {
          const hasApprovedCompletion = task.completions.some(
            c => c.status === CompletionStatus.APPROVED
          );
          if (hasApprovedCompletion) {
            return false;
          }
        }

        return true;
      });
    };
  }, [taskFilters]);

  const stats = useMemo(() => {
    const now = new Date();

    const activeTasks = tasks.filter(
      task =>
        task.status === TaskStatus.ACTIVE &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );

    const completedTasks = tasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );

    const overdueTasks = activeTasks.filter(task => task.dueDate && new Date(task.dueDate) < now);

    const pendingVerification = tasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.PENDING)
    );

    const dueSoonTasks = activeTasks.filter(task => {
      if (!task.dueDate) return false;
      const hoursUntilDue = (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
      return hoursUntilDue <= 24 && hoursUntilDue > 0;
    });

    const myTasks = activeTasks.filter(task =>
      task.assignments?.some(a => a.memberId === currentMember.id)
    );
    const unassignedTasks = activeTasks.filter(
      task => !task.assignments || task.assignments.length === 0
    );

    const completionRate = tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0;

    return {
      total: tasks.length,
      active: activeTasks.length,
      completed: completedTasks.length,
      overdue: overdueTasks.length,
      pendingVerification: pendingVerification.length,
      dueSoon: dueSoonTasks.length,
      myTasks: myTasks.length,
      unassigned: unassignedTasks.length,
      completionRate,
    };
  }, [tasks, currentMember.id]);

  const priorityTasks = useMemo(() => {
    const now = new Date();

    // Apply filters first
    const filteredTasks = filterTasks(tasks);

    // Priority: overdue > due soon > pending verification > my tasks
    const overdue = filteredTasks.filter(
      task =>
        task.status === TaskStatus.ACTIVE &&
        task.dueDate &&
        new Date(task.dueDate) < now &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );

    const dueSoon = filteredTasks.filter(task => {
      if (!task.dueDate || task.status !== TaskStatus.ACTIVE) return false;
      if (task.completions.some(c => c.status === CompletionStatus.APPROVED)) return false;
      const hoursUntilDue = (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
      return hoursUntilDue <= 24 && hoursUntilDue > 0;
    });

    const pending = filteredTasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.PENDING)
    );

    const myActiveTasks = filteredTasks.filter(
      task =>
        task.assignments?.some(a => a.memberId === currentMember.id) &&
        task.status === TaskStatus.ACTIVE &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED) &&
        !overdue.includes(task) &&
        !dueSoon.includes(task)
    );

    return [...overdue, ...dueSoon, ...pending, ...myActiveTasks].slice(0, priorityTaskLimit);
  }, [tasks, currentMember.id, priorityTaskLimit, filterTasks]);

  const getMemberDisplayName = (
    member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
  ) => {
    if (member?.user?.firstName && member?.user?.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    if (member?.user?.email) {
      return member.user.email.split('@')[0];
    }
    return 'Nieznany użytkownik';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard zadań</h1>
          <p className="text-gray-600">Przegląd aktywności i postępów w rodzinie</p>
        </div>
        {canManageTasks && (
          <TaskDialog
            familyId={familyId}
            familyMembers={familyMembers}
            mode="create"
            trigger={
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Dodaj zadanie
              </Button>
            }
            onTaskSuccess={onTaskUpdate}
          />
        )}
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Aktywne zadania</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Ukończone</p>
                <p className="text-2xl font-bold">{stats.completed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Przeterminowane</p>
                <p className="text-2xl font-bold">{stats.overdue}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Postęp</p>
                <p className="text-2xl font-bold">{Math.round(stats.completionRate)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Postęp rodziny</span>
              <span>
                {stats.completed} z {stats.total} zadań
              </span>
            </div>
            <Progress value={stats.completionRate} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5" />
              Moje zadania
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">{stats.myTasks}</div>
            <p className="text-sm text-gray-600">Przypisane do Ciebie</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Wymagają uwagi
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.pendingVerification > 0 && (
                <div className="flex justify-between">
                  <span className="text-sm">Do weryfikacji</span>
                  <Badge variant="outline">{stats.pendingVerification}</Badge>
                </div>
              )}
              {stats.dueSoon > 0 && (
                <div className="flex justify-between">
                  <span className="text-sm">Kończą się dziś</span>
                  <Badge variant="outline" className="bg-yellow-50 border-yellow-200">
                    {stats.dueSoon}
                  </Badge>
                </div>
              )}
              {stats.unassigned > 0 && (
                <div className="flex justify-between">
                  <span className="text-sm">Nieprzypisane</span>
                  <Badge variant="outline">{stats.unassigned}</Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Top wykonawcy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {familyMembers
                .sort((a, b) => b.points - a.points)
                .slice(0, 3)
                .map((member, index) => (
                  <div key={member.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">#{index + 1}</span>
                      <span className="text-sm">{getMemberDisplayName(member)}</span>
                    </div>
                    <span className="text-sm font-medium">{member.points} pkt</span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task Categories */}
      <TaskCategories
        familyId={familyId}
        tasks={tasks}
        currentMember={currentMember}
        onFilterChange={setTaskFilters}
        className="mb-6"
      />

      {/* Priority Tasks */}
      <PriorityTasksSection
        priorityTasks={priorityTasks}
        priorityTaskLimit={priorityTaskLimit}
        setPriorityTaskLimit={setPriorityTaskLimit}
        taskFilters={taskFilters}
        setTaskFilters={setTaskFilters}
        familyMembers={familyMembers}
        currentMember={currentMember}
        canManageTasks={canManageTasks}
        canAssignTasks={canAssignTasks}
        canCompleteTasks={canCompleteTasks}
        onTaskUpdate={onTaskUpdate}
        updateTaskLocally={updateTaskLocally}
        showFamilyBadge={false}
      />

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Szybkie akcje</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {canManageTasks && (
              <TaskDialog
                familyId={familyId}
                familyMembers={familyMembers}
                mode="create"
                trigger={
                  <Button variant="outline" className="h-auto p-4 flex flex-col gap-2">
                    <Plus className="h-6 w-6" />
                    <span>Dodaj zadanie</span>
                  </Button>
                }
                onTaskSuccess={onTaskUpdate}
              />
            )}

            <Button asChild variant="outline" className="h-auto p-4">
              <a href={`/families/${familyId}/tasks?view=all`} className="flex flex-col gap-2">
                <Target className="h-6 w-6" />
                <span>Wszystkie zadania</span>
              </a>
            </Button>

            {stats.pendingVerification > 0 && canManageTasks && (
              <Button asChild variant="outline" className="h-auto p-4">
                <a
                  href={`/families/${familyId}/tasks?status=pending_verification`}
                  className="flex flex-col gap-2"
                >
                  <Clock className="h-6 w-6" />
                  <span>Do weryfikacji</span>
                  <Badge variant="outline">{stats.pendingVerification}</Badge>
                </a>
              </Button>
            )}

            <Button asChild variant="outline" className="h-auto p-4">
              <a href={`/families/${familyId}/members`} className="flex flex-col gap-2">
                <Users className="h-6 w-6" />
                <span>Członkowie</span>
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
