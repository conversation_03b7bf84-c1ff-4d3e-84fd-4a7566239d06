'use client';

import { useState, useMemo } from 'react';
import {
  Task,
  TaskStatus,
  FamilyMember,
  User,
  TaskCompletion,
  CompletionStatus,
} from '@prisma/client';
import { Card, CardContent } from '@/components/ui/card';
// import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CheckCircle, Clock, Plus, AlertCircle, Trophy, Target, Pause } from 'lucide-react';
import { TaskCard } from './TaskCard';
import { TaskWithRelations } from '@/hooks/useTasks';
import { useSuspensions } from '@/hooks/useSuspensions';
import { BulkTaskActions } from './BulkTaskActions';
import { Checkbox } from '@/components/ui/checkbox';

interface TaskListProps {
  familyId: string;
  tasks: TaskWithRelations[];
  currentMember: FamilyMember;
  familyMembers: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  canManageTasks: boolean;
  canAssignTasks: boolean;
  canCompleteTasks: boolean;
  onTaskUpdate?: () => void;
  updateTaskLocally?: (
    taskId: string,
    updater: (task: TaskWithRelations) => TaskWithRelations
  ) => void;
  initialFilter?: FilterStatus;
}

type FilterStatus =
  | 'all'
  | 'active'
  | 'completed'
  | 'overdue'
  | 'pending_verification'
  | 'suspended';
type SortBy = 'dueDate' | 'created' | 'points' | 'title';

export function TaskList({
  familyId,
  tasks,
  currentMember,
  familyMembers,
  canManageTasks,
  canAssignTasks,
  canCompleteTasks,
  onTaskUpdate,
  updateTaskLocally,
  initialFilter = 'all',
}: TaskListProps) {
  const [filterStatus, setFilterStatus] = useState<FilterStatus>(initialFilter);
  const [filterMember, setFilterMember] = useState<string>('all');
  const [sortBy, setSortBy] = useState<SortBy>('dueDate');
  const [showCompleted, setShowCompleted] = useState(false);
  const [showSuspended, setShowSuspended] = useState(false);
  const [selectedCompletions, setSelectedCompletions] = useState<
    { id: string; taskId: string; status: CompletionStatus; task: { id: string; title: string } }[]
  >([]);
  const [bulkSelectMode, setBulkSelectMode] = useState(false);

  const { getTaskSuspensionStatus, suspensionStats } = useSuspensions(familyId);

  const filteredAndSortedTasks = useMemo(() => {
    let filtered = [...tasks];

    // Filter by status
    switch (filterStatus) {
      case 'active':
        filtered = filtered.filter(
          task =>
            task.status === TaskStatus.ACTIVE &&
            !task.completions.some(c => c.status === CompletionStatus.APPROVED)
        );
        break;
      case 'completed':
        filtered = filtered.filter(task =>
          task.completions.some(c => c.status === CompletionStatus.APPROVED)
        );
        break;
      case 'overdue':
        filtered = filtered.filter(
          task =>
            task.status === TaskStatus.ACTIVE &&
            task.dueDate &&
            new Date(task.dueDate) < new Date() &&
            !task.completions.some(c => c.status === CompletionStatus.APPROVED)
        );
        break;
      case 'pending_verification':
        filtered = filtered.filter(task =>
          task.completions.some(c => c.status === CompletionStatus.PENDING)
        );
        break;
      case 'suspended':
        filtered = filtered.filter(task => {
          const assignedMemberId = task.assignments?.[0]?.memberId;
          const suspensionInfo = getTaskSuspensionStatus(task.id, assignedMemberId);
          return suspensionInfo.isSuspended;
        });
        break;
    }

    // Filter by member
    if (filterMember !== 'all') {
      if (filterMember === 'unassigned') {
        filtered = filtered.filter(task => !task.assignments || task.assignments.length === 0);
      } else {
        filtered = filtered.filter(task =>
          task.assignments?.some(assignment => assignment.memberId === filterMember)
        );
      }
    }

    // Show completed tasks only if explicitly requested
    if (!showCompleted && filterStatus !== 'completed') {
      filtered = filtered.filter(
        task => !task.completions.some(c => c.status === CompletionStatus.APPROVED)
      );
    }

    // Sort tasks
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0;
          if (!a.dueDate) return 1;
          if (!b.dueDate) return -1;
          return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'points':
          return b.points - a.points;
        case 'title':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tasks, filterStatus, filterMember, sortBy, showCompleted, getTaskSuspensionStatus]);

  const taskStats = useMemo(() => {
    const now = new Date();
    const activeTasks = tasks.filter(
      task =>
        task.status === TaskStatus.ACTIVE &&
        !task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );
    const completedTasks = tasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.APPROVED)
    );
    const overdueTasks = activeTasks.filter(task => task.dueDate && new Date(task.dueDate) < now);
    const pendingVerification = tasks.filter(task =>
      task.completions.some(c => c.status === CompletionStatus.PENDING)
    );
    const suspendedTasks = tasks.filter(task => {
      const assignedMemberId = task.assignments?.[0]?.memberId;
      const suspensionInfo = getTaskSuspensionStatus(task.id, assignedMemberId);
      return suspensionInfo.isSuspended;
    });

    return {
      active: activeTasks.length,
      completed: completedTasks.length,
      overdue: overdueTasks.length,
      pendingVerification: pendingVerification.length,
      suspended: suspendedTasks.length,
      total: tasks.length,
    };
  }, [tasks, getTaskSuspensionStatus]);

  const getStatusColor = (status: FilterStatus) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'pending_verification':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: FilterStatus) => {
    switch (status) {
      case 'active':
        return <Target className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'overdue':
        return <AlertCircle className="h-4 w-4" />;
      case 'pending_verification':
        return <Clock className="h-4 w-4" />;
      case 'suspended':
        return <Pause className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getMemberDisplayName = (
    member: FamilyMember & { user: Pick<User, 'firstName' | 'lastName' | 'email'> }
  ) => {
    if (member?.user?.firstName && member?.user?.lastName) {
      return `${member.user.firstName} ${member.user.lastName}`;
    }
    if (member?.user?.email) {
      return member.user.email.split('@')[0];
    }
    return 'Nieznany użytkownik';
  };

  // Bulk selection functions
  const pendingTasks = filteredAndSortedTasks.filter(task =>
    task.completions.some(c => c.status === CompletionStatus.PENDING)
  );

  const allPendingCompletions = pendingTasks.flatMap(task =>
    task.completions
      .filter(c => c.status === CompletionStatus.PENDING)
      .map(c => ({
        id: c.id,
        taskId: task.id,
        status: c.status,
        task: { id: task.id, title: task.title },
      }))
  );

  const handleToggleCompletion = (completionId: string, taskId: string, title: string) => {
    setSelectedCompletions(prev => {
      const exists = prev.find(c => c.id === completionId);
      if (exists) {
        return prev.filter(c => c.id !== completionId);
      } else {
        return [
          ...prev,
          {
            id: completionId,
            taskId,
            status: CompletionStatus.PENDING,
            task: { id: taskId, title },
          },
        ];
      }
    });
  };

  const handleSelectAllPending = () => {
    if (selectedCompletions.length === allPendingCompletions.length) {
      setSelectedCompletions([]);
    } else {
      setSelectedCompletions(allPendingCompletions);
    }
  };

  const handleClearSelection = () => {
    setSelectedCompletions([]);
    setBulkSelectMode(false);
  };

  const isVerificationView = filterStatus === 'pending_verification';
  const showBulkActions = isVerificationView && canManageTasks;

  return (
    <div className="space-y-6">
      {/* Main Tasks Container - Prototype Design */}
      <Card className="bg-white card-shadow">
        <CardContent className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-h2 font-display text-gray-900">Zadania rodziny na dziś</h2>
              <p className="text-gray-600 font-body mt-1">
                {taskStats.active > 0
                  ? `${taskStats.completed}/${taskStats.total} ukończone`
                  : 'Wszystkie zadania ukończone!'}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {showBulkActions && pendingTasks.length > 0 && (
                <Button
                  onClick={() => setBulkSelectMode(!bulkSelectMode)}
                  variant={bulkSelectMode ? 'default' : 'outline'}
                  size="sm"
                >
                  {bulkSelectMode ? 'Anuluj zaznaczanie' : 'Zaznacz do weryfikacji'}
                </Button>
              )}
              {canManageTasks && (
                <Button
                  asChild
                  className="bg-purple-600 hover:bg-purple-700 hover-lift transition-all"
                >
                  <a href={`/families/${familyId}/tasks/create`}>
                    <Plus className="mr-2 h-4 w-4" />
                    Dodaj zadanie
                  </a>
                </Button>
              )}
            </div>
          </div>

          {/* Progress Bar */}
          {taskStats.total > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Dzienny postęp</span>
                <span className="font-semibold text-green-600">
                  {Math.round((taskStats.completed / taskStats.total) * 100)}% ukończone
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-1000 ease-out"
                  style={{ width: `${(taskStats.completed / taskStats.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          {/* Bulk Selection Header */}
          {showBulkActions && bulkSelectMode && allPendingCompletions.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-3">
                <Checkbox
                  checked={
                    selectedCompletions.length === allPendingCompletions.length &&
                    allPendingCompletions.length > 0
                  }
                  onCheckedChange={handleSelectAllPending}
                  id="select-all"
                />
                <label
                  htmlFor="select-all"
                  className="text-sm font-medium text-blue-900 cursor-pointer"
                >
                  Zaznacz wszystkie oczekujące zadania ({allPendingCompletions.length})
                </label>
              </div>
            </div>
          )}

          {/* Bulk Actions */}
          {showBulkActions && (
            <BulkTaskActions
              familyId={familyId}
              selectedCompletions={selectedCompletions}
              onClearSelection={handleClearSelection}
              onActionComplete={onTaskUpdate || (() => {})}
              canVerifyTasks={canManageTasks}
            />
          )}

          {/* Tasks Grid - Prototype Layout */}
          <div className="space-y-3">
            {filteredAndSortedTasks.length === 0 ? (
              <div className="text-center py-12">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Brak zadań</h3>
                <p className="text-gray-600 mb-4">
                  {filterStatus === 'all'
                    ? 'Nie ma jeszcze żadnych zadań w tej rodzinie.'
                    : 'Nie znaleziono zadań spełniających wybrane kryteria.'}
                </p>
                {canManageTasks && filterStatus === 'all' && (
                  <Button asChild className="bg-purple-600 hover:bg-purple-700">
                    <a href={`/families/${familyId}/tasks/create`}>
                      <Plus className="mr-2 h-4 w-4" />
                      Utwórz pierwsze zadanie
                    </a>
                  </Button>
                )}
              </div>
            ) : (
              filteredAndSortedTasks.map((task, index) => {
                const suspensionInfo = getTaskSuspensionStatus(
                  task.id,
                  task.assignments?.[0]?.memberId
                );

                // Find pending completion for this task for bulk selection
                const pendingCompletion = task.completions.find(
                  c => c.status === CompletionStatus.PENDING
                );
                const isTaskSelected = pendingCompletion
                  ? selectedCompletions.some(sc => sc.id === pendingCompletion.id)
                  : false;

                return (
                  <TaskCard
                    key={task.id}
                    task={task}
                    currentMember={currentMember}
                    canManageTasks={canManageTasks}
                    canAssignTasks={canAssignTasks}
                    canCompleteTasks={canCompleteTasks}
                    suspensionInfo={suspensionInfo}
                    onTaskUpdate={onTaskUpdate}
                    updateTaskLocally={updateTaskLocally}
                    bulkSelectMode={bulkSelectMode && isVerificationView}
                    isSelected={isTaskSelected}
                    onToggleSelection={
                      pendingCompletion
                        ? () => handleToggleCompletion(pendingCompletion.id, task.id, task.title)
                        : undefined
                    }
                  />
                );
              })
            )}
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Aktywne</p>
                <p className="text-2xl font-bold">{taskStats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Ukończone</p>
                <p className="text-2xl font-bold">{taskStats.completed}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Przeterminowane</p>
                <p className="text-2xl font-bold">{taskStats.overdue}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm text-gray-600">Do weryfikacji</p>
                <p className="text-2xl font-bold">{taskStats.pendingVerification}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Pause className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm text-gray-600">Zawieszone</p>
                <p className="text-2xl font-bold">{taskStats.suspended}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Łącznie</p>
                <p className="text-2xl font-bold">{taskStats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Sorting */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Status Filter */}
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Status</label>
              <div className="flex gap-2 flex-wrap">
                {[
                  { value: 'all', label: 'Wszystkie' },
                  { value: 'active', label: 'Aktywne' },
                  { value: 'completed', label: 'Ukończone' },
                  { value: 'overdue', label: 'Przeterminowane' },
                  { value: 'pending_verification', label: 'Do weryfikacji' },
                  { value: 'suspended', label: 'Zawieszone' },
                ].map(status => (
                  <Button
                    key={status.value}
                    variant={filterStatus === status.value ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilterStatus(status.value as FilterStatus)}
                    className={
                      filterStatus === status.value
                        ? getStatusColor(status.value as FilterStatus)
                        : ''
                    }
                  >
                    {getStatusIcon(status.value as FilterStatus)}
                    <span className="ml-1">{status.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Member Filter */}
            <div className="w-full md:w-48">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Członek</label>
              <Select value={filterMember} onValueChange={setFilterMember}>
                <SelectTrigger>
                  <SelectValue placeholder="Wszyscy" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Wszyscy</SelectItem>
                  <SelectItem value="unassigned">Nieprzypisane</SelectItem>
                  {familyMembers.map(member => (
                    <SelectItem key={member.id} value={member.id}>
                      {getMemberDisplayName(member)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Sort By */}
            <div className="w-full md:w-48">
              <label className="text-sm font-medium text-gray-700 mb-2 block">Sortuj</label>
              <Select value={sortBy} onValueChange={value => setSortBy(value as SortBy)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dueDate">Termin</SelectItem>
                  <SelectItem value="created">Data utworzenia</SelectItem>
                  <SelectItem value="points">Punkty</SelectItem>
                  <SelectItem value="title">Alfabetycznie</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Toggles */}
            <div className="flex items-end gap-2">
              <Button
                variant={showCompleted ? 'default' : 'outline'}
                size="sm"
                onClick={() => setShowCompleted(!showCompleted)}
              >
                {showCompleted ? 'Ukryj ukończone' : 'Pokaż ukończone'}
              </Button>
              <Button
                variant={showSuspended ? 'default' : 'outline'}
                size="sm"
                onClick={() => setShowSuspended(!showSuspended)}
              >
                <Pause className="mr-1 h-3 w-3" />
                {showSuspended ? 'Ukryj zawieszone' : 'Pokaż zawieszone'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tasks Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredAndSortedTasks.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Brak zadań</h3>
            <p className="text-gray-600 mb-4">
              {filterStatus === 'all'
                ? 'Nie ma jeszcze żadnych zadań w tej rodzinie.'
                : 'Nie znaleziono zadań spełniających wybrane kryteria.'}
            </p>
            {canManageTasks && filterStatus === 'all' && (
              <Button asChild>
                <a href={`/families/${familyId}/tasks/create`}>
                  <Plus className="mr-2 h-4 w-4" />
                  Utwórz pierwsze zadanie
                </a>
              </Button>
            )}
          </div>
        ) : (
          filteredAndSortedTasks.map(task => {
            const assignedMemberId = task.assignments?.[0]?.memberId;
            const suspensionInfo = getTaskSuspensionStatus(task.id, assignedMemberId);
            return (
              <TaskCard
                key={task.id}
                task={task}
                currentMember={currentMember}
                canManageTasks={canManageTasks}
                canAssignTasks={canAssignTasks}
                canCompleteTasks={canCompleteTasks}
                suspensionInfo={suspensionInfo}
                onTaskUpdate={onTaskUpdate}
                updateTaskLocally={updateTaskLocally}
              />
            );
          })
        )}
      </div>
    </div>
  );
}
