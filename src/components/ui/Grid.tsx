'use client';

import { cn } from '@/lib/utils';

interface GridProps {
  children: React.ReactNode;
  cols?: 1 | 2 | 3 | 4 | 6 | 12 | 'auto' | 'responsive';
  mdCols?: 1 | 2 | 3 | 4 | 6 | 12;
  lgCols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  as?: 'div' | 'section' | 'article';
}

export function Grid({
  children,
  cols = 'responsive',
  mdCols,
  lgCols,
  gap = 'md',
  className,
  as: Component = 'div',
}: GridProps) {
  const getColsClass = (cols: GridProps['cols']) => {
    if (cols === 'responsive') return 'grid-responsive';
    if (cols === 'auto') return 'grid-cols-auto';
    return `grid-${cols}`;
  };

  const getGapClass = (gap: GridProps['gap']) => {
    const gapClasses = {
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };
    return gapClasses[gap!];
  };

  return (
    <Component
      className={cn(
        'grid',
        getColsClass(cols),
        mdCols && `md:grid-${mdCols}`,
        lgCols && `lg:grid-${lgCols}`,
        getGapClass(gap),
        className
      )}
    >
      {children}
    </Component>
  );
}

interface GridItemProps {
  children: React.ReactNode;
  span?: 1 | 2 | 3 | 4 | 6 | 12 | 'full';
  mdSpan?: 1 | 2 | 3 | 4 | 6 | 12 | 'full';
  lgSpan?: 1 | 2 | 3 | 4 | 6 | 12 | 'full';
  start?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  end?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
  className?: string;
  as?: 'div' | 'section' | 'article';
}

export function GridItem({
  children,
  span,
  mdSpan,
  lgSpan,
  start,
  end,
  className,
  as: Component = 'div',
}: GridItemProps) {
  const getSpanClass = (span: GridItemProps['span']) => {
    if (span === 'full') return 'col-span-full';
    return span ? `col-span-${span}` : '';
  };

  return (
    <Component
      className={cn(
        getSpanClass(span),
        mdSpan && `md:${getSpanClass(mdSpan)}`,
        lgSpan && `lg:${getSpanClass(lgSpan)}`,
        start && `col-start-${start}`,
        end && `col-end-${end}`,
        className
      )}
    >
      {children}
    </Component>
  );
}

// Responsive container component
interface ContainerProps {
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  padding?: boolean;
  className?: string;
  as?: 'div' | 'section' | 'article' | 'main';
}

export function Container({
  children,
  size = 'xl',
  padding = true,
  className,
  as: Component = 'div',
}: ContainerProps) {
  const getSizeClass = (size: ContainerProps['size']) => {
    const sizeClasses = {
      sm: 'max-w-screen-sm',
      md: 'max-w-screen-md',
      lg: 'max-w-screen-lg',
      xl: 'max-w-screen-xl',
      full: 'max-w-full',
    };
    return sizeClasses[size!];
  };

  return (
    <Component
      className={cn('mx-auto w-full', getSizeClass(size), padding && 'mobile-spacing', className)}
    >
      {children}
    </Component>
  );
}

// Flex utilities for responsive layouts
interface FlexProps {
  children: React.ReactNode;
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';
  wrap?: boolean;
  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  as?: 'div' | 'section' | 'nav' | 'header' | 'footer';
}

export function Flex({
  children,
  direction = 'row',
  wrap = false,
  align = 'start',
  justify = 'start',
  gap = 'md',
  className,
  as: Component = 'div',
}: FlexProps) {
  const getGapClass = (gap: FlexProps['gap']) => {
    const gapClasses = {
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    };
    return gapClasses[gap!];
  };

  return (
    <Component
      className={cn(
        'flex',
        `flex-${direction}`,
        wrap && 'flex-wrap',
        `items-${align}`,
        `justify-${justify}`,
        getGapClass(gap),
        className
      )}
    >
      {children}
    </Component>
  );
}
