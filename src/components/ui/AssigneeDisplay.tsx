import React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { TaskAssignmentType } from '@prisma/client';

export interface AssigneeInfo {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string;
}

export interface AssignmentInfo {
  id: string;
  member: {
    id: string;
    user: AssigneeInfo;
  };
}

interface AssigneeDisplayProps {
  assignments?: AssignmentInfo[];
  assignedTo?: {
    id: string;
    user: AssigneeInfo;
  } | null;
  assignmentType?: TaskAssignmentType;
  variant?: 'calendar' | 'modal' | 'compact';
  maxVisible?: number;
}

export function AssigneeDisplay({
  assignments,
  assignedTo,
  assignmentType,
  variant = 'modal',
  maxVisible = 3,
}: AssigneeDisplayProps) {
  // Normalizuj dane - obsłuż zarówno nowe jak i stare API
  const normalizedAssignees = React.useMemo(() => {
    if (assignments && assignments.length > 0) {
      return assignments.map(assignment => ({
        id: assignment.member.id,
        user: assignment.member.user,
      }));
    }

    if (assignedTo) {
      return [assignedTo];
    }

    return [];
  }, [assignments, assignedTo]);

  if (normalizedAssignees.length === 0) {
    return null;
  }

  const getDisplayName = (user: AssigneeInfo): string => {
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();
    return fullName || user.email.split('@')[0];
  };

  const getInitials = (user: AssigneeInfo): string => {
    const firstName = user.firstName || '';
    const lastName = user.lastName || '';

    if (firstName && lastName) {
      return `${firstName[0]}${lastName[0]}`.toUpperCase();
    }

    if (firstName) {
      return firstName.slice(0, 2).toUpperCase();
    }

    return user.email.slice(0, 2).toUpperCase();
  };

  // Wariant kalendarzowy - ultra kompaktowy
  if (variant === 'calendar') {
    if (normalizedAssignees.length === 1) {
      return (
        <div className="text-xs opacity-80 truncate leading-tight">
          {getDisplayName(normalizedAssignees[0].user)}
        </div>
      );
    }

    return (
      <div className="text-xs opacity-80 truncate leading-tight">
        +{normalizedAssignees.length} osób
      </div>
    );
  }

  // Wariant kompaktowy
  if (variant === 'compact') {
    if (normalizedAssignees.length === 1) {
      return (
        <div className="flex items-center space-x-1">
          <Avatar className="h-5 w-5">
            <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
              {getInitials(normalizedAssignees[0].user)}
            </AvatarFallback>
          </Avatar>
          <span className="text-xs">{getDisplayName(normalizedAssignees[0].user)}</span>
        </div>
      );
    }

    return (
      <div className="flex items-center space-x-1">
        <div className="flex -space-x-1">
          {normalizedAssignees.slice(0, maxVisible).map((assignee, index) => (
            <Avatar key={assignee.id} className="h-5 w-5 border border-white">
              <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
                {getInitials(assignee.user)}
              </AvatarFallback>
            </Avatar>
          ))}
          {normalizedAssignees.length > maxVisible && (
            <div className="h-5 w-5 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium">
              +{normalizedAssignees.length - maxVisible}
            </div>
          )}
        </div>
        {assignmentType === TaskAssignmentType.ALL && (
          <Badge variant="secondary" className="text-xs">
            wszyscy
          </Badge>
        )}
      </div>
    );
  }

  // Wariant modalny - pełny widok
  if (normalizedAssignees.length === 1) {
    return (
      <div className="flex items-center space-x-2">
        <Avatar className="h-6 w-6">
          <AvatarFallback className="text-xs">
            {getInitials(normalizedAssignees[0].user)}
          </AvatarFallback>
        </Avatar>
        <span className="text-sm">{getDisplayName(normalizedAssignees[0].user)}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <div className="flex -space-x-2">
        {normalizedAssignees.slice(0, maxVisible).map((assignee, index) => (
          <Avatar key={assignee.id} className="h-6 w-6 border-2 border-white">
            <AvatarFallback className="text-xs bg-blue-100 text-blue-700">
              {getInitials(assignee.user)}
            </AvatarFallback>
          </Avatar>
        ))}
        {normalizedAssignees.length > maxVisible && (
          <div className="h-6 w-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium border-2 border-white">
            +{normalizedAssignees.length - maxVisible}
          </div>
        )}
      </div>
      <div className="flex items-center space-x-2">
        <span className="text-sm">
          {normalizedAssignees.length}{' '}
          {normalizedAssignees.length === 1
            ? 'osoba'
            : normalizedAssignees.length < 5
              ? 'osoby'
              : 'osób'}
        </span>
        {assignmentType === TaskAssignmentType.ALL && (
          <Badge variant="secondary" className="text-xs">
            wszyscy
          </Badge>
        )}
      </div>
    </div>
  );
}
