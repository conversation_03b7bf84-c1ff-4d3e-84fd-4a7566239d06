'use client';

import { useState, useEffect } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle } from 'lucide-react';

interface ErrorDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  message: string;
}

export function ErrorDialog({ open, onOpenChange, title = 'Błąd', message }: ErrorDialogProps) {
  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription>{message}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction onClick={() => onOpenChange(false)}>OK</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

// Hook do wyświetlania błędów
export function useErrorDialog() {
  const [errorState, setErrorState] = useState<{
    open: boolean;
    title?: string;
    message: string;
  }>({
    open: false,
    title: '',
    message: '',
  });

  const showError = (message: string, title?: string) => {
    setErrorState({
      open: true,
      title,
      message,
    });
  };

  const hideError = () => {
    setErrorState({
      open: false,
      title: '',
      message: '',
    });
  };

  const ErrorDialogComponent = () => (
    <ErrorDialog
      open={errorState.open}
      onOpenChange={hideError}
      title={errorState.title}
      message={errorState.message}
    />
  );

  return {
    showError,
    ErrorDialog: ErrorDialogComponent,
  };
}
