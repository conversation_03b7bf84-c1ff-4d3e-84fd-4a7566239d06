'use client';

import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'white' | 'gray';
}

export function LoadingSpinner({ size = 'md', className, color = 'primary' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
    xl: 'h-12 w-12',
  };

  const colorClasses = {
    primary: 'border-purple-600 border-t-transparent',
    white: 'border-white border-t-transparent',
    gray: 'border-gray-600 border-t-transparent',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
      role="status"
      aria-label="Loading"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
}

// Skeleton loading component
interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export function Skeleton({
  className,
  variant = 'rectangular',
  width,
  height,
  lines = 1,
}: SkeletonProps) {
  if (variant === 'text' && lines > 1) {
    return (
      <div className={cn('space-y-2', className)}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              'animate-pulse bg-gray-200 dark:bg-gray-700 rounded',
              index === lines - 1 && lines > 1 ? 'w-3/4' : 'w-full',
              'h-4'
            )}
            style={{ width, height }}
          />
        ))}
      </div>
    );
  }

  const variantClasses = {
    text: 'h-4 w-full rounded',
    circular: 'rounded-full',
    rectangular: 'rounded',
  };

  return (
    <div
      className={cn(
        'animate-pulse bg-gray-200 dark:bg-gray-700',
        variantClasses[variant],
        className
      )}
      style={{ width, height }}
    />
  );
}

// Card Loading skeleton
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('p-6 bg-white dark:bg-gray-800 rounded-lg card-shadow', className)}>
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <Skeleton variant="circular" width={40} height={40} />
          <div className="flex-1">
            <Skeleton variant="text" width="60%" height={16} />
            <Skeleton variant="text" width="40%" height={12} className="mt-2" />
          </div>
        </div>
        <Skeleton variant="text" lines={3} />
        <div className="flex gap-2">
          <Skeleton width={80} height={32} />
          <Skeleton width={100} height={32} />
        </div>
      </div>
    </div>
  );
}

// List Loading skeleton
export function ListSkeleton({ items = 5, className }: { items?: number; className?: string }) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div
          key={index}
          className="flex items-center gap-3 p-4 bg-white dark:bg-gray-800 rounded-lg"
        >
          <Skeleton variant="circular" width={32} height={32} />
          <div className="flex-1">
            <Skeleton variant="text" width="70%" height={16} />
            <Skeleton variant="text" width="50%" height={12} className="mt-2" />
          </div>
          <Skeleton width={60} height={24} />
        </div>
      ))}
    </div>
  );
}

// Table Loading skeleton
export function TableSkeleton({
  rows = 5,
  columns = 4,
  className,
}: {
  rows?: number;
  columns?: number;
  className?: string;
}) {
  return (
    <div className={cn('space-y-2', className)}>
      {/* Header */}
      <div
        className="grid gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={index} variant="text" width="80%" height={16} />
        ))}
      </div>

      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div
          key={rowIndex}
          className="grid gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg"
          style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
        >
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} variant="text" width="90%" height={16} />
          ))}
        </div>
      ))}
    </div>
  );
}

// Page Loading component
interface PageLoadingProps {
  title?: string;
  className?: string;
}

export function PageLoading({ title = 'Loading...', className }: PageLoadingProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center py-20', className)}>
      <LoadingSpinner size="lg" className="mb-4" />
      <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">{title}</h2>
      <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
        Proszę czekać, trwa ładowanie danych...
      </p>
    </div>
  );
}

// Button with loading state
interface LoadingButtonProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  onClick?: () => void;
}

export function LoadingButton({
  isLoading,
  children,
  className,
  disabled,
  onClick,
}: LoadingButtonProps) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center gap-2 px-4 py-2 rounded-lg font-medium',
        'bg-purple-600 text-white hover:bg-purple-700 disabled:opacity-50',
        'transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2',
        className
      )}
      disabled={disabled || isLoading}
      onClick={onClick}
    >
      {isLoading && <LoadingSpinner size="sm" color="white" />}
      <span className={isLoading ? 'opacity-70' : ''}>{children}</span>
    </button>
  );
}
