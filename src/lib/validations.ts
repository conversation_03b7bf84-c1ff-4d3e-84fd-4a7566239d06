import { z } from 'zod';

export const createTaskSchema = z.object({
  title: z
    .string()
    .min(1, 'Tytuł jest wymagany')
    .max(100, 'Tytuł nie może być dłuższy niż 100 znaków'),
  description: z.string().optional(),
  dueDate: z.date().optional(),
  assigneeId: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
});

export const familyMemberSchema = z.object({
  name: z.string().min(1, 'Imię jest wymagane').max(50, 'Imię nie może być dłuższe niż 50 znaków'),
  email: z.string().email('Nieprawidłowy adres email').optional(),
  role: z.enum(['parent', 'child', 'guardian']).default('child'),
});

export type CreateTaskInput = z.infer<typeof createTaskSchema>;
export type FamilyMemberInput = z.infer<typeof familyMemberSchema>;
