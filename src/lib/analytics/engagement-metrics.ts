import { prisma } from '@/lib/prisma';
import { trackEvent } from './events';

export class EngagementMetrics {
  // Track daily active users (DAU)
  static async trackDailyActiveUsers() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const activeUsers = await prisma.user.count({
      where: {
        familyMembers: {
          some: {
            lastActive: {
              gte: today,
            },
          },
        },
      },
    });

    trackEvent({
      name: 'daily_active_users',
      properties: {
        count: activeUsers,
        date: today.toISOString(),
      },
    });

    return activeUsers;
  }

  // Track task completion rate for families (main KPI from PRD)
  static async trackTaskCompletionRate(
    familyId: string,
    period: 'day' | 'week' | 'month' = 'week'
  ) {
    const now = new Date();
    let startDate: Date;

    switch (period) {
      case 'day':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
    }

    const totalTasks = await prisma.task.count({
      where: {
        familyId,
        status: 'ACTIVE',
        createdAt: {
          gte: startDate,
        },
      },
    });

    const completedTasks = await prisma.taskCompletion.count({
      where: {
        task: { familyId },
        status: 'APPROVED',
        completedAt: {
          gte: startDate,
        },
      },
    });

    const rate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    trackEvent({
      name: 'task_completion_rate',
      properties: {
        family_id: familyId,
        rate,
        total: totalTasks,
        completed: completedTasks,
        period,
      },
    });

    return {
      rate,
      totalTasks,
      completedTasks,
      period,
    };
  }

  // Track user session engagement
  static async trackUserSessionEngagement(
    userId: string,
    sessionDuration: number,
    pagesViewed: number
  ) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        familyMembers: {
          select: {
            familyId: true,
          },
        },
      },
    });

    const familyId = user?.familyMembers[0]?.familyId;

    trackEvent({
      name: 'user_session_engagement',
      properties: {
        user_id: userId,
        family_id: familyId,
        session_duration: sessionDuration,
        pages_viewed: pagesViewed,
        engagement_score: this.calculateEngagementScore(sessionDuration, pagesViewed),
      },
    });

    return {
      sessionDuration,
      pagesViewed,
      engagementScore: this.calculateEngagementScore(sessionDuration, pagesViewed),
    };
  }

  // Calculate engagement score based on session metrics
  private static calculateEngagementScore(sessionDuration: number, pagesViewed: number): number {
    // Normalize session duration (cap at 30 minutes = 1800 seconds)
    const durationScore = Math.min(sessionDuration / 1800, 1) * 50;

    // Normalize pages viewed (cap at 10 pages)
    const pagesScore = Math.min(pagesViewed / 10, 1) * 50;

    return Math.round(durationScore + pagesScore);
  }

  // Track task creation patterns
  static async trackTaskCreationPatterns(familyId: string) {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const tasksByDay = await prisma.task.groupBy({
      by: ['createdAt'],
      where: {
        familyId,
        createdAt: {
          gte: weekAgo,
        },
      },
      _count: {
        id: true,
      },
    });

    const tasksByWeight = await prisma.task.groupBy({
      by: ['weight'],
      where: {
        familyId,
        createdAt: {
          gte: weekAgo,
        },
      },
      _count: {
        id: true,
      },
    });

    const tasksByFrequency = await prisma.task.groupBy({
      by: ['frequency'],
      where: {
        familyId,
        createdAt: {
          gte: weekAgo,
        },
      },
      _count: {
        id: true,
      },
    });

    trackEvent({
      name: 'task_creation_patterns',
      properties: {
        family_id: familyId,
        tasks_by_weight: tasksByWeight,
        tasks_by_frequency: tasksByFrequency,
        total_tasks_week: tasksByDay.reduce((sum, day) => sum + day._count.id, 0),
        period: 'week',
      },
    });

    return {
      tasksByDay,
      tasksByWeight,
      tasksByFrequency,
    };
  }

  // Track family activity levels
  static async trackFamilyActivityLevels(familyId: string) {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const familyMembers = await prisma.familyMember.findMany({
      where: { familyId },
      select: {
        id: true,
        role: true,
        lastActive: true,
        user: {
          select: {
            id: true,
          },
        },
      },
    });

    const activeMembers = familyMembers.filter(
      member => member.lastActive && member.lastActive >= weekAgo
    );

    const tasksCreatedThisWeek = await prisma.task.count({
      where: {
        familyId,
        createdAt: {
          gte: weekAgo,
        },
      },
    });

    const tasksCompletedThisWeek = await prisma.taskCompletion.count({
      where: {
        task: { familyId },
        completedAt: {
          gte: weekAgo,
        },
      },
    });

    const activityScore = this.calculateFamilyActivityScore(
      familyMembers.length,
      activeMembers.length,
      tasksCreatedThisWeek,
      tasksCompletedThisWeek
    );

    trackEvent({
      name: 'family_activity_level',
      properties: {
        family_id: familyId,
        total_members: familyMembers.length,
        active_members: activeMembers.length,
        tasks_created_week: tasksCreatedThisWeek,
        tasks_completed_week: tasksCompletedThisWeek,
        activity_score: activityScore,
        period: 'week',
      },
    });

    return {
      totalMembers: familyMembers.length,
      activeMembers: activeMembers.length,
      tasksCreated: tasksCreatedThisWeek,
      tasksCompleted: tasksCompletedThisWeek,
      activityScore,
    };
  }

  // Calculate family activity score
  private static calculateFamilyActivityScore(
    totalMembers: number,
    activeMembers: number,
    tasksCreated: number,
    tasksCompleted: number
  ): number {
    if (totalMembers === 0) return 0;

    const memberEngagementScore = (activeMembers / totalMembers) * 40;
    const taskCreationScore = Math.min(tasksCreated / 10, 1) * 30;
    const taskCompletionScore = Math.min(tasksCompleted / 10, 1) * 30;

    return Math.round(memberEngagementScore + taskCreationScore + taskCompletionScore);
  }

  // Track feature adoption rates
  static async trackFeatureAdoption() {
    const totalFamilies = await prisma.family.count();

    const familiesUsingCalendar = await prisma.family.count({
      where: {
        calendarSettings: {
          isNot: null,
        },
      },
    });

    const familiesUsingSuspensions = await prisma.family.count({
      where: {
        suspensions: {
          some: {},
        },
      },
    });

    const familiesWithInvitations = await prisma.family.count({
      where: {
        invitations: {
          some: {},
        },
      },
    });

    const calendarAdoptionRate =
      totalFamilies > 0 ? (familiesUsingCalendar / totalFamilies) * 100 : 0;
    const suspensionAdoptionRate =
      totalFamilies > 0 ? (familiesUsingSuspensions / totalFamilies) * 100 : 0;
    const invitationAdoptionRate =
      totalFamilies > 0 ? (familiesWithInvitations / totalFamilies) * 100 : 0;

    trackEvent({
      name: 'feature_adoption_rates',
      properties: {
        total_families: totalFamilies,
        calendar_adoption_rate: calendarAdoptionRate,
        suspension_adoption_rate: suspensionAdoptionRate,
        invitation_adoption_rate: invitationAdoptionRate,
        calendar_families: familiesUsingCalendar,
        suspension_families: familiesUsingSuspensions,
        invitation_families: familiesWithInvitations,
      },
    });

    return {
      totalFamilies,
      calendarAdoptionRate,
      suspensionAdoptionRate,
      invitationAdoptionRate,
    };
  }

  // Track points and gamification engagement
  static async trackGamificationEngagement() {
    const membersWithPoints = await prisma.familyMember.count({
      where: {
        points: {
          gt: 0,
        },
      },
    });

    const totalMembers = await prisma.familyMember.count();

    const averagePoints = await prisma.familyMember.aggregate({
      _avg: {
        points: true,
      },
    });

    const pointsDistribution = await prisma.familyMember.groupBy({
      by: ['points'],
      _count: {
        id: true,
      },
      orderBy: {
        points: 'desc',
      },
      take: 10,
    });

    const engagementRate = totalMembers > 0 ? (membersWithPoints / totalMembers) * 100 : 0;

    trackEvent({
      name: 'gamification_engagement',
      properties: {
        total_members: totalMembers,
        members_with_points: membersWithPoints,
        engagement_rate: engagementRate,
        average_points: averagePoints._avg.points || 0,
        points_distribution: pointsDistribution,
      },
    });

    return {
      totalMembers,
      membersWithPoints,
      engagementRate,
      averagePoints: averagePoints._avg.points || 0,
      pointsDistribution,
    };
  }

  // Track retention and churn metrics
  static async trackRetentionMetrics() {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Users who were active 30 days ago
    const usersActive30DaysAgo = await prisma.user.findMany({
      where: {
        familyMembers: {
          some: {
            lastActive: {
              gte: thirtyDaysAgo,
              lt: new Date(thirtyDaysAgo.getTime() + 24 * 60 * 60 * 1000),
            },
          },
        },
      },
      select: { id: true },
    });

    // Of those users, how many are still active in the last 7 days
    const retainedUsers = await prisma.user.count({
      where: {
        id: {
          in: usersActive30DaysAgo.map(u => u.id),
        },
        familyMembers: {
          some: {
            lastActive: {
              gte: sevenDaysAgo,
            },
          },
        },
      },
    });

    const retentionRate =
      usersActive30DaysAgo.length > 0 ? (retainedUsers / usersActive30DaysAgo.length) * 100 : 0;

    trackEvent({
      name: 'user_retention_30_day',
      properties: {
        users_active_30_days_ago: usersActive30DaysAgo.length,
        retained_users: retainedUsers,
        retention_rate: retentionRate,
        period: '30_day',
      },
    });

    return {
      usersActive30DaysAgo: usersActive30DaysAgo.length,
      retainedUsers,
      retentionRate,
    };
  }

  // Comprehensive engagement report
  static async generateEngagementReport(familyId?: string) {
    try {
      const [dailyActiveUsers, featureAdoption, gamificationEngagement, retentionMetrics] =
        await Promise.all([
          this.trackDailyActiveUsers(),
          this.trackFeatureAdoption(),
          this.trackGamificationEngagement(),
          this.trackRetentionMetrics(),
        ]);

      let familySpecificMetrics = null;
      if (familyId) {
        const [completionRate, activityLevel, creationPatterns] = await Promise.all([
          this.trackTaskCompletionRate(familyId),
          this.trackFamilyActivityLevels(familyId),
          this.trackTaskCreationPatterns(familyId),
        ]);

        familySpecificMetrics = {
          completionRate,
          activityLevel,
          creationPatterns,
        };
      }

      trackEvent({
        name: 'engagement_report_generated',
        properties: {
          family_id: familyId,
          timestamp: new Date().toISOString(),
          includes_family_metrics: !!familyId,
        },
      });

      return {
        global: {
          dailyActiveUsers,
          featureAdoption,
          gamificationEngagement,
          retentionMetrics,
        },
        family: familySpecificMetrics,
        generatedAt: new Date().toISOString(),
      };
    } catch (error) {
      trackEvent({
        name: 'engagement_report_failed',
        properties: {
          family_id: familyId,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
      });

      throw error;
    }
  }
}
