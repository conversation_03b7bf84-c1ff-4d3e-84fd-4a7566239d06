// Global gtag interface
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}

export interface AnalyticsEvent {
  name: string;
  properties: Record<string, any>;
  timestamp?: number;
  userId?: string;
  sessionId?: string;
}

export interface UserProperties {
  familyRole?: string;
  familySize?: number;
  accountAge?: number;
  lastActive?: string;
}

// Core tracking function
export function trackEvent(event: AnalyticsEvent) {
  const enrichedEvent = {
    ...event,
    timestamp: event.timestamp || Date.now(),
    url: typeof window !== 'undefined' ? window.location.href : undefined,
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
    sessionId: getSessionId(),
  };

  // Send to Google Analytics if available
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', event.name, {
      ...event.properties,
      custom_parameter_1: event.userId,
      custom_parameter_2: event.sessionId,
    });
  }

  // Send to custom analytics endpoint
  if (typeof window !== 'undefined') {
    fetch('/api/analytics/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(enrichedEvent),
    }).catch(error => {
      console.warn('Failed to send analytics event:', error);
    });
  }

  // Log in development
  if (process.env.NODE_ENV === 'development') {
    console.log('[Analytics]', event.name, event.properties);
  }
}

// User property tracking
export function setUserProperties(properties: UserProperties) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'GA_MEASUREMENT_ID', {
      custom_map: properties,
    });
  }

  // Send to custom endpoint
  if (typeof window !== 'undefined') {
    fetch('/api/analytics/user-properties', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        properties,
        timestamp: Date.now(),
      }),
    }).catch(error => {
      console.warn('Failed to set user properties:', error);
    });
  }
}

// Session management
function getSessionId(): string {
  if (typeof window === 'undefined') return '';

  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
}

// Specific events for KPI tracking (from PRD)
export const analyticsEvents = {
  // User lifecycle events
  userSignUp: (method: string) =>
    trackEvent({
      name: 'user_sign_up',
      properties: { method },
    }),

  userSignIn: (method: string) =>
    trackEvent({
      name: 'user_sign_in',
      properties: { method },
    }),

  // Family events
  familyCreated: (familyId: string, memberCount: number = 1) =>
    trackEvent({
      name: 'family_created',
      properties: {
        family_id: familyId,
        initial_member_count: memberCount,
      },
    }),

  memberInvited: (familyId: string, role: string, invitedBy: string) =>
    trackEvent({
      name: 'member_invited',
      properties: {
        family_id: familyId,
        role,
        invited_by: invitedBy,
      },
    }),

  memberJoined: (familyId: string, role: string, invitationAccepted: boolean) =>
    trackEvent({
      name: 'member_joined',
      properties: {
        family_id: familyId,
        role,
        invitation_accepted: invitationAccepted,
      },
    }),

  // Task events
  taskCreated: (
    familyId: string,
    taskId: string,
    weight: string,
    frequency: string,
    points: number
  ) =>
    trackEvent({
      name: 'task_created',
      properties: {
        family_id: familyId,
        task_id: taskId,
        weight,
        frequency,
        points,
      },
    }),

  taskAssigned: (familyId: string, taskId: string, assignedTo: string, assignedBy: string) =>
    trackEvent({
      name: 'task_assigned',
      properties: {
        family_id: familyId,
        task_id: taskId,
        assigned_to: assignedTo,
        assigned_by: assignedBy,
      },
    }),

  taskCompleted: (
    familyId: string,
    taskId: string,
    completedBy: string,
    points: number,
    timeToComplete?: number
  ) =>
    trackEvent({
      name: 'task_completed',
      properties: {
        family_id: familyId,
        task_id: taskId,
        completed_by: completedBy,
        points,
        time_to_complete: timeToComplete,
      },
    }),

  taskVerified: (
    familyId: string,
    taskId: string,
    verifiedBy: string,
    approved: boolean,
    points?: number
  ) =>
    trackEvent({
      name: 'task_verified',
      properties: {
        family_id: familyId,
        task_id: taskId,
        verified_by: verifiedBy,
        approved,
        points: approved ? points : 0,
      },
    }),

  // KPI: Współczynnik ukończenia zadań
  completionRateCalculated: (
    familyId: string,
    period: string,
    rate: number,
    totalTasks: number,
    completedTasks: number
  ) =>
    trackEvent({
      name: 'completion_rate_calculated',
      properties: {
        family_id: familyId,
        period,
        rate,
        total_tasks: totalTasks,
        completed_tasks: completedTasks,
      },
    }),

  // Points and gamification
  pointsAwarded: (familyId: string, memberId: string, points: number, reason: string) =>
    trackEvent({
      name: 'points_awarded',
      properties: {
        family_id: familyId,
        member_id: memberId,
        points,
        reason,
      },
    }),

  levelUp: (familyId: string, memberId: string, newLevel: number, totalPoints: number) =>
    trackEvent({
      name: 'level_up',
      properties: {
        family_id: familyId,
        member_id: memberId,
        new_level: newLevel,
        total_points: totalPoints,
      },
    }),

  // Engagement events
  dailyActiveUser: (familyId: string, memberId: string, sessionsToday: number) =>
    trackEvent({
      name: 'daily_active_user',
      properties: {
        family_id: familyId,
        member_id: memberId,
        sessions_today: sessionsToday,
      },
    }),

  weeklyActiveFamily: (familyId: string, activeMemberCount: number, totalMemberCount: number) =>
    trackEvent({
      name: 'weekly_active_family',
      properties: {
        family_id: familyId,
        active_member_count: activeMemberCount,
        total_member_count: totalMemberCount,
        activity_rate: activeMemberCount / totalMemberCount,
      },
    }),

  // Feature usage
  featureUsed: (feature: string, context?: string) =>
    trackEvent({
      name: 'feature_used',
      properties: {
        feature,
        context,
      },
    }),

  // Calendar events
  calendarEventCreated: (familyId: string, eventType: string, duration?: number) =>
    trackEvent({
      name: 'calendar_event_created',
      properties: {
        family_id: familyId,
        event_type: eventType,
        duration,
      },
    }),

  // Reports and analytics
  reportGenerated: (familyId: string, reportType: string, period: string) =>
    trackEvent({
      name: 'report_generated',
      properties: {
        family_id: familyId,
        report_type: reportType,
        period,
      },
    }),

  // Error tracking
  errorOccurred: (error: string, context: string, userId?: string) =>
    trackEvent({
      name: 'error_occurred',
      properties: {
        error,
        context,
        user_id: userId,
      },
    }),

  // Performance tracking
  pageLoadTime: (page: string, loadTime: number) =>
    trackEvent({
      name: 'page_load_time',
      properties: {
        page,
        load_time: loadTime,
      },
    }),

  // Suspension events
  suspensionCreated: (familyId: string, suspensionType: string, duration: number, reason: string) =>
    trackEvent({
      name: 'suspension_created',
      properties: {
        family_id: familyId,
        suspension_type: suspensionType,
        duration,
        reason,
      },
    }),
};

// Batch event tracking for performance
class EventBatcher {
  private events: AnalyticsEvent[] = [];
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds
  private timer?: NodeJS.Timeout;

  constructor() {
    if (typeof window !== 'undefined') {
      // Flush on page unload
      window.addEventListener('beforeunload', () => this.flush());

      // Start periodic flush
      this.startPeriodicFlush();
    }
  }

  addEvent(event: AnalyticsEvent) {
    this.events.push(event);

    if (this.events.length >= this.batchSize) {
      this.flush();
    }
  }

  flush() {
    if (this.events.length === 0) return;

    const eventsToSend = [...this.events];
    this.events = [];

    if (typeof window !== 'undefined') {
      fetch('/api/analytics/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ events: eventsToSend }),
      }).catch(error => {
        console.warn('Failed to send batch analytics:', error);
        // Re-add events to queue on failure
        this.events.unshift(...eventsToSend);
      });
    }
  }

  private startPeriodicFlush() {
    this.timer = setInterval(() => this.flush(), this.flushInterval);
  }

  destroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    this.flush();
  }
}

// Export singleton batcher
export const eventBatcher = new EventBatcher();
