import { prisma } from '@/lib/prisma';
import { trackEvent } from './events';

export class UsageMetrics {
  // Track active families (KPI from PRD)
  static async trackActiveFamilies() {
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const weeklyActiveFamilies = await prisma.family.count({
      where: {
        members: {
          some: {
            lastActive: {
              gte: weekAgo,
            },
          },
        },
      },
    });

    const monthlyActiveFamilies = await prisma.family.count({
      where: {
        members: {
          some: {
            lastActive: {
              gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
            },
          },
        },
      },
    });

    trackEvent({
      name: 'weekly_active_families',
      properties: {
        count: weeklyActiveFamilies,
        period: 'week',
      },
    });

    trackEvent({
      name: 'monthly_active_families',
      properties: {
        count: monthlyActiveFamilies,
        period: 'month',
      },
    });

    return {
      weekly: weeklyActiveFamilies,
      monthly: monthlyActiveFamilies,
    };
  }

  // Track average tasks per family
  static async trackAverageTasksPerFamily() {
    const familyTaskCounts = await prisma.family.findMany({
      select: {
        id: true,
        _count: {
          select: {
            tasks: true,
          },
        },
      },
    });

    const totalFamilies = familyTaskCounts.length;
    const totalTasks = familyTaskCounts.reduce((sum, family) => sum + family._count.tasks, 0);
    const averageTasksPerFamily = totalFamilies > 0 ? totalTasks / totalFamilies : 0;

    trackEvent({
      name: 'avg_tasks_per_family',
      properties: {
        average: averageTasksPerFamily,
        total_families: totalFamilies,
        total_tasks: totalTasks,
      },
    });

    return averageTasksPerFamily;
  }

  // Track task completion rates by family
  static async trackTaskCompletionRates() {
    const families = await prisma.family.findMany({
      select: {
        id: true,
        name: true,
        _count: {
          select: {
            tasks: true,
          },
        },
      },
    });

    const completionRates = await Promise.all(
      families.map(async family => {
        const totalTasks = await prisma.task.count({
          where: { familyId: family.id },
        });

        const completedTasks = await prisma.taskCompletion.count({
          where: {
            task: { familyId: family.id },
            status: 'APPROVED',
          },
        });

        const rate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

        trackEvent({
          name: 'family_completion_rate',
          properties: {
            family_id: family.id,
            rate,
            total_tasks: totalTasks,
            completed_tasks: completedTasks,
          },
        });

        return {
          familyId: family.id,
          familyName: family.name,
          rate,
          totalTasks,
          completedTasks,
        };
      })
    );

    // Calculate overall completion rate
    const overallTotal = completionRates.reduce((sum, family) => sum + family.totalTasks, 0);
    const overallCompleted = completionRates.reduce(
      (sum, family) => sum + family.completedTasks,
      0
    );
    const overallRate = overallTotal > 0 ? (overallCompleted / overallTotal) * 100 : 0;

    trackEvent({
      name: 'overall_completion_rate',
      properties: {
        rate: overallRate,
        total_tasks: overallTotal,
        completed_tasks: overallCompleted,
        families_count: families.length,
      },
    });

    return {
      overall: overallRate,
      byFamily: completionRates,
    };
  }

  // Track user engagement metrics
  static async trackUserEngagement() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const dailyActiveUsers = await prisma.familyMember.count({
      where: {
        lastActive: {
          gte: today,
        },
      },
    });

    const weeklyActiveUsers = await prisma.familyMember.count({
      where: {
        lastActive: {
          gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        },
      },
    });

    const monthlyActiveUsers = await prisma.familyMember.count({
      where: {
        lastActive: {
          gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
        },
      },
    });

    trackEvent({
      name: 'daily_active_users',
      properties: {
        count: dailyActiveUsers,
        date: today.toISOString(),
      },
    });

    trackEvent({
      name: 'weekly_active_users',
      properties: {
        count: weeklyActiveUsers,
      },
    });

    trackEvent({
      name: 'monthly_active_users',
      properties: {
        count: monthlyActiveUsers,
      },
    });

    return {
      daily: dailyActiveUsers,
      weekly: weeklyActiveUsers,
      monthly: monthlyActiveUsers,
    };
  }

  // Track points distribution
  static async trackPointsDistribution() {
    const pointsStats = await prisma.familyMember.aggregate({
      _avg: { points: true },
      _min: { points: true },
      _max: { points: true },
      _sum: { points: true },
      _count: { points: true },
    });

    const topPerformers = await prisma.familyMember.findMany({
      orderBy: { points: 'desc' },
      take: 10,
      select: {
        id: true,
        points: true,
        role: true,
        family: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    trackEvent({
      name: 'points_distribution',
      properties: {
        average: pointsStats._avg.points || 0,
        min: pointsStats._min.points || 0,
        max: pointsStats._max.points || 0,
        total: pointsStats._sum.points || 0,
        member_count: pointsStats._count.points || 0,
      },
    });

    return {
      stats: pointsStats,
      topPerformers,
    };
  }

  // Track feature usage
  static async trackFeatureUsage() {
    const now = new Date();
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Count families with calendar settings configured
    const calendarSettingsCount = await prisma.calendarSettings.count({
      where: {
        createdAt: {
          gte: monthAgo,
        },
      },
    });

    // Count suspensions created
    const suspensionsCount = await prisma.taskSuspension.count({
      where: {
        createdAt: {
          gte: monthAgo,
        },
      },
    });

    // Count family invitations sent
    const invitationsCount = await prisma.familyInvitation.count({
      where: {
        createdAt: {
          gte: monthAgo,
        },
      },
    });

    trackEvent({
      name: 'feature_usage_monthly',
      properties: {
        calendar_settings: calendarSettingsCount,
        suspensions: suspensionsCount,
        invitations: invitationsCount,
        period: 'month',
      },
    });

    return {
      calendarSettings: calendarSettingsCount,
      suspensions: suspensionsCount,
      invitations: invitationsCount,
    };
  }

  // Track retention metrics
  static async trackRetentionMetrics() {
    const now = new Date();

    // Day 1 retention (users who signed up yesterday and came back today)
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const yesterdayStart = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate()
    );
    const yesterdayEnd = new Date(yesterdayStart.getTime() + 24 * 60 * 60 * 1000);

    const newUsersYesterday = await prisma.user.findMany({
      where: {
        createdAt: {
          gte: yesterdayStart,
          lt: yesterdayEnd,
        },
      },
      select: { id: true },
    });

    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const returnedToday = await prisma.familyMember.count({
      where: {
        userId: {
          in: newUsersYesterday.map(u => u.id),
        },
        lastActive: {
          gte: todayStart,
        },
      },
    });

    const day1Retention =
      newUsersYesterday.length > 0 ? (returnedToday / newUsersYesterday.length) * 100 : 0;

    trackEvent({
      name: 'day_1_retention',
      properties: {
        rate: day1Retention,
        new_users_yesterday: newUsersYesterday.length,
        returned_today: returnedToday,
      },
    });

    return {
      day1Retention,
      newUsersYesterday: newUsersYesterday.length,
      returnedToday,
    };
  }

  // Track system performance metrics
  static async trackSystemMetrics() {
    const now = new Date();
    const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    // Count API requests (this would need to be tracked separately)
    // For now, we'll track database query counts as a proxy

    const recentTasks = await prisma.task.count({
      where: {
        createdAt: {
          gte: hourAgo,
        },
      },
    });

    const recentCompletions = await prisma.taskCompletion.count({
      where: {
        completedAt: {
          gte: hourAgo,
        },
      },
    });

    trackEvent({
      name: 'system_activity_hourly',
      properties: {
        tasks_created: recentTasks,
        tasks_completed: recentCompletions,
        timestamp: now.toISOString(),
      },
    });

    return {
      tasksCreated: recentTasks,
      tasksCompleted: recentCompletions,
    };
  }

  // Run all metrics collection
  static async collectAllMetrics() {
    try {
      const [
        activeFamilies,
        avgTasks,
        completionRates,
        engagement,
        pointsDistribution,
        featureUsage,
        retention,
        systemMetrics,
      ] = await Promise.all([
        this.trackActiveFamilies(),
        this.trackAverageTasksPerFamily(),
        this.trackTaskCompletionRates(),
        this.trackUserEngagement(),
        this.trackPointsDistribution(),
        this.trackFeatureUsage(),
        this.trackRetentionMetrics(),
        this.trackSystemMetrics(),
      ]);

      trackEvent({
        name: 'metrics_collection_completed',
        properties: {
          timestamp: new Date().toISOString(),
          metrics_collected: 8,
        },
      });

      return {
        activeFamilies,
        avgTasks,
        completionRates,
        engagement,
        pointsDistribution,
        featureUsage,
        retention,
        systemMetrics,
      };
    } catch (error) {
      trackEvent({
        name: 'metrics_collection_failed',
        properties: {
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        },
      });

      throw error;
    }
  }
}
