import { Locale } from '@/lib/i18n/config';

export interface EmailTemplate {
  subject: string;
  body: string;
}

export interface NotificationData {
  userName?: string;
  taskName?: string;
  familyName?: string;
  memberName?: string;
  dueDate?: string;
  points?: number;
  [key: string]: any;
}

const emailTemplates = {
  familyInvitation: {
    pl: {
      subject: 'Zaproszenie do rodziny {familyName}',
      body: `
        Witaj!
        
        Zostałeś zaproszony do dołączenia do rodziny "{familyName}" w aplikacji FamilyTasks.
        
        Aby <PERSON>, kliknij w poniższy link:
        {invitationLink}
        
        FamilyTasks pomaga rodzinom organizować zadania domowe i nagradzać dzieci za ich wykonanie.
        
        Pozdrawienia,
        Zespół FamilyTasks
      `,
    },
    en: {
      subject: 'Family invitation to {familyName}',
      body: `
        Hello!
        
        You have been invited to join the "{familyName}" family in the FamilyTasks app.
        
        To join, click the link below:
        {invitationLink}
        
        FamilyTasks helps families organize household tasks and reward children for completing them.
        
        Best regards,
        FamilyTasks Team
      `,
    },
    de: {
      subject: 'Familieneinladung zu {familyName}',
      body: `
        Hallo!
        
        <PERSON>e wurden eingeladen, der Familie "{familyName}" in der FamilyTasks-App beizutreten.
        
        Um beizutreten, klicken Sie auf den Link unten:
        {invitationLink}
        
        FamilyTasks hilft Familien dabei, Haushaltsaufgaben zu organisieren und Kinder für deren Erledigung zu belohnen.
        
        Mit freundlichen Grüßen,
        FamilyTasks Team
      `,
    },
    es: {
      subject: 'Invitación familiar a {familyName}',
      body: `
        ¡Hola!
        
        Has sido invitado a unirte a la familia "{familyName}" en la aplicación FamilyTasks.
        
        Para unirte, haz clic en el enlace de abajo:
        {invitationLink}
        
        FamilyTasks ayuda a las familias a organizar las tareas domésticas y recompensar a los niños por completarlas.
        
        Saludos cordiales,
        Equipo FamilyTasks
      `,
    },
  },
  taskReminder: {
    pl: {
      subject: 'Przypomnienie o zadaniu: {taskName}',
      body: `
        Cześć {userName}!
        
        Przypominamy o zadaniu "{taskName}", które ma termin wykonania: {dueDate}.
        
        Pamiętaj, że za to zadanie możesz otrzymać {points} punktów!
        
        Powodzenia,
        FamilyTasks
      `,
    },
    en: {
      subject: 'Task reminder: {taskName}',
      body: `
        Hi {userName}!
        
        This is a reminder about the task "{taskName}" which is due: {dueDate}.
        
        Remember, you can earn {points} points for this task!
        
        Good luck,
        FamilyTasks
      `,
    },
  },
  taskCompleted: {
    pl: {
      subject: 'Zadanie wykonane: {taskName}',
      body: `
        Gratulacje {userName}!
        
        Pomyślnie wykonałeś zadanie "{taskName}" i otrzymujesz {points} punktów!
        
        Świetna robota!
        FamilyTasks
      `,
    },
    en: {
      subject: 'Task completed: {taskName}',
      body: `
        Congratulations {userName}!
        
        You have successfully completed the task "{taskName}" and earned {points} points!
        
        Great job!
        FamilyTasks
      `,
    },
  },
};

export function getLocalizedEmailTemplate(
  templateName: keyof typeof emailTemplates,
  locale: Locale,
  variables: NotificationData
): EmailTemplate {
  const templateGroup = emailTemplates[templateName];
  const template = (templateGroup as any)[locale] || (templateGroup as any)['pl'];

  if (!template) {
    throw new Error(`Email template '${templateName}' not found for locale '${locale}'`);
  }

  return {
    subject: interpolateTemplate(template.subject, variables),
    body: interpolateTemplate(template.body, variables),
  };
}

function interpolateTemplate(template: string, variables: NotificationData): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key]?.toString() || match;
  });
}
