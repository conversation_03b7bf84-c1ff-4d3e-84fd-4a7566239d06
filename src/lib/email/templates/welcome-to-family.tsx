import { FamilyRole } from '@prisma/client';
import { getRoleDisplayName } from '@/lib/auth';

interface WelcomeToFamilyTemplateProps {
  memberName: string;
  familyName: string;
  role: FamilyRole;
  dashboardUrl: string;
}

export function WelcomeToFamilyTemplate({
  memberName,
  familyName,
  role,
  dashboardUrl,
}: WelcomeToFamilyTemplateProps) {
  const roleDisplayName = getRoleDisplayName(role);

  const html = `
    <!DOCTYPE html>
    <html lang="pl">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Witaj w rodzinie!</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .container {
          background-color: white;
          border-radius: 12px;
          padding: 40px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 10px;
        }
        .title {
          font-size: 28px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 10px;
        }
        .subtitle {
          font-size: 16px;
          color: #6b7280;
        }
        .welcome-emoji {
          font-size: 48px;
          margin-bottom: 20px;
        }
        .content {
          margin: 30px 0;
        }
        .family-info {
          background-color: #f0fdf4;
          border: 1px solid #bbf7d0;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .family-name {
          font-size: 20px;
          font-weight: bold;
          color: #166534;
          margin-bottom: 8px;
        }
        .role {
          display: inline-block;
          background-color: #dbeafe;
          color: #1e40af;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 14px;
          font-weight: 500;
        }
        .features {
          margin: 30px 0;
        }
        .feature {
          display: flex;
          align-items: flex-start;
          margin-bottom: 15px;
          padding: 15px;
          background-color: #f9fafb;
          border-radius: 8px;
        }
        .feature-icon {
          font-size: 20px;
          margin-right: 12px;
          flex-shrink: 0;
        }
        .feature-content {
          flex: 1;
        }
        .feature-title {
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4px;
        }
        .feature-description {
          color: #6b7280;
          font-size: 14px;
        }
        .cta-button {
          display: inline-block;
          background-color: #16a34a;
          color: white;
          padding: 14px 28px;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          margin: 20px auto;
          text-align: center;
        }
        .cta-container {
          text-align: center;
          margin: 30px 0;
        }
        .footer {
          text-align: center;
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          color: #6b7280;
          font-size: 14px;
        }
        .link {
          color: #2563eb;
          text-decoration: none;
        }
        .link:hover {
          text-decoration: underline;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">👨‍👩‍👧‍👦 FamilyTasks</div>
          <div class="welcome-emoji">🎉</div>
          <h1 class="title">Witaj w rodzinie!</h1>
          <p class="subtitle">
            Cześć ${memberName}, świetnie Cię widzieć!
          </p>
        </div>

        <div class="content">
          <div class="family-info">
            <div class="family-name">📋 ${familyName}</div>
            <p>Twoja rola: <span class="role">${roleDisplayName}</span></p>
          </div>

          <p>
            Gratulacje! Oficjalnie dołączyłeś do rodziny "${familyName}" w aplikacji FamilyTasks. 
            Teraz możesz organizować zadania domowe razem z rodziną i być nagradzanym za swój wkład!
          </p>

          <div class="features">
            <div class="feature">
              <div class="feature-icon">✅</div>
              <div class="feature-content">
                <div class="feature-title">Zarządzaj zadaniami</div>
                <div class="feature-description">
                  ${
                    role === 'CHILD'
                      ? 'Wykonuj zadania przydzielone przez rodziców i opiekunów'
                      : 'Twórz, przydzielaj i sprawdzaj zadania dla członków rodziny'
                  }
                </div>
              </div>
            </div>

            <div class="feature">
              <div class="feature-icon">🏆</div>
              <div class="feature-content">
                <div class="feature-title">Zbieraj punkty</div>
                <div class="feature-description">
                  Otrzymuj punkty za wykonane zadania i śledź swoje postępy
                </div>
              </div>
            </div>

            <div class="feature">
              <div class="feature-icon">📊</div>
              <div class="feature-content">
                <div class="feature-title">Monitoruj postępy</div>
                <div class="feature-description">
                  ${
                    role === 'CHILD'
                      ? 'Zobacz swoje statystyki i porównaj się z innymi członkami rodziny'
                      : 'Śledź aktywność całej rodziny i sprawdzaj statystyki'
                  }
                </div>
              </div>
            </div>

            <div class="feature">
              <div class="feature-icon">👥</div>
              <div class="feature-content">
                <div class="feature-title">Współpracuj z rodziną</div>
                <div class="feature-description">
                  Pracuj razem z rodziną nad utrzymaniem porządku w domu
                </div>
              </div>
            </div>
          </div>

          <div class="cta-container">
            <a href="${dashboardUrl}" class="cta-button">
              Przejdź do panelu rodziny
            </a>
          </div>
        </div>

        <div class="footer">
          <p>
            Potrzebujesz pomocy? Sprawdź nasz przewodnik dla nowych użytkowników lub skontaktuj się z rodziną.
          </p>
          <p>
            <small>
              Ten email został wysłany z aplikacji FamilyTasks.<br>
              Jeśli masz problemy z kliknięciem przycisku, skopiuj i wklej ten link do przeglądarki:<br>
              <a href="${dashboardUrl}" class="link">${dashboardUrl}</a>
            </small>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
Witaj w rodzinie "${familyName}"!

Cześć ${memberName},

Gratulacje! Oficjalnie dołączyłeś do rodziny "${familyName}" w aplikacji FamilyTasks. 

Twoja rola: ${roleDisplayName}

Co możesz teraz robić:

✅ Zarządzaj zadaniami
${
  role === 'CHILD'
    ? 'Wykonuj zadania przydzielone przez rodziców i opiekunów'
    : 'Twórz, przydzielaj i sprawdzaj zadania dla członków rodziny'
}

🏆 Zbieraj punkty
Otrzymuj punkty za wykonane zadania i śledź swoje postępy

📊 Monitoruj postępy
${
  role === 'CHILD'
    ? 'Zobacz swoje statystyki i porównaj się z innymi członkami rodziny'
    : 'Śledź aktywność całej rodziny i sprawdzaj statystyki'
}

👥 Współpracuj z rodziną
Pracuj razem z rodziną nad utrzymaniem porządku w domu

Przejdź do panelu rodziny:
${dashboardUrl}

Potrzebujesz pomocy? Sprawdź nasz przewodnik dla nowych użytkowników lub skontaktuj się z rodziną.

---
Ten email został wysłany z aplikacji FamilyTasks.
  `;

  return { html, text };
}
