import { FamilyRole } from '@prisma/client';
import { getRoleDisplayName } from '@/lib/auth';

interface FamilyInvitationTemplateProps {
  familyName: string;
  inviterName: string;
  role: FamilyRole;
  invitationUrl: string;
  expiresAt: Date;
  isReminder?: boolean;
}

export function FamilyInvitationTemplate({
  familyName,
  inviterName,
  role,
  invitationUrl,
  expiresAt,
  isReminder = false,
}: FamilyInvitationTemplateProps) {
  const roleDisplayName = getRoleDisplayName(role);
  const expirationDate = expiresAt.toLocaleDateString('pl-PL', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const html = `
    <!DOCTYPE html>
    <html lang="pl">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${isReminder ? 'Przypomnienie o' : ''} Zaproszenie do rodziny</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .container {
          background-color: white;
          border-radius: 12px;
          padding: 40px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #2563eb;
          margin-bottom: 10px;
        }
        .title {
          font-size: 28px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 10px;
        }
        .subtitle {
          font-size: 16px;
          color: #6b7280;
        }
        .content {
          margin: 30px 0;
        }
        .family-info {
          background-color: #f3f4f6;
          border-radius: 8px;
          padding: 20px;
          margin: 20px 0;
        }
        .family-name {
          font-size: 20px;
          font-weight: bold;
          color: #1f2937;
          margin-bottom: 8px;
        }
        .role {
          display: inline-block;
          background-color: #dbeafe;
          color: #1e40af;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 14px;
          font-weight: 500;
        }
        .cta-button {
          display: inline-block;
          background-color: #2563eb;
          color: white;
          padding: 14px 28px;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 600;
          font-size: 16px;
          margin: 20px auto;
          text-align: center;
        }
        .cta-container {
          text-align: center;
          margin: 30px 0;
        }
        .expires {
          color: #dc2626;
          font-weight: 500;
          font-size: 14px;
          text-align: center;
          margin-top: 20px;
        }
        .footer {
          text-align: center;
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #e5e7eb;
          color: #6b7280;
          font-size: 14px;
        }
        .link {
          color: #2563eb;
          text-decoration: none;
        }
        .link:hover {
          text-decoration: underline;
        }
        ${
          isReminder
            ? `
        .reminder-badge {
          background-color: #fbbf24;
          color: #92400e;
          padding: 6px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          margin-bottom: 20px;
          display: inline-block;
        }
        `
            : ''
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">👨‍👩‍👧‍👦 FamilyTasks</div>
          ${isReminder ? '<div class="reminder-badge">Przypomnienie</div>' : ''}
          <h1 class="title">${isReminder ? 'Przypomnienie o zaproszeniu' : 'Zostałeś zaproszony!'}</h1>
          <p class="subtitle">
            ${inviterName} zaprasza Cię do dołączenia do rodziny
          </p>
        </div>

        <div class="content">
          <div class="family-info">
            <div class="family-name">📋 ${familyName}</div>
            <p>Twoja rola: <span class="role">${roleDisplayName}</span></p>
          </div>

          <p>
            ${
              isReminder
                ? `To przypomnienie o zaproszeniu do rodziny "${familyName}". Zaproszenie wkrótce wygaśnie!`
                : `Witaj! Zostałeś zaproszony do dołączenia do rodziny "${familyName}" w aplikacji FamilyTasks.`
            }
          </p>

          <p>
            FamilyTasks to aplikacja, która pomaga rodzinom organizować zadania domowe, 
            śledzić postępy i nagradzać wszystkich członków rodziny za ich wkład.
          </p>

          <div class="cta-container">
            <a href="${invitationUrl}" class="cta-button">
              ${isReminder ? 'Sprawdź zaproszenie' : 'Akceptuj zaproszenie'}
            </a>
          </div>

          <div class="expires">
            ⏰ Zaproszenie wygasa: ${expirationDate}
          </div>
        </div>

        <div class="footer">
          <p>
            Jeśli nie spodziewałeś się tego zaproszenia, możesz je zignorować.<br>
            <a href="${invitationUrl}" class="link">Kliknij tutaj, aby odrzucić zaproszenie</a>
          </p>
          <p>
            <small>
              Ten email został wysłany z aplikacji FamilyTasks.<br>
              Jeśli masz problemy z kliknięciem przycisku, skopiuj i wklej ten link do przeglądarki:<br>
              <a href="${invitationUrl}" class="link">${invitationUrl}</a>
            </small>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  const text = `
${isReminder ? 'PRZYPOMNIENIE: ' : ''}Zaproszenie do rodziny "${familyName}"

${inviterName} zaprasza Cię do dołączenia do rodziny "${familyName}" w aplikacji FamilyTasks.

Twoja rola: ${roleDisplayName}

${
  isReminder
    ? `To przypomnienie o zaproszeniu do rodziny. Zaproszenie wkrótce wygaśnie!`
    : `FamilyTasks to aplikacja, która pomaga rodzinom organizować zadania domowe, śledzić postępy i nagradzać wszystkich członków rodziny za ich wkład.`
}

Aby ${isReminder ? 'sprawdzić' : 'zaakceptować'} zaproszenie, kliknij link:
${invitationUrl}

Zaproszenie wygasa: ${expirationDate}

Jeśli nie spodziewałeś się tego zaproszenia, możesz je zignorować lub odrzucić klikając link powyżej.

---
Ten email został wysłany z aplikacji FamilyTasks.
  `;

  return { html, text };
}
