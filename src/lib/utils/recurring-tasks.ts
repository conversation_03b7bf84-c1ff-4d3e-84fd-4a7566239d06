/**
 * Utility functions dla zadań cyklicznych
 * Obsługuje generowanie dat wystąpień i sprawdzanie aktywności zadań
 */

import {
  addDays,
  addWeeks,
  addMonths,
  addYears,
  startOfDay,
  isAfter,
  isBefore,
  isSameDay,
} from 'date-fns';
import { TaskFrequency } from '@prisma/client';

export interface RecurringTaskOptions {
  startDate: Date;
  endDate?: Date;
  frequency: TaskFrequency;
  interval?: number; // dla CUSTOM - co ile dni/tygodni/miesięcy
  recurrenceRule?: string; // RRULE format dla zaawansowanych przypadków
}

export interface TaskOccurrence {
  date: Date;
  isActive: boolean;
}

/**
 * Generuje daty wystąpień zadania cyklicznego w podanym zakresie
 */
export function generateRecurringDates(
  options: RecurringTaskOptions,
  rangeStart: Date,
  rangeEnd: Date
): TaskOccurrence[] {
  const { startDate, endDate, frequency, interval = 1 } = options;
  const occurrences: TaskOccurrence[] = [];

  // Normalizujemy daty do początku dnia
  const normalizedStart = startOfDay(startDate);
  const normalizedRangeStart = startOfDay(rangeStart);
  const normalizedRangeEnd = startOfDay(rangeEnd);
  const normalizedEndDate = endDate ? startOfDay(endDate) : null;

  let currentDate = normalizedStart;

  // Jeśli zadanie zaczyna się po końcu zakresu, zwracamy pustą tablicę
  if (isAfter(normalizedStart, normalizedRangeEnd)) {
    return [];
  }

  // Przesuwamy początkową datę do pierwszego wystąpienia w zakresie
  while (isBefore(currentDate, normalizedRangeStart)) {
    currentDate = getNextOccurrence(currentDate, frequency, interval);
  }

  // Generujemy wystąpienia w zakresie
  while (!isAfter(currentDate, normalizedRangeEnd)) {
    // Sprawdzamy czy zadanie nie skończyło się
    if (normalizedEndDate && isAfter(currentDate, normalizedEndDate)) {
      break;
    }

    occurrences.push({
      date: new Date(currentDate),
      isActive: true,
    });

    // Dla zadań jednorazowych, dodajemy tylko jedno wystąpienie
    if (frequency === 'ONCE') {
      break;
    }

    currentDate = getNextOccurrence(currentDate, frequency, interval);
  }

  return occurrences;
}

/**
 * Sprawdza czy zadanie cykliczne jest aktywne w danym dniu
 */
export function isTaskActiveOnDate(options: RecurringTaskOptions, checkDate: Date): boolean {
  const { startDate, endDate, frequency, interval = 1 } = options;

  const normalizedCheckDate = startOfDay(checkDate);
  const normalizedStartDate = startOfDay(startDate);
  const normalizedEndDate = endDate ? startOfDay(endDate) : null;

  // Sprawdzamy czy data jest w zakresie zadania
  if (isBefore(normalizedCheckDate, normalizedStartDate)) {
    return false;
  }

  if (normalizedEndDate && isAfter(normalizedCheckDate, normalizedEndDate)) {
    return false;
  }

  // Sprawdzamy czy data pasuje do wzorca cykliczności
  return doesDateMatchPattern(normalizedStartDate, normalizedCheckDate, frequency, interval);
}

/**
 * Oblicza następne wystąpienie zadania cyklicznego
 */
function getNextOccurrence(
  currentDate: Date,
  frequency: TaskFrequency,
  interval: number = 1
): Date {
  switch (frequency) {
    case 'DAILY':
      return addDays(currentDate, interval);

    case 'WEEKLY':
      return addWeeks(currentDate, interval);

    case 'MONTHLY':
      return addMonths(currentDate, interval);

    case 'YEARLY':
      return addYears(currentDate, interval);

    case 'CUSTOM':
      // Dla CUSTOM używamy interval jako liczba dni
      return addDays(currentDate, interval);

    case 'ONCE':
    default:
      // Zadania jednorazowe nie mają następnego wystąpienia
      return addYears(currentDate, 100); // Daleka przyszłość
  }
}

/**
 * Sprawdza czy data pasuje do wzorca cykliczności
 */
function doesDateMatchPattern(
  startDate: Date,
  checkDate: Date,
  frequency: TaskFrequency,
  interval: number = 1
): boolean {
  if (isSameDay(startDate, checkDate)) {
    return true;
  }

  switch (frequency) {
    case 'DAILY':
      const daysDiff = Math.floor(
        (checkDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      return daysDiff > 0 && daysDiff % interval === 0;

    case 'WEEKLY':
      const weeksDiff = Math.floor(
        (checkDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7)
      );
      return (
        weeksDiff > 0 && weeksDiff % interval === 0 && checkDate.getDay() === startDate.getDay()
      );

    case 'MONTHLY':
      const monthsDiff =
        (checkDate.getFullYear() - startDate.getFullYear()) * 12 +
        (checkDate.getMonth() - startDate.getMonth());
      return (
        monthsDiff > 0 && monthsDiff % interval === 0 && checkDate.getDate() === startDate.getDate()
      );

    case 'YEARLY':
      const yearsDiff = checkDate.getFullYear() - startDate.getFullYear();
      return (
        yearsDiff > 0 &&
        yearsDiff % interval === 0 &&
        checkDate.getMonth() === startDate.getMonth() &&
        checkDate.getDate() === startDate.getDate()
      );

    case 'CUSTOM':
      // CUSTOM używa interval jako liczba dni
      const customDaysDiff = Math.floor(
        (checkDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      return customDaysDiff > 0 && customDaysDiff % interval === 0;

    case 'ONCE':
    default:
      return false;
  }
}

/**
 * Pobiera wszystkie zadania cykliczne aktywne w podanym zakresie dat
 */
export function getRecurringTasksForDateRange<T extends RecurringTaskOptions>(
  tasks: T[],
  rangeStart: Date,
  rangeEnd: Date
): Array<T & { occurrences: TaskOccurrence[] }> {
  return tasks
    .filter(task => task.frequency !== 'ONCE') // Filtrujemy tylko zadania cykliczne
    .map(task => ({
      ...task,
      occurrences: generateRecurringDates(task, rangeStart, rangeEnd),
    }))
    .filter(task => task.occurrences.length > 0); // Tylko zadania z wystąpieniami w zakresie
}

/**
 * Parsuje interval z recurrenceRule (format: "INTERVAL=2")
 */
export function parseIntervalFromRRule(recurrenceRule?: string): number {
  if (!recurrenceRule) return 1;

  const intervalMatch = recurrenceRule.match(/INTERVAL=(\d+)/);
  return intervalMatch ? parseInt(intervalMatch[1], 10) : 1;
}

/**
 * Tworzy prosty RRULE dla zadań z intervalem
 */
export function createSimpleRRule(frequency: TaskFrequency, interval: number = 1): string {
  const freqMap = {
    DAILY: 'DAILY',
    WEEKLY: 'WEEKLY',
    MONTHLY: 'MONTHLY',
    YEARLY: 'YEARLY',
    CUSTOM: 'DAILY', // CUSTOM używa DAILY z intervalem
    ONCE: '',
  };

  const freq = freqMap[frequency];
  if (!freq) return '';

  return interval > 1 ? `FREQ=${freq};INTERVAL=${interval}` : `FREQ=${freq}`;
}

/**
 * Sprawdza czy zadanie jest cykliczne
 */
export function isRecurringTask(frequency: TaskFrequency): boolean {
  return frequency !== 'ONCE';
}

/**
 * Oblicza następną datę wystąpienia zadania cyklicznego po podanej dacie
 */
export function getNextTaskOccurrence(
  options: RecurringTaskOptions,
  afterDate: Date = new Date()
): Date | null {
  const { startDate, endDate, frequency, interval = 1 } = options;

  if (frequency === 'ONCE') return null;

  let nextDate = startOfDay(afterDate);

  // Jeśli jesteśmy przed datą rozpoczęcia, zwracamy datę rozpoczęcia
  if (isBefore(nextDate, startOfDay(startDate))) {
    return startOfDay(startDate);
  }

  // Szukamy następnego wystąpienia
  const maxIterations = 1000; // Zabezpieczenie przed nieskończoną pętlą
  let iterations = 0;

  do {
    nextDate = getNextOccurrence(nextDate, frequency, interval);
    iterations++;

    if (iterations > maxIterations) {
      console.warn('getNextTaskOccurrence: przekroczono maksymalną liczbę iteracji');
      return null;
    }

    // Sprawdzamy czy nie przekroczyliśmy daty końcowej
    if (endDate && isAfter(nextDate, startOfDay(endDate))) {
      return null;
    }
  } while (!isTaskActiveOnDate(options, nextDate));

  return nextDate;
}
