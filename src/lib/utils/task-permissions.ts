import {
  FamilyRole,
  CompletionStatus,
  type TaskCompletion,
  type FamilyMember,
} from '@prisma/client';
import { canManageRole } from '@/lib/auth';

/**
 * Sprawdza czy użytkownik może modyfikować ukończone zadanie - wersja frontendowa
 */
export function canModifyCompletedTaskClient(
  userRole: FamilyRole,
  taskCreatorRole: FamilyRole,
  taskCompletedByRole: FamilyRole,
  completionDate: Date,
  isTaskCreator: boolean = false,
  isTaskCompleter: boolean = false
): boolean {
  // Właściciel może modyfikować wszystkie ukończone zadania
  if (userRole === ('OWNER' as FamilyRole)) return true;

  // Sprawdź czy zadanie jest "stare" (ukończone ponad 30 dni temu)
  const daysSinceCompletion = Math.floor(
    (Date.now() - completionDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  const isOldTask = daysSinceCompletion > 30;

  // Stare zadania mogą być modyfikowane tylko przez właściciela
  if (isOldTask && userRole !== ('OWNER' as FamilyRole)) {
    return false;
  }

  // Rodzice mogą modyfikować ukończone zadania utworzone przez siebie lub ukończone przez opiekunów/dzieci
  if (userRole === 'PARENT') {
    // Może modyfikować własne utworzone zadania
    if (isTaskCreator) return true;
    // Może modyfikować zadania ukończone przez opiekunów lub dzieci
    if (['GUARDIAN', 'CHILD'].includes(taskCompletedByRole)) return true;
    // Może modyfikować zadania utworzone przez niższych w hierarchii
    if (canManageRole(userRole, taskCreatorRole)) return true;
  }

  // Opiekunowie mogą modyfikować tylko zadania ukończone przez dzieci lub własne
  if (userRole === 'GUARDIAN') {
    // Może modyfikować własne ukończone zadania (w ciągu 7 dni)
    if (isTaskCompleter && daysSinceCompletion <= 7) return true;
    // Może modyfikować zadania ukończone przez dzieci
    if (taskCompletedByRole === 'CHILD') return true;
    // Może modyfikować własne utworzone zadania
    if (isTaskCreator) return true;
  }

  // Dzieci mogą modyfikować tylko własne ukończone zadania (w ciągu 24 godzin)
  if (userRole === 'CHILD') {
    if (isTaskCompleter && daysSinceCompletion <= 1) return true;
  }

  return false;
}

/**
 * Sprawdza czy użytkownik może usunąć ukończone zadanie - wersja frontendowa
 */
export function canDeleteCompletedTaskClient(
  userRole: FamilyRole,
  taskCreatorRole: FamilyRole,
  taskCompletedByRole: FamilyRole,
  completionDate: Date,
  isTaskCreator: boolean = false
): boolean {
  // Tylko właściciel może usuwać ukończone zadania
  if (userRole === ('OWNER' as FamilyRole)) return true;

  // Sprawdź czy zadanie jest bardzo nowe (ukończone mniej niż 1 godzinę temu)
  const hoursSinceCompletion = (Date.now() - completionDate.getTime()) / (1000 * 60 * 60);
  const isVeryNewTask = hoursSinceCompletion < 1;

  // Rodzice mogą usuwać tylko bardzo nowe zadania które sami utworzyli
  if (userRole === 'PARENT' && isTaskCreator && isVeryNewTask) {
    return true;
  }

  // Pozostałe role nie mogą usuwać ukończonych zadań
  return false;
}

/**
 * Sprawdza czy zadanie ma zatwierdzone ukończenia
 */
export function hasApprovedCompletions(completions: TaskCompletion[]): boolean {
  return completions.some(c => c.status === CompletionStatus.APPROVED);
}

/**
 * Sprawdza czy użytkownik może modyfikować zadanie z uwzględnieniem stanu ukończenia
 */
export function canModifyTask(
  task: any, // TaskWithRelations type
  currentMember: FamilyMember,
  canManageTasks: boolean
): { canEdit: boolean; canDelete: boolean; reason?: string } {
  // Jeśli nie ma uprawnień MANAGE_TASKS, nie może nic robić
  if (!canManageTasks) {
    return { canEdit: false, canDelete: false, reason: 'Brak uprawnień do zarządzania zadaniami' };
  }

  // Sprawdź czy zadanie ma zatwierdzone ukończenia
  const approvedCompletions =
    task.completions?.filter((c: any) => c.status === CompletionStatus.APPROVED) || [];

  if (approvedCompletions.length === 0) {
    // Brak zatwierdzonych ukończeń - normalne uprawnienia
    return { canEdit: true, canDelete: true };
  }

  // Zadanie ma zatwierdzone ukończenia - sprawdź rozszerzone uprawnienia
  const latestCompletion = approvedCompletions[0];
  const taskCreator = task.assignedByUser || task.assignedBy;

  if (!taskCreator || !latestCompletion.completedBy) {
    return {
      canEdit: false,
      canDelete: false,
      reason: 'Brak informacji o twórcy zadania lub osobie która je ukończyła',
    };
  }

  const isTaskCreator = currentMember.id === taskCreator.id;
  const isTaskCompleter = currentMember.id === latestCompletion.completedById;

  const canEdit = canModifyCompletedTaskClient(
    currentMember.role,
    taskCreator.role,
    latestCompletion.completedBy.role,
    new Date(latestCompletion.completedAt),
    isTaskCreator,
    isTaskCompleter
  );

  const canDelete = canDeleteCompletedTaskClient(
    currentMember.role,
    taskCreator.role,
    latestCompletion.completedBy.role,
    new Date(latestCompletion.completedAt),
    isTaskCreator
  );

  let reason = '';
  if (!canEdit && !canDelete) {
    reason =
      'Ukończone zadania mogą być modyfikowane tylko przez właściciela rodziny lub w określonych przypadkach przez ich twórców';
  } else if (!canDelete && canEdit) {
    reason = 'Możesz edytować to zadanie, ale nie możesz go usunąć';
  }

  return { canEdit, canDelete, reason };
}
