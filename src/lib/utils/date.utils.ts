import {
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  startOfDay,
  endOfDay,
  format,
  isWithinInterval,
  parseISO,
  isValid,
  addDays,
  addWeeks,
  addMonths,
  subDays,
  subWeeks,
  subMonths,
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  isSameDay,
  isSameWeek,
  isSameMonth,
  isToday,
  isFuture,
  isPast,
  getDay,
  setDay,
} from 'date-fns';
import { pl } from 'date-fns/locale';
import { WeekDay, TimeFormat } from '@prisma/client';
import { CalendarTask } from '@/lib/validations/calendar';

/**
 * Get month bounds for calendar view
 */
export function getMonthBounds(date: Date): { start: Date; end: Date } {
  return {
    start: startOfMonth(date),
    end: endOfMonth(date),
  };
}

/**
 * Get week bounds based on start day preference
 */
export function getWeekBounds(date: Date, startWeekOn: WeekDay): { start: Date; end: Date } {
  const weekStartsOn = startWeekOn === WeekDay.SUNDAY ? 0 : 1;

  return {
    start: startOfWeek(date, { weekStartsOn }),
    end: endOfWeek(date, { weekStartsOn }),
  };
}

/**
 * Get day bounds
 */
export function getDayBounds(date: Date): { start: Date; end: Date } {
  return {
    start: startOfDay(date),
    end: endOfDay(date),
  };
}

/**
 * Format event time based on time format preference
 */
export function formatEventTime(event: CalendarTask, timeFormat: TimeFormat): string {
  if (event.allDay) {
    return 'Cały dzień';
  }

  const formatPattern = timeFormat === TimeFormat.HOUR_12 ? 'h:mm a' : 'HH:mm';
  const startTime = format(event.start, formatPattern, { locale: pl });

  if (event.end) {
    const endTime = format(event.end, formatPattern, { locale: pl });
    return `${startTime} - ${endTime}`;
  }

  return startTime;
}

/**
 * Format date for display in various contexts
 */
export function formatDateForDisplay(
  date: Date,
  formatType: 'short' | 'medium' | 'long' | 'time' = 'medium'
): string {
  switch (formatType) {
    case 'short':
      return format(date, 'd.MM.yyyy', { locale: pl });
    case 'medium':
      return format(date, 'd MMMM yyyy', { locale: pl });
    case 'long':
      return format(date, 'EEEE, d MMMM yyyy', { locale: pl });
    case 'time':
      return format(date, 'HH:mm', { locale: pl });
    default:
      return format(date, 'd MMMM yyyy', { locale: pl });
  }
}

/**
 * Get relative time description (e.g., "za 2 dni", "wczoraj")
 */
export function getRelativeTimeDescription(date: Date): string {
  const now = new Date();
  const daysDiff = differenceInDays(date, now);

  if (isToday(date)) {
    return 'dziś';
  }

  if (daysDiff === 1) {
    return 'jutro';
  }

  if (daysDiff === -1) {
    return 'wczoraj';
  }

  if (daysDiff > 0 && daysDiff <= 7) {
    return `za ${daysDiff} ${daysDiff === 1 ? 'dzień' : 'dni'}`;
  }

  if (daysDiff < 0 && daysDiff >= -7) {
    return `${Math.abs(daysDiff)} ${Math.abs(daysDiff) === 1 ? 'dzień' : 'dni'} temu`;
  }

  if (daysDiff > 7) {
    const weeksDiff = Math.floor(daysDiff / 7);
    return `za ${weeksDiff} ${weeksDiff === 1 ? 'tydzień' : 'tygodni'}`;
  }

  if (daysDiff < -7) {
    const weeksDiff = Math.floor(Math.abs(daysDiff) / 7);
    return `${weeksDiff} ${weeksDiff === 1 ? 'tydzień' : 'tygodni'} temu`;
  }

  return formatDateForDisplay(date, 'short');
}

/**
 * Check if date is within business hours
 */
export function isWithinBusinessHours(date: Date, startHour = 8, endHour = 18): boolean {
  const hour = date.getHours();
  return hour >= startHour && hour <= endHour;
}

/**
 * Check if date is on weekend
 */
export function isWeekend(date: Date): boolean {
  const day = getDay(date);
  return day === 0 || day === 6; // Sunday or Saturday
}

/**
 * Get next business day
 */
export function getNextBusinessDay(date: Date): Date {
  let nextDay = addDays(date, 1);

  while (isWeekend(nextDay)) {
    nextDay = addDays(nextDay, 1);
  }

  return nextDay;
}

/**
 * Convert time string to Date object for today
 */
export function timeStringToDate(timeString: string, baseDate: Date = new Date()): Date | null {
  // Parse time in format "HH:MM" or "H:MM"
  const timeRegex = /^(\d{1,2}):(\d{2})$/;
  const match = timeString.match(timeRegex);

  if (!match) {
    return null;
  }

  const hours = parseInt(match[1], 10);
  const minutes = parseInt(match[2], 10);

  if (hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    return null;
  }

  const result = new Date(baseDate);
  result.setHours(hours, minutes, 0, 0);

  return result;
}

/**
 * Calculate duration between two dates in human-readable format
 */
export function calculateDuration(start: Date, end: Date): string {
  const diffMinutes = differenceInMinutes(end, start);

  if (diffMinutes < 60) {
    return `${diffMinutes} min`;
  }

  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;

  if (hours < 24) {
    return minutes > 0 ? `${hours}h ${minutes}min` : `${hours}h`;
  }

  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;

  return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
}

/**
 * Detect conflicts between calendar events
 */
interface ConflictGroup {
  events: CalendarTask[];
  startTime: Date;
  endTime: Date;
  severity: 'low' | 'medium' | 'high';
}

export function detectDateConflicts(events: CalendarTask[]): ConflictGroup[] {
  const conflicts: ConflictGroup[] = [];
  const sortedEvents = [...events].sort((a, b) => a.start.getTime() - b.start.getTime());

  for (let i = 0; i < sortedEvents.length; i++) {
    const currentEvent = sortedEvents[i];
    const conflictingEvents = [currentEvent];

    // Skip all-day events for conflict detection
    if (currentEvent.allDay) continue;

    const currentEnd = currentEvent.end || currentEvent.start;

    for (let j = i + 1; j < sortedEvents.length; j++) {
      const nextEvent = sortedEvents[j];

      // Skip all-day events
      if (nextEvent.allDay) continue;

      const nextEnd = nextEvent.end || nextEvent.start;

      // Check for overlap
      if (nextEvent.start < currentEnd && currentEvent.start < nextEnd) {
        conflictingEvents.push(nextEvent);
      }
    }

    if (conflictingEvents.length > 1) {
      const startTimes = conflictingEvents.map(e => e.start);
      const endTimes = conflictingEvents.map(e => e.end || e.start);

      conflicts.push({
        events: conflictingEvents,
        startTime: new Date(Math.min(...startTimes.map(d => d.getTime()))),
        endTime: new Date(Math.max(...endTimes.map(d => d.getTime()))),
        severity:
          conflictingEvents.length > 3 ? 'high' : conflictingEvents.length > 2 ? 'medium' : 'low',
      });
    }
  }

  return conflicts;
}

/**
 * Generate time slots for a given day
 */
export function generateTimeSlots(
  date: Date,
  intervalMinutes: number = 30,
  startHour: number = 8,
  endHour: number = 20
): Date[] {
  const slots: Date[] = [];
  const startTime = new Date(date);
  startTime.setHours(startHour, 0, 0, 0);

  const endTime = new Date(date);
  endTime.setHours(endHour, 0, 0, 0);

  let currentSlot = new Date(startTime);

  while (currentSlot <= endTime) {
    slots.push(new Date(currentSlot));
    currentSlot = new Date(currentSlot.getTime() + intervalMinutes * 60 * 1000);
  }

  return slots;
}

/**
 * Check if a time slot is available (no conflicts with existing events)
 */
export function isTimeSlotAvailable(
  startTime: Date,
  endTime: Date,
  existingEvents: CalendarTask[]
): boolean {
  return !existingEvents.some(event => {
    if (event.allDay) return false;

    const eventEnd = event.end || event.start;

    return (
      (startTime >= event.start && startTime < eventEnd) ||
      (endTime > event.start && endTime <= eventEnd) ||
      (startTime <= event.start && endTime >= eventEnd)
    );
  });
}

/**
 * Find available time slots in a day
 */
export function findAvailableTimeSlots(
  date: Date,
  durationMinutes: number,
  existingEvents: CalendarTask[],
  intervalMinutes: number = 30,
  startHour: number = 8,
  endHour: number = 20
): { start: Date; end: Date }[] {
  const allSlots = generateTimeSlots(date, intervalMinutes, startHour, endHour);
  const availableSlots: { start: Date; end: Date }[] = [];

  for (const slot of allSlots) {
    const slotEnd = new Date(slot.getTime() + durationMinutes * 60 * 1000);

    // Don't suggest slots that extend beyond end hour
    if (slotEnd.getHours() > endHour) continue;

    if (isTimeSlotAvailable(slot, slotEnd, existingEvents)) {
      availableSlots.push({
        start: slot,
        end: slotEnd,
      });
    }
  }

  return availableSlots;
}

/**
 * Parse natural language date/time inputs (basic implementation)
 */
export function parseNaturalLanguageDateTime(
  input: string,
  baseDate: Date = new Date()
): Date | null {
  const lowerInput = input.toLowerCase().trim();

  // Handle "dziś", "jutro", "wczoraj"
  if (lowerInput === 'dziś' || lowerInput === 'dzisiaj') {
    return new Date(baseDate);
  }

  if (lowerInput === 'jutro') {
    return addDays(baseDate, 1);
  }

  if (lowerInput === 'wczoraj') {
    return subDays(baseDate, 1);
  }

  // Handle "za X dni"
  const daysPattern = /za (\d+) dni?/;
  const daysMatch = lowerInput.match(daysPattern);
  if (daysMatch) {
    const days = parseInt(daysMatch[1], 10);
    return addDays(baseDate, days);
  }

  // Handle time patterns like "14:30", "2:30 PM"
  const timePattern = /(\d{1,2}):(\d{2})/;
  const timeMatch = lowerInput.match(timePattern);
  if (timeMatch) {
    return timeStringToDate(`${timeMatch[1]}:${timeMatch[2]}`, baseDate);
  }

  // Try to parse as ISO date
  try {
    const parsed = parseISO(input);
    if (isValid(parsed)) {
      return parsed;
    }
  } catch {
    // Ignore parsing errors
  }

  return null;
}
