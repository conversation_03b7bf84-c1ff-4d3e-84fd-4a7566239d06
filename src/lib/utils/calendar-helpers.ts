/**
 * Utility functions dla kalendarza - wsparcie dla zadań cyklicznych
 */

import { startOfMonth, endOfMonth, startOfWeek, endOfWeek, startOfDay, endOfDay } from 'date-fns';
import { Task } from '@prisma/client';
import {
  generateRecurringDates,
  isTaskActiveOnDate,
  getRecurringTasksForDateRange,
  parseIntervalFromRRule,
  RecurringTaskOptions,
  TaskOccurrence,
} from './recurring-tasks';

import { CalendarTask } from '@/lib/validations/calendar';

export interface CalendarDateRange {
  start: Date;
  end: Date;
}

/**
 * Konwertuje Task z bazy danych na format CalendarTask
 */
export function taskToCalendarTask(task: Task & { assignments?: any[] }): CalendarTask {
  return {
    id: task.id,
    title: task.title,
    description: task.description || undefined,
    start: task.startDate || task.dueDate || new Date(),
    end: task.endDate || undefined,
    allDay: task.allDay,
    color: task.color || undefined,
    type: 'task',
    isRecurring: task.frequency !== 'ONCE',
    frequency: task.frequency,
    recurringInterval: parseIntervalFromRRule(task.recurrenceRule || undefined),
    recurrenceRule: task.recurrenceRule || undefined,
    assignments: task.assignments,
  };
}

/**
 * Generuje CalendarTask dla wszystkich wystąpień zadań cyklicznych w podanym zakresie
 */
export function generateCalendarTasksForRecurring(
  recurringTasks: (Task & { assignments?: any[] })[],
  rangeStart: Date,
  rangeEnd: Date
): CalendarTask[] {
  const calendarTasks: CalendarTask[] = [];

  for (const task of recurringTasks) {
    if (task.frequency === 'ONCE' || !task.startDate) {
      continue; // Pomijamy zadania jednorazowe
    }

    const recurringOptions: RecurringTaskOptions = {
      startDate: task.startDate,
      endDate: task.recurrenceEnd || task.endDate || undefined,
      frequency: task.frequency,
      interval: parseIntervalFromRRule(task.recurrenceRule || undefined),
      recurrenceRule: task.recurrenceRule || undefined,
    };

    const occurrences = generateRecurringDates(recurringOptions, rangeStart, rangeEnd);

    // Tworzymy CalendarTask dla każdego wystąpienia
    occurrences.forEach((occurrence, index) => {
      const duration =
        task.endDate && task.startDate ? task.endDate.getTime() - task.startDate.getTime() : 0;

      calendarTasks.push({
        id: `${task.id}-occurrence-${occurrence.date.getTime()}`,
        title: task.title,
        description: task.description || undefined,
        start: occurrence.date,
        end: duration > 0 ? new Date(occurrence.date.getTime() + duration) : undefined,
        allDay: task.allDay,
        color: task.color || undefined,
        type: 'task',
        isRecurring: true,
        frequency: task.frequency,
        recurringInterval: parseIntervalFromRRule(task.recurrenceRule || undefined),
        originalTaskId: task.id,
        recurrenceRule: task.recurrenceRule || undefined,
        assignments: task.assignments,
      });
    });
  }

  return calendarTasks;
}

/**
 * Łączy zadania jednorazowe z wystąpieniami zadań cyklicznych
 */
export function combineTasksForCalendar(
  oneTimeTasks: (Task & { assignments?: any[] })[],
  recurringTasks: (Task & { assignments?: any[] })[],
  rangeStart: Date,
  rangeEnd: Date
): CalendarTask[] {
  // Konwertujemy zadania jednorazowe
  const oneTimeCalendarTasks = oneTimeTasks
    .filter(task => task.frequency === 'ONCE')
    .map(task => taskToCalendarTask(task));

  // Generujemy wystąpienia zadań cyklicznych
  const recurringCalendarTasks = generateCalendarTasksForRecurring(
    recurringTasks.filter(task => task.frequency !== 'ONCE'),
    rangeStart,
    rangeEnd
  );

  return [...oneTimeCalendarTasks, ...recurringCalendarTasks];
}

/**
 * Pobiera zakres dat dla danego widoku kalendarza
 */
export function getCalendarRange(view: 'month' | 'week' | 'day', date: Date): CalendarDateRange {
  switch (view) {
    case 'month':
      return {
        start: startOfWeek(startOfMonth(date), { weekStartsOn: 1 }), // Poniedziałek
        end: endOfWeek(endOfMonth(date), { weekStartsOn: 1 }),
      };

    case 'week':
      return {
        start: startOfWeek(date, { weekStartsOn: 1 }),
        end: endOfWeek(date, { weekStartsOn: 1 }),
      };

    case 'day':
      return {
        start: startOfDay(date),
        end: endOfDay(date),
      };

    default:
      throw new Error(`Nieobsługiwany widok kalendarza: ${view}`);
  }
}

/**
 * Filtruje zadania dla konkretnego dnia
 */
export function getTasksForDay(tasks: CalendarTask[], day: Date): CalendarTask[] {
  const dayStart = startOfDay(day);
  const dayEnd = endOfDay(day);

  return tasks.filter(task => {
    const taskStart = startOfDay(task.start);
    const taskEnd = task.end ? startOfDay(task.end) : taskStart;

    // Sprawdzamy czy zadanie zachodzi na dany dzień
    return taskStart <= dayEnd && taskEnd >= dayStart;
  });
}

/**
 * Grupuje zadania według dat
 */
export function groupTasksByDate(tasks: CalendarTask[]): Map<string, CalendarTask[]> {
  const grouped = new Map<string, CalendarTask[]>();

  tasks.forEach(task => {
    const dateKey = startOfDay(task.start).toISOString().split('T')[0];

    if (!grouped.has(dateKey)) {
      grouped.set(dateKey, []);
    }

    grouped.get(dateKey)!.push(task);
  });

  return grouped;
}

/**
 * Sprawdza czy dany dzień ma jakieś zadania
 */
export function hasTasksOnDay(tasks: CalendarTask[], day: Date): boolean {
  return getTasksForDay(tasks, day).length > 0;
}

/**
 * Liczy zadania w danym dniu
 */
export function countTasksOnDay(tasks: CalendarTask[], day: Date): number {
  return getTasksForDay(tasks, day).length;
}

/**
 * Sprawdza czy zadanie cykliczne jest aktywne w danym dniu
 * (wrapper dla isTaskActiveOnDate z Task object)
 */
export function isRecurringTaskActiveOnDay(task: Task, day: Date): boolean {
  if (task.frequency === 'ONCE') return false;
  if (!task.startDate) return false;

  const recurringOptions: RecurringTaskOptions = {
    startDate: task.startDate,
    endDate: task.recurrenceEnd || task.endDate || undefined,
    frequency: task.frequency,
    interval: parseIntervalFromRRule(task.recurrenceRule || undefined),
    recurrenceRule: task.recurrenceRule || undefined,
  };

  return isTaskActiveOnDate(recurringOptions, day);
}

/**
 * Sortuje zadania według priorytetu wyświetlania w kalendarzu
 */
export function sortTasksForCalendar(tasks: CalendarTask[]): CalendarTask[] {
  return tasks.sort((a, b) => {
    // Najpierw zadania całodniowe
    if (a.allDay && !b.allDay) return -1;
    if (!a.allDay && b.allDay) return 1;

    // Potem według czasu rozpoczęcia
    return a.start.getTime() - b.start.getTime();
  });
}

/**
 * Sprawdza czy zadanie jest przeterminowane
 */
export function isTaskOverdue(task: CalendarTask): boolean {
  const now = new Date();
  const taskEnd = task.end || task.start;

  return taskEnd < now;
}

/**
 * Pobiera kolor zadania z uwzględnieniem stanu
 */
export function getTaskDisplayColor(task: CalendarTask): string {
  if (isTaskOverdue(task)) {
    return '#ef4444'; // Czerwony dla przeterminowanych
  }

  if (task.color) {
    return task.color;
  }

  // Domyślny kolor w zależności od typu
  if (task.isRecurring) {
    return '#8b5cf6'; // Fioletowy dla cyklicznych
  }

  return '#6b7280'; // Szary dla zwykłych
}

/**
 * Parsuje ID zadania z możliwym suffixem occurrence
 */
export function parseOccurrenceId(taskId: string): {
  originalTaskId: string;
  isOccurrence: boolean;
  occurrenceTimestamp?: number;
} {
  const occurrencePattern = /^(.+)-occurrence-(\d+)$/;
  const match = taskId.match(occurrencePattern);

  if (match) {
    return {
      originalTaskId: match[1],
      isOccurrence: true,
      occurrenceTimestamp: parseInt(match[2], 10),
    };
  }

  return {
    originalTaskId: taskId,
    isOccurrence: false,
  };
}
