/**
 * Zaawansowany system cache dla zadań cyklicznych
 * Optymalizuje wydajność generowania wystąpień
 */

import { CalendarTask } from '@/lib/validations/calendar';
import { TaskFrequency } from '@prisma/client';
import { generateRecurringDates, RecurringTaskOptions, TaskOccurrence } from './recurring-tasks';
import { differenceInDays, startOfDay } from 'date-fns';

interface CacheEntry {
  occurrences: TaskOccurrence[];
  generated: number; // timestamp
  startDate: string;
  endDate: string;
  taskId: string;
  frequency: TaskFrequency;
  interval: number;
  expiresAt: number; // timestamp
}

interface CacheStats {
  hits: number;
  misses: number;
  entries: number;
  totalSize: number; // w bajtach (aproksymacja)
}

export class RecurringTasksCache {
  private static cache = new Map<string, CacheEntry>();
  private static stats: CacheStats = {
    hits: 0,
    misses: 0,
    entries: 0,
    totalSize: 0,
  };

  // Konfiguracja cache
  private static readonly MAX_ENTRIES = 100;
  private static readonly DEFAULT_TTL = 30 * 60 * 1000; // 30 minut
  private static readonly MAX_RANGE_DAYS = 365; // Maksymalny zakres do cache'owania

  /**
   * Generuje klucz cache dla zadania i zakresu dat
   */
  private static generateCacheKey(
    taskId: string,
    startDate: Date,
    endDate: Date,
    frequency: TaskFrequency,
    interval: number
  ): string {
    const start = startOfDay(startDate).toISOString().split('T')[0];
    const end = startOfDay(endDate).toISOString().split('T')[0];
    return `${taskId}-${frequency}-${interval}-${start}-${end}`;
  }

  /**
   * Sprawdza czy entry jest aktualny
   */
  private static isEntryValid(entry: CacheEntry): boolean {
    return Date.now() < entry.expiresAt;
  }

  /**
   * Oblicza TTL na podstawie częstotliwości zadania
   */
  private static calculateTTL(frequency: TaskFrequency, interval: number): number {
    const baseTTL = this.DEFAULT_TTL;

    // Częstsze zadania mają krótszy cache
    switch (frequency) {
      case 'DAILY':
        return interval === 1 ? baseTTL / 4 : baseTTL / 2; // 7.5min / 15min
      case 'WEEKLY':
        return baseTTL; // 30min
      case 'MONTHLY':
        return baseTTL * 2; // 1h
      case 'YEARLY':
        return baseTTL * 4; // 2h
      case 'CUSTOM':
        return baseTTL / 2; // 15min
      default:
        return baseTTL;
    }
  }

  /**
   * Sprawdza czy zakres dat jest odpowiedni do cache'owania
   */
  private static shouldCache(startDate: Date, endDate: Date): boolean {
    const daysDiff = differenceInDays(endDate, startDate);
    return daysDiff <= this.MAX_RANGE_DAYS && daysDiff > 0;
  }

  /**
   * Pobiera wystąpienia z cache lub generuje nowe
   */
  static getOccurrences(
    taskId: string,
    options: RecurringTaskOptions,
    startDate: Date,
    endDate: Date
  ): TaskOccurrence[] {
    // Sprawdź czy warto cache'ować
    if (!this.shouldCache(startDate, endDate)) {
      this.stats.misses++;
      return generateRecurringDates(options, startDate, endDate);
    }

    const interval = options.interval || 1;
    const cacheKey = this.generateCacheKey(taskId, startDate, endDate, options.frequency, interval);

    // Sprawdź cache
    const cached = this.cache.get(cacheKey);
    if (cached && this.isEntryValid(cached)) {
      this.stats.hits++;
      return cached.occurrences;
    }

    // Cache miss - generuj wystąpienia
    this.stats.misses++;
    const occurrences = generateRecurringDates(options, startDate, endDate);

    // Zapisz w cache
    const ttl = this.calculateTTL(options.frequency, interval);
    const entry: CacheEntry = {
      occurrences,
      generated: Date.now(),
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      taskId,
      frequency: options.frequency,
      interval,
      expiresAt: Date.now() + ttl,
    };

    this.setEntry(cacheKey, entry);
    return occurrences;
  }

  /**
   * Zapisuje entry do cache z cleanup
   */
  private static setEntry(key: string, entry: CacheEntry): void {
    // Cleanup jeśli przekroczono limit
    if (this.cache.size >= this.MAX_ENTRIES) {
      this.evictOldEntries();
    }

    this.cache.set(key, entry);
    this.updateStats();
  }

  /**
   * Usuwa najstarsze entries
   */
  private static evictOldEntries(): void {
    const now = Date.now();
    const entriesToRemove: string[] = [];

    // Najpierw usuń wygasłe
    for (const [key, entry] of this.cache) {
      if (!this.isEntryValid(entry)) {
        entriesToRemove.push(key);
      }
    }

    // Jeśli to nie wystarczy, usuń najstarsze
    if (entriesToRemove.length < 20) {
      const sortedEntries = Array.from(this.cache.entries()).sort(
        ([, a], [, b]) => a.generated - b.generated
      );

      const toRemove = sortedEntries.slice(0, 20 - entriesToRemove.length);
      entriesToRemove.push(...toRemove.map(([key]) => key));
    }

    // Usuń entries
    entriesToRemove.forEach(key => this.cache.delete(key));
  }

  /**
   * Aktualizuje statystyki cache
   */
  private static updateStats(): void {
    this.stats.entries = this.cache.size;

    // Aproksymacja rozmiaru cache
    let totalSize = 0;
    for (const entry of this.cache.values()) {
      totalSize += entry.occurrences.length * 100; // ~100 bajtów na occurrence
      totalSize += 200; // metadata entry
    }
    this.stats.totalSize = totalSize;
  }

  /**
   * Invaliduje cache dla konkretnego zadania
   */
  static invalidateTask(taskId: string): void {
    const keysToRemove: string[] = [];

    for (const [key, entry] of this.cache) {
      if (entry.taskId === taskId) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => this.cache.delete(key));
    this.updateStats();
  }

  /**
   * Invaliduje cache dla zakresu dat
   */
  static invalidateDateRange(startDate: Date, endDate: Date): void {
    const start = startOfDay(startDate);
    const end = startOfDay(endDate);
    const keysToRemove: string[] = [];

    for (const [key, entry] of this.cache) {
      const entryStart = new Date(entry.startDate);
      const entryEnd = new Date(entry.endDate);

      // Sprawdź czy zakresy się nakładają
      if (entryStart <= end && entryEnd >= start) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => this.cache.delete(key));
    this.updateStats();
  }

  /**
   * Preloaduje cache dla popularnych zakresów
   */
  static async preloadCache(
    tasks: Array<{ id: string; options: RecurringTaskOptions }>,
    commonRanges: Array<{ start: Date; end: Date }>
  ): Promise<void> {
    const preloadPromises: Promise<void>[] = [];

    for (const task of tasks) {
      for (const range of commonRanges) {
        if (this.shouldCache(range.start, range.end)) {
          const promise = new Promise<void>(resolve => {
            // Asynchroniczne generowanie w tle
            setTimeout(() => {
              this.getOccurrences(task.id, task.options, range.start, range.end);
              resolve();
            }, 0);
          });
          preloadPromises.push(promise);
        }
      }
    }

    await Promise.all(preloadPromises);
  }

  /**
   * Czyści cały cache
   */
  static clearCache(): void {
    this.cache.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      entries: 0,
      totalSize: 0,
    };
  }

  /**
   * Pobiera statystyki cache
   */
  static getStats(): CacheStats & { hitRate: number } {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;

    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
    };
  }

  /**
   * Cleanup wygasłych entries (do wywołania okresowego)
   */
  static cleanup(): void {
    const now = Date.now();
    const keysToRemove: string[] = [];

    for (const [key, entry] of this.cache) {
      if (!this.isEntryValid(entry)) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => this.cache.delete(key));
    this.updateStats();
  }

  /**
   * Zwraca informacje debug o cache
   */
  static getDebugInfo(): {
    stats: ReturnType<typeof this.getStats>;
    entries: Array<{
      key: string;
      taskId: string;
      frequency: TaskFrequency;
      occurrences: number;
      generated: Date;
      expiresAt: Date;
      valid: boolean;
    }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      taskId: entry.taskId,
      frequency: entry.frequency,
      occurrences: entry.occurrences.length,
      generated: new Date(entry.generated),
      expiresAt: new Date(entry.expiresAt),
      valid: this.isEntryValid(entry),
    }));

    return {
      stats: this.getStats(),
      entries,
    };
  }
}

/**
 * Hook do automatycznego cleanup cache
 */
export function useRecurringTasksCache() {
  // Cleanup co 5 minut
  if (typeof window !== 'undefined') {
    setInterval(
      () => {
        RecurringTasksCache.cleanup();
      },
      5 * 60 * 1000
    );
  }

  return {
    getOccurrences: RecurringTasksCache.getOccurrences.bind(RecurringTasksCache),
    invalidateTask: RecurringTasksCache.invalidateTask.bind(RecurringTasksCache),
    invalidateDateRange: RecurringTasksCache.invalidateDateRange.bind(RecurringTasksCache),
    clearCache: RecurringTasksCache.clearCache.bind(RecurringTasksCache),
    getStats: RecurringTasksCache.getStats.bind(RecurringTasksCache),
    preloadCache: RecurringTasksCache.preloadCache.bind(RecurringTasksCache),
  };
}
