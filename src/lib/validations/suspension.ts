import { z } from 'zod';

export const createSuspensionSchema = z
  .object({
    title: z
      .string()
      .min(1, 'Tytuł jest wymagany')
      .max(200, 'Tytuł może mieć maksymalnie 200 znaków'),
    description: z.string().max(1000, 'Opis może mieć maksymalnie 1000 znaków').optional(),
    reason: z.enum(
      ['HOLIDAY_VACATION', 'TRAVEL', 'ILLNESS', 'SCHOOL_BREAK', 'MAINTENANCE', 'OTHER'],
      {
        errorMap: () => ({ message: 'Nieprawidłowy powód zawieszenia' }),
      }
    ),
    startDate: z.date({
      required_error: 'Data rozpoczęcia jest wymagana',
      invalid_type_error: 'Nieprawidłowy format daty rozpoczęcia',
    }),
    endDate: z.date({
      required_error: 'Data zakończenia jest wymagana',
      invalid_type_error: 'Nieprawidłowy format daty zakończenia',
    }),
    taskIds: z.array(z.string()).optional().default([]),
    memberIds: z.array(z.string()).optional().default([]),
  })
  .refine(data => data.endDate > data.startDate, {
    message: 'Data końca musi być późniejsza niż data rozpoczęcia',
    path: ['endDate'],
  })
  .refine(data => data.startDate >= new Date(new Date().setHours(0, 0, 0, 0)), {
    message: 'Data rozpoczęcia nie może być w przeszłości',
    path: ['startDate'],
  });

export const updateSuspensionSchema = z
  .object({
    title: z
      .string()
      .min(1, 'Tytuł jest wymagany')
      .max(200, 'Tytuł może mieć maksymalnie 200 znaków')
      .optional(),
    description: z.string().max(1000, 'Opis może mieć maksymalnie 1000 znaków').optional(),
    reason: z
      .enum(['HOLIDAY_VACATION', 'TRAVEL', 'ILLNESS', 'SCHOOL_BREAK', 'MAINTENANCE', 'OTHER'], {
        errorMap: () => ({ message: 'Nieprawidłowy powód zawieszenia' }),
      })
      .optional(),
    startDate: z
      .date({
        invalid_type_error: 'Nieprawidłowy format daty rozpoczęcia',
      })
      .optional(),
    endDate: z
      .date({
        invalid_type_error: 'Nieprawidłowy format daty zakończenia',
      })
      .optional(),
    status: z
      .enum(['ACTIVE', 'ENDED', 'CANCELLED'], {
        errorMap: () => ({ message: 'Nieprawidłowy status zawieszenia' }),
      })
      .optional(),
  })
  .refine(
    data => {
      if (data.startDate && data.endDate) {
        return data.endDate > data.startDate;
      }
      return true;
    },
    {
      message: 'Data końca musi być późniejsza niż data rozpoczęcia',
      path: ['endDate'],
    }
  );

export const manageSuspensionItemsSchema = z
  .object({
    taskIds: z.array(z.string()).optional().default([]),
    memberIds: z.array(z.string()).optional().default([]),
  })
  .refine(data => data.taskIds.length > 0 || data.memberIds.length > 0, {
    message: 'Musisz wybrać przynajmniej jedno zadanie lub członka',
    path: ['taskIds'],
  });

export const suspensionFiltersSchema = z.object({
  status: z.array(z.enum(['ACTIVE', 'ENDED', 'CANCELLED'])).optional(),
  reason: z
    .array(
      z.enum(['HOLIDAY_VACATION', 'TRAVEL', 'ILLNESS', 'SCHOOL_BREAK', 'MAINTENANCE', 'OTHER'])
    )
    .optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  search: z.string().optional(),
  includeExpired: z.boolean().optional().default(false),
});

// Types extracted from schemas
export type CreateSuspensionData = z.infer<typeof createSuspensionSchema>;
export type UpdateSuspensionData = z.infer<typeof updateSuspensionSchema>;
export type ManageSuspensionItemsData = z.infer<typeof manageSuspensionItemsSchema>;
export type SuspensionFilters = z.infer<typeof suspensionFiltersSchema>;

// Helper function to convert string dates to Date objects
export const parseCreateSuspensionData = (data: any): CreateSuspensionData => {
  return createSuspensionSchema.parse({
    ...data,
    startDate: typeof data.startDate === 'string' ? new Date(data.startDate) : data.startDate,
    endDate: typeof data.endDate === 'string' ? new Date(data.endDate) : data.endDate,
  });
};

export const parseUpdateSuspensionData = (data: any): UpdateSuspensionData => {
  return updateSuspensionSchema.parse({
    ...data,
    startDate: data.startDate
      ? typeof data.startDate === 'string'
        ? new Date(data.startDate)
        : data.startDate
      : undefined,
    endDate: data.endDate
      ? typeof data.endDate === 'string'
        ? new Date(data.endDate)
        : data.endDate
      : undefined,
  });
};

// Validation helpers
export const validateSuspensionDates = (startDate: Date, endDate: Date) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  if (startDate < today) {
    throw new Error('Data rozpoczęcia nie może być w przeszłości');
  }

  if (endDate <= startDate) {
    throw new Error('Data zakończenia musi być późniejsza niż data rozpoczęcia');
  }

  // Check if suspension is longer than 1 year
  const oneYearLater = new Date(startDate);
  oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);

  if (endDate > oneYearLater) {
    throw new Error('Zawieszenie nie może trwać dłużej niż rok');
  }
};

// Reason display helpers
export const getSuspensionReasonLabel = (reason: string): string => {
  const labels: Record<string, string> = {
    HOLIDAY_VACATION: 'Święta/Urlop',
    TRAVEL: 'Wyjazd',
    ILLNESS: 'Choroba',
    SCHOOL_BREAK: 'Przerwa szkolna',
    MAINTENANCE: 'Prace remontowe',
    OTHER: 'Inny powód',
  };

  return labels[reason] || reason;
};

export const getSuspensionStatusLabel = (status: string): string => {
  const labels: Record<string, string> = {
    ACTIVE: 'Aktywne',
    ENDED: 'Zakończone',
    CANCELLED: 'Anulowane',
  };

  return labels[status] || status;
};
