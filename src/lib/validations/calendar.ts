import { z } from 'zod';
import { CalendarView, WeekDay, TimeFormat } from '@prisma/client';

/**
 * UWAGA: Ten plik definiuje typy dla kalen<PERSON>, ale ka<PERSON> "CalendarTask"
 * to w rzeczywistości zadanie (Task) wyświetlane w formie kalendarza.
 * Aplikacja nie obsługuje prawdziwych "wydarzeń" - wszystko to są zadania
 * z różnymi formatami czasowymi (startDate, endDate, allDay, itp.)
 */

export const createCalendarEventSchema = z
  .object({
    title: z
      .string()
      .min(1, 'Tytuł jest wymagany')
      .max(200, 'Tytuł nie może przekraczać 200 znaków'),
    description: z.string().max(1000, 'Opis nie może przekraczać 1000 znaków').optional(),
    startDate: z.string().pipe(z.coerce.date()).or(z.date()),
    endDate: z.string().pipe(z.coerce.date()).or(z.date()).optional(),
    allDay: z.boolean().default(true),
    assignedMemberIds: z.array(z.string()).optional(),
    color: z
      .string()
      .regex(/^#[0-9A-F]{6}$/i, 'Kolor musi być w formacie hex (#RRGGBB)')
      .optional(),
    recurrenceRule: z.string().optional(),
  })
  .refine(data => !data.endDate || new Date(data.endDate) > new Date(data.startDate), {
    message: 'Data końca musi być późniejsza niż data rozpoczęcia',
    path: ['endDate'],
  });

export const updateCalendarEventSchema = z.object({
  title: z
    .string()
    .min(1, 'Tytuł jest wymagany')
    .max(200, 'Tytuł nie może przekraczać 200 znaków')
    .optional(),
  description: z.string().max(1000, 'Opis nie może przekraczać 1000 znaków').optional(),
  startDate: z.string().pipe(z.coerce.date()).or(z.date()).optional(),
  endDate: z.string().pipe(z.coerce.date()).or(z.date()).optional(),
  allDay: z.boolean().optional(),
  assignedMemberIds: z.array(z.string()).optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, 'Kolor musi być w formacie hex (#RRGGBB)')
    .optional(),
  recurrenceRule: z.string().optional(),
});

export const calendarSettingsSchema = z.object({
  defaultView: z.nativeEnum(CalendarView),
  startWeekOn: z.nativeEnum(WeekDay),
  timeFormat: z.nativeEnum(TimeFormat),
  showWeekends: z.boolean(),
  showCompleted: z.boolean(),
  colorScheme: z.string(),
  googleCalendarEnabled: z.boolean(),
  appleCalendarEnabled: z.boolean(),
});

export const calendarFiltersSchema = z.object({
  memberId: z.string().optional(),
  taskStatus: z.array(z.string()).optional(),
  taskCategory: z.array(z.string()).optional(),
  showCompleted: z.boolean().default(false),
  startDate: z.date(),
  endDate: z.date(),
});

// Types
/**
 * CalendarTask reprezentuje zadanie wyświetlane w kalendarzu.
 *
 * UWAGA ARCHITEKTURALNA:
 * - Wszystkie elementy kalendarza to zadania (Task z bazy danych)
 * - Nie ma osobnych "wydarzeń" vs "zadań" - to wszystko są zadania
 * - Zadania mogą mieć różne formaty czasowe:
 *   - startDate + endDate (jak wydarzenie)
 *   - tylko dueDate (tradycyjne zadanie)
 *   - allDay = true (całodniowe)
 * - type jest zawsze 'task'
 */
export interface CalendarTask {
  id: string;
  title: string;
  description?: string;
  start: Date; // startDate lub dueDate z Task
  end?: Date; // endDate z Task (opcjonalne)
  allDay: boolean;
  color?: string;
  type: 'task'; // Zawsze 'task' - aplikacja nie obsługuje wydarzeń
  // Pola dla zadań cyklicznych
  isRecurring?: boolean; // Czy zadanie jest cykliczne
  frequency?: import('@prisma/client').TaskFrequency; // Częstotliwość powtarzania
  recurringInterval?: number; // Interwał dla zadań niestandardowych
  originalTaskId?: string; // ID oryginalnego zadania (dla wystąpień cyklicznych)
  recurrenceRule?: string; // RRULE format
  // Przypisania wielo-osobowe
  assignments?: {
    id: string;
    memberId: string;
    member: {
      id: string;
      user: {
        id: string;
        firstName: string | null;
        lastName: string | null;
        email: string;
      };
    };
  }[];
  assignedTo?: {
    id: string;
    user: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
  } | null;
  assignmentType?: import('@prisma/client').TaskAssignmentType;
  metadata?: Record<string, unknown>;
}

export interface CalendarFilters {
  memberId?: string;
  taskStatus?: string[];
  taskCategory?: string[];
  showCompleted: boolean;
  startDate: Date;
  endDate: Date;
}

export interface CreateEventData {
  title: string;
  description?: string;
  startDate: Date;
  endDate?: Date;
  allDay: boolean;
  assignedMemberIds?: string[];
  color?: string;
  recurrenceRule?: string;
}

export interface UpdateEventData extends Partial<CreateEventData> {
  id?: string;
}

export interface CalendarSettings {
  id: string;
  familyId: string;
  defaultView: CalendarView;
  startWeekOn: WeekDay;
  timeFormat: TimeFormat;
  showWeekends: boolean;
  showCompleted: boolean;
  colorScheme: string;
  googleCalendarEnabled: boolean;
  appleCalendarEnabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ConflictDetection {
  hasConflicts: boolean;
  conflicts: {
    id: string;
    title: string;
    start: Date;
    end: Date;
    type: 'task'; // Tylko zadania
  }[];
}

export interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
  score?: number; // For ranking suggestions
}

export interface SchedulingConstraints {
  preferredTimeSlots?: TimeSlot[];
  minDuration: number; // in minutes
  maxDuration: number; // in minutes
  allowWeekends: boolean;
  startTime: string; // "HH:MM"
  endTime: string; // "HH:MM"
}

export interface SchedulingSuggestion {
  taskId: string;
  suggestedTime: TimeSlot;
  confidence: number; // 0-1
  reason: string;
}

export type CreateCalendarEventSchema = z.infer<typeof createCalendarEventSchema>;
export type UpdateCalendarEventSchema = z.infer<typeof updateCalendarEventSchema>;
export type CalendarSettingsSchema = z.infer<typeof calendarSettingsSchema>;
export type CalendarFiltersSchema = z.infer<typeof calendarFiltersSchema>;
