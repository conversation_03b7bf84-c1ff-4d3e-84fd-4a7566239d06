import { z } from 'zod';
import { FamilyRole } from '@prisma/client';

export const createFamilySchema = z.object({
  name: z
    .string()
    .min(1, 'Nazwa rodziny jest wymagana')
    .max(100, 'Nazwa rodziny nie może być dłuższa niż 100 znaków')
    .trim(),
  description: z.string().max(500, 'Opis nie może być dłuższy niż 500 znaków').trim().optional(),
});

export const updateFamilySchema = z.object({
  name: z
    .string()
    .min(1, 'Nazwa rodziny jest wymagana')
    .max(100, 'Nazwa rodziny nie może być dłuższa niż 100 znaków')
    .trim()
    .optional(),
  description: z.string().max(500, 'Opis nie może być dłuższy niż 500 znaków').trim().optional(),
});

export const inviteMemberSchema = z.object({
  email: z.string().email('Podaj poprawny adres email').toLowerCase().trim(),
  role: z.nativeEnum(FamilyRole, {
    errorMap: () => ({ message: 'Nieprawidłowa rola' }),
  }),
  isAdult: z.boolean().optional().default(false),
});

export const changeMemberRoleSchema = z.object({
  role: z.nativeEnum(FamilyRole, {
    errorMap: () => ({ message: 'Nieprawidłowa rola' }),
  }),
  isAdult: z.boolean().optional(),
});

export const familyIdSchema = z.object({
  familyId: z.string().cuid('Nieprawidłowy identyfikator rodziny'),
});

export const memberIdSchema = z.object({
  memberId: z.string().cuid('Nieprawidłowy identyfikator członka'),
});

export const invitationTokenSchema = z.object({
  token: z.string().min(1, 'Token jest wymagany'),
});

export const cancelInvitationSchema = z.object({
  invitationId: z.string().cuid('Nieprawidłowy identyfikator zaproszenia'),
});

// Types
export type CreateFamilyInput = z.infer<typeof createFamilySchema>;
export type UpdateFamilyInput = z.infer<typeof updateFamilySchema>;
export type InviteMemberInput = z.infer<typeof inviteMemberSchema>;
export type ChangeMemberRoleInput = z.infer<typeof changeMemberRoleSchema>;
export type FamilyIdInput = z.infer<typeof familyIdSchema>;
export type MemberIdInput = z.infer<typeof memberIdSchema>;
export type InvitationTokenInput = z.infer<typeof invitationTokenSchema>;
export type CancelInvitationInput = z.infer<typeof cancelInvitationSchema>;

// Validation helpers
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validateFamilyName(name: string): boolean {
  return name.trim().length >= 1 && name.trim().length <= 100;
}

export function isValidRole(role: string): role is FamilyRole {
  return Object.values(FamilyRole).includes(role as FamilyRole);
}

// Role validation
export const ROLE_HIERARCHY = {
  [FamilyRole.OWNER]: 4,
  [FamilyRole.PARENT]: 3,
  [FamilyRole.GUARDIAN]: 2,
  [FamilyRole.CHILD]: 1,
} as const;

export function isRoleHigherOrEqual(role1: FamilyRole, role2: FamilyRole): boolean {
  return ROLE_HIERARCHY[role1] >= ROLE_HIERARCHY[role2];
}

export function canAssignRole(assignerRole: FamilyRole, targetRole: FamilyRole): boolean {
  // Owner can assign any role
  if (assignerRole === FamilyRole.OWNER) return true;

  // Parent can assign guardian and child roles
  if (assignerRole === FamilyRole.PARENT) {
    return targetRole === FamilyRole.GUARDIAN || targetRole === FamilyRole.CHILD;
  }

  // Guardian can only assign child role
  if (assignerRole === FamilyRole.GUARDIAN) {
    return targetRole === FamilyRole.CHILD;
  }

  return false;
}
