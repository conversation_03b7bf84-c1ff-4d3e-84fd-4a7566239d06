export interface WebVitalMetric {
  name: 'CLS' | 'FCP' | 'FID' | 'LCP' | 'TTFB' | 'INP';
  value: number;
  id: string;
  delta: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

export function trackWebVitals(metric: WebVitalMetric) {
  const { name, value, id, rating } = metric;

  console.log(`[Performance] ${name}: ${value}ms (${rating})`);

  // Send to analytics service (Google Analytics example)
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', name, {
      value: Math.round(name === 'CLS' ? value * 1000 : value),
      event_label: id,
      non_interaction: true,
      custom_map: {
        metric_id: 'performance_metric',
        metric_value: value,
        metric_rating: rating,
      },
    });
  }

  // Send to custom analytics endpoint
  if (typeof window !== 'undefined') {
    fetch('/api/analytics/performance', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        metric: name,
        value,
        id,
        rating,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
      }),
    }).catch(error => {
      console.warn('Failed to send performance metric:', error);
    });
  }

  // Log performance issues
  if (name === 'LCP' && value > 2500) {
    console.warn('Poor LCP detected:', value, 'ms');
    logPerformanceIssue('LCP', value, 'Page load is slower than recommended');
  }

  if (name === 'FID' && value > 100) {
    console.warn('Poor FID detected:', value, 'ms');
    logPerformanceIssue('FID', value, 'First input delay is higher than recommended');
  }

  if (name === 'CLS' && value > 0.1) {
    console.warn('Poor CLS detected:', value);
    logPerformanceIssue('CLS', value, 'Cumulative layout shift is higher than recommended');
  }

  if (name === 'FCP' && value > 1800) {
    console.warn('Poor FCP detected:', value, 'ms');
    logPerformanceIssue('FCP', value, 'First contentful paint is slower than recommended');
  }

  if (name === 'TTFB' && value > 800) {
    console.warn('Poor TTFB detected:', value, 'ms');
    logPerformanceIssue('TTFB', value, 'Time to first byte is slower than recommended');
  }
}

function logPerformanceIssue(metric: string, value: number, message: string) {
  // Log to error tracking service (Sentry, etc.)
  if (typeof window !== 'undefined' && (window as any).Sentry) {
    (window as any).Sentry.addBreadcrumb({
      category: 'performance',
      message: `${metric}: ${message}`,
      level: 'warning',
      data: {
        metric,
        value,
      },
    });
  }

  // Log to custom monitoring endpoint
  fetch('/api/monitoring/performance-issues', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      metric,
      value,
      message,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    }),
  }).catch(() => {
    // Silently fail - don't let monitoring break the app
  });
}

// Resource loading performance tracking
export function trackResourceTiming() {
  if (typeof window === 'undefined' || !window.performance) {
    return;
  }

  // Wait for page load to complete
  window.addEventListener('load', () => {
    setTimeout(() => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];

      const slowResources = resources.filter(resource => resource.duration > 1000);

      if (slowResources.length > 0) {
        console.warn('Slow resources detected:', slowResources);

        slowResources.forEach(resource => {
          fetch('/api/monitoring/slow-resources', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: resource.name,
              duration: resource.duration,
              transferSize: resource.transferSize,
              timestamp: Date.now(),
              url: window.location.href,
            }),
          }).catch(() => {
            // Silently fail
          });
        });
      }
    }, 1000);
  });
}

// Memory usage tracking
export function trackMemoryUsage() {
  if (typeof window === 'undefined' || !(performance as any).memory) {
    return;
  }

  const memory = (performance as any).memory;

  const memoryInfo = {
    usedJSHeapSize: memory.usedJSHeapSize,
    totalJSHeapSize: memory.totalJSHeapSize,
    jsHeapSizeLimit: memory.jsHeapSizeLimit,
    timestamp: Date.now(),
  };

  // Log if memory usage is high
  const memoryUsagePercent = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;

  if (memoryUsagePercent > 80) {
    console.warn('High memory usage detected:', memoryUsagePercent, '%');

    fetch('/api/monitoring/memory-usage', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...memoryInfo,
        usagePercent: memoryUsagePercent,
        url: window.location.href,
      }),
    }).catch(() => {
      // Silently fail
    });
  }
}

// User interaction performance tracking
export function trackUserInteractionPerformance() {
  if (typeof window === 'undefined') {
    return;
  }

  let interactionStart = 0;

  // Track click interactions
  document.addEventListener('click', event => {
    interactionStart = performance.now();

    // Track time to next paint after interaction
    requestAnimationFrame(() => {
      const interactionTime = performance.now() - interactionStart;

      if (interactionTime > 100) {
        console.warn('Slow interaction detected:', interactionTime, 'ms');

        fetch('/api/monitoring/slow-interactions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            duration: interactionTime,
            target: (event.target as Element)?.tagName || 'unknown',
            timestamp: Date.now(),
            url: window.location.href,
          }),
        }).catch(() => {
          // Silently fail
        });
      }
    });
  });
}

// Initialize performance monitoring
export function initPerformanceMonitoring() {
  if (typeof window === 'undefined') {
    return;
  }

  // Set up periodic memory monitoring
  setInterval(trackMemoryUsage, 30000); // Every 30 seconds

  // Set up resource timing monitoring
  trackResourceTiming();

  // Set up interaction performance monitoring
  trackUserInteractionPerformance();

  console.log('[Performance] Monitoring initialized');
}

// Performance budget checker
export function checkPerformanceBudget() {
  if (typeof window === 'undefined' || !window.performance) {
    return;
  }

  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType(
        'navigation'
      )[0] as PerformanceNavigationTiming;

      const budgets = {
        firstContentfulPaint: 1500,
        largestContentfulPaint: 2500,
        totalBlockingTime: 300,
        cumulativeLayoutShift: 0.1,
        speedIndex: 3000,
      };

      const results = {
        fcp: navigation.responseEnd - navigation.fetchStart,
        // Note: Real LCP, TBT, CLS, SI would need to be measured with Web Vitals library
      };

      const violations = [];

      if (results.fcp > budgets.firstContentfulPaint) {
        violations.push({
          metric: 'FCP',
          actual: results.fcp,
          budget: budgets.firstContentfulPaint,
        });
      }

      if (violations.length > 0) {
        console.warn('Performance budget violations:', violations);

        fetch('/api/monitoring/budget-violations', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            violations,
            timestamp: Date.now(),
            url: window.location.href,
          }),
        }).catch(() => {
          // Silently fail
        });
      }
    }, 2000);
  });
}
