import { Locale } from '@/lib/i18n/config';

export type NotificationType =
  | 'taskCompleted'
  | 'taskOverdue'
  | 'taskAssigned'
  | 'taskVerified'
  | 'pointsAwarded'
  | 'familyInvitation'
  | 'memberJoined';

export interface NotificationData {
  userName?: string;
  taskName?: string;
  familyName?: string;
  memberName?: string;
  points?: number;
  dueDate?: string;
  [key: string]: any;
}

export interface LocalizedNotification {
  title: string;
  body: string;
  action?: string;
}

const notificationTemplates = {
  taskCompleted: {
    pl: {
      title: 'Zadanie wykonane! 🎉',
      body: '{userName} wykonał zadanie "{taskName}"',
      action: 'Zobacz szczegóły',
    },
    en: {
      title: 'Task completed! 🎉',
      body: '{userName} completed task "{taskName}"',
      action: 'View details',
    },
    de: {
      title: 'Aufgabe erledigt! 🎉',
      body: '{userName} hat die Aufgabe "{taskName}" erledigt',
      action: 'Details ansehen',
    },
    es: {
      title: '¡Tarea completada! 🎉',
      body: '{userName} completó la tarea "{taskName}"',
      action: 'Ver detalles',
    },
  },
  taskOverdue: {
    pl: {
      title: 'Zadanie opóźnione ⏰',
      body: 'Zadanie "{taskName}" jest opóźnione',
      action: 'Wykonaj teraz',
    },
    en: {
      title: 'Task overdue ⏰',
      body: 'Task "{taskName}" is overdue',
      action: 'Complete now',
    },
    de: {
      title: 'Aufgabe überfällig ⏰',
      body: 'Aufgabe "{taskName}" ist überfällig',
      action: 'Jetzt erledigen',
    },
    es: {
      title: 'Tarea atrasada ⏰',
      body: 'La tarea "{taskName}" está atrasada',
      action: 'Completar ahora',
    },
  },
  taskAssigned: {
    pl: {
      title: 'Nowe zadanie! 📋',
      body: 'Otrzymałeś nowe zadanie: "{taskName}"',
      action: 'Zobacz zadanie',
    },
    en: {
      title: 'New task! 📋',
      body: 'You have been assigned a new task: "{taskName}"',
      action: 'View task',
    },
    de: {
      title: 'Neue Aufgabe! 📋',
      body: 'Dir wurde eine neue Aufgabe zugewiesen: "{taskName}"',
      action: 'Aufgabe ansehen',
    },
    es: {
      title: '¡Nueva tarea! 📋',
      body: 'Se te ha asignado una nueva tarea: "{taskName}"',
      action: 'Ver tarea',
    },
  },
  taskVerified: {
    pl: {
      title: 'Zadanie zweryfikowane ✅',
      body: 'Twoje zadanie "{taskName}" zostało zweryfikowane. Otrzymujesz {points} punktów!',
      action: 'Zobacz punkty',
    },
    en: {
      title: 'Task verified ✅',
      body: 'Your task "{taskName}" has been verified. You earned {points} points!',
      action: 'View points',
    },
    de: {
      title: 'Aufgabe verifiziert ✅',
      body: 'Deine Aufgabe "{taskName}" wurde verifiziert. Du hast {points} Punkte erhalten!',
      action: 'Punkte ansehen',
    },
    es: {
      title: 'Tarea verificada ✅',
      body: 'Tu tarea "{taskName}" ha sido verificada. ¡Ganaste {points} puntos!',
      action: 'Ver puntos',
    },
  },
  pointsAwarded: {
    pl: {
      title: 'Punkty przyznane! 🌟',
      body: 'Otrzymałeś {points} punktów za wykonanie zadań',
      action: 'Zobacz ranking',
    },
    en: {
      title: 'Points awarded! 🌟',
      body: 'You earned {points} points for completing tasks',
      action: 'View leaderboard',
    },
  },
  familyInvitation: {
    pl: {
      title: 'Zaproszenie do rodziny 👨‍👩‍👧‍👦',
      body: 'Zostałeś zaproszony do rodziny "{familyName}"',
      action: 'Przyjmij zaproszenie',
    },
    en: {
      title: 'Family invitation 👨‍👩‍👧‍👦',
      body: 'You have been invited to join the "{familyName}" family',
      action: 'Accept invitation',
    },
  },
  memberJoined: {
    pl: {
      title: 'Nowy członek rodziny! 🎊',
      body: '{memberName} dołączył do rodziny',
      action: 'Powitaj',
    },
    en: {
      title: 'New family member! 🎊',
      body: '{memberName} joined the family',
      action: 'Say hello',
    },
  },
};

export function getLocalizedNotification(
  type: NotificationType,
  locale: Locale,
  data: NotificationData
): LocalizedNotification {
  const templateGroup = notificationTemplates[type];
  const template = (templateGroup as any)[locale] || (templateGroup as any)['pl'];

  if (!template) {
    console.warn(`Notification template '${type}' not found for locale '${locale}'`);
    return {
      title: 'Notification',
      body: `Notification: ${type}`,
      action: 'View',
    };
  }

  return {
    title: interpolateNotification(template.title, data),
    body: interpolateNotification(template.body, data),
    action: template.action ? interpolateNotification(template.action, data) : undefined,
  };
}

function interpolateNotification(template: string, data: NotificationData): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return data[key]?.toString() || match;
  });
}

export function getAvailableNotificationTypes(): NotificationType[] {
  return Object.keys(notificationTemplates) as NotificationType[];
}
