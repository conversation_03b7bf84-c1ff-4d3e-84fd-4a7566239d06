import { <PERSON><PERSON><PERSON>, FamilyMember } from '@prisma/client';
import { prisma } from './prisma';

/**
 * Konwertuje Clerk User ID na lokalny User ID, tworząc użytkownika jeśli nie istnieje
 * UWAGA: Ta funkcja może być używana tylko po stronie serwera
 */
export async function getOrCreateLocalUser(
  clerkUserId: string,
  clerkUserData?: {
    email: string;
    firstName?: string | null;
    lastName?: string | null;
  }
) {
  // Sprawdź czy użytkownik już istnieje lokalnie
  let localUser = await prisma.user.findUnique({
    where: { clerkId: clerkUserId },
  });

  // Jeśli nie istnieje, stwórz go
  if (!localUser) {
    if (!clerkUserData) {
      throw new Error('Clerk user data is required to create local user');
    }

    localUser = await prisma.user.create({
      data: {
        clerkId: clerkUserId,
        email: clerkUserData.email,
        firstName: clerkUserData.firstName,
        lastName: clerkUserData.lastName,
        preferredLanguage: 'pl',
      },
    });

    console.log(`Created local user for Clerk ID: ${clerkUserId}`);
  }

  return localUser;
}

export const ROLE_HIERARCHY = {
  ['OWNER']: 4,
  ['PARENT']: 3,
  ['GUARDIAN']: 2,
  ['CHILD']: 1,
} as const;

export const PERMISSIONS = {
  MANAGE_FAMILY: ['OWNER', 'PARENT'] as const,
  MANAGE_TASKS: ['OWNER', 'PARENT', 'GUARDIAN'] as const,
  VIEW_TASKS: ['OWNER', 'PARENT', 'GUARDIAN', 'CHILD'] as const,
  MANAGE_MEMBERS: ['OWNER', 'PARENT'] as const,
  ASSIGN_TASKS: ['OWNER', 'PARENT', 'GUARDIAN'] as const,
  COMPLETE_TASKS: ['OWNER', 'PARENT', 'GUARDIAN', 'CHILD'] as const,
  VIEW_STATISTICS: ['OWNER', 'PARENT', 'GUARDIAN'] as const,
};

export type Permission = keyof typeof PERMISSIONS;

export function hasRole(userRole: FamilyRole, requiredRoles: FamilyRole[]): boolean {
  return requiredRoles.includes(userRole);
}

export function hasPermission(userRole: FamilyRole, permission: Permission): boolean {
  return (PERMISSIONS[permission] as readonly FamilyRole[]).includes(userRole);
}

export function isRoleHigherOrEqual(userRole: FamilyRole, compareRole: FamilyRole): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[compareRole];
}

export async function checkFamilyPermission(
  userId: string,
  familyId: string,
  requiredRoles: FamilyRole[]
): Promise<boolean> {
  try {
    const familyMember = await getFamilyMemberWithRole(userId, familyId);
    if (!familyMember) return false;

    return hasRole(familyMember.role, requiredRoles);
  } catch (error) {
    console.error('Error checking family permission:', error);
    return false;
  }
}

export async function getFamilyMemberWithRole(
  userId: string,
  familyId: string
): Promise<FamilyMember | null> {
  try {
    return await prisma.familyMember.findUnique({
      where: {
        userId_familyId: {
          userId,
          familyId,
        },
      },
      include: {
        user: true,
        family: true,
      },
    });
  } catch (error) {
    console.error('Error getting family member with role:', error);
    return null;
  }
}

export async function getUserFamilies(userId: string) {
  try {
    return await prisma.familyMember.findMany({
      where: { userId },
      include: {
        family: true,
      },
      orderBy: {
        joinedAt: 'asc',
      },
    });
  } catch (error) {
    console.error('Error getting user families:', error);
    return [];
  }
}

export function getRoleDisplayName(role: FamilyRole): string {
  const roleNames = {
    ['OWNER']: 'Właściciel',
    ['PARENT']: 'Rodzic',
    ['GUARDIAN']: 'Opiekun',
    ['CHILD']: 'Dziecko',
  };

  return roleNames[role];
}

export function canManageRole(userRole: FamilyRole, targetRole: FamilyRole): boolean {
  // Właściciel może zarządzać wszystkimi rolami
  if (userRole === 'OWNER') return true;

  // Rodzice mogą zarządzać opiekunami i dziećmi
  if (userRole === 'PARENT') {
    return ['GUARDIAN', 'CHILD'].includes(targetRole);
  }

  // Opiekunowie mogą zarządzać tylko dziećmi
  if (userRole === 'GUARDIAN') {
    return targetRole === 'CHILD';
  }

  return false;
}

/**
 * Sprawdza czy użytkownik może modyfikować ukończone zadanie
 * @param userRole - rola użytkownika próbującego modyfikować zadanie
 * @param taskCreatorRole - rola twórcy zadania
 * @param taskCompletedByRole - rola osoby która ukończyła zadanie
 * @param completionDate - data ukończenia zadania
 * @param isTaskCreator - czy użytkownik jest twórcą zadania
 * @param isTaskCompleter - czy użytkownik ukończył zadanie
 */
export function canModifyCompletedTask(
  userRole: FamilyRole,
  taskCreatorRole: FamilyRole,
  taskCompletedByRole: FamilyRole,
  completionDate: Date,
  isTaskCreator: boolean = false,
  isTaskCompleter: boolean = false
): boolean {
  // Właściciel może modyfikować wszystkie ukończone zadania
  if (userRole === 'OWNER') return true;

  // Sprawdź czy zadanie jest "stare" (ukończone ponad 30 dni temu)
  const daysSinceCompletion = Math.floor(
    (Date.now() - completionDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  const isOldTask = daysSinceCompletion > 30;

  // Stare zadania mogą być modyfikowane tylko przez właściciela
  if (isOldTask && userRole !== 'OWNER') {
    return false;
  }

  // Rodzice mogą modyfikować ukończone zadania utworzone przez siebie lub ukończone przez opiekunów/dzieci
  if (userRole === 'PARENT') {
    // Może modyfikować własne utworzone zadania
    if (isTaskCreator) return true;
    // Może modyfikować zadania ukończone przez opiekunów lub dzieci
    if (['GUARDIAN', 'CHILD'].includes(taskCompletedByRole)) return true;
    // Może modyfikować zadania utworzone przez niższych w hierarchii
    if (canManageRole(userRole, taskCreatorRole)) return true;
  }

  // Opiekunowie mogą modyfikować tylko zadania ukończone przez dzieci lub własne
  if (userRole === 'GUARDIAN') {
    // Może modyfikować własne ukończone zadania (w ciągu 7 dni)
    if (isTaskCompleter && daysSinceCompletion <= 7) return true;
    // Może modyfikować zadania ukończone przez dzieci
    if (taskCompletedByRole === 'CHILD') return true;
    // Może modyfikować własne utworzone zadania
    if (isTaskCreator) return true;
  }

  // Dzieci mogą modyfikować tylko własne ukończone zadania (w ciągu 24 godzin)
  if (userRole === 'CHILD') {
    if (isTaskCompleter && daysSinceCompletion <= 1) return true;
  }

  return false;
}

/**
 * Sprawdza czy użytkownik może usunąć ukończone zadanie
 * Usuwanie ukończonych zadań wymaga wyższych uprawnień niż edycja
 */
export function canDeleteCompletedTask(
  userRole: FamilyRole,
  taskCreatorRole: FamilyRole,
  taskCompletedByRole: FamilyRole,
  completionDate: Date,
  isTaskCreator: boolean = false
): boolean {
  // Tylko właściciel może usuwać ukończone zadania
  if (userRole === 'OWNER') return true;

  // Sprawdź czy zadanie jest bardzo nowe (ukończone mniej niż 1 godzinę temu)
  const hoursSinceCompletion = (Date.now() - completionDate.getTime()) / (1000 * 60 * 60);
  const isVeryNewTask = hoursSinceCompletion < 1;

  // Rodzice mogą usuwać tylko bardzo nowe zadania które sami utworzyli
  if (userRole === 'PARENT' && isTaskCreator && isVeryNewTask) {
    return true;
  }

  // Pozostałe role nie mogą usuwać ukończonych zadań
  return false;
}
