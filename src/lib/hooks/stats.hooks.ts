import { useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { ActivityLogService } from '@/lib/services/activity.service';
import { StatsService } from '@/lib/services/stats.service';
import { ActivityAction } from '@prisma/client';

interface StatsUpdaterOptions {
  familyId: string;
  enableRealtime?: boolean;
  updateInterval?: number; // in milliseconds
  autoInvalidate?: boolean;
}

interface TaskCompletionEvent {
  taskId: string;
  memberId: string;
  familyId: string;
  pointsAwarded: number;
  completedAt: Date;
}

interface StatsUpdateEvent {
  type: 'task_completed' | 'task_verified' | 'member_joined' | 'points_awarded';
  familyId: string;
  data: any;
}

export function useStatsUpdater(options: StatsUpdaterOptions) {
  const {
    familyId,
    enableRealtime = true,
    updateInterval = 30000, // 30 seconds
    autoInvalidate = true,
  } = options;

  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<Date>(new Date());

  // Function to invalidate related queries
  const invalidateStatsQueries = useCallback(() => {
    if (!autoInvalidate) return;

    // Invalidate family stats queries
    queryClient.invalidateQueries({
      queryKey: ['familyStats', familyId],
    });

    // Invalidate member stats queries
    queryClient.invalidateQueries({
      queryKey: ['memberStats', familyId],
    });

    // Invalidate leaderboard queries
    queryClient.invalidateQueries({
      queryKey: ['leaderboard', familyId],
    });

    // Invalidate activity logs
    queryClient.invalidateQueries({
      queryKey: ['activityLog', familyId],
    });

    // Invalidate overview data
    queryClient.invalidateQueries({
      queryKey: ['statsOverview', familyId],
    });

    console.log('Stats queries invalidated for family:', familyId);
  }, [queryClient, familyId, autoInvalidate]);

  // Function to log activity and update stats
  const logActivity = useCallback(
    async (
      action: ActivityAction,
      entityType: string,
      entityId?: string,
      metadata?: Record<string, any>
    ) => {
      try {
        await ActivityLogService.logActivity({
          familyId,
          action,
          entityType,
          entityId,
          metadata,
        });

        // Trigger optimistic updates
        invalidateStatsQueries();
      } catch (error) {
        console.error('Error logging activity:', error);
      }
    },
    [familyId, invalidateStatsQueries]
  );

  // Function to handle task completion
  const handleTaskCompletion = useCallback(
    async (event: TaskCompletionEvent) => {
      try {
        // Log the activity
        await logActivity('TASK_COMPLETED', 'task', event.taskId, {
          memberId: event.memberId,
          pointsAwarded: event.pointsAwarded,
          completedAt: event.completedAt,
        });

        // Optimistically update cached data
        if (autoInvalidate) {
          // Update family stats cache
          queryClient.setQueryData(['familyStats', familyId], (oldData: any) => {
            if (!oldData) return oldData;
            return {
              ...oldData,
              summary: {
                ...oldData.summary,
                tasksCompleted: (oldData.summary?.tasksCompleted || 0) + 1,
                totalPoints: (oldData.summary?.totalPoints || 0) + event.pointsAwarded,
              },
            };
          });

          // Update member stats cache
          queryClient.setQueryData(['memberStats', event.memberId], (oldData: any) => {
            if (!oldData) return oldData;
            return {
              ...oldData,
              summary: {
                ...oldData.summary,
                totalTasksCompleted: (oldData.summary?.totalTasksCompleted || 0) + 1,
                totalPointsEarned: (oldData.summary?.totalPointsEarned || 0) + event.pointsAwarded,
              },
            };
          });
        }

        // Invalidate queries to fetch fresh data
        setTimeout(invalidateStatsQueries, 1000);
      } catch (error) {
        console.error('Error handling task completion:', error);
      }
    },
    [logActivity, queryClient, familyId, autoInvalidate, invalidateStatsQueries]
  );

  // Function to handle points awarded
  const handlePointsAwarded = useCallback(
    async (memberId: string, points: number, reason: string) => {
      try {
        await logActivity('POINTS_AWARDED', 'member', memberId, {
          points,
          reason,
          awardedAt: new Date(),
        });

        // Optimistically update member points
        if (autoInvalidate) {
          queryClient.setQueryData(['memberStats', memberId], (oldData: any) => {
            if (!oldData) return oldData;
            return {
              ...oldData,
              member: {
                ...oldData.member,
                totalPoints: (oldData.member?.totalPoints || 0) + points,
              },
            };
          });
        }

        setTimeout(invalidateStatsQueries, 1000);
      } catch (error) {
        console.error('Error handling points awarded:', error);
      }
    },
    [logActivity, queryClient, autoInvalidate, invalidateStatsQueries]
  );

  // Function to handle member activity
  const handleMemberActivity = useCallback(
    async (memberId: string, action: ActivityAction, metadata?: any) => {
      try {
        await logActivity(action, 'member', memberId, metadata);
        setTimeout(invalidateStatsQueries, 1000);
      } catch (error) {
        console.error('Error handling member activity:', error);
      }
    },
    [logActivity, invalidateStatsQueries]
  );

  // Periodic stats refresh
  useEffect(() => {
    if (!enableRealtime || updateInterval <= 0) return;

    intervalRef.current = setInterval(() => {
      const now = new Date();
      const timeSinceLastUpdate = now.getTime() - lastUpdateRef.current.getTime();

      // Only invalidate if it's been a while since the last update
      if (timeSinceLastUpdate > updateInterval) {
        invalidateStatsQueries();
        lastUpdateRef.current = now;
      }
    }, updateInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [enableRealtime, updateInterval, invalidateStatsQueries]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    logActivity,
    handleTaskCompletion,
    handlePointsAwarded,
    handleMemberActivity,
    invalidateStatsQueries,
    forceRefresh: invalidateStatsQueries,
  };
}

// Hook for listening to stats changes
export function useRealtimeStats(familyId: string) {
  const queryClient = useQueryClient();

  const refreshStats = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: ['familyStats', familyId],
    });
    queryClient.invalidateQueries({
      queryKey: ['memberStats', familyId],
    });
    queryClient.invalidateQueries({
      queryKey: ['activityLog', familyId],
    });
  }, [queryClient, familyId]);

  // In a real implementation, this could listen to WebSocket events
  // For now, we'll just provide the refresh function
  useEffect(() => {
    // Listen for custom events that might be dispatched by other parts of the app
    const handleStatsUpdate = (event: CustomEvent<StatsUpdateEvent>) => {
      if (event.detail.familyId === familyId) {
        refreshStats();
      }
    };

    window.addEventListener('statsUpdate', handleStatsUpdate as EventListener);

    return () => {
      window.removeEventListener('statsUpdate', handleStatsUpdate as EventListener);
    };
  }, [familyId, refreshStats]);

  return {
    refreshStats,
  };
}

// Utility function to dispatch stats update events
export function dispatchStatsUpdate(familyId: string, type: string, data: any) {
  const event = new CustomEvent('statsUpdate', {
    detail: {
      type,
      familyId,
      data,
    },
  });
  window.dispatchEvent(event);
}

// Hook for automatic stats generation triggers
export function useStatsGeneration(familyId: string) {
  const generateStats = useCallback(
    async (date?: Date, period?: 'DAILY' | 'WEEKLY' | 'MONTHLY') => {
      try {
        const response = await fetch('/api/cron/stats', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            familyId,
            date: date || new Date(),
            period: period || 'DAILY',
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to generate stats');
        }

        const result = await response.json();

        // Dispatch update event
        dispatchStatsUpdate(familyId, 'stats_generated', result.data);

        return result;
      } catch (error) {
        console.error('Error generating stats:', error);
        throw error;
      }
    },
    [familyId]
  );

  return {
    generateStats,
  };
}
