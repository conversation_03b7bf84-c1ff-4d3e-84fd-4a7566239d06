'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { StatsPeriod } from '@prisma/client';

export interface DateRange {
  start: Date;
  end: Date;
}

// Hook for family stats
export function useFamilyStats(
  familyId: string,
  period: StatsPeriod = 'DAILY',
  dateRange?: DateRange
) {
  return useQuery({
    queryKey: ['familyStats', familyId, period, dateRange],
    queryFn: async () => {
      const params = new URLSearchParams({
        period,
      });

      if (dateRange) {
        params.append('startDate', dateRange.start.toISOString());
        params.append('endDate', dateRange.end.toISOString());
      }

      const response = await fetch(`/api/families/${familyId}/stats?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch family stats');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for family stats overview
export function useFamilyStatsOverview(familyId: string) {
  return useQuery({
    queryKey: ['familyStatsOverview', familyId],
    queryFn: async () => {
      const response = await fetch(`/api/families/${familyId}/stats/overview`);
      if (!response.ok) {
        throw new Error('Failed to fetch family stats overview');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
  });
}

// Hook for member stats
export function useMemberStats(memberId: string, period: StatsPeriod = 'DAILY') {
  return useQuery({
    queryKey: ['memberStats', memberId, period],
    queryFn: async () => {
      const response = await fetch(
        `/api/families/[familyId]/members/${memberId}/stats?period=${period}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch member stats');
      }
      return response.json();
    },
    enabled: !!memberId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for activity log
export function useActivityLog(
  familyId: string,
  filters?: {
    action?: string;
    entityType?: string;
    memberId?: string;
    limit?: number;
  }
) {
  return useQuery({
    queryKey: ['activityLog', familyId, filters],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (filters?.action) params.append('action', filters.action);
      if (filters?.entityType) params.append('entityType', filters.entityType);
      if (filters?.memberId) params.append('memberId', filters.memberId);
      if (filters?.limit) params.append('limit', filters.limit.toString());

      const response = await fetch(`/api/families/${familyId}/activity?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch activity log');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: true,
  });
}

// Hook for leaderboard
export function useLeaderboard(
  familyId: string,
  metric: 'points' | 'completion_rate' = 'points',
  period: StatsPeriod = 'MONTHLY'
) {
  return useQuery({
    queryKey: ['leaderboard', familyId, metric, period],
    queryFn: async () => {
      const response = await fetch(
        `/api/families/${familyId}/stats/leaderboard?metric=${metric}&period=${period}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch leaderboard');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 3 * 60 * 1000, // 3 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for trends data
export function useTrendsData(
  familyId: string,
  metric:
    | 'completion_rate'
    | 'tasks_completed'
    | 'points_awarded'
    | 'active_members' = 'completion_rate',
  period: StatsPeriod = 'DAILY',
  days: number = 30
) {
  return useQuery({
    queryKey: ['trendsData', familyId, metric, period, days],
    queryFn: async () => {
      const response = await fetch(
        `/api/families/${familyId}/stats/trends?metric=${metric}&period=${period}&days=${days}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch trends data');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for weekly report
export function useWeeklyReport(familyId: string, weekOffset: number = 0) {
  return useQuery({
    queryKey: ['weeklyReport', familyId, weekOffset],
    queryFn: async () => {
      const response = await fetch(
        `/api/families/${familyId}/reports/weekly?weekOffset=${weekOffset}`
      );
      if (!response.ok) {
        throw new Error('Failed to fetch weekly report');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for monthly report
export function useMonthlyReport(
  familyId: string,
  monthOffset: number = 0,
  year?: number,
  month?: number
) {
  return useQuery({
    queryKey: ['monthlyReport', familyId, monthOffset, year, month],
    queryFn: async () => {
      const params = new URLSearchParams();

      if (year && month) {
        params.append('year', year.toString());
        params.append('month', month.toString());
      } else {
        params.append('monthOffset', monthOffset.toString());
      }

      const response = await fetch(`/api/families/${familyId}/reports/monthly?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch monthly report');
      }
      return response.json();
    },
    enabled: !!familyId,
    staleTime: 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: false,
  });
}

// Hook for invalidating stats queries
export function useStatsInvalidation() {
  const queryClient = useQueryClient();

  const invalidateAllStats = (familyId: string) => {
    queryClient.invalidateQueries({ queryKey: ['familyStats', familyId] });
    queryClient.invalidateQueries({ queryKey: ['familyStatsOverview', familyId] });
    queryClient.invalidateQueries({ queryKey: ['activityLog', familyId] });
    queryClient.invalidateQueries({ queryKey: ['leaderboard', familyId] });
    queryClient.invalidateQueries({ queryKey: ['trendsData', familyId] });
  };

  const invalidateMemberStats = (memberId: string) => {
    queryClient.invalidateQueries({ queryKey: ['memberStats', memberId] });
  };

  const invalidateReports = (familyId: string) => {
    queryClient.invalidateQueries({ queryKey: ['weeklyReport', familyId] });
    queryClient.invalidateQueries({ queryKey: ['monthlyReport', familyId] });
  };

  return {
    invalidateAllStats,
    invalidateMemberStats,
    invalidateReports,
  };
}

// Hook for stats mutations
export function useStatsActions(familyId: string) {
  const { invalidateAllStats } = useStatsInvalidation();

  const generateStats = async (date?: Date, period?: StatsPeriod) => {
    const response = await fetch('/api/cron/stats', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        familyId,
        date: date || new Date(),
        period: period || 'DAILY',
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate stats');
    }

    const result = await response.json();

    // Invalidate related queries
    invalidateAllStats(familyId);

    return result;
  };

  const exportStats = async (
    exportType: 'stats' | 'activity' | 'reports',
    dateRange: DateRange,
    format: 'csv' = 'csv'
  ) => {
    const response = await fetch(`/api/families/${familyId}/export/stats`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        exportType,
        dateRange,
        format,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to export data');
    }

    return response.json();
  };

  return {
    generateStats,
    exportStats,
  };
}
