import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';
import { FamilyRole } from '@prisma/client';
import { Permission, hasPermission, getFamilyMemberWithRole } from './auth';

export interface AuthContext {
  userId: string;
  familyId?: string;
  role?: FamilyRole;
}

export interface AuthMiddlewareOptions {
  requiredRoles?: FamilyRole[];
  requiredPermission?: Permission;
  requireFamilyMember?: boolean;
}

export async function withAuth(
  request: NextRequest,
  options: AuthMiddlewareOptions = {}
): Promise<{ success: boolean; context?: AuthContext; error?: string }> {
  try {
    // Sprawdź uwierzytelnienie w Clerk
    const { userId } = await auth();

    if (!userId) {
      return { success: false, error: 'Unauthorized' };
    }

    const context: AuthContext = { userId };

    // Jeśli nie wymagamy sprawdzania rodziny, zw<PERSON><PERSON><PERSON> sukces
    if (!options.requireFamilyMember && !options.requiredRoles && !options.requiredPermission) {
      return { success: true, context };
    }

    // Pobierz familyId z URL lub body
    const familyId = getFamilyIdFromRequest(request);

    if (!familyId && options.requireFamilyMember) {
      return { success: false, error: 'Family ID required' };
    }

    if (familyId) {
      context.familyId = familyId;

      // Pobierz rolę użytkownika w rodzinie
      const familyMember = await getFamilyMemberWithRole(userId, familyId);

      if (!familyMember) {
        return { success: false, error: 'Not a family member' };
      }

      context.role = familyMember.role;

      // Sprawdź wymagane role
      if (options.requiredRoles && !options.requiredRoles.includes(familyMember.role)) {
        return { success: false, error: 'Insufficient permissions' };
      }

      // Sprawdź wymagane uprawnienia
      if (
        options.requiredPermission &&
        !hasPermission(familyMember.role, options.requiredPermission)
      ) {
        return { success: false, error: 'Insufficient permissions' };
      }
    }

    return { success: true, context };
  } catch (error) {
    console.error('Auth middleware error:', error);
    return { success: false, error: 'Internal server error' };
  }
}

export function createAuthMiddleware(options: AuthMiddlewareOptions) {
  return async function authMiddleware(
    request: NextRequest,
    handler: (context: AuthContext) => Promise<Response> | Response
  ): Promise<Response> {
    const result = await withAuth(request, options);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: result.error === 'Unauthorized' ? 401 : 403 }
      );
    }

    return handler(result.context!);
  };
}

export function requireRole(...roles: FamilyRole[]) {
  return createAuthMiddleware({ requiredRoles: roles, requireFamilyMember: true });
}

export function requirePermission(permission: Permission) {
  return createAuthMiddleware({ requiredPermission: permission, requireFamilyMember: true });
}

export function requireAuth() {
  return createAuthMiddleware({});
}

function getFamilyIdFromRequest(request: NextRequest): string | undefined {
  // Sprawdź parametry URL
  const url = new URL(request.url);
  const pathSegments = url.pathname.split('/');

  // Szukaj wzorca /family/[id] lub /api/family/[id]
  const familyIndex = pathSegments.findIndex(segment => segment === 'family');
  if (familyIndex !== -1 && pathSegments[familyIndex + 1]) {
    return pathSegments[familyIndex + 1];
  }

  // Sprawdź query params
  const familyId = url.searchParams.get('familyId');
  if (familyId) return familyId;

  // TODO: Sprawdź body dla POST/PUT requests
  return undefined;
}
