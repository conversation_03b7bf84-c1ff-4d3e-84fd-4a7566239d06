import { NextRequest } from 'next/server';
import { SuspensionService } from '@/lib/services/suspension.service';

export class SuspensionError extends Error {
  constructor(
    message: string,
    public suspensionType: 'task' | 'member',
    public suspensionTitle?: string,
    public suspensionReason?: string,
    public suspensionEndDate?: Date
  ) {
    super(message);
    this.name = 'SuspensionError';
  }
}

export interface SuspensionCheckResult {
  isSuspended: boolean;
  suspensionType?: 'task' | 'member';
  suspensionTitle?: string;
  suspensionReason?: string;
  suspensionEndDate?: Date;
  message?: string;
}

/**
 * Check if a task is suspended and prevent actions if it is
 */
export async function checkTaskSuspension(
  taskId: string,
  action: string = 'wykonanie'
): Promise<SuspensionCheckResult> {
  try {
    const activeSuspensions = await SuspensionService.getActiveSuspensionsForTask(taskId);

    if (activeSuspensions.length > 0) {
      const suspension = activeSuspensions[0]; // Get the first active suspension

      return {
        isSuspended: true,
        suspensionType: 'task',
        suspensionTitle: suspension.title,
        suspensionReason: suspension.reason,
        suspensionEndDate: suspension.endDate,
        message: `<PERSON><PERSON> mo<PERSON> wykona<PERSON> akcji "${action}" - zadanie jest zawieszone do ${suspension.endDate.toLocaleDateString('pl-PL')}. Powód: ${suspension.title}`,
      };
    }

    return { isSuspended: false };
  } catch (error) {
    console.error('Error checking task suspension:', error);
    // In case of error, allow the action to proceed
    return { isSuspended: false };
  }
}

/**
 * Check if a member is suspended and prevent actions if they are
 */
export async function checkMemberSuspension(
  memberId: string,
  action: string = 'wykonanie akcji'
): Promise<SuspensionCheckResult> {
  try {
    const activeSuspensions = await SuspensionService.getActiveSuspensionsForMember(memberId);

    if (activeSuspensions.length > 0) {
      const suspension = activeSuspensions[0]; // Get the first active suspension

      return {
        isSuspended: true,
        suspensionType: 'member',
        suspensionTitle: suspension.title,
        suspensionReason: suspension.reason,
        suspensionEndDate: suspension.endDate,
        message: `Nie można wykonać akcji "${action}" - członek rodziny jest zawieszony do ${suspension.endDate.toLocaleDateString('pl-PL')}. Powód: ${suspension.title}`,
      };
    }

    return { isSuspended: false };
  } catch (error) {
    console.error('Error checking member suspension:', error);
    // In case of error, allow the action to proceed
    return { isSuspended: false };
  }
}

/**
 * Comprehensive suspension check for task actions
 * Checks both task-specific and member-specific suspensions
 */
export async function checkSuspensions(
  taskId?: string,
  memberId?: string,
  action: string = 'wykonanie akcji'
): Promise<SuspensionCheckResult> {
  // Check task suspension first if taskId is provided
  if (taskId) {
    const taskSuspension = await checkTaskSuspension(taskId, action);
    if (taskSuspension.isSuspended) {
      return taskSuspension;
    }
  }

  // Check member suspension if memberId is provided
  if (memberId) {
    const memberSuspension = await checkMemberSuspension(memberId, action);
    if (memberSuspension.isSuspended) {
      return memberSuspension;
    }
  }

  return { isSuspended: false };
}

/**
 * Middleware function that can be used in API routes to check suspensions
 * Throws SuspensionError if any suspension is active
 */
export async function suspensionMiddleware(
  request: NextRequest,
  taskId?: string,
  memberId?: string,
  action?: string
): Promise<void> {
  const result = await checkSuspensions(taskId, memberId, action);

  if (result.isSuspended) {
    throw new SuspensionError(
      result.message || 'Akcja została zablokowana ze względu na aktywne zawieszenie',
      result.suspensionType!,
      result.suspensionTitle,
      result.suspensionReason,
      result.suspensionEndDate
    );
  }
}

/**
 * Helper function to format suspension error response
 */
export function formatSuspensionErrorResponse(error: SuspensionError) {
  return {
    error: 'Suspension active',
    message: error.message,
    suspensionType: error.suspensionType,
    suspensionTitle: error.suspensionTitle,
    suspensionReason: error.suspensionReason,
    suspensionEndDate: error.suspensionEndDate?.toISOString(),
  };
}
