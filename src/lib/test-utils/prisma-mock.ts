import { PrismaClient } from '@prisma/client';
import { DeepMockProxy, mockDeep, mockReset } from 'jest-mock-extended';

export type MockPrisma = DeepMockProxy<PrismaClient>;

export const prismaMock = mockDeep<PrismaClient>();

export function resetPrismaMock() {
  mockReset(prismaMock);
}

// Mock the prisma singleton
jest.mock('@/lib/prisma', () => ({
  __esModule: true,
  prisma: prismaMock,
}));
