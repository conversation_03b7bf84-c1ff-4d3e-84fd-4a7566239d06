import { TaskWeight, TaskFrequency, TaskStatus, CompletionStatus } from '@prisma/client';

export const mockUser = {
  id: 'user-1',
  firstName: 'Test',
  lastName: 'User',
  email: '<EMAIL>',
};

export const mockMember = {
  id: 'member-1',
  familyId: 'family-1',
  userId: 'user-1',
  role: 'CHILD',
  points: 100,
  user: mockUser,
};

export const mockFamily = {
  id: 'family-1',
  name: 'Test Family',
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

export const mockTask = {
  id: 'task-1',
  familyId: 'family-1',
  title: 'Test Task',
  description: 'Test task description',
  points: 5,
  weight: TaskWeight.NORMAL,
  frequency: TaskFrequency.ONCE,
  status: TaskStatus.ACTIVE,
  dueDate: new Date('2024-06-30'),
  assignedBy: 'member-2',
  assignedAt: new Date('2024-06-25'),
  createdAt: new Date('2024-06-25'),
  updatedAt: new Date('2024-06-25'),
  assignedByUser: mockMember,
  assignments: [
    {
      id: 'assignment-1',
      taskId: 'task-1',
      memberId: 'member-1',
      assignedAt: new Date('2024-06-25'),
      assignedBy: 'member-2',
      assignmentType: 'ANY',
      member: mockMember,
    },
  ],
  completions: [],
  _count: {
    completions: 0,
  },
};

export const mockTaskCompletion = {
  id: 'completion-1',
  taskId: 'task-1',
  completedById: 'member-1',
  notes: 'Task completed successfully',
  status: CompletionStatus.PENDING,
  pointsAwarded: 0,
  completedAt: new Date('2024-06-26'),
  verifiedAt: null,
  verifiedById: null,
  task: mockTask,
  completedBy: mockMember,
  verifiedBy: null,
};

export const mockCreateTaskData = {
  title: 'New Task',
  description: 'New task description',
  points: 3,
  weight: TaskWeight.LIGHT,
  frequency: TaskFrequency.WEEKLY,
  dueDate: new Date('2024-07-01'),
  assignedMemberIds: ['member-1'],
};
