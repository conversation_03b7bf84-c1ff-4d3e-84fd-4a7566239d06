export const locales = ['pl', 'en', 'de', 'es'] as const;
export const defaultLocale = 'pl';

export type Locale = (typeof locales)[number];

export const localeConfig = {
  pl: {
    name: '<PERSON><PERSON>',
    flag: '🇵🇱',
    dir: 'ltr',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    currency: 'PLN',
  },
  en: {
    name: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
    dateFormat: 'MM/dd/yyyy',
    timeFormat: 'h:mm a',
    currency: 'USD',
  },
  de: {
    name: '<PERSON><PERSON><PERSON>',
    flag: '🇩🇪',
    dir: 'ltr',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    currency: 'EUR',
  },
  es: {
    name: 'Español',
    flag: '🇪🇸',
    dir: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    currency: 'EUR',
  },
};

export function getLocaleConfig(locale: Locale) {
  return localeConfig[locale] || localeConfig[defaultLocale];
}
