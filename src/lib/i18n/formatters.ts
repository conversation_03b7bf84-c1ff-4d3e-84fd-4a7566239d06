import { Locale, getLocaleConfig } from './config';

export function formatDate(date: Date, locale: Locale, format?: string): string {
  const localeConfig = getLocaleConfig(locale);
  return new Intl.DateTimeFormat(locale, {
    dateStyle: 'medium',
  }).format(date);
}

export function formatTime(date: Date, locale: Locale): string {
  const localeConfig = getLocaleConfig(locale);
  const use24Hour = localeConfig.timeFormat === 'HH:mm';

  return new Intl.DateTimeFormat(locale, {
    timeStyle: 'short',
    hour12: !use24Hour,
  }).format(date);
}

export function formatDateTime(date: Date, locale: Locale): string {
  return `${formatDate(date, locale)} ${formatTime(date, locale)}`;
}

export function formatRelativeTime(date: Date, locale: Locale): string {
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
  const diff = Math.round((date.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
  return rtf.format(diff, 'day');
}

export function formatNumber(number: number, locale: Locale): string {
  return new Intl.NumberFormat(locale).format(number);
}

export function formatCurrency(amount: number, locale: Locale): string {
  const localeConfig = getLocaleConfig(locale);
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: localeConfig.currency,
  }).format(amount);
}

export function formatPoints(points: number, locale: Locale): string {
  const pointsText =
    locale === 'pl'
      ? 'punktów'
      : locale === 'en'
        ? 'points'
        : locale === 'de'
          ? 'Punkte'
          : locale === 'es'
            ? 'puntos'
            : 'points';

  return `${formatNumber(points, locale)} ${pointsText}`;
}

export function formatPercentage(value: number, locale: Locale): string {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1,
  }).format(value / 100);
}
