import { prisma } from '@/lib/prisma';
import { Task, TaskCompletion, CompletionStatus } from '@prisma/client';
import { TASK_WEIGHT_POINTS } from '@/lib/validations/task';

export interface DateRange {
  from: Date;
  to: Date;
}

export interface MemberPoints {
  memberId: string;
  member: {
    id: string;
    user: {
      id: string;
      firstName: string | null;
      lastName: string | null;
      email: string;
    };
    role: string;
    isAdult: boolean;
  };
  points: number;
  taskCount: number;
  completionRate: number;
}

export interface PointsHistory {
  date: Date;
  points: number;
  reason: string;
  taskTitle?: string;
  taskId?: string;
}

export class PointsService {
  static async calculateTaskPoints(task: Task, completion: TaskCompletion): Promise<number> {
    const basePoints = TASK_WEIGHT_POINTS[task.weight];

    // Add bonus points for various factors
    let bonusPoints = 0;

    // Early completion bonus
    if (task.dueDate && completion.completedAt < task.dueDate) {
      const hoursEarly = Math.floor(
        (task.dueDate.getTime() - completion.completedAt.getTime()) / (1000 * 60 * 60)
      );
      if (hoursEarly > 0) {
        // 1 bonus point per day early, max 3 points
        bonusPoints += Math.min(Math.floor(hoursEarly / 24), 3);
      }
    }

    // Frequency bonus (recurring tasks get small bonus)
    switch (task.frequency) {
      case 'DAILY':
        bonusPoints += 1;
        break;
      case 'WEEKLY':
        bonusPoints += 2;
        break;
      case 'MONTHLY':
        bonusPoints += 1;
        break;
    }

    return basePoints + bonusPoints;
  }

  static async awardPoints(
    memberId: string,
    points: number,
    _reason: string,
    _taskId?: string
  ): Promise<void> {
    await prisma.$transaction(async tx => {
      // Update member points
      await tx.familyMember.update({
        where: { id: memberId },
        data: {
          points: {
            increment: points,
          },
        },
      });

      // Could log points history here if we had a PointsHistory model
      // For now, points are tracked through TaskCompletion records
    });
  }

  static async getMemberPoints(memberId: string, period?: DateRange): Promise<number> {
    if (!period) {
      // Get total points from member record
      const member = await prisma.familyMember.findUnique({
        where: { id: memberId },
        select: { points: true },
      });

      return member?.points || 0;
    }

    // Calculate points for specific period from task completions
    const completions = await prisma.taskCompletion.findMany({
      where: {
        completedById: memberId,
        status: CompletionStatus.APPROVED,
        completedAt: {
          gte: period.from,
          lte: period.to,
        },
      },
      select: {
        pointsAwarded: true,
      },
    });

    return completions.reduce((total, completion) => total + completion.pointsAwarded, 0);
  }

  static async getFamilyLeaderboard(familyId: string, period?: DateRange): Promise<MemberPoints[]> {
    if (!period) {
      // Get leaderboard based on total points
      const members = await prisma.familyMember.findMany({
        where: { familyId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          completedTasks: {
            where: {
              status: CompletionStatus.APPROVED,
            },
            select: {
              id: true,
              pointsAwarded: true,
            },
          },
          assignedTasks: {
            where: {
              status: 'ACTIVE',
            },
            select: {
              id: true,
            },
          },
        },
        orderBy: {
          points: 'desc',
        },
      });

      return members.map(member => {
        const completedTasksCount = member.completedTasks.length;
        const assignedTasksCount = member.assignedTasks.length;
        const totalTasks = completedTasksCount + assignedTasksCount;

        return {
          memberId: member.id,
          member: {
            id: member.id,
            user: member.user,
            role: member.role,
            isAdult: member.isAdult,
          },
          points: member.points,
          taskCount: completedTasksCount,
          completionRate: totalTasks > 0 ? (completedTasksCount / totalTasks) * 100 : 0,
        };
      });
    }

    // Get leaderboard for specific period
    const members = await prisma.familyMember.findMany({
      where: { familyId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        completedTasks: {
          where: {
            status: CompletionStatus.APPROVED,
            completedAt: {
              gte: period.from,
              lte: period.to,
            },
          },
          select: {
            id: true,
            pointsAwarded: true,
          },
        },
        assignedTasks: {
          where: {
            status: 'ACTIVE',
            createdAt: {
              gte: period.from,
              lte: period.to,
            },
          },
          select: {
            id: true,
          },
        },
      },
    });

    const memberPoints = members.map(member => {
      const periodPoints = member.completedTasks.reduce(
        (total, completion) => total + completion.pointsAwarded,
        0
      );
      const completedTasksCount = member.completedTasks.length;
      const assignedTasksCount = member.assignedTasks.length;
      const totalTasks = completedTasksCount + assignedTasksCount;

      return {
        memberId: member.id,
        member: {
          id: member.id,
          user: member.user,
          role: member.role,
          isAdult: member.isAdult,
        },
        points: periodPoints,
        taskCount: completedTasksCount,
        completionRate: totalTasks > 0 ? (completedTasksCount / totalTasks) * 100 : 0,
      };
    });

    // Sort by points for the period
    return memberPoints.sort((a, b) => b.points - a.points);
  }

  static async getPointsHistory(memberId: string, period?: DateRange): Promise<PointsHistory[]> {
    const where: any = {
      completedById: memberId,
      status: CompletionStatus.APPROVED,
    };

    if (period) {
      where.completedAt = {
        gte: period.from,
        lte: period.to,
      };
    }

    const completions = await prisma.taskCompletion.findMany({
      where,
      include: {
        task: {
          select: {
            id: true,
            title: true,
          },
        },
      },
      orderBy: {
        completedAt: 'desc',
      },
    });

    return completions.map(completion => ({
      date: completion.completedAt,
      points: completion.pointsAwarded,
      reason: 'Task completion',
      taskTitle: completion.task.title,
      taskId: completion.task.id,
    }));
  }

  static async getFamilyPointsStats(familyId: string, period?: DateRange) {
    const where: any = {
      task: {
        familyId,
      },
      status: CompletionStatus.APPROVED,
    };

    if (period) {
      where.completedAt = {
        gte: period.from,
        lte: period.to,
      };
    }

    const [totalPoints, totalCompletions, averagePoints] = await Promise.all([
      // Total points awarded
      prisma.taskCompletion.aggregate({
        where,
        _sum: {
          pointsAwarded: true,
        },
      }),

      // Total completions count
      prisma.taskCompletion.count({
        where,
      }),

      // Average points per completion
      prisma.taskCompletion.aggregate({
        where,
        _avg: {
          pointsAwarded: true,
        },
      }),
    ]);

    return {
      totalPoints: totalPoints._sum.pointsAwarded || 0,
      totalCompletions,
      averagePoints: Math.round((averagePoints._avg.pointsAwarded || 0) * 100) / 100,
    };
  }

  static async getTopPerformers(
    familyId: string,
    period?: DateRange,
    limit: number = 5
  ): Promise<MemberPoints[]> {
    const leaderboard = await this.getFamilyLeaderboard(familyId, period);
    return leaderboard.slice(0, limit);
  }

  static async resetMemberPoints(memberId: string): Promise<void> {
    await prisma.familyMember.update({
      where: { id: memberId },
      data: { points: 0 },
    });
  }

  static async resetFamilyPoints(familyId: string): Promise<void> {
    await prisma.familyMember.updateMany({
      where: { familyId },
      data: { points: 0 },
    });
  }

  // Utility methods for points calculation
  static async recalculateMemberPoints(memberId: string): Promise<number> {
    // Recalculate points from all approved task completions
    const completions = await prisma.taskCompletion.findMany({
      where: {
        completedById: memberId,
        status: CompletionStatus.APPROVED,
      },
      select: {
        pointsAwarded: true,
      },
    });

    const totalPoints = completions.reduce(
      (total, completion) => total + completion.pointsAwarded,
      0
    );

    // Update member record
    await prisma.familyMember.update({
      where: { id: memberId },
      data: { points: totalPoints },
    });

    return totalPoints;
  }

  static async getMemberRank(memberId: string, familyId: string): Promise<number> {
    const member = await prisma.familyMember.findUnique({
      where: { id: memberId },
      select: { points: true },
    });

    if (!member) return 0;

    const higherRankedCount = await prisma.familyMember.count({
      where: {
        familyId,
        points: {
          gt: member.points,
        },
      },
    });

    return higherRankedCount + 1;
  }
}
