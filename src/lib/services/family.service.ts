import { prisma } from '@/lib/prisma';
import {
  FamilyRole,
  InvitationStatus,
  type Family,
  type FamilyMember,
  type FamilyInvitation,
} from '@prisma/client';
import { randomBytes } from 'crypto';
import { EmailService } from './email.service';

export interface CreateFamilyData {
  name: string;
  description?: string;
}

export interface InvitationData {
  email: string;
  role: FamilyRole;
  isAdult: boolean;
}

export class FamilyService {
  static async createFamily(userId: string, data: CreateFamilyData): Promise<Family> {
    const family = await prisma.family.create({
      data: {
        name: data.name,
        description: data.description,
        members: {
          create: {
            userId,
            role: FamilyRole.OWNER,
            isAdult: true,
          },
        },
      },
      include: {
        members: {
          include: {
            user: true,
          },
        },
      },
    });

    return family;
  }

  static async inviteMember(
    familyId: string,
    invitation: InvitationData
  ): Promise<FamilyInvitation> {
    // Generate secure token
    const token = randomBytes(32).toString('hex');

    // Set expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create invitation
    const familyInvitation = await prisma.familyInvitation.create({
      data: {
        familyId,
        email: invitation.email,
        role: invitation.role,
        isAdult: invitation.isAdult,
        token,
        expiresAt,
        status: InvitationStatus.PENDING,
      },
      include: {
        family: {
          include: {
            members: {
              where: { role: FamilyRole.OWNER },
              include: {
                user: true,
              },
              take: 1,
            },
          },
        },
      },
    });

    // Send invitation email
    try {
      await EmailService.sendFamilyInvitation(familyInvitation);
    } catch (error) {
      console.error('Failed to send invitation email:', error);
      // Don't fail the invitation creation if email fails
    }

    return familyInvitation;
  }

  static async acceptInvitation(token: string, userId: string): Promise<FamilyMember> {
    return await prisma.$transaction(async tx => {
      // Get invitation
      const invitation = await tx.familyInvitation.findUnique({
        where: { token },
        include: { family: true },
      });

      if (!invitation) {
        throw new Error('Invitation not found');
      }

      if (invitation.status !== InvitationStatus.PENDING) {
        throw new Error('Invitation is no longer valid');
      }

      if (invitation.expiresAt < new Date()) {
        // Mark as expired
        await tx.familyInvitation.update({
          where: { id: invitation.id },
          data: { status: InvitationStatus.EXPIRED },
        });
        throw new Error('Invitation has expired');
      }

      // Get user
      const user = await tx.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new Error('User not found');
      }

      if (user.email !== invitation.email) {
        throw new Error('Email mismatch');
      }

      // Check if user is already a member
      const existingMember = await tx.familyMember.findUnique({
        where: {
          userId_familyId: {
            userId,
            familyId: invitation.familyId,
          },
        },
      });

      if (existingMember) {
        throw new Error('User is already a family member');
      }

      // Create family member
      const member = await tx.familyMember.create({
        data: {
          userId,
          familyId: invitation.familyId,
          role: invitation.role,
          isAdult: invitation.isAdult,
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          family: true,
        },
      });

      // Mark invitation as accepted
      await tx.familyInvitation.update({
        where: { id: invitation.id },
        data: { status: InvitationStatus.ACCEPTED },
      });

      // Send welcome email
      try {
        await EmailService.sendWelcomeToFamily(member);
      } catch (error) {
        console.error('Failed to send welcome email:', error);
      }

      return member;
    });
  }

  static async changeMemberRole(
    familyId: string,
    memberId: string,
    newRole: FamilyRole,
    isAdult?: boolean
  ): Promise<FamilyMember> {
    return await prisma.$transaction(async tx => {
      const member = await tx.familyMember.findUnique({
        where: { id: memberId },
      });

      if (!member || member.familyId !== familyId) {
        throw new Error('Member not found');
      }

      // Check if removing the last owner
      if (member.role === FamilyRole.OWNER && newRole !== FamilyRole.OWNER) {
        const ownerCount = await tx.familyMember.count({
          where: {
            familyId,
            role: FamilyRole.OWNER,
          },
        });

        if (ownerCount <= 1) {
          throw new Error('Family must have at least one owner');
        }
      }

      const updatedMember = await tx.familyMember.update({
        where: { id: memberId },
        data: {
          role: newRole,
          isAdult: isAdult ?? member.isAdult,
          lastActive: new Date(),
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      return updatedMember;
    });
  }

  static async removeMember(familyId: string, memberId: string): Promise<void> {
    await prisma.$transaction(async tx => {
      const member = await tx.familyMember.findUnique({
        where: { id: memberId },
      });

      if (!member || member.familyId !== familyId) {
        throw new Error('Member not found');
      }

      // Check if removing the last owner
      if (member.role === FamilyRole.OWNER) {
        const ownerCount = await tx.familyMember.count({
          where: {
            familyId,
            role: FamilyRole.OWNER,
          },
        });

        const memberCount = await tx.familyMember.count({
          where: { familyId },
        });

        if (ownerCount <= 1 && memberCount > 1) {
          throw new Error('Transfer ownership before removing the last owner');
        }
      }

      await tx.familyMember.delete({
        where: { id: memberId },
      });
    });
  }

  static async leaveFamily(familyId: string, userId: string): Promise<void> {
    const member = await prisma.familyMember.findUnique({
      where: {
        userId_familyId: {
          userId,
          familyId,
        },
      },
    });

    if (!member) {
      throw new Error('Not a family member');
    }

    await this.removeMember(familyId, member.id);
  }

  static async cleanupExpiredInvitations(): Promise<void> {
    await prisma.familyInvitation.updateMany({
      where: {
        status: InvitationStatus.PENDING,
        expiresAt: {
          lt: new Date(),
        },
      },
      data: {
        status: InvitationStatus.EXPIRED,
      },
    });
  }

  // Additional methods for calendar API
  static async isFamilyMember(familyId: string, userId: string): Promise<boolean> {
    const member = await prisma.familyMember.findUnique({
      where: {
        userId_familyId: {
          userId,
          familyId,
        },
      },
    });

    return !!member;
  }

  static async getMemberRole(familyId: string, userId: string): Promise<FamilyRole | null> {
    const member = await prisma.familyMember.findUnique({
      where: {
        userId_familyId: {
          userId,
          familyId,
        },
      },
      select: {
        role: true,
      },
    });

    return member?.role || null;
  }

  static async getFamilyMember(familyId: string, userId: string): Promise<FamilyMember | null> {
    const member = await prisma.familyMember.findUnique({
      where: {
        userId_familyId: {
          userId,
          familyId,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return member;
  }

  static async getFamilyStats(familyId: string) {
    const [memberCount, taskCount, activeTaskCount, completedTaskCount] = await Promise.all([
      prisma.familyMember.count({
        where: { familyId },
      }),

      prisma.task.count({
        where: { familyId },
      }),

      prisma.task.count({
        where: {
          familyId,
          completions: {
            none: {},
          },
        },
      }),

      prisma.taskCompletion.count({
        where: {
          task: {
            familyId,
          },
        },
      }),
    ]);

    return {
      memberCount,
      taskCount,
      activeTaskCount,
      completedTaskCount,
    };
  }

  static async getFamilyWithMembers(
    familyId: string
  ): Promise<(Family & { members: FamilyMember[] }) | null> {
    return await prisma.family.findUnique({
      where: { id: familyId },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
      },
    });
  }
}
