import { <PERSON>In<PERSON>, <PERSON>M<PERSON><PERSON>, Family, User } from '@prisma/client';
import { FamilyInvitationTemplate } from '@/lib/email/templates/family-invitation';
import { WelcomeToFamilyTemplate } from '@/lib/email/templates/welcome-to-family';
import { prisma } from '@/lib/prisma';
import nodemailer from 'nodemailer';

// Email service interface - can be implemented with different providers
interface EmailProvider {
  sendEmail(params: { to: string; subject: string; html: string; text?: string }): Promise<void>;
}

// Mock email provider for development
class MockEmailProvider implements EmailProvider {
  async sendEmail(params: {
    to: string;
    subject: string;
    html: string;
    text?: string;
  }): Promise<void> {
    console.log('📧 Mock Email Sent:');
    console.log(`To: ${params.to}`);
    console.log(`Subject: ${params.subject}`);
    console.log(`HTML: ${params.html}`);
    if (params.text) {
      console.log(`Text: ${params.text}`);
    }
    console.log('---');
  }
}

// Resend email provider (when configured) - commented out as not currently used
// class ResendEmailProvider implements EmailProvider {
//   private apiKey: string;
//   private fromEmail: string;

//   constructor(apiKey: string, fromEmail: string) {
//     this.apiKey = apiKey;
//     this.fromEmail = fromEmail;
//   }

//   async sendEmail(params: {
//     to: string;
//     subject: string;
//     html: string;
//     text?: string;
//   }): Promise<void> {
//     const response = await fetch('https://api.resend.com/emails', {
//       method: 'POST',
//       headers: {
//         Authorization: `Bearer ${this.apiKey}`,
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         from: this.fromEmail,
//         to: params.to,
//         subject: params.subject,
//         html: params.html,
//         text: params.text,
//       }),
//     });

//     if (!response.ok) {
//       const error = await response.text();
//       throw new Error(`Failed to send email: ${error}`);
//     }
//   }
// }

// SMTP email provider using nodemailer
class SMTPEmailProvider implements EmailProvider {
  private transporter: nodemailer.Transporter;

  constructor() {
    const config = {
      host: process.env.MAIL_HOST || '',
      port: parseInt(process.env.MAIL_PORT || '587'),
      user: process.env.MAIL_USER || '',
      pass: process.env.MAIL_PASS || '',
    };

    if (!config.host || !config.user || !config.pass) {
      throw new Error(
        'Brak konfiguracji SMTP w zmiennych środowiskowych (MAIL_HOST, MAIL_USER, MAIL_PASS)'
      );
    }

    this.transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.port === 465,
      auth: {
        user: config.user,
        pass: config.pass,
      },
      tls: {
        rejectUnauthorized: false,
      },
    });
  }

  async sendEmail(params: {
    to: string;
    subject: string;
    html: string;
    text?: string;
  }): Promise<void> {
    const mailOptions = {
      from: process.env.MAIL_FROM,
      to: params.to,
      subject: params.subject,
      html: params.html,
      text: params.text,
    };

    await this.transporter.sendMail(mailOptions);
  }
}

type FamilyInvitationWithDetails = FamilyInvitation & {
  family: Family & {
    members: (FamilyMember & {
      user: User;
    })[];
  };
};

type FamilyMemberWithDetails = FamilyMember & {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  family: Family;
};

export class EmailService {
  private static provider: EmailProvider;

  static initialize() {
    // const resendApiKey = process.env.RESEND_API_KEY;
    // const fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    // const mailHost = process.env.MAIL_HOST;

    // if (resendApiKey) {
    //   this.provider = new ResendEmailProvider(resendApiKey, fromEmail);
    // } else if (mailHost) {
    try {
      this.provider = new SMTPEmailProvider();
    } catch (error) {
      console.warn('Błąd konfiguracji SMTP, używanie mock providera:', error);
      this.provider = new MockEmailProvider();
    }
    // } else {
    //   this.provider = new MockEmailProvider();
    // }
  }

  static async sendFamilyInvitation(invitation: FamilyInvitationWithDetails): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    const owner = invitation.family.members.find(m => m.role === 'OWNER');
    const ownerName = owner
      ? `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim() || owner.user.email
      : 'Członek rodziny';

    const invitationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invitations/${invitation.token}`;

    const { html, text } = FamilyInvitationTemplate({
      familyName: invitation.family.name,
      inviterName: ownerName,
      role: invitation.role,
      invitationUrl,
      expiresAt: invitation.expiresAt,
    });

    await this.provider.sendEmail({
      to: invitation.email,
      subject: `Zaproszenie do rodziny "${invitation.family.name}"`,
      html,
      text,
    });
  }

  static async sendInvitationReminder(invitation: FamilyInvitationWithDetails): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    const owner = invitation.family.members.find(m => m.role === 'OWNER');
    const ownerName = owner
      ? `${owner.user.firstName || ''} ${owner.user.lastName || ''}`.trim() || owner.user.email
      : 'Członek rodziny';

    const invitationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/invitations/${invitation.token}`;

    const { html, text } = FamilyInvitationTemplate({
      familyName: invitation.family.name,
      inviterName: ownerName,
      role: invitation.role,
      invitationUrl,
      expiresAt: invitation.expiresAt,
      isReminder: true,
    });

    await this.provider.sendEmail({
      to: invitation.email,
      subject: `Przypomnienie: Zaproszenie do rodziny "${invitation.family.name}"`,
      html,
      text,
    });
  }

  static async sendWelcomeToFamily(member: FamilyMemberWithDetails): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    const memberName =
      `${member.user.firstName || ''} ${member.user.lastName || ''}`.trim() || member.user.email;
    const dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL}/families/${member.familyId}`;

    const { html, text } = WelcomeToFamilyTemplate({
      memberName,
      familyName: member.family.name,
      role: member.role,
      dashboardUrl,
    });

    await this.provider.sendEmail({
      to: member.user.email,
      subject: `Witaj w rodzinie "${member.family.name}"!`,
      html,
      text,
    });
  }

  // Utility method for testing
  static async sendTestEmail(to: string): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    await this.provider.sendEmail({
      to,
      subject: 'Test Email z Family Tasks',
      html: '<h1>To jest testowy email</h1><p>Jeśli otrzymałeś ten email, konfiguracja działa poprawnie!</p>',
      text: 'To jest testowy email. Jeśli otrzymałeś ten email, konfiguracja działa poprawnie!',
    });
  }

  // Helper method to check user notification preferences
  private static async shouldSendEmail(
    userId: string,
    notificationType: 'taskAssigned' | 'taskCompleted' | 'taskVerified' | 'taskReminder'
  ): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          emailNotificationsEnabled: true,
          notifyTaskAssigned: true,
          notifyTaskCompleted: true,
          notifyTaskVerified: true,
          notifyTaskReminder: true,
        },
      });

      if (!user || !user.emailNotificationsEnabled) {
        return false;
      }

      switch (notificationType) {
        case 'taskAssigned':
          return user.notifyTaskAssigned;
        case 'taskCompleted':
          return user.notifyTaskCompleted;
        case 'taskVerified':
          return user.notifyTaskVerified;
        case 'taskReminder':
          return user.notifyTaskReminder;
        default:
          return false;
      }
    } catch (error) {
      console.warn('Error checking notification preferences, defaulting to disabled:', error);
      return false;
    }
  }

  // Task notification methods
  static async sendTaskAssignment(taskId: string, memberIds: string[]): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    try {
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          family: true,
          assignedByUser: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!task) {
        throw new Error('Zadanie nie zostało znalezione');
      }

      const members = await prisma.familyMember.findMany({
        where: {
          id: { in: memberIds },
        },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      const assignedByName =
        task.assignedByUser.user.firstName && task.assignedByUser.user.lastName
          ? `${task.assignedByUser.user.firstName} ${task.assignedByUser.user.lastName}`
          : task.assignedByUser.user.email;

      for (const member of members) {
        // Check if user wants to receive task assignment notifications
        const shouldSend = await this.shouldSendEmail(member.user.id, 'taskAssigned');
        if (!shouldSend) {
          console.log(
            `Skipping task assignment email for ${member.user.email} - notifications disabled`
          );
          continue;
        }

        const memberName =
          member.user.firstName && member.user.lastName
            ? `${member.user.firstName} ${member.user.lastName}`
            : member.user.email;

        const subject = `Nowe zadanie: ${task.title}`;
        const html = this.generateTaskAssignmentTemplate({
          memberName,
          taskTitle: task.title,
          taskDescription: task.description,
          assignedBy: assignedByName,
          familyName: task.family.name,
          dueDate: task.dueDate,
          points: task.points,
        });

        try {
          await this.provider.sendEmail({
            to: member.user.email,
            subject,
            html,
          });

          // Zapisz powiadomienie w bazie jako wysłane
          await prisma.emailNotification.create({
            data: {
              familyId: task.familyId,
              userId: member.user.id,
              type: 'taskAssigned',
              subject,
              content: html,
              sentAt: new Date(),
            },
          });
        } catch (emailError) {
          console.error(`Błąd wysyłania e-maila do ${member.user.email}:`, emailError);

          // Zapisz powiadomienie w bazie jako niewysłane
          await prisma.emailNotification.create({
            data: {
              familyId: task.familyId,
              userId: member.user.id,
              type: 'taskAssigned',
              subject,
              content: html,
              sentAt: null, // Nie wysłane
            },
          });
        }
      }
    } catch (error) {
      console.error('Błąd wysyłania powiadomień o przypisaniu zadania:', error);
      throw error;
    }
  }

  static async sendTaskReminder(taskId: string, memberId: string): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    try {
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          family: true,
        },
      });

      const member = await prisma.familyMember.findUnique({
        where: { id: memberId },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!task || !member) {
        throw new Error('Zadanie lub członek rodziny nie został znaleziony');
      }

      // Check if user wants to receive task reminder notifications
      const shouldSend = await this.shouldSendEmail(member.user.id, 'taskReminder');
      if (!shouldSend) {
        console.log(
          `Skipping task reminder email for ${member.user.email} - notifications disabled`
        );
        return;
      }

      const memberName =
        member.user.firstName && member.user.lastName
          ? `${member.user.firstName} ${member.user.lastName}`
          : member.user.email;

      const subject = `Przypomnienie: ${task.title}`;
      const html = this.generateTaskReminderTemplate({
        memberName,
        taskTitle: task.title,
        taskDescription: task.description,
        familyName: task.family.name,
        dueDate: task.dueDate,
        points: task.points,
      });

      try {
        await this.provider.sendEmail({
          to: member.user.email,
          subject,
          html,
        });

        await prisma.emailNotification.create({
          data: {
            familyId: task.familyId,
            userId: member.user.id,
            type: 'taskOverdue',
            subject,
            content: html,
            sentAt: new Date(),
          },
        });
      } catch (emailError) {
        console.error(`Błąd wysyłania przypomnienia do ${member.user.email}:`, emailError);

        await prisma.emailNotification.create({
          data: {
            familyId: task.familyId,
            userId: member.user.id,
            type: 'taskOverdue',
            subject,
            content: html,
            sentAt: null,
          },
        });
        throw emailError;
      }
    } catch (error) {
      console.error('Błąd wysyłania przypomnienia o zadaniu:', error);
      throw error;
    }
  }

  static async sendTaskCompletion(taskId: string, completedBy: string): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    try {
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          family: true,
          assignedByUser: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      const completedByMember = await prisma.familyMember.findUnique({
        where: { id: completedBy },
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      if (!task || !completedByMember) {
        throw new Error('Zadanie lub członek rodziny nie został znaleziony');
      }

      // Check if user (who assigned the task) wants to receive task completion notifications
      const shouldSend = await this.shouldSendEmail(task.assignedByUser.user.id, 'taskCompleted');
      if (!shouldSend) {
        console.log(
          `Skipping task completion email for ${task.assignedByUser.user.email} - notifications disabled`
        );
        return;
      }

      const completedByName =
        completedByMember.user.firstName && completedByMember.user.lastName
          ? `${completedByMember.user.firstName} ${completedByMember.user.lastName}`
          : completedByMember.user.email;

      const subject = `Zadanie wykonane: ${task.title}`;
      const html = this.generateTaskCompletionTemplate({
        taskTitle: task.title,
        completedBy: completedByName,
        familyName: task.family.name,
        points: task.points,
      });

      try {
        await this.provider.sendEmail({
          to: task.assignedByUser.user.email,
          subject,
          html,
        });

        await prisma.emailNotification.create({
          data: {
            familyId: task.familyId,
            userId: task.assignedByUser.user.id,
            type: 'taskCompleted',
            subject,
            content: html,
            sentAt: new Date(),
          },
        });
      } catch (emailError) {
        console.error(
          `Błąd wysyłania powiadomienia o wykonaniu do ${task.assignedByUser.user.email}:`,
          emailError
        );

        await prisma.emailNotification.create({
          data: {
            familyId: task.familyId,
            userId: task.assignedByUser.user.id,
            type: 'taskCompleted',
            subject,
            content: html,
            sentAt: null,
          },
        });
        throw emailError;
      }
    } catch (error) {
      console.error('Błąd wysyłania powiadomienia o wykonaniu zadania:', error);
      throw error;
    }
  }

  static async sendTaskVerified(
    completionId: string,
    approved: boolean,
    feedback?: string
  ): Promise<void> {
    if (!this.provider) {
      this.initialize();
    }

    try {
      const completion = await prisma.taskCompletion.findUnique({
        where: { id: completionId },
        include: {
          task: {
            include: {
              family: true,
            },
          },
          completedBy: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          verifiedBy: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!completion) {
        throw new Error('Ukończenie zadania nie zostało znalezione');
      }

      // Check if user (who completed the task) wants to receive task verification notifications
      const shouldSend = await this.shouldSendEmail(completion.completedBy.user.id, 'taskVerified');
      if (!shouldSend) {
        console.log(
          `Skipping task verification email for ${completion.completedBy.user.email} - notifications disabled`
        );
        return;
      }

      const completedByName =
        completion.completedBy.user.firstName && completion.completedBy.user.lastName
          ? `${completion.completedBy.user.firstName} ${completion.completedBy.user.lastName}`
          : completion.completedBy.user.email;

      const verifierName = completion.verifiedBy
        ? completion.verifiedBy.user.firstName && completion.verifiedBy.user.lastName
          ? `${completion.verifiedBy.user.firstName} ${completion.verifiedBy.user.lastName}`
          : completion.verifiedBy.user.email
        : 'Administrator';

      const subject = approved
        ? `Zadanie zatwierdzone: ${completion.task.title}`
        : `Zadanie odrzucone: ${completion.task.title}`;

      const html = this.generateTaskVerificationTemplate({
        completedByName,
        taskTitle: completion.task.title,
        approved,
        verifierName,
        familyName: completion.task.family.name,
        points: approved ? completion.pointsAwarded : 0,
        feedback,
      });

      try {
        await this.provider.sendEmail({
          to: completion.completedBy.user.email,
          subject,
          html,
        });

        await prisma.emailNotification.create({
          data: {
            familyId: completion.task.familyId,
            userId: completion.completedBy.user.id,
            type: approved ? 'taskApproved' : 'taskRejected',
            subject,
            content: html,
            sentAt: new Date(),
          },
        });
      } catch (emailError) {
        console.error(
          `Błąd wysyłania powiadomienia o weryfikacji do ${completion.completedBy.user.email}:`,
          emailError
        );

        await prisma.emailNotification.create({
          data: {
            familyId: completion.task.familyId,
            userId: completion.completedBy.user.id,
            type: approved ? 'taskApproved' : 'taskRejected',
            subject,
            content: html,
            sentAt: null,
          },
        });
        throw emailError;
      }
    } catch (error) {
      console.error('Błąd wysyłania powiadomienia o weryfikacji zadania:', error);
      throw error;
    }
  }

  // Email templates
  private static generateTaskAssignmentTemplate(data: {
    memberName: string;
    taskTitle: string;
    taskDescription?: string | null;
    assignedBy: string;
    familyName: string;
    dueDate?: Date | null;
    points: number;
  }): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Nowe zadanie</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #3B82F6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; background: #f9f9f9; }
        .task-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #3B82F6; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .btn { display: inline-block; padding: 10px 20px; background: #3B82F6; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 Nowe zadanie przypisane</h1>
        </div>
        <div class="content">
            <p>Cześć <strong>${data.memberName}</strong>!</p>
            <p>Zostało Ci przypisane nowe zadanie w rodzinie <strong>${data.familyName}</strong>.</p>
            
            <div class="task-details">
                <h3>📌 ${data.taskTitle}</h3>
                ${data.taskDescription ? `<p><strong>Opis:</strong> ${data.taskDescription}</p>` : ''}
                <p><strong>👤 Przypisane przez:</strong> ${data.assignedBy}</p>
                <p><strong>🏆 Punkty:</strong> ${data.points}</p>
                ${data.dueDate ? `<p><strong>📅 Termin:</strong> ${data.dueDate.toLocaleDateString('pl-PL')}</p>` : ''}
            </div>
            
            <p>Zaloguj się do aplikacji FamilyTasks, aby zobaczyć szczegóły zadania i oznaczyć je jako wykonane.</p>
        </div>
        <div class="footer">
            <p>🏠 FamilyTasks - Zarządzanie zadaniami rodzinnymi</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  private static generateTaskReminderTemplate(data: {
    memberName: string;
    taskTitle: string;
    taskDescription?: string | null;
    familyName: string;
    dueDate?: Date | null;
    points: number;
  }): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Przypomnienie o zadaniu</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #F59E0B; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; background: #f9f9f9; }
        .task-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #F59E0B; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .urgent { color: #EF4444; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⏰ Przypomnienie o zadaniu</h1>
        </div>
        <div class="content">
            <p>Cześć <strong>${data.memberName}</strong>!</p>
            <p class="urgent">⚠️ Przypominamy o zadaniu, które masz do wykonania w rodzinie <strong>${data.familyName}</strong>.</p>
            
            <div class="task-details">
                <h3>📌 ${data.taskTitle}</h3>
                ${data.taskDescription ? `<p><strong>Opis:</strong> ${data.taskDescription}</p>` : ''}
                <p><strong>🏆 Punkty:</strong> ${data.points}</p>
                ${data.dueDate ? `<p class="urgent"><strong>📅 Termin:</strong> ${data.dueDate.toLocaleDateString('pl-PL')}</p>` : ''}
            </div>
            
            <p>Zaloguj się do aplikacji FamilyTasks, aby oznaczyć zadanie jako wykonane.</p>
        </div>
        <div class="footer">
            <p>🏠 FamilyTasks - Zarządzanie zadaniami rodzinnymi</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  private static generateTaskCompletionTemplate(data: {
    taskTitle: string;
    completedBy: string;
    familyName: string;
    points: number;
  }): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Zadanie wykonane</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #10B981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; background: #f9f9f9; }
        .task-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #10B981; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .success { color: #10B981; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Zadanie wykonane!</h1>
        </div>
        <div class="content">
            <p class="success">🎉 Świetne wieści!</p>
            <p>Zadanie zostało oznaczone jako wykonane w rodzinie <strong>${data.familyName}</strong>.</p>
            
            <div class="task-details">
                <h3>📌 ${data.taskTitle}</h3>
                <p><strong>👤 Wykonane przez:</strong> ${data.completedBy}</p>
                <p><strong>🏆 Punkty:</strong> ${data.points}</p>
            </div>
            
            <p>Zaloguj się do aplikacji FamilyTasks, aby zweryfikować wykonanie zadania i przyznać punkty.</p>
        </div>
        <div class="footer">
            <p>🏠 FamilyTasks - Zarządzanie zadaniami rodzinnymi</p>
        </div>
    </div>
</body>
</html>
    `;
  }

  private static generateTaskVerificationTemplate(data: {
    completedByName: string;
    taskTitle: string;
    approved: boolean;
    verifierName: string;
    familyName: string;
    points: number;
    feedback?: string;
  }): string {
    const statusColor = data.approved ? '#10B981' : '#EF4444';
    const statusIcon = data.approved ? '✅' : '❌';
    const statusText = data.approved ? 'ZATWIERDZONE' : 'ODRZUCONE';
    const statusMessage = data.approved
      ? 'Gratulacje! Twoje zadanie zostało zatwierdzone.'
      : 'Twoje zadanie zostało odrzucone. Sprawdź uwagi poniżej.';

    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Weryfikacja zadania</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: ${statusColor}; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 20px; background: #f9f9f9; }
        .task-details { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid ${statusColor}; }
        .status { background: ${statusColor}; color: white; padding: 8px 12px; border-radius: 4px; font-weight: bold; display: inline-block; margin: 10px 0; }
        .feedback { background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
        .success { color: #10B981; }
        .error { color: #EF4444; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${statusIcon} Weryfikacja zadania</h1>
        </div>
        <div class="content">
            <p>Cześć <strong>${data.completedByName}</strong>!</p>
            <p class="${data.approved ? 'success' : 'error'}">${statusMessage}</p>
            
            <div class="task-details">
                <h3>📌 ${data.taskTitle}</h3>
                <div class="status">${statusText}</div>
                <p><strong>👤 Zweryfikowane przez:</strong> ${data.verifierName}</p>
                <p><strong>🏠 Rodzina:</strong> ${data.familyName}</p>
                ${data.approved ? `<p><strong>🏆 Przyznane punkty:</strong> ${data.points}</p>` : ''}
            </div>
            
            ${
              data.feedback
                ? `
            <div class="feedback">
                <h4>💬 Uwagi od weryfikującego:</h4>
                <p>${data.feedback}</p>
            </div>
            `
                : ''
            }
            
            <p>Zaloguj się do aplikacji FamilyTasks, aby zobaczyć zaktualizowany status zadania${data.approved ? ' i swoje punkty' : ''}.</p>
        </div>
        <div class="footer">
            <p>🏠 FamilyTasks - Zarządzanie zadaniami rodzinnymi</p>
        </div>
    </div>
</body>
</html>
    `;
  }
}
