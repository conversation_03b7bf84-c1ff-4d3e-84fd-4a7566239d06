import { prisma } from '@/lib/prisma';
import {
  TaskWeight,
  TaskFrequency,
  TaskStatus,
  CompletionStatus,
  TaskAssignmentType,
  type Task,
  type TaskCompletion,
  type TaskAssignment,
  type FamilyMember,
  type TaskSuspension,
} from '@prisma/client';
import { TASK_WEIGHT_POINTS } from '@/lib/validations/task';
import { SuspensionService, type DateRange } from './suspension.service';
import { EmailService } from './email.service';

export interface CreateTaskData {
  title: string;
  description?: string;
  points: number;
  weight: TaskWeight;
  frequency: TaskFrequency;
  assignedMemberIds?: string[];
  assignmentType?: TaskAssignmentType;
  // Nowe semantyczne pola czasowe
  startDateTime?: Date;
  endDateTime?: Date;
  cycleEndDate?: Date;
  // Calendar-specific fields
  allDay?: boolean;
  color?: string;
  recurrenceRule?: string;
  // Przestarzałe pola - zachowane dla kompatybilności
  dueDate?: Date; // @deprecated Użyj startDateTime
  startDate?: Date; // @deprecated Użyj startDateTime
  endDate?: Date; // @deprecated Użyj endDateTime lub cycleEndDate
}

export interface UpdateTaskData {
  title?: string;
  description?: string;
  points?: number;
  weight?: TaskWeight;
  frequency?: TaskFrequency;
  status?: TaskStatus;
  assignedMemberIds?: string[];
  assignmentType?: TaskAssignmentType;
  // Nowe semantyczne pola czasowe
  startDateTime?: Date;
  endDateTime?: Date;
  cycleEndDate?: Date;
  // Calendar-specific fields
  allDay?: boolean;
  color?: string;
  recurrenceRule?: string;
  // Przestarzałe pola - zachowane dla kompatybilności
  dueDate?: Date; // @deprecated Użyj startDateTime
  startDate?: Date; // @deprecated Użyj startDateTime
  endDate?: Date; // @deprecated Użyj endDateTime lub cycleEndDate
}

export interface TaskFilters {
  status?: TaskStatus[];
  assignedMemberIds?: string[];
  dueDate?: {
    from?: Date;
    to?: Date;
  };
  weight?: TaskWeight[];
  frequency?: TaskFrequency[];
  search?: string;
  completionStatus?: ('pending' | 'approved' | 'rejected' | 'overdue' | 'due_soon')[];
  pointsRange?: {
    min?: number;
    max?: number;
  };
  includeCompleted?: boolean;
}

export type TaskWithRelations = Task & {
  assignedByUser: FamilyMember & {
    user: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
    };
  };
  completions: TaskCompletion[];
  assignments: (TaskAssignment & {
    member: FamilyMember & {
      user: {
        id: string;
        firstName: string;
        lastName: string;
        email: string;
      };
    };
  })[];
  _count?: {
    completions: number;
  };
};

export class TaskService {
  // CRUD operations
  static async createTask(
    familyId: string,
    assignedById: string,
    data: CreateTaskData
  ): Promise<any> {
    const task = await prisma.task.create({
      data: {
        familyId,
        title: data.title,
        description: data.description,
        points: data.points,
        weight: data.weight,
        frequency: data.frequency,
        assignedBy: assignedById,
        assignedAt: data.assignedMemberIds?.length ? new Date() : null,
        status: TaskStatus.ACTIVE,
        // Nowe semantyczne pola czasowe
        startDateTime: data.startDateTime,
        endDateTime: data.endDateTime,
        cycleEndDate: data.cycleEndDate,
        // Calendar-specific fields
        allDay: data.allDay ?? true,
        color: data.color,
        recurrenceRule: data.recurrenceRule,
        // Kompatybilność wsteczna - mapowanie starych pól na nowe
        dueDate: data.dueDate || data.startDateTime, // Fallback dla kompatybilności
        startDate: data.startDate || data.startDateTime, // Fallback dla kompatybilności
        endDate: data.endDate || data.endDateTime || data.cycleEndDate, // Fallback dla kompatybilności
      },
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: {
          include: {
            completedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Utworzenie przypisań do wielu członków jeśli podano assignedMemberIds
    if (data.assignedMemberIds && data.assignedMemberIds.length > 0) {
      try {
        console.log(
          `Tworzenie ${data.assignedMemberIds.length} przypisań dla zadania ${task.id}...`
        );

        const assignments = await prisma.taskAssignment.createMany({
          data: data.assignedMemberIds.map(memberId => ({
            taskId: task.id,
            memberId,
            assignedBy: assignedById,
            assignedAt: new Date(),
            assignmentType: data.assignmentType || TaskAssignmentType.ANY,
          })),
        });

        console.log(`Utworzono ${assignments.count} przypisań dla zadania ${task.id}`);

        // Wyślij powiadomienia e-mail o przypisaniu zadania
        try {
          await EmailService.sendTaskAssignment(task.id, data.assignedMemberIds);
          console.log('Wysłano powiadomienia e-mail o przypisaniu zadania');
        } catch (emailError) {
          console.warn('Nie udało się wysłać powiadomień e-mail:', emailError);
          // Nie przerywamy procesu - to nie jest krytyczny błąd
        }
      } catch (assignmentError) {
        console.error('Błąd podczas tworzenia przypisań zadania:', assignmentError);
        const errorMessage =
          assignmentError instanceof Error ? assignmentError.message : 'Nieznany błąd';
        throw new Error(`Nie udało się utworzyć przypisań zadania: ${errorMessage}`);
      }
    }

    return task;
  }

  static async updateTask(taskId: string, data: UpdateTaskData): Promise<any> {
    // Separate fields that belong to Task vs TaskAssignment
    const { assignedMemberIds, assignmentType, ...taskData } = data;

    // Pobierz istniejące zadanie aby sprawdzić czy jest cykliczne
    const existingTask = await prisma.task.findUnique({
      where: { id: taskId },
      select: {
        id: true,
        frequency: true,
        recurrenceRule: true,
        title: true,
      },
    });

    if (!existingTask) {
      throw new Error('Zadanie nie zostało znalezione');
    }

    // Logowanie diagnostyczne dla zadań cyklicznych
    if (existingTask.frequency !== 'ONCE') {
      console.log('🔄 Aktualizacja zadania cyklicznego:', {
        taskId,
        existingFrequency: existingTask.frequency,
        existingRecurrenceRule: existingTask.recurrenceRule,
        newFrequency: data.frequency,
        newRecurrenceRule: data.recurrenceRule,
        title: existingTask.title,
      });

      // Walidacja: nie pozwalaj na zmianę frequency na ONCE dla zadań cyklicznych
      // chyba że to jest celowa zmiana
      if (data.frequency === TaskFrequency.ONCE && existingTask.frequency !== TaskFrequency.ONCE) {
        console.warn('⚠️ OSTRZEŻENIE: Próba zmiany zadania cyklicznego na jednorazowe!');
      }

      // Zachowaj właściwości cykliczne jeśli nie zostały explicite podane
      if (data.frequency === undefined) {
        taskData.frequency = existingTask.frequency;
        console.log('✅ Zachowano frequency:', existingTask.frequency);
      }

      if (data.recurrenceRule === undefined) {
        taskData.recurrenceRule = existingTask.recurrenceRule || undefined;
        console.log('✅ Zachowano recurrenceRule:', existingTask.recurrenceRule);
      }
    }

    const task = await prisma.task.update({
      where: { id: taskId },
      data: {
        ...taskData,
        // Update assignmentType if provided
        ...(assignmentType && { assignmentType }),
        // Kompatybilność wsteczna - mapowanie nowych pól na stare
        ...(taskData.startDateTime && {
          dueDate: taskData.startDateTime,
          startDate: taskData.startDateTime,
        }),
        ...(taskData.endDateTime && { endDate: taskData.endDateTime }),
        ...(taskData.cycleEndDate && {
          endDate: taskData.cycleEndDate,
          recurrenceEnd: taskData.cycleEndDate,
        }),
        updatedAt: new Date(),
      },
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: {
          include: {
            completedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Handle TaskAssignment updates if assignedMemberIds is provided
    if (assignedMemberIds !== undefined) {
      try {
        // Delete existing assignments
        await prisma.taskAssignment.deleteMany({
          where: { taskId },
        });

        // Create new assignments if there are any
        if (assignedMemberIds.length > 0) {
          console.log(
            `Aktualizacja ${assignedMemberIds.length} przypisań dla zadania ${taskId}...`
          );

          const assignments = await prisma.taskAssignment.createMany({
            data: assignedMemberIds.map(memberId => ({
              taskId,
              memberId,
              assignedBy: task.assignedBy, // Use the original assignedBy from task
              assignedAt: new Date(),
            })),
          });

          console.log(`Zaktualizowano ${assignments.count} przypisań dla zadania ${taskId}`);
        } else {
          console.log(`Usunięto wszystkie przypisania dla zadania ${taskId}`);
        }
      } catch (assignmentError) {
        console.error('Błąd podczas aktualizacji przypisań zadania:', assignmentError);
        // Don't throw error - task update succeeded, assignment update failed
        console.warn('Zadanie zostało zaktualizowane, ale przypisania mogą być niepoprawne');
      }
    }

    return task;
  }

  static async deleteTask(taskId: string): Promise<void> {
    await prisma.task.delete({
      where: { id: taskId },
    });
  }

  static async getTask(taskId: string): Promise<any | null> {
    return await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: {
          include: {
            completedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
            verifiedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            completions: true,
          },
        },
      },
    });
  }

  static async getFamilyTasks(familyId: string, filters?: TaskFilters): Promise<any[]> {
    const where: any = {
      familyId,
    };

    if (filters) {
      if (filters.status) {
        where.status = { in: filters.status };
      }
      if (filters.assignedMemberIds && filters.assignedMemberIds.length > 0) {
        where.assignments = {
          some: {
            memberId: { in: filters.assignedMemberIds },
          },
        };
      }
      if (filters.dueDate) {
        where.dueDate = {};
        if (filters.dueDate.from) {
          where.dueDate.gte = filters.dueDate.from;
        }
        if (filters.dueDate.to) {
          where.dueDate.lte = filters.dueDate.to;
        }
      }
      if (filters.weight) {
        where.weight = { in: filters.weight };
      }
      if (filters.frequency) {
        where.frequency = { in: filters.frequency };
      }
    }

    return await prisma.task.findMany({
      where,
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: {
          include: {
            completedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            completions: true,
          },
        },
      },
      orderBy: [{ dueDate: 'asc' }, { createdAt: 'desc' }],
    });
  }

  // Assignment logic
  static async assignTask(taskId: string, memberId: string): Promise<any> {
    const task = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedAt: new Date(),
      },
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: true,
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return task;
  }

  static async unassignTask(taskId: string): Promise<any> {
    const task = await prisma.task.update({
      where: { id: taskId },
      data: {
        assignedAt: null,
      },
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: true,
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return task;
  }

  // Completion logic
  static async completeTask(
    taskId: string,
    memberId: string,
    notes?: string
  ): Promise<TaskCompletion> {
    const completion = await prisma.$transaction(async tx => {
      // Check if task exists and is active
      const task = await tx.task.findUnique({
        where: { id: taskId },
      });

      if (!task) {
        throw new Error('Task not found');
      }

      if (task.status !== TaskStatus.ACTIVE) {
        throw new Error('Task is not active');
      }

      // Check if already completed by this member
      const existingCompletion = await tx.taskCompletion.findFirst({
        where: {
          taskId,
          completedById: memberId,
          status: { in: [CompletionStatus.PENDING, CompletionStatus.APPROVED] },
        },
      });

      if (existingCompletion) {
        throw new Error('Task already completed by this member');
      }

      // Create completion
      const completion = await tx.taskCompletion.create({
        data: {
          taskId,
          completedById: memberId,
          notes,
          status: CompletionStatus.PENDING,
          pointsAwarded: 0, // Will be set when verified
        },
        include: {
          task: true,
          completedBy: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      return completion;
    });

    // Send email notification after successful completion
    try {
      await EmailService.sendTaskCompletion(taskId, memberId);
    } catch (emailError) {
      console.warn('Failed to send task completion email:', emailError);
      // Don't throw error - email notification failure shouldn't break task completion
    }

    return completion;
  }

  static async verifyCompletion(
    completionId: string,
    verifierId: string,
    approved: boolean,
    feedback?: string
  ): Promise<TaskCompletion> {
    const updatedCompletion = await prisma.$transaction(async tx => {
      const completion = await tx.taskCompletion.findUnique({
        where: { id: completionId },
        include: {
          task: true,
          completedBy: true,
        },
      });

      if (!completion) {
        throw new Error('Completion not found');
      }

      if (completion.status !== CompletionStatus.PENDING) {
        throw new Error('Completion already verified');
      }

      const pointsToAward = approved ? await this.calculatePoints(completion.task) : 0;

      // Update completion
      const updatedCompletion = await tx.taskCompletion.update({
        where: { id: completionId },
        data: {
          status: approved ? CompletionStatus.APPROVED : CompletionStatus.REJECTED,
          verifiedById: verifierId,
          verifiedAt: new Date(),
          pointsAwarded: pointsToAward,
          notes: feedback ? `${completion.notes || ''}\n\nFeedback: ${feedback}` : completion.notes,
        },
        include: {
          task: true,
          completedBy: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          verifiedBy: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      // Award points if approved
      if (approved && pointsToAward > 0) {
        await tx.familyMember.update({
          where: { id: completion.completedById },
          data: {
            points: {
              increment: pointsToAward,
            },
          },
        });
      }

      // Generate next recurring task if needed
      if (approved && completion.task.frequency !== TaskFrequency.ONCE) {
        await this.generateRecurringTask(completion.task);
      }

      return updatedCompletion;
    });

    // Send email notification after successful verification
    try {
      await EmailService.sendTaskVerified(completionId, approved, feedback);
    } catch (emailError) {
      console.warn('Failed to send task verification email:', emailError);
      // Don't throw error - email notification failure shouldn't break task verification
    }

    return updatedCompletion;
  }

  // Points calculation
  static async calculatePoints(task: Task): Promise<number> {
    const basePoints = TASK_WEIGHT_POINTS[task.weight];

    // Add bonus points for various factors
    let bonusPoints = 0;

    // Early completion bonus
    if (task.dueDate && new Date() < task.dueDate) {
      const daysEarly = Math.floor(
        (task.dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );
      if (daysEarly > 0) {
        bonusPoints += Math.min(daysEarly, 3); // Max 3 bonus points for early completion
      }
    }

    // Frequency bonus
    if (task.frequency === TaskFrequency.DAILY) {
      bonusPoints += 1;
    } else if (task.frequency === TaskFrequency.WEEKLY) {
      bonusPoints += 2;
    }

    return basePoints + bonusPoints;
  }

  // Recurring tasks
  static async generateRecurringTask(task: Task): Promise<Task | null> {
    if (task.frequency === TaskFrequency.ONCE) {
      return null;
    }

    const nextDueDate = this.calculateNextDueDate(task.dueDate, task.frequency);

    if (!nextDueDate) {
      return null;
    }

    const newTask = await prisma.task.create({
      data: {
        familyId: task.familyId,
        title: task.title,
        description: task.description,
        points: task.points,
        weight: task.weight,
        frequency: task.frequency,
        status: TaskStatus.ACTIVE,
        dueDate: nextDueDate,
        assignedBy: task.assignedBy,
        assignedAt: new Date(),
      },
    });

    return newTask;
  }

  private static calculateNextDueDate(
    currentDueDate: Date | null,
    frequency: TaskFrequency
  ): Date | null {
    if (!currentDueDate) return null;

    const nextDate = new Date(currentDueDate);

    switch (frequency) {
      case TaskFrequency.DAILY:
        nextDate.setDate(nextDate.getDate() + 1);
        break;
      case TaskFrequency.WEEKLY:
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case TaskFrequency.MONTHLY:
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      default:
        return null;
    }

    return nextDate;
  }

  static async processRecurringTasks(): Promise<void> {
    // Find completed recurring tasks that need new instances
    const completedRecurringTasks = await prisma.task.findMany({
      where: {
        frequency: { not: TaskFrequency.ONCE },
        status: TaskStatus.ACTIVE,
        completions: {
          some: {
            status: CompletionStatus.APPROVED,
            verifiedAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
            },
          },
        },
      },
    });

    for (const task of completedRecurringTasks) {
      await this.generateRecurringTask(task);
    }
  }

  // Utility methods
  static async getTaskStats(familyId: string) {
    const [totalTasks, activeTasks, completedTasks, overdueTasks] = await Promise.all([
      prisma.task.count({
        where: { familyId },
      }),

      prisma.task.count({
        where: {
          familyId,
          status: TaskStatus.ACTIVE,
        },
      }),

      prisma.taskCompletion.count({
        where: {
          task: { familyId },
          status: CompletionStatus.APPROVED,
        },
      }),

      prisma.task.count({
        where: {
          familyId,
          status: TaskStatus.ACTIVE,
          dueDate: {
            lt: new Date(),
          },
        },
      }),
    ]);

    return {
      totalTasks,
      activeTasks,
      completedTasks,
      overdueTasks,
    };
  }

  static async getMemberTasks(memberId: string, status?: TaskStatus[]) {
    const where: any = {
      assignments: {
        some: {
          memberId: memberId,
        },
      },
    };

    if (status) {
      where.status = { in: status };
    }

    return await prisma.task.findMany({
      where,
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        completions: {
          where: {
            completedById: memberId,
          },
          orderBy: {
            completedAt: 'desc',
          },
          take: 1,
        },
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: [{ dueDate: 'asc' }, { createdAt: 'desc' }],
    });
  }

  // Suspension integration methods
  static async isTaskSuspended(taskId: string): Promise<boolean> {
    const activeSuspensions = await SuspensionService.getActiveSuspensionsForTask(taskId);
    return activeSuspensions.length > 0;
  }

  static async isMemberSuspended(memberId: string): Promise<boolean> {
    const activeSuspensions = await SuspensionService.getActiveSuspensionsForMember(memberId);
    return activeSuspensions.length > 0;
  }

  static async getTasksWithSuspensions(familyId: string, filters?: TaskFilters): Promise<any[]> {
    const tasks = await this.getFamilyTasks(familyId, filters);

    // Enrich tasks with suspension information
    const tasksWithSuspensions = await Promise.all(
      tasks.map(async task => {
        const suspensions = await SuspensionService.getActiveSuspensionsForTask(task.id);
        const memberSuspensions =
          task.assignments.length > 0
            ? await Promise.all(
                task.assignments.map(assignment =>
                  SuspensionService.getActiveSuspensionsForMember(assignment.memberId)
                )
              ).then(results => results.flat())
            : [];

        return {
          ...task,
          suspensions: {
            taskSuspensions: suspensions,
            memberSuspensions: memberSuspensions,
            isSuspended: suspensions.length > 0 || memberSuspensions.length > 0,
          },
        };
      })
    );

    return tasksWithSuspensions;
  }

  static async adjustPointsForSuspension(
    taskId: string,
    suspensionPeriod: DateRange
  ): Promise<void> {
    // Find task completions during suspension period
    const suspendedCompletions = await prisma.taskCompletion.findMany({
      where: {
        taskId,
        completedAt: {
          gte: suspensionPeriod.start,
          lte: suspensionPeriod.end,
        },
        status: CompletionStatus.APPROVED,
        pointsAwarded: { gt: 0 },
      },
      include: {
        completedBy: true,
      },
    });

    if (suspendedCompletions.length === 0) {
      return;
    }

    await prisma.$transaction(async tx => {
      for (const completion of suspendedCompletions) {
        // Reverse the points awarded during suspension
        await tx.familyMember.update({
          where: { id: completion.completedById },
          data: {
            points: {
              decrement: completion.pointsAwarded,
            },
          },
        });

        // Mark completion as suspended (could add a new status or notes)
        await tx.taskCompletion.update({
          where: { id: completion.id },
          data: {
            notes: `${completion.notes || ''}\n\n[SUSPENDED] Points adjusted due to task suspension`,
            pointsAwarded: 0,
          },
        });
      }
    });
  }

  // Enhanced task completion that checks for suspensions
  static async completeTaskWithSuspensionCheck(
    taskId: string,
    memberId: string,
    notes?: string
  ): Promise<TaskCompletion> {
    // Check if task is suspended
    const isTaskSuspended = await this.isTaskSuspended(taskId);
    if (isTaskSuspended) {
      throw new Error('Cannot complete suspended task');
    }

    // Check if member is suspended
    const isMemberSuspended = await this.isMemberSuspended(memberId);
    if (isMemberSuspended) {
      throw new Error('Cannot complete task - member is suspended');
    }

    // If not suspended, proceed with normal completion
    return await this.completeTask(taskId, memberId, notes);
  }

  // Enhanced task assignment that checks for suspensions
  static async assignTaskWithSuspensionCheck(taskId: string, memberId: string): Promise<any> {
    // Check if member is suspended
    const isMemberSuspended = await this.isMemberSuspended(memberId);
    if (isMemberSuspended) {
      throw new Error('Cannot assign task to suspended member');
    }

    // Check if task is suspended
    const isTaskSuspended = await this.isTaskSuspended(taskId);
    if (isTaskSuspended) {
      throw new Error('Cannot assign suspended task');
    }

    // If not suspended, proceed with normal assignment
    return await this.assignTask(taskId, memberId);
  }

  // Get tasks excluding suspended ones
  static async getActiveTasks(familyId: string, filters?: TaskFilters): Promise<any[]> {
    const tasks = await this.getFamilyTasks(familyId, filters);

    // Filter out suspended tasks
    const activeTasks = [];
    for (const task of tasks) {
      const isTaskSuspended = await this.isTaskSuspended(task.id);
      const memberSuspensions = await Promise.all(
        task.assignments.map(assignment => this.isMemberSuspended(assignment.memberId))
      );
      const isMemberSuspended = memberSuspensions.some(suspended => suspended);

      if (!isTaskSuspended && !isMemberSuspended) {
        activeTasks.push(task);
      }
    }

    return activeTasks;
  }

  // Get only suspended tasks
  static async getSuspendedTasks(familyId: string): Promise<any[]> {
    const tasks = await this.getFamilyTasks(familyId);

    // Filter to only suspended tasks
    const suspendedTasks = [];
    for (const task of tasks) {
      const isTaskSuspended = await this.isTaskSuspended(task.id);
      const memberSuspensions = await Promise.all(
        task.assignments.map(assignment => this.isMemberSuspended(assignment.memberId))
      );
      const isMemberSuspended = memberSuspensions.some(suspended => suspended);

      if (isTaskSuspended || isMemberSuspended) {
        const taskSuspensions = await SuspensionService.getActiveSuspensionsForTask(task.id);
        const memberSuspensions =
          task.assignments.length > 0
            ? await Promise.all(
                task.assignments.map(assignment =>
                  SuspensionService.getActiveSuspensionsForMember(assignment.memberId)
                )
              ).then(results => results.flat())
            : [];

        suspendedTasks.push({
          ...task,
          suspensions: {
            taskSuspensions,
            memberSuspensions,
            reason: taskSuspensions[0]?.reason || memberSuspensions[0]?.reason,
          },
        });
      }
    }

    return suspendedTasks;
  }

  // Enhanced task stats that account for suspensions
  static async getTaskStatsWithSuspensions(familyId: string) {
    const basicStats = await this.getTaskStats(familyId);

    const [suspendedTasks, suspendedMembers] = await Promise.all([
      // Count tasks that are directly suspended
      prisma.task.count({
        where: {
          familyId,
          status: TaskStatus.ACTIVE,
          suspensions: {
            some: {
              suspension: {
                status: 'ACTIVE',
                startDate: { lte: new Date() },
                endDate: { gte: new Date() },
              },
            },
          },
        },
      }),

      // Count tasks assigned to suspended members
      prisma.task.count({
        where: {
          familyId,
          status: TaskStatus.ACTIVE,
          assignments: {
            some: {
              member: {
                memberSuspensions: {
                  some: {
                    suspension: {
                      status: 'ACTIVE',
                      startDate: { lte: new Date() },
                      endDate: { gte: new Date() },
                    },
                  },
                },
              },
            },
          },
        },
      }),
    ]);

    return {
      ...basicStats,
      suspendedTasks: suspendedTasks + suspendedMembers,
      activeTasks: basicStats.activeTasks - suspendedTasks - suspendedMembers,
    };
  }

  // Semantic datetime helper functions

  /**
   * Konwertuje przestarzałe pola czasowe na nowe semantyczne pola
   */
  static convertLegacyToSemanticDates(task: any): {
    startDateTime?: Date;
    endDateTime?: Date;
    cycleEndDate?: Date;
  } {
    const result: any = {};

    // Priorytet: nowe pola > stare pola
    if (task.startDateTime) {
      result.startDateTime = task.startDateTime;
    } else if (task.dueDate) {
      result.startDateTime = task.dueDate;
    } else if (task.startDate) {
      result.startDateTime = task.startDate;
    }

    if (task.endDateTime) {
      result.endDateTime = task.endDateTime;
    } else if (task.frequency === 'ONCE' && task.endDate) {
      result.endDateTime = task.endDate;
    }

    if (task.cycleEndDate) {
      result.cycleEndDate = task.cycleEndDate;
    } else if (task.frequency !== 'ONCE' && task.recurrenceEnd) {
      result.cycleEndDate = task.recurrenceEnd;
    } else if (task.frequency !== 'ONCE' && task.endDate) {
      result.cycleEndDate = task.endDate;
    }

    return result;
  }

  /**
   * Sprawdza czy zadanie jest aktywne w danym czasie
   */
  static isTaskActiveAtTime(task: any, checkTime: Date = new Date()): boolean {
    const semantic = this.convertLegacyToSemanticDates(task);

    // Zadanie musi się już rozpocząć
    if (semantic.startDateTime && checkTime < semantic.startDateTime) {
      return false;
    }

    // Dla zadań jednodniowych - sprawdź endDateTime
    if (task.frequency === 'ONCE' && semantic.endDateTime) {
      return checkTime <= semantic.endDateTime;
    }

    // Dla zadań cyklicznych - sprawdź cycleEndDate
    if (task.frequency !== 'ONCE' && semantic.cycleEndDate) {
      return checkTime <= semantic.cycleEndDate;
    }

    return true;
  }

  /**
   * Pobiera semantyczne daty dla zadania
   */
  static getTaskSemanticDates(task: any): {
    start?: Date;
    end?: Date;
    cycleEnd?: Date;
    isRecurring: boolean;
  } {
    const semantic = this.convertLegacyToSemanticDates(task);

    return {
      start: semantic.startDateTime,
      end: semantic.endDateTime,
      cycleEnd: semantic.cycleEndDate,
      isRecurring: task.frequency !== 'ONCE',
    };
  }

  /**
   * Waliduje semantyczną spójność dat w zadaniu
   */
  static validateTaskSemanticDates(task: any): { isValid: boolean; errors: string[] } {
    const semantic = this.convertLegacyToSemanticDates(task);
    const errors: string[] = [];

    // Podstawowa walidacja czasów
    if (semantic.startDateTime && semantic.endDateTime) {
      if (semantic.endDateTime <= semantic.startDateTime) {
        errors.push('Data/czas końca musi być późniejsza niż data/czas rozpoczęcia');
      }
    }

    // Walidacja dla zadań cyklicznych
    if (task.frequency !== 'ONCE') {
      if (semantic.startDateTime && semantic.cycleEndDate) {
        if (semantic.cycleEndDate < semantic.startDateTime) {
          errors.push('Data końca cyklu musi być późniejsza lub równa dacie rozpoczęcia');
        }
      }
    }

    // Walidacja dla zadań jednodniowych
    if (task.frequency === 'ONCE' && semantic.cycleEndDate) {
      errors.push('Zadania jednorazowe nie powinny mieć daty końca cyklu');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Get all tasks for a user across all their families
  static async getUserTasks(userId: string, filters?: TaskFilters): Promise<any[]> {
    // First, get all families where the user is a member
    const familyMembers = await prisma.familyMember.findMany({
      where: {
        userId: userId,
      },
      select: {
        familyId: true,
        family: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (familyMembers.length === 0) {
      return [];
    }

    const familyIds = familyMembers.map(member => member.familyId);

    // Build the where clause for tasks
    const where: any = {
      familyId: { in: familyIds },
    };

    // Apply filters similar to getFamilyTasks but adapted for user context
    if (filters) {
      if (filters.status) {
        where.status = { in: filters.status };
      }
      if (filters.assignedMemberIds && filters.assignedMemberIds.length > 0) {
        where.assignments = {
          some: {
            memberId: { in: filters.assignedMemberIds },
          },
        };
      }
      if (filters.dueDate) {
        where.dueDate = {};
        if (filters.dueDate.from) {
          where.dueDate.gte = filters.dueDate.from;
        }
        if (filters.dueDate.to) {
          where.dueDate.lte = filters.dueDate.to;
        }
      }
      if (filters.weight) {
        where.weight = { in: filters.weight };
      }
      if (filters.frequency) {
        where.frequency = { in: filters.frequency };
      }
      if (filters.search) {
        where.OR = [
          { title: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
        ];
      }
      if (filters.pointsRange) {
        where.points = {};
        if (filters.pointsRange.min !== undefined) {
          where.points.gte = filters.pointsRange.min;
        }
        if (filters.pointsRange.max !== undefined) {
          where.points.lte = filters.pointsRange.max;
        }
      }

      // Note: completionStatus and includeCompleted filters are applied after fetching
      // due to complex logic requirements that can't be efficiently expressed in Prisma where clause
    }

    const tasks = await prisma.task.findMany({
      where,
      include: {
        assignedByUser: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        completions: {
          include: {
            completedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
            verifiedBy: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
          orderBy: { completedAt: 'desc' },
        },
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    id: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
        family: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            completions: true,
          },
        },
      },
      orderBy: [{ dueDate: 'asc' }, { createdAt: 'desc' }],
      distinct: ['id'], // Zabezpieczenie przed duplikatami na poziomie SQL
    });

    // Dodatkowe zabezpieczenie - deduplikacja po ID
    let uniqueTasks = tasks.reduce(
      (acc, task) => {
        if (!acc.find(t => t.id === task.id)) {
          acc.push(task);
        }
        return acc;
      },
      [] as typeof tasks
    );

    // Apply complex filters that couldn't be handled at the database level
    if (filters) {
      uniqueTasks = this.applyPostFetchFilters(uniqueTasks, filters);
    }

    return uniqueTasks;
  }

  /**
   * Apply filters that require complex logic and cannot be efficiently handled at the database level
   */
  private static applyPostFetchFilters(tasks: any[], filters: TaskFilters): any[] {
    const now = new Date();

    return tasks.filter(task => {
      // completionStatus filter
      if (filters.completionStatus && filters.completionStatus.length > 0) {
        // Sort completions by date (newest first) and get the latest for each status
        const sortedCompletions = [...task.completions].sort(
          (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
        );

        const approvedCompletion = sortedCompletions.find(
          c => c.status === CompletionStatus.APPROVED
        );
        const pendingCompletion = sortedCompletions.find(
          c => c.status === CompletionStatus.PENDING
        );
        const rejectedCompletion = sortedCompletions.find(
          c => c.status === CompletionStatus.REJECTED
        );

        const isOverdue =
          task.status === TaskStatus.ACTIVE &&
          task.dueDate &&
          new Date(task.dueDate) < now &&
          !approvedCompletion;

        const isDueSoon =
          task.status === TaskStatus.ACTIVE &&
          task.dueDate &&
          !approvedCompletion &&
          (() => {
            const hoursUntilDue =
              (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60);
            return hoursUntilDue <= 24 && hoursUntilDue > 0;
          })();

        const hasMatchingStatus = filters.completionStatus.some(status => {
          switch (status) {
            case 'approved':
              return !!approvedCompletion;
            case 'pending':
              return !!pendingCompletion;
            case 'rejected':
              return !!rejectedCompletion;
            case 'overdue':
              return isOverdue;
            case 'due_soon':
              return isDueSoon;
            default:
              return false;
          }
        });

        if (!hasMatchingStatus) {
          return false;
        }
      }

      // includeCompleted filter
      if (!filters.includeCompleted) {
        // Check if task has any approved completion (latest first)
        const sortedCompletions = [...task.completions].sort(
          (a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
        );
        const hasApprovedCompletion = sortedCompletions.some(
          c => c.status === CompletionStatus.APPROVED
        );
        if (hasApprovedCompletion) {
          return false;
        }
      }

      return true;
    });
  }
}
