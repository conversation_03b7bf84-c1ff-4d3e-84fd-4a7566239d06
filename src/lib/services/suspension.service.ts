import { prisma } from '@/lib/prisma';
import {
  type TaskSuspension,
  type SuspendedTask,
  type SuspendedMember,
  type SuspensionStatus,
  type SuspensionReason,
} from '@prisma/client';
import {
  type CreateSuspensionData,
  type UpdateSuspensionData,
  type SuspensionFilters,
  validateSuspensionDates,
} from '@/lib/validations/suspension';

export interface DateRange {
  start: Date;
  end: Date;
}

export interface SuspensionWithDetails extends TaskSuspension {
  createdBy: {
    id: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  };
  suspendedTasks: (SuspendedTask & {
    task: {
      id: string;
      title: string;
      description?: string | null;
      points: number;
    };
  })[];
  suspendedMembers: (SuspendedMember & {
    member: {
      id: string;
      user: {
        firstName: string | null;
        lastName: string | null;
      };
    };
  })[];
}

export class SuspensionService {
  // CRUD operations
  static async createSuspension(
    familyId: string,
    createdById: string,
    data: CreateSuspensionData
  ): Promise<SuspensionWithDetails> {
    // Validate dates
    validateSuspensionDates(data.startDate, data.endDate);

    // Check for overlapping suspensions for the same tasks/members
    await this.validateNoOverlappingSuspensions(
      familyId,
      data.taskIds,
      data.memberIds,
      data.startDate,
      data.endDate
    );

    // Create suspension in transaction
    const suspension = await prisma.$transaction(async tx => {
      // Create the suspension
      const newSuspension = await tx.taskSuspension.create({
        data: {
          familyId,
          title: data.title,
          description: data.description,
          reason: data.reason,
          startDate: data.startDate,
          endDate: data.endDate,
          createdById,
        },
      });

      // Add suspended tasks
      if (data.taskIds.length > 0) {
        await tx.suspendedTask.createMany({
          data: data.taskIds.map(taskId => ({
            suspensionId: newSuspension.id,
            taskId,
          })),
        });
      }

      // Add suspended members
      if (data.memberIds.length > 0) {
        await tx.suspendedMember.createMany({
          data: data.memberIds.map(memberId => ({
            suspensionId: newSuspension.id,
            memberId,
          })),
        });
      }

      return newSuspension;
    });

    // Return full suspension with details
    return (await this.getSuspensionById(suspension.id)) as SuspensionWithDetails;
  }

  static async updateSuspension(
    suspensionId: string,
    data: UpdateSuspensionData
  ): Promise<SuspensionWithDetails> {
    const existingSuspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
    });

    if (!existingSuspension) {
      throw new Error('Suspension not found');
    }

    // Validate dates if provided
    if (data.startDate || data.endDate) {
      const startDate = data.startDate || existingSuspension.startDate;
      const endDate = data.endDate || existingSuspension.endDate;
      validateSuspensionDates(startDate, endDate);
    }

    const updatedSuspension = await prisma.taskSuspension.update({
      where: { id: suspensionId },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });

    return (await this.getSuspensionById(suspensionId)) as SuspensionWithDetails;
  }

  static async deleteSuspension(suspensionId: string): Promise<void> {
    await prisma.taskSuspension.delete({
      where: { id: suspensionId },
    });
  }

  static async getSuspensionById(suspensionId: string): Promise<SuspensionWithDetails | null> {
    return await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  }

  static async getFamilySuspensions(
    familyId: string,
    filters: SuspensionFilters = { includeExpired: false }
  ): Promise<SuspensionWithDetails[]> {
    const where: any = { familyId };

    // Apply status filter
    if (filters.status && filters.status.length > 0) {
      where.status = { in: filters.status };
    }

    // Apply reason filter
    if (filters.reason && filters.reason.length > 0) {
      where.reason = { in: filters.reason };
    }

    // Apply date filters
    if (filters.startDate) {
      where.startDate = { gte: filters.startDate };
    }
    if (filters.endDate) {
      where.endDate = { lte: filters.endDate };
    }

    // Apply search filter
    if (filters.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } },
      ];
    }

    // Exclude expired suspensions unless requested
    if (!filters.includeExpired) {
      where.endDate = { ...where.endDate, gte: new Date() };
    }

    return await prisma.taskSuspension.findMany({
      where,
      include: {
        createdBy: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        suspendedTasks: {
          include: {
            task: {
              select: {
                id: true,
                title: true,
                description: true,
                points: true,
              },
            },
          },
        },
        suspendedMembers: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  // Suspension management
  static async addTasksToSuspension(suspensionId: string, taskIds: string[]): Promise<void> {
    if (taskIds.length === 0) return;

    await prisma.suspendedTask.createMany({
      data: taskIds.map(taskId => ({
        suspensionId,
        taskId,
      })),
    });
  }

  static async removeTasksFromSuspension(suspensionId: string, taskIds: string[]): Promise<void> {
    if (taskIds.length === 0) return;

    await prisma.suspendedTask.deleteMany({
      where: {
        suspensionId,
        taskId: { in: taskIds },
      },
    });
  }

  static async addMembersToSuspension(suspensionId: string, memberIds: string[]): Promise<void> {
    if (memberIds.length === 0) return;

    await prisma.suspendedMember.createMany({
      data: memberIds.map(memberId => ({
        suspensionId,
        memberId,
      })),
    });
  }

  static async removeMembersFromSuspension(
    suspensionId: string,
    memberIds: string[]
  ): Promise<void> {
    if (memberIds.length === 0) return;

    await prisma.suspendedMember.deleteMany({
      where: {
        suspensionId,
        memberId: { in: memberIds },
      },
    });
  }

  // Activation logic
  static async activateSuspension(suspensionId: string): Promise<SuspensionWithDetails> {
    const suspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
    });

    if (!suspension) {
      throw new Error('Suspension not found');
    }

    if (suspension.status === 'ACTIVE') {
      throw new Error('Suspension is already active');
    }

    if (suspension.status === 'ENDED') {
      throw new Error('Cannot activate ended suspension');
    }

    const now = new Date();
    if (suspension.endDate < now) {
      throw new Error('Cannot activate suspension that has already ended');
    }

    const updatedSuspension = await prisma.taskSuspension.update({
      where: { id: suspensionId },
      data: {
        status: 'ACTIVE',
        updatedAt: new Date(),
      },
    });

    return (await this.getSuspensionById(suspensionId)) as SuspensionWithDetails;
  }

  static async deactivateSuspension(suspensionId: string): Promise<SuspensionWithDetails> {
    const suspension = await prisma.taskSuspension.findUnique({
      where: { id: suspensionId },
    });

    if (!suspension) {
      throw new Error('Suspension not found');
    }

    if (suspension.status !== 'ACTIVE') {
      throw new Error('Only active suspensions can be deactivated');
    }

    const updatedSuspension = await prisma.taskSuspension.update({
      where: { id: suspensionId },
      data: {
        status: 'CANCELLED',
        updatedAt: new Date(),
      },
    });

    return (await this.getSuspensionById(suspensionId)) as SuspensionWithDetails;
  }

  // Automated management
  static async processExpiredSuspensions(): Promise<void> {
    const now = new Date();

    // End active suspensions that have expired
    await prisma.taskSuspension.updateMany({
      where: {
        status: 'ACTIVE',
        endDate: { lt: now },
      },
      data: {
        status: 'ENDED',
        updatedAt: now,
      },
    });

    // Activate suspensions that should start now
    await prisma.taskSuspension.updateMany({
      where: {
        status: 'ACTIVE',
        startDate: { lte: now },
        endDate: { gt: now },
      },
      data: {
        updatedAt: now,
      },
    });
  }

  static async getActiveSuspensionsForTask(taskId: string): Promise<TaskSuspension[]> {
    const now = new Date();

    return await prisma.taskSuspension.findMany({
      where: {
        status: 'ACTIVE',
        startDate: { lte: now },
        endDate: { gte: now },
        suspendedTasks: {
          some: { taskId },
        },
      },
    });
  }

  static async getActiveSuspensionsForMember(memberId: string): Promise<TaskSuspension[]> {
    const now = new Date();

    return await prisma.taskSuspension.findMany({
      where: {
        status: 'ACTIVE',
        startDate: { lte: now },
        endDate: { gte: now },
        suspendedMembers: {
          some: { memberId },
        },
      },
    });
  }

  // Helper methods
  private static async validateNoOverlappingSuspensions(
    familyId: string,
    taskIds: string[],
    memberIds: string[],
    startDate: Date,
    endDate: Date,
    excludeSuspensionId?: string
  ): Promise<void> {
    const where: any = {
      familyId,
      status: 'ACTIVE',
      OR: [
        {
          startDate: { lte: endDate },
          endDate: { gte: startDate },
        },
      ],
    };

    if (excludeSuspensionId) {
      where.id = { not: excludeSuspensionId };
    }

    // Check for overlapping task suspensions
    if (taskIds.length > 0) {
      const overlappingTaskSuspensions = await prisma.taskSuspension.findMany({
        where: {
          ...where,
          suspendedTasks: {
            some: {
              taskId: { in: taskIds },
            },
          },
        },
        include: {
          suspendedTasks: {
            where: {
              taskId: { in: taskIds },
            },
            include: {
              task: {
                select: { title: true },
              },
            },
          },
        },
      });

      if (overlappingTaskSuspensions.length > 0) {
        const conflictingTasks = overlappingTaskSuspensions
          .flatMap(s => s.suspendedTasks)
          .map(st => st.task.title)
          .join(', ');

        throw new Error(
          `Następujące zadania mają już aktywne zawieszenia w tym okresie: ${conflictingTasks}`
        );
      }
    }

    // Check for overlapping member suspensions
    if (memberIds.length > 0) {
      const overlappingMemberSuspensions = await prisma.taskSuspension.findMany({
        where: {
          ...where,
          suspendedMembers: {
            some: {
              memberId: { in: memberIds },
            },
          },
        },
        include: {
          suspendedMembers: {
            where: {
              memberId: { in: memberIds },
            },
            include: {
              member: {
                include: {
                  user: {
                    select: { firstName: true, lastName: true },
                  },
                },
              },
            },
          },
        },
      });

      if (overlappingMemberSuspensions.length > 0) {
        const conflictingMembers = overlappingMemberSuspensions
          .flatMap(s => s.suspendedMembers)
          .map(sm => `${sm.member.user.firstName} ${sm.member.user.lastName}`)
          .join(', ');

        throw new Error(
          `Następujący członkowie mają już aktywne zawieszenia w tym okresie: ${conflictingMembers}`
        );
      }
    }
  }

  // Statistics and reporting
  static async getSuspensionStats(familyId: string) {
    const [totalSuspensions, activeSuspensions, endedSuspensions, cancelledSuspensions] =
      await Promise.all([
        prisma.taskSuspension.count({
          where: { familyId },
        }),

        prisma.taskSuspension.count({
          where: {
            familyId,
            status: 'ACTIVE',
          },
        }),

        prisma.taskSuspension.count({
          where: {
            familyId,
            status: 'ENDED',
          },
        }),

        prisma.taskSuspension.count({
          where: {
            familyId,
            status: 'CANCELLED',
          },
        }),
      ]);

    return {
      totalSuspensions,
      activeSuspensions,
      endedSuspensions,
      cancelledSuspensions,
    };
  }

  static async getUpcomingSuspensions(
    familyId: string,
    days: number = 7
  ): Promise<TaskSuspension[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    return await prisma.taskSuspension.findMany({
      where: {
        familyId,
        status: 'ACTIVE',
        endDate: {
          gte: now,
          lte: futureDate,
        },
      },
      orderBy: {
        endDate: 'asc',
      },
    });
  }
}
