import { prisma } from '@/lib/prisma';
import { StatsService, DateRange } from './stats.service';

export interface WeeklyReport {
  familyId: string;
  weekStart: Date;
  weekEnd: Date;
  summary: {
    tasksCreated: number;
    tasksCompleted: number;
    completionRate: number;
    totalPoints: number;
    activeMembersCount: number;
  };
  memberPerformance: Array<{
    memberId: string;
    memberName: string;
    tasksCompleted: number;
    pointsEarned: number;
    completionRate: number;
  }>;
  dailyBreakdown: Array<{
    date: Date;
    tasksCompleted: number;
    pointsAwarded: number;
  }>;
  topAchievements: Array<{
    memberId: string;
    memberName: string;
    achievement: string;
    value: number;
  }>;
}

export interface MonthlyReport {
  familyId: string;
  month: Date;
  summary: {
    tasksCreated: number;
    tasksCompleted: number;
    completionRate: number;
    totalPoints: number;
    activeMembersCount: number;
    averageTasksPerDay: number;
    streakRecord: number;
  };
  memberPerformance: Array<{
    memberId: string;
    memberName: string;
    tasksCompleted: number;
    pointsEarned: number;
    completionRate: number;
    bestStreak: number;
    rank: number;
  }>;
  weeklyBreakdown: Array<{
    weekStart: Date;
    tasksCompleted: number;
    completionRate: number;
    pointsAwarded: number;
  }>;
  trends: {
    tasksCreated: 'up' | 'down' | 'stable';
    completionRate: 'up' | 'down' | 'stable';
    memberActivity: 'up' | 'down' | 'stable';
  };
  insights: string[];
}

export class ReportsService {
  static async generateWeeklyReport(familyId: string, weekStart: Date): Promise<WeeklyReport> {
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    const dateRange: DateRange = {
      start: weekStart,
      end: weekEnd,
    };

    // Get family stats for the week
    const familyStats = await StatsService.getFamilyStats(familyId, dateRange, 'DAILY');

    // Calculate summary
    const summary = {
      tasksCreated: familyStats.reduce((sum, stat) => sum + stat.tasksCreated, 0),
      tasksCompleted: familyStats.reduce((sum, stat) => sum + stat.tasksCompleted, 0),
      completionRate:
        familyStats.length > 0
          ? familyStats.reduce((sum, stat) => sum + stat.completionRate, 0) / familyStats.length
          : 0,
      totalPoints: familyStats.reduce((sum, stat) => sum + stat.totalPointsAwarded, 0),
      activeMembersCount: Math.max(...familyStats.map(stat => stat.activeMembersCount), 0),
    };

    // Get member performance
    const members = await prisma.familyMember.findMany({
      where: {
        familyId,
      },
      include: {
        user: true,
      },
    });

    const memberPerformance = await Promise.all(
      members.map(async member => {
        const memberStats = await StatsService.getMemberStats(member.id, dateRange, 'DAILY');

        return {
          memberId: member.id,
          memberName: `${member.user.firstName} ${member.user.lastName}`,
          tasksCompleted: memberStats.reduce((sum, stat) => sum + stat.tasksCompleted, 0),
          pointsEarned: memberStats.reduce((sum, stat) => sum + stat.pointsEarned, 0),
          completionRate:
            memberStats.length > 0
              ? memberStats.reduce((sum, stat) => sum + stat.completionRate, 0) / memberStats.length
              : 0,
        };
      })
    );

    // Get daily breakdown
    const dailyBreakdown = familyStats.map(stat => ({
      date: stat.date,
      tasksCompleted: stat.tasksCompleted,
      pointsAwarded: stat.totalPointsAwarded,
    }));

    // Generate top achievements
    const topAchievements = this.generateTopAchievements(memberPerformance);

    return {
      familyId,
      weekStart,
      weekEnd,
      summary,
      memberPerformance,
      dailyBreakdown,
      topAchievements,
    };
  }

  static async generateMonthlyReport(familyId: string, month: Date): Promise<MonthlyReport> {
    const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
    const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    monthEnd.setHours(23, 59, 59, 999);

    const dateRange: DateRange = {
      start: monthStart,
      end: monthEnd,
    };

    // Get monthly stats
    const monthlyStats = await StatsService.getFamilyStats(familyId, dateRange, 'MONTHLY');

    const monthStat = monthlyStats[0];
    const daysInMonth = monthEnd.getDate();

    const summary = {
      tasksCreated: monthStat?.tasksCreated || 0,
      tasksCompleted: monthStat?.tasksCompleted || 0,
      completionRate: monthStat?.completionRate || 0,
      totalPoints: monthStat?.totalPointsAwarded || 0,
      activeMembersCount: monthStat?.activeMembersCount || 0,
      averageTasksPerDay: monthStat ? monthStat.tasksCompleted / daysInMonth : 0,
      streakRecord: 0, // Would need to calculate from daily data
    };

    // Get member performance with rankings
    const members = await prisma.familyMember.findMany({
      where: {
        familyId,
      },
      include: {
        user: true,
      },
    });

    const memberPerformance = await Promise.all(
      members.map(async member => {
        const memberStats = await StatsService.getMemberStats(member.id, dateRange, 'MONTHLY');

        const memberStat = memberStats[0];

        return {
          memberId: member.id,
          memberName: `${member.user.firstName} ${member.user.lastName}`,
          tasksCompleted: memberStat?.tasksCompleted || 0,
          pointsEarned: memberStat?.pointsEarned || 0,
          completionRate: memberStat?.completionRate || 0,
          bestStreak: memberStat?.streakDays || 0,
          rank: 0, // Will be calculated below
        };
      })
    );

    // Assign ranks based on points earned
    memberPerformance.sort((a, b) => b.pointsEarned - a.pointsEarned);
    memberPerformance.forEach((member, index) => {
      member.rank = index + 1;
    });

    // Get weekly breakdown
    const weeklyBreakdown = await this.getWeeklyBreakdown(familyId, dateRange);

    // Generate trends (simplified)
    const trends = {
      tasksCreated: 'stable' as const,
      completionRate: 'up' as const,
      memberActivity: 'stable' as const,
    };

    // Generate insights
    const insights = this.generateMonthlyInsights(summary, memberPerformance);

    return {
      familyId,
      month,
      summary,
      memberPerformance,
      weeklyBreakdown,
      trends,
      insights,
    };
  }

  static async exportStatsToCSV(familyId: string, dateRange: DateRange): Promise<Buffer> {
    const stats = await StatsService.getFamilyStats(familyId, dateRange, 'DAILY');

    const csvHeaders = [
      'Date',
      'Period',
      'Tasks Created',
      'Tasks Completed',
      'Tasks Verified',
      'Completion Rate',
      'Total Points Awarded',
      'Average Points Per Task',
      'Active Members Count',
    ];

    const csvRows = stats.map(stat => [
      stat.date.toISOString().split('T')[0],
      stat.period,
      stat.tasksCreated.toString(),
      stat.tasksCompleted.toString(),
      stat.tasksVerified.toString(),
      stat.completionRate.toFixed(2),
      stat.totalPointsAwarded.toString(),
      stat.averagePointsPerTask.toFixed(2),
      stat.activeMembersCount.toString(),
    ]);

    const csvContent = [csvHeaders, ...csvRows].map(row => row.join(',')).join('\n');

    return Buffer.from(csvContent, 'utf-8');
  }

  static async exportActivityToCSV(familyId: string, dateRange: DateRange): Promise<Buffer> {
    const activityLogs = await prisma.activityLog.findMany({
      where: {
        familyId,
        timestamp: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      include: {
        user: true,
        member: {
          include: {
            user: true,
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    });

    const csvHeaders = [
      'Timestamp',
      'Action',
      'Entity Type',
      'Entity ID',
      'User Email',
      'Member Name',
      'Metadata',
    ];

    const csvRows = activityLogs.map(log => [
      log.timestamp.toISOString(),
      log.action,
      log.entityType,
      log.entityId || '',
      log.user?.email || '',
      log.member ? `${log.member.user.firstName} ${log.member.user.lastName}` : '',
      log.metadata ? JSON.stringify(log.metadata) : '',
    ]);

    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');

    return Buffer.from(csvContent, 'utf-8');
  }

  // Helper methods
  private static generateTopAchievements(
    memberPerformance: Array<{
      memberId: string;
      memberName: string;
      tasksCompleted: number;
      pointsEarned: number;
      completionRate: number;
    }>
  ): Array<{
    memberId: string;
    memberName: string;
    achievement: string;
    value: number;
  }> {
    const achievements: Array<{
      memberId: string;
      memberName: string;
      achievement: string;
      value: number;
    }> = [];

    // Most tasks completed
    const topTaskCompleter = memberPerformance.reduce((prev, current) =>
      current.tasksCompleted > prev.tasksCompleted ? current : prev
    );

    if (topTaskCompleter.tasksCompleted > 0) {
      achievements.push({
        memberId: topTaskCompleter.memberId,
        memberName: topTaskCompleter.memberName,
        achievement: 'Najwięcej wykonanych zadań',
        value: topTaskCompleter.tasksCompleted,
      });
    }

    // Most points earned
    const topPointsEarner = memberPerformance.reduce((prev, current) =>
      current.pointsEarned > prev.pointsEarned ? current : prev
    );

    if (topPointsEarner.pointsEarned > 0) {
      achievements.push({
        memberId: topPointsEarner.memberId,
        memberName: topPointsEarner.memberName,
        achievement: 'Najwięcej zdobytych punktów',
        value: topPointsEarner.pointsEarned,
      });
    }

    // Best completion rate
    const bestCompletionRate = memberPerformance.reduce((prev, current) =>
      current.completionRate > prev.completionRate ? current : prev
    );

    if (bestCompletionRate.completionRate > 0) {
      achievements.push({
        memberId: bestCompletionRate.memberId,
        memberName: bestCompletionRate.memberName,
        achievement: 'Najwyższy wskaźnik ukończenia',
        value: Math.round(bestCompletionRate.completionRate),
      });
    }

    return achievements;
  }

  private static async getWeeklyBreakdown(
    familyId: string,
    dateRange: DateRange
  ): Promise<
    Array<{
      weekStart: Date;
      tasksCompleted: number;
      completionRate: number;
      pointsAwarded: number;
    }>
  > {
    const weeks: Array<{
      weekStart: Date;
      tasksCompleted: number;
      completionRate: number;
      pointsAwarded: number;
    }> = [];

    const currentDate = new Date(dateRange.start);

    while (currentDate <= dateRange.end) {
      const weekStart = new Date(currentDate);
      const weekEnd = new Date(currentDate);
      weekEnd.setDate(weekEnd.getDate() + 6);

      if (weekEnd > dateRange.end) {
        weekEnd.setTime(dateRange.end.getTime());
      }

      const weekStats = await StatsService.getFamilyStats(
        familyId,
        { start: weekStart, end: weekEnd },
        'DAILY'
      );

      const tasksCompleted = weekStats.reduce((sum, stat) => sum + stat.tasksCompleted, 0);
      const completionRate =
        weekStats.length > 0
          ? weekStats.reduce((sum, stat) => sum + stat.completionRate, 0) / weekStats.length
          : 0;
      const pointsAwarded = weekStats.reduce((sum, stat) => sum + stat.totalPointsAwarded, 0);

      weeks.push({
        weekStart,
        tasksCompleted,
        completionRate,
        pointsAwarded,
      });

      currentDate.setDate(currentDate.getDate() + 7);
    }

    return weeks;
  }

  private static generateMonthlyInsights(
    summary: MonthlyReport['summary'],
    memberPerformance: MonthlyReport['memberPerformance']
  ): string[] {
    const insights: string[] = [];

    // Completion rate insight
    if (summary.completionRate > 80) {
      insights.push('Doskonała wydajność! Wskaźnik ukończenia zadań przekracza 80%.');
    } else if (summary.completionRate > 60) {
      insights.push('Dobra wydajność rodziny. Rozważ niewielkie ulepszenia w organizacji zadań.');
    } else {
      insights.push('Wskaźnik ukończenia zadań wymaga poprawy. Rozważ przegląd systemu zadań.');
    }

    // Member activity insight
    const activeMembersRatio =
      memberPerformance.filter(m => m.tasksCompleted > 0).length / memberPerformance.length;
    if (activeMembersRatio === 1) {
      insights.push('Wszyscy członkowie rodziny są aktywni w wykonywaniu zadań!');
    } else if (activeMembersRatio > 0.7) {
      insights.push('Większość członków rodziny aktywnie uczestniczy w zadaniach.');
    } else {
      insights.push('Rozważ zachęcenie mniej aktywnych członków do większego zaangażowania.');
    }

    // Points distribution insight
    const totalPoints = memberPerformance.reduce((sum, m) => sum + m.pointsEarned, 0);
    const avgPoints = totalPoints / memberPerformance.length;
    const topPerformer = memberPerformance[0];

    if (topPerformer && topPerformer.pointsEarned > avgPoints * 2) {
      insights.push(
        `${topPerformer.memberName} zdominował(-a) miesiąc z ${topPerformer.pointsEarned} punktami!`
      );
    }

    // Task consistency insight
    if (summary.averageTasksPerDay > 3) {
      insights.push('Rodzina utrzymuje wysoką aktywność z więcej niż 3 zadaniami dziennie.');
    } else if (summary.averageTasksPerDay < 1) {
      insights.push('Rozważ zwiększenie liczby codziennych zadań dla lepszej rutyny.');
    }

    return insights;
  }
}
