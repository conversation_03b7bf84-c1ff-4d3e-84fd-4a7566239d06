import { prisma } from '@/lib/prisma';
import { DateRange } from './stats.service';

export class ArchivalService {
  /**
   * Archive old activity logs by moving them to a separate table or deleting them
   */
  static async archiveOldActivityLogs(olderThan: Date): Promise<number> {
    try {
      // First, get count of logs to be archived
      const countToArchive = await prisma.activityLog.count({
        where: {
          timestamp: {
            lt: olderThan,
          },
        },
      });

      // In a production environment, you might want to:
      // 1. Export to cold storage (S3, etc.)
      // 2. Move to an archive table
      // 3. Create a backup before deletion

      // For now, we'll just delete old logs
      const result = await prisma.activityLog.deleteMany({
        where: {
          timestamp: {
            lt: olderThan,
          },
        },
      });

      console.log(`Archived ${result.count} activity logs older than ${olderThan.toISOString()}`);

      return result.count;
    } catch (error) {
      console.error('Error archiving activity logs:', error);
      throw new Error('Failed to archive activity logs');
    }
  }

  /**
   * Clean up expired stats records
   */
  static async cleanupExpiredStats(olderThan: Date): Promise<number> {
    try {
      let totalDeleted = 0;

      // Delete old daily stats (keep only recent daily data)
      const dailyStatsResult = await prisma.familyStats.deleteMany({
        where: {
          period: 'DAILY',
          date: {
            lt: olderThan,
          },
        },
      });
      totalDeleted += dailyStatsResult.count;

      const dailyMemberStatsResult = await prisma.memberStats.deleteMany({
        where: {
          period: 'DAILY',
          date: {
            lt: olderThan,
          },
        },
      });
      totalDeleted += dailyMemberStatsResult.count;

      // For weekly/monthly/yearly stats, we might want to keep them longer
      // or not delete them at all

      console.log(
        `Cleaned up ${totalDeleted} expired stats records older than ${olderThan.toISOString()}`
      );

      return totalDeleted;
    } catch (error) {
      console.error('Error cleaning up expired stats:', error);
      throw new Error('Failed to cleanup expired stats');
    }
  }

  /**
   * Export historical data for a family
   */
  static async exportHistoricalData(familyId: string, dateRange: DateRange): Promise<Buffer> {
    try {
      // Get family info
      const family = await prisma.family.findUnique({
        where: { id: familyId },
        include: {
          members: {
            include: {
              user: true,
            },
          },
        },
      });

      if (!family) {
        throw new Error('Family not found');
      }

      // Get all historical data
      const [activityLogs, familyStats, memberStats, tasks, completions] = await Promise.all([
        // Activity logs
        prisma.activityLog.findMany({
          where: {
            familyId,
            timestamp: {
              gte: dateRange.start,
              lte: dateRange.end,
            },
          },
          include: {
            user: true,
            member: {
              include: {
                user: true,
              },
            },
          },
          orderBy: {
            timestamp: 'desc',
          },
        }),

        // Family stats
        prisma.familyStats.findMany({
          where: {
            familyId,
            date: {
              gte: dateRange.start,
              lte: dateRange.end,
            },
          },
          orderBy: {
            date: 'desc',
          },
        }),

        // Member stats
        prisma.memberStats.findMany({
          where: {
            member: {
              familyId,
            },
            date: {
              gte: dateRange.start,
              lte: dateRange.end,
            },
          },
          include: {
            member: {
              include: {
                user: true,
              },
            },
          },
          orderBy: {
            date: 'desc',
          },
        }),

        // Tasks
        prisma.task.findMany({
          where: {
            familyId,
            createdAt: {
              gte: dateRange.start,
              lte: dateRange.end,
            },
          },
          include: {
            assignments: {
              include: {
                member: {
                  include: {
                    user: true,
                  },
                },
              },
            },
            assignedByUser: {
              include: {
                user: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        }),

        // Task completions
        prisma.taskCompletion.findMany({
          where: {
            task: {
              familyId,
            },
            completedAt: {
              gte: dateRange.start,
              lte: dateRange.end,
            },
          },
          include: {
            task: true,
            completedBy: {
              include: {
                user: true,
              },
            },
            verifiedBy: {
              include: {
                user: true,
              },
            },
          },
          orderBy: {
            completedAt: 'desc',
          },
        }),
      ]);

      // Create comprehensive export data
      const exportData = {
        family: {
          id: family.id,
          name: family.name,
          description: family.description,
          createdAt: family.createdAt,
          members: family.members.map(member => ({
            id: member.id,
            name: `${member.user.firstName} ${member.user.lastName}`,
            email: member.user.email,
            role: member.role,
            points: member.points,
            joinedAt: member.joinedAt,
          })),
        },
        exportInfo: {
          generatedAt: new Date(),
          dateRange,
          recordCounts: {
            activityLogs: activityLogs.length,
            familyStats: familyStats.length,
            memberStats: memberStats.length,
            tasks: tasks.length,
            completions: completions.length,
          },
        },
        data: {
          activityLogs: activityLogs.map(log => ({
            id: log.id,
            action: log.action,
            entityType: log.entityType,
            entityId: log.entityId,
            timestamp: log.timestamp,
            metadata: log.metadata,
            user: log.user
              ? {
                  name: `${log.user.firstName} ${log.user.lastName}`,
                  email: log.user.email,
                }
              : null,
            member: log.member
              ? {
                  name: `${log.member.user.firstName} ${log.member.user.lastName}`,
                }
              : null,
          })),
          familyStats: familyStats.map(stat => ({
            date: stat.date,
            period: stat.period,
            tasksCreated: stat.tasksCreated,
            tasksCompleted: stat.tasksCompleted,
            tasksVerified: stat.tasksVerified,
            completionRate: stat.completionRate,
            totalPointsAwarded: stat.totalPointsAwarded,
            averagePointsPerTask: stat.averagePointsPerTask,
            activeMembersCount: stat.activeMembersCount,
          })),
          memberStats: memberStats.map(stat => ({
            memberName: `${stat.member.user.firstName} ${stat.member.user.lastName}`,
            date: stat.date,
            period: stat.period,
            tasksAssigned: stat.tasksAssigned,
            tasksCompleted: stat.tasksCompleted,
            tasksOnTime: stat.tasksOnTime,
            tasksLate: stat.tasksLate,
            completionRate: stat.completionRate,
            pointsEarned: stat.pointsEarned,
            streakDays: stat.streakDays,
            activeDays: stat.activeDays,
          })),
          tasks: tasks.map(task => ({
            id: task.id,
            title: task.title,
            description: task.description,
            points: task.points,
            weight: task.weight,
            frequency: task.frequency,
            status: task.status,
            dueDate: task.dueDate,
            createdAt: task.createdAt,
            assignedTo: task.assignments?.[0]
              ? `${task.assignments[0].member.user.firstName} ${task.assignments[0].member.user.lastName}`
              : null,
            assignedBy: `${task.assignedByUser.user.firstName} ${task.assignedByUser.user.lastName}`,
          })),
          completions: completions.map(completion => ({
            taskTitle: completion.task.title,
            completedBy: `${completion.completedBy.user.firstName} ${completion.completedBy.user.lastName}`,
            completedAt: completion.completedAt,
            verifiedBy: completion.verifiedBy
              ? `${completion.verifiedBy.user.firstName} ${completion.verifiedBy.user.lastName}`
              : null,
            verifiedAt: completion.verifiedAt,
            status: completion.status,
            pointsAwarded: completion.pointsAwarded,
            notes: completion.notes,
          })),
        },
      };

      // Convert to JSON and then to Buffer
      const jsonData = JSON.stringify(exportData, null, 2);
      return Buffer.from(jsonData, 'utf8');
    } catch (error) {
      console.error('Error exporting historical data:', error);
      throw new Error('Failed to export historical data');
    }
  }

  /**
   * Get database storage statistics
   */
  static async getStorageStats(): Promise<{
    activityLogs: number;
    familyStats: number;
    memberStats: number;
    oldestActivityLog?: Date;
    oldestFamilyStats?: Date;
    totalFamilies: number;
  }> {
    try {
      const [
        activityLogsCount,
        familyStatsCount,
        memberStatsCount,
        oldestActivity,
        oldestStats,
        familiesCount,
      ] = await Promise.all([
        prisma.activityLog.count(),
        prisma.familyStats.count(),
        prisma.memberStats.count(),
        prisma.activityLog.findFirst({
          orderBy: { timestamp: 'asc' },
          select: { timestamp: true },
        }),
        prisma.familyStats.findFirst({
          orderBy: { date: 'asc' },
          select: { date: true },
        }),
        prisma.family.count(),
      ]);

      return {
        activityLogs: activityLogsCount,
        familyStats: familyStatsCount,
        memberStats: memberStatsCount,
        oldestActivityLog: oldestActivity?.timestamp,
        oldestFamilyStats: oldestStats?.date,
        totalFamilies: familiesCount,
      };
    } catch (error) {
      console.error('Error getting storage stats:', error);
      throw new Error('Failed to get storage statistics');
    }
  }

  /**
   * Perform routine maintenance
   */
  static async performMaintenance(): Promise<{
    archivedLogs: number;
    cleanedStats: number;
    storageStats: any;
  }> {
    try {
      // Archive logs older than 2 years
      const twoYearsAgo = new Date();
      twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);

      // Clean daily stats older than 1 year
      const oneYearAgo = new Date();
      oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

      const [archivedLogs, cleanedStats, storageStats] = await Promise.all([
        this.archiveOldActivityLogs(twoYearsAgo),
        this.cleanupExpiredStats(oneYearAgo),
        this.getStorageStats(),
      ]);

      console.log('Maintenance completed:', {
        archivedLogs,
        cleanedStats,
        storageStats,
      });

      return {
        archivedLogs,
        cleanedStats,
        storageStats,
      };
    } catch (error) {
      console.error('Error performing maintenance:', error);
      throw new Error('Failed to perform maintenance');
    }
  }
}
