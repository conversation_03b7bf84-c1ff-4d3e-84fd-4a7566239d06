import { prisma } from '@/lib/prisma';
import { StatsPeriod, FamilyStats, MemberStats } from '@prisma/client';

export interface DateRange {
  start: Date;
  end: Date;
}

export interface MemberRanking {
  memberId: string;
  memberName: string;
  value: number;
  rank: number;
}

export interface CategoryStats {
  category: string;
  tasksCreated: number;
  tasksCompleted: number;
  completionRate: number;
  totalPoints: number;
}

export interface TrendData {
  date: Date;
  value: number;
  period: StatsPeriod;
}

export interface FamilyInsights {
  familyId: string;
  totalTasks: number;
  completionRate: number;
  totalPointsAwarded: number;
  mostActiveDay: string;
  topPerformer: {
    memberId: string;
    memberName: string;
    points: number;
  };
  trends: {
    tasksCreated: 'up' | 'down' | 'stable';
    completionRate: 'up' | 'down' | 'stable';
    memberActivity: 'up' | 'down' | 'stable';
  };
}

export class StatsService {
  // Family statistics
  static async generateFamilyStats(
    familyId: string,
    date: Date,
    period: StatsPeriod
  ): Promise<FamilyStats> {
    const dateRange = this.getDateRangeForPeriod(date, period);

    // Get task statistics
    const taskStats = await prisma.task.groupBy({
      by: ['status'],
      where: {
        familyId,
        createdAt: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      _count: {
        id: true,
      },
    });

    const completions = await prisma.taskCompletion.findMany({
      where: {
        task: {
          familyId,
        },
        completedAt: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      include: {
        task: true,
      },
    });

    const verifiedCompletions = completions.filter(c => c.status === 'APPROVED');

    const tasksCreated = taskStats.reduce((sum, stat) => sum + stat._count.id, 0);
    const tasksCompleted = completions.length;
    const tasksVerified = verifiedCompletions.length;
    const completionRate = tasksCreated > 0 ? (tasksCompleted / tasksCreated) * 100 : 0;

    const totalPointsAwarded = verifiedCompletions.reduce(
      (sum, completion) => sum + completion.pointsAwarded,
      0
    );
    const averagePointsPerTask = tasksCompleted > 0 ? totalPointsAwarded / tasksCompleted : 0;

    // Get active members count
    const activeMembersCount = await prisma.familyMember.count({
      where: {
        familyId,
        lastActive: {
          gte: dateRange.start,
        },
      },
    });

    // Create or update stats record
    return await prisma.familyStats.upsert({
      where: {
        familyId_date_period: {
          familyId,
          date,
          period,
        },
      },
      update: {
        tasksCreated,
        tasksCompleted,
        tasksVerified,
        completionRate,
        totalPointsAwarded,
        averagePointsPerTask,
        activeMembersCount,
      },
      create: {
        familyId,
        date,
        period,
        tasksCreated,
        tasksCompleted,
        tasksVerified,
        completionRate,
        totalPointsAwarded,
        averagePointsPerTask,
        activeMembersCount,
      },
    });
  }

  static async getFamilyStats(
    familyId: string,
    dateRange: DateRange,
    period: StatsPeriod
  ): Promise<FamilyStats[]> {
    return await prisma.familyStats.findMany({
      where: {
        familyId,
        date: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
        period,
      },
      orderBy: {
        date: 'asc',
      },
    });
  }

  static async getFamilyCompletionRate(familyId: string, dateRange: DateRange): Promise<number> {
    const stats = await prisma.familyStats.findMany({
      where: {
        familyId,
        date: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
        period: 'DAILY',
      },
    });

    if (stats.length === 0) return 0;

    const averageRate = stats.reduce((sum, stat) => sum + stat.completionRate, 0) / stats.length;
    return averageRate;
  }

  // Member statistics
  static async generateMemberStats(
    memberId: string,
    date: Date,
    period: StatsPeriod
  ): Promise<MemberStats> {
    const dateRange = this.getDateRangeForPeriod(date, period);

    // Get member's tasks
    const assignedTasks = await prisma.task.count({
      where: {
        assignments: {
          some: {
            memberId: memberId,
          },
        },
        createdAt: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
    });

    const completions = await prisma.taskCompletion.findMany({
      where: {
        completedById: memberId,
        completedAt: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      include: {
        task: true,
      },
    });

    const tasksCompleted = completions.length;
    const tasksOnTime = completions.filter(
      c => !c.task.dueDate || c.completedAt <= c.task.dueDate
    ).length;
    const tasksLate = completions.filter(
      c => c.task.dueDate && c.completedAt > c.task.dueDate
    ).length;
    const completionRate = assignedTasks > 0 ? (tasksCompleted / assignedTasks) * 100 : 0;

    const pointsEarned = completions.reduce((sum, completion) => sum + completion.pointsAwarded, 0);
    const pointsFromTasks = pointsEarned; // For now, all points are from tasks
    const bonusPoints = 0; // To be implemented later

    // Calculate activity metrics
    const activeDays = await this.calculateActiveDays(memberId, dateRange);
    const streakDays = await this.calculateStreakDays(memberId, date);

    return await prisma.memberStats.upsert({
      where: {
        memberId_date_period: {
          memberId,
          date,
          period,
        },
      },
      update: {
        tasksAssigned: assignedTasks,
        tasksCompleted,
        tasksOnTime,
        tasksLate,
        completionRate,
        pointsEarned,
        pointsFromTasks,
        bonusPoints,
        activeDays,
        streakDays,
      },
      create: {
        memberId,
        date,
        period,
        tasksAssigned: assignedTasks,
        tasksCompleted,
        tasksOnTime,
        tasksLate,
        completionRate,
        pointsEarned,
        pointsFromTasks,
        bonusPoints,
        activeDays,
        streakDays,
      },
    });
  }

  static async getMemberStats(
    memberId: string,
    dateRange: DateRange,
    period: StatsPeriod
  ): Promise<MemberStats[]> {
    return await prisma.memberStats.findMany({
      where: {
        memberId,
        date: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
        period,
      },
      orderBy: {
        date: 'asc',
      },
    });
  }

  static async getMemberRanking(
    familyId: string,
    metric: 'points' | 'completion_rate',
    period: StatsPeriod
  ): Promise<MemberRanking[]> {
    const endDate = new Date();
    const startDate = this.getDateRangeForPeriod(endDate, period).start;

    const members = await prisma.familyMember.findMany({
      where: {
        familyId,
      },
      include: {
        user: true,
        memberStats: {
          where: {
            date: {
              gte: startDate,
              lte: endDate,
            },
            period,
          },
        },
      },
    });

    const rankings = members.map(member => {
      const stats = member.memberStats;
      let value = 0;

      if (metric === 'points') {
        value = stats.reduce((sum, stat) => sum + stat.pointsEarned, 0);
      } else if (metric === 'completion_rate') {
        const avgRate =
          stats.length > 0
            ? stats.reduce((sum, stat) => sum + stat.completionRate, 0) / stats.length
            : 0;
        value = avgRate;
      }

      const firstName = member.user.firstName || '';
      const lastName = member.user.lastName || '';
      const memberName =
        firstName && lastName
          ? `${firstName} ${lastName}`
          : member.user.email?.split('@')[0] || 'Nieznany użytkownik';

      return {
        memberId: member.id,
        memberName,
        value,
        rank: 0, // Will be set below
      };
    });

    // Sort and assign ranks
    rankings.sort((a, b) => b.value - a.value);
    rankings.forEach((ranking, index) => {
      ranking.rank = index + 1;
    });

    return rankings;
  }

  // Advanced analytics
  static async getTaskCategoryStats(
    familyId: string,
    dateRange: DateRange
  ): Promise<CategoryStats[]> {
    // This would need a category field in Task model
    // For now, return empty array
    return [];
  }

  static async getProductivityTrends(familyId: string, dateRange: DateRange): Promise<TrendData[]> {
    const stats = await prisma.familyStats.findMany({
      where: {
        familyId,
        date: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
        period: 'DAILY',
      },
      orderBy: {
        date: 'asc',
      },
    });

    return stats.map(stat => ({
      date: stat.date,
      value: stat.completionRate,
      period: stat.period,
    }));
  }

  static async generateInsights(familyId: string): Promise<FamilyInsights> {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dateRange = {
      start: thirtyDaysAgo,
      end: new Date(),
    };

    // Get basic family stats
    const totalTasks = await prisma.task.count({
      where: {
        familyId,
        createdAt: {
          gte: dateRange.start,
        },
      },
    });

    const completionRate = await this.getFamilyCompletionRate(familyId, dateRange);

    const totalPointsAwarded = await prisma.taskCompletion.aggregate({
      where: {
        task: {
          familyId,
        },
        completedAt: {
          gte: dateRange.start,
        },
      },
      _sum: {
        pointsAwarded: true,
      },
    });

    // Get top performer
    const rankings = await this.getMemberRanking(familyId, 'points', 'MONTHLY');
    const topPerformer = rankings[0] || {
      memberId: '',
      memberName: 'Brak danych',
      points: 0,
    };

    return {
      familyId,
      totalTasks,
      completionRate,
      totalPointsAwarded: totalPointsAwarded._sum.pointsAwarded || 0,
      mostActiveDay: 'Poniedziałek', // Would need proper calculation
      topPerformer: {
        memberId: topPerformer.memberId,
        memberName: topPerformer.memberName,
        points: topPerformer.value,
      },
      trends: {
        tasksCreated: 'stable',
        completionRate: 'up',
        memberActivity: 'stable',
      },
    };
  }

  // Helper methods
  private static getDateRangeForPeriod(date: Date, period: StatsPeriod): DateRange {
    const start = new Date(date);
    const end = new Date(date);

    switch (period) {
      case 'DAILY':
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        break;
      case 'WEEKLY':
        const dayOfWeek = start.getDay();
        start.setDate(start.getDate() - dayOfWeek);
        start.setHours(0, 0, 0, 0);
        end.setDate(start.getDate() + 6);
        end.setHours(23, 59, 59, 999);
        break;
      case 'MONTHLY':
        start.setDate(1);
        start.setHours(0, 0, 0, 0);
        end.setMonth(end.getMonth() + 1);
        end.setDate(0);
        end.setHours(23, 59, 59, 999);
        break;
      case 'YEARLY':
        start.setMonth(0, 1);
        start.setHours(0, 0, 0, 0);
        end.setMonth(11, 31);
        end.setHours(23, 59, 59, 999);
        break;
    }

    return { start, end };
  }

  private static async calculateActiveDays(
    memberId: string,
    dateRange: DateRange
  ): Promise<number> {
    const completions = await prisma.taskCompletion.findMany({
      where: {
        completedById: memberId,
        completedAt: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      select: {
        completedAt: true,
      },
    });

    const uniqueDays = new Set(
      completions.map(completion => completion.completedAt.toISOString().split('T')[0])
    );

    return uniqueDays.size;
  }

  private static async calculateStreakDays(memberId: string, asOfDate: Date): Promise<number> {
    // Get recent completions
    const thirtyDaysAgo = new Date(asOfDate);
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const completions = await prisma.taskCompletion.findMany({
      where: {
        completedById: memberId,
        completedAt: {
          gte: thirtyDaysAgo,
          lte: asOfDate,
        },
      },
      select: {
        completedAt: true,
      },
      orderBy: {
        completedAt: 'desc',
      },
    });

    const activeDays = new Set(
      completions.map(completion => completion.completedAt.toISOString().split('T')[0])
    );

    // Calculate consecutive days from today
    let streak = 0;
    const today = new Date(asOfDate);

    for (let i = 0; i < 30; i++) {
      const checkDate = new Date(today);
      checkDate.setDate(checkDate.getDate() - i);
      const dateString = checkDate.toISOString().split('T')[0];

      if (activeDays.has(dateString)) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }
}
