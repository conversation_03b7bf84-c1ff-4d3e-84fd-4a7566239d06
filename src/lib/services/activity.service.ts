import { prisma } from '@/lib/prisma';
import { ActivityAction, ActivityLog } from '@prisma/client';

export interface ActivityFilters {
  action?: ActivityAction;
  entityType?: string;
  entityId?: string;
  userId?: string;
  memberId?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface DateRange {
  start: Date;
  end: Date;
}

export class ActivityLogService {
  static async logActivity(data: {
    familyId: string;
    userId?: string;
    memberId?: string;
    action: ActivityAction;
    entityType: string;
    entityId?: string;
    metadata?: Record<string, any>;
  }): Promise<ActivityLog> {
    return await prisma.activityLog.create({
      data: {
        familyId: data.familyId,
        userId: data.userId,
        memberId: data.memberId,
        action: data.action,
        entityType: data.entityType,
        entityId: data.entityId,
        metadata: data.metadata,
      },
    });
  }

  static async getActivityHistory(
    familyId: string,
    filters: ActivityFilters = {}
  ): Promise<ActivityLog[]> {
    const where: any = {
      familyId,
    };

    if (filters.action) {
      where.action = filters.action;
    }

    if (filters.entityType) {
      where.entityType = filters.entityType;
    }

    if (filters.entityId) {
      where.entityId = filters.entityId;
    }

    if (filters.userId) {
      where.userId = filters.userId;
    }

    if (filters.memberId) {
      where.memberId = filters.memberId;
    }

    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate;
      }
    }

    return await prisma.activityLog.findMany({
      where,
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        member: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    });
  }

  static async getRecentActivity(familyId: string, limit: number = 10): Promise<ActivityLog[]> {
    return await prisma.activityLog.findMany({
      where: {
        familyId,
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        member: {
          select: {
            id: true,
            user: {
              select: {
                firstName: true,
                lastName: true,
              },
            },
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
      take: limit,
    });
  }

  static async getMemberActivity(memberId: string, dateRange: DateRange): Promise<ActivityLog[]> {
    return await prisma.activityLog.findMany({
      where: {
        memberId,
        timestamp: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
      orderBy: {
        timestamp: 'desc',
      },
    });
  }

  static async deleteOldActivityLogs(olderThan: Date): Promise<number> {
    const result = await prisma.activityLog.deleteMany({
      where: {
        timestamp: {
          lt: olderThan,
        },
      },
    });

    return result.count;
  }

  static async getActivityStats(
    familyId: string,
    dateRange: DateRange
  ): Promise<Record<ActivityAction, number>> {
    const activities = await prisma.activityLog.groupBy({
      by: ['action'],
      where: {
        familyId,
        timestamp: {
          gte: dateRange.start,
          lte: dateRange.end,
        },
      },
      _count: {
        id: true,
      },
    });

    const stats: Record<ActivityAction, number> = {} as Record<ActivityAction, number>;

    activities.forEach(activity => {
      stats[activity.action] = activity._count.id;
    });

    return stats;
  }
}
