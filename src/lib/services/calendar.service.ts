import { prisma } from '@/lib/prisma';
import {
  CalendarTask,
  CalendarFilters,
  CreateEventData,
  UpdateEventData,
  ConflictDetection,
  TimeSlot,
  SchedulingConstraints,
  SchedulingSuggestion,
} from '@/lib/validations/calendar';
import {
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  startOfWeek,
  endOfWeek,
  addDays,
  addMinutes,
  isWithinInterval,
} from 'date-fns';
import { pl } from 'date-fns/locale';
import { Task, TaskStatus, CalendarView, WeekDay } from '@prisma/client';
import { combineTasksForCalendar } from '@/lib/utils/calendar-helpers';

/**
 * CalendarService - obsługuje wyświetlanie zadań w formie kalendarza
 *
 * UWAGA ARCHITEKTURALNA:
 * - Kalendarz nie zawiera osobnych "wydarzeń" - wsz<PERSON><PERSON><PERSON> to są zadania (Task)
 * - Ka<PERSON><PERSON> zadanie może być wyświetlone w kalendarzu jeśli ma:
 *   - startDate (data rozpoczęcia)
 *   - dueDate (termin wykońania)
 *   - lub oba
 * - convertTaskToCalendarTask() przekształca Task na CalendarTask
 * - Wszystkie funkcje operują na zadaniach, nie na wydarzeniach
 */

export class CalendarService {
  // Task management dla kalendarza
  // Pobiera zadania do wyświetlenia w kalendarzu z obsługą zadań cyklicznych
  static async getCalendarTasks(
    familyId: string,
    startDate: Date,
    endDate: Date,
    filters?: CalendarFilters
  ): Promise<CalendarTask[]> {
    // Podstawowe filtry dla zadań
    const baseWhereClause: any = {
      familyId,
      status: filters?.showCompleted ? undefined : { not: TaskStatus.COMPLETED },
    };

    // Zastosuj filtr statusu jeśli podany
    if (filters?.taskStatus && filters.taskStatus.length > 0) {
      baseWhereClause.status = { in: filters.taskStatus };
    }

    // POBIERANIE ZADAŃ JEDNORAZOWYCH (ONCE) W ZAKRESIE DAT
    const oneTimeWhereClause = {
      ...baseWhereClause,
      frequency: 'ONCE',
      OR: [
        // Tasks with specific dates in range
        {
          AND: [
            { startDate: { not: null } },
            {
              OR: [
                { startDate: { gte: startDate, lte: endDate } },
                { endDate: { gte: startDate, lte: endDate } },
                {
                  AND: [{ startDate: { lte: startDate } }, { endDate: { gte: endDate } }],
                },
              ],
            },
          ],
        },
        // Tasks with due dates but no start date
        {
          AND: [{ startDate: null }, { dueDate: { gte: startDate, lte: endDate } }],
        },
      ],
    };

    // POBIERANIE ZADAŃ CYKLICZNYCH (nie ONCE)
    const recurringWhereClause = {
      ...baseWhereClause,
      frequency: { not: 'ONCE' },
      // Zadanie powinno się zaczynać przed końcem zakresu
      startDate: {
        lte: endDate,
      },
      // I kończyć po początku zakresu (lub nie mieć końca)
      OR: [{ recurrenceEnd: null }, { recurrenceEnd: { gte: startDate } }],
    };

    // Zastosuj filtr członka rodziny jeśli podany
    if (filters?.memberId) {
      const memberFilter = {
        assignments: {
          some: {
            memberId: filters.memberId,
          },
        },
      };

      oneTimeWhereClause.AND = oneTimeWhereClause.AND || [];
      oneTimeWhereClause.AND.push(memberFilter);

      recurringWhereClause.AND = recurringWhereClause.AND || [];
      recurringWhereClause.AND.push(memberFilter);
    }

    const includeClause = {
      assignments: {
        include: {
          member: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
        },
      },
    };

    // Pobierz zadania jednorazowe
    const oneTimeTasks = await prisma.task.findMany({
      where: oneTimeWhereClause,
      include: includeClause,
      orderBy: [{ startDate: 'asc' }, { dueDate: 'asc' }, { createdAt: 'asc' }],
    });

    // Pobierz zadania cykliczne
    const recurringTasks = await prisma.task.findMany({
      where: recurringWhereClause,
      include: includeClause,
      orderBy: [{ startDate: 'asc' }, { createdAt: 'asc' }],
    });

    // Połącz zadania używając logiki z calendar-helpers
    // Ta funkcja wygeneruje wszystkie wystąpienia zadań cyklicznych w podanym zakresie
    return combineTasksForCalendar(oneTimeTasks, recurringTasks, startDate, endDate);
  }

  static async createCalendarTask(
    familyId: string,
    data: CreateEventData,
    createdByMemberId?: string
  ): Promise<CalendarTask> {
    const task = await prisma.task.create({
      data: {
        familyId,
        title: data.title,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
        allDay: data.allDay,
        color: data.color,
        recurrenceRule: data.recurrenceRule,
        assignedBy: createdByMemberId || (data.assignedMemberIds?.[0] ?? ''),
        points: 1, // Default points
      },
      include: {
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return this.convertTaskToCalendarTask(task);
  }

  static async updateCalendarTask(taskId: string, data: UpdateEventData): Promise<CalendarTask> {
    const task = await prisma.task.update({
      where: { id: taskId },
      data: {
        title: data.title,
        description: data.description,
        startDate: data.startDate,
        endDate: data.endDate,
        allDay: data.allDay,
        color: data.color,
        recurrenceRule: data.recurrenceRule,
      },
      include: {
        assignments: {
          include: {
            member: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    email: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return this.convertTaskToCalendarTask(task);
  }

  static async deleteCalendarTask(taskId: string): Promise<void> {
    await prisma.task.delete({
      where: { id: taskId },
    });
  }

  /**
   * Konwertuje zadanie (Task) na format kalendarza (CalendarTask)
   *
   * KLUCZOWA FUNKCJA:
   * - Wszystkie elementy kalendarza są generowane z Tasków
   * - startDate lub dueDate staje się 'start' w kalendarzu
   * - endDate (jeśli istnieje) staje się 'end' w kalendarzu
   * - allDay kontroluje czy zadanie jest całodniowe
   * - metadata zawiera punkty, wagę, częstotliwość, status
   *
   * @param task - Zadanie z bazy danych (z opcjonalnym assignedTo)
   * @returns CalendarTask - obiekt do wyświetlenia w kalendarzu
   */
  static async convertTaskToCalendarTask(
    task: Task & {
      assignments?: {
        id: string;
        memberId: string;
        member: {
          id: string;
          user: { firstName: string | null; lastName: string | null; email: string };
        };
      }[];
    }
  ): Promise<CalendarTask> {
    // Użyj startDate jeśli istnieje, w przeciwnym razie dueDate
    const startDate = task.startDate || task.dueDate;

    // endDate lub (dla allDay) dużyć startDate/dueDate jako koniec
    const endDate = task.endDate || (task.allDay ? task.startDate || task.dueDate : undefined);

    // Debug: sprawdźmy kolor zadania
    console.log(
      `ConvertTaskToCalendarTask - Task ${task.id} (${task.title}): color = ${task.color}`
    );

    return {
      id: task.id,
      title: task.title,
      description: task.description || undefined,
      start: startDate!, // Zawsze musi być data
      end: endDate || undefined,
      allDay: task.allDay,
      color: task.color || undefined,
      type: 'task', // Zawsze 'task' - nie ma eventów
      assignments: task.assignments || undefined,
      metadata: {
        points: task.points,
        weight: task.weight,
        frequency: task.frequency,
        status: task.status,
        recurrenceRule: task.recurrenceRule,
      },
    };
  }

  static async syncTaskWithCalendar(taskId: string): Promise<void> {
    // This would integrate with external calendar APIs
    // For now, it's a placeholder
    const task = await prisma.task.findUnique({
      where: { id: taskId },
    });

    if (!task) {
      throw new Error('Task not found');
    }

    // TODO: Implement Google Calendar / Apple Calendar sync
  }

  // Recurring tasks (placeholder for future implementation)
  static async generateRecurringTasks(
    baseTask: CalendarTask,
    rrule: string,
    endDate: Date
  ): Promise<CalendarTask[]> {
    // This would use a library like rrule to generate recurring tasks
    // For now, return the base task
    return [baseTask];
  }

  // View helpers
  static async getMonthTasks(
    familyId: string,
    year: number,
    month: number
  ): Promise<CalendarTask[]> {
    const monthStart = startOfMonth(new Date(year, month - 1));
    const monthEnd = endOfMonth(new Date(year, month - 1));

    return this.getCalendarTasks(familyId, monthStart, monthEnd);
  }

  static async getWeekTasks(familyId: string, weekStart: Date): Promise<CalendarTask[]> {
    const weekEnd = endOfWeek(weekStart, { locale: pl });

    return this.getCalendarTasks(familyId, weekStart, weekEnd);
  }

  static async getDayTasks(familyId: string, date: Date): Promise<CalendarTask[]> {
    const dayStart = startOfDay(date);
    const dayEnd = endOfDay(date);

    return this.getCalendarTasks(familyId, dayStart, dayEnd);
  }

  // Conflict detection
  static async detectConflicts(
    memberId: string,
    startDate: Date,
    endDate: Date,
    excludeEventId?: string
  ): Promise<ConflictDetection> {
    const whereClause: any = {
      assignments: {
        some: {
          memberId: memberId,
        },
      },
      status: { not: TaskStatus.COMPLETED },
      OR: [
        {
          AND: [
            { startDate: { not: null } },
            { startDate: { lt: endDate } },
            {
              OR: [{ endDate: { gt: startDate } }, { endDate: null, allDay: true }],
            },
          ],
        },
      ],
    };

    if (excludeEventId) {
      whereClause.id = { not: excludeEventId };
    }

    const conflictingTasks = await prisma.task.findMany({
      where: whereClause,
      select: {
        id: true,
        title: true,
        startDate: true,
        endDate: true,
        allDay: true,
      },
    });

    const conflicts = conflictingTasks.map(task => ({
      id: task.id,
      title: task.title,
      start: task.startDate!,
      end: task.endDate || task.startDate!,
      type: 'task' as const,
    }));

    return {
      hasConflicts: conflicts.length > 0,
      conflicts,
    };
  }

  static async suggestAlternativeTimes(
    memberId: string,
    duration: number, // in minutes
    preferredDate: Date
  ): Promise<TimeSlot[]> {
    // Enhanced implementation with better scoring
    const suggestions: TimeSlot[] = [];
    const dayStart = startOfDay(preferredDate);

    // Generate 30-minute slots from 7 AM to 9 PM
    for (let hour = 7; hour < 21; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const slotStart = addMinutes(dayStart, hour * 60 + minute);
        const slotEnd = addMinutes(slotStart, duration);

        // Don't suggest slots that end after 9 PM
        if (slotEnd.getHours() >= 21) continue;

        const conflicts = await this.detectConflicts(memberId, slotStart, slotEnd);

        if (!conflicts.hasConflicts) {
          // Calculate score based on time preference
          let score = 1;

          // Prefer times between 9 AM and 6 PM
          if (hour >= 9 && hour <= 18) {
            score += 0.3;
          }

          // Slight preference for exact hours
          if (minute === 0) {
            score += 0.1;
          }

          // Avoid very early or very late hours
          if (hour < 8 || hour > 19) {
            score -= 0.2;
          }

          suggestions.push({
            start: slotStart,
            end: slotEnd,
            available: true,
            score,
          });
        }
      }
    }

    // Sort by score and return top 8 suggestions
    return suggestions.sort((a, b) => (b.score || 0) - (a.score || 0)).slice(0, 8);
  }

  static async getOptimalTaskScheduling(
    familyId: string,
    tasks: Task[],
    constraints: SchedulingConstraints
  ): Promise<SchedulingSuggestion[]> {
    const suggestions: SchedulingSuggestion[] = [];

    // Sort tasks by priority and due date
    const prioritizedTasks = tasks
      .filter(task => task.dueDate)
      .sort((a, b) => {
        // First by weight (priority)
        const weightOrder = { CRITICAL: 4, HEAVY: 3, NORMAL: 2, LIGHT: 1 };
        const weightDiff = (weightOrder[b.weight] || 2) - (weightOrder[a.weight] || 2);

        if (weightDiff !== 0) return weightDiff;

        // Then by due date
        return new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime();
      });

    for (const task of prioritizedTasks) {
      const estimatedDuration = this.getEstimatedTaskDuration(task);

      // Sprawdź czy zadanie ma przypisania
      const taskWithAssignments = await prisma.task.findUnique({
        where: { id: task.id },
        include: { assignments: true },
      });

      if (!taskWithAssignments?.assignments?.[0]) continue;

      const preferredSlots = await this.suggestAlternativeTimes(
        taskWithAssignments.assignments[0].memberId,
        estimatedDuration,
        task.dueDate!
      );

      if (preferredSlots.length > 0) {
        const bestSlot = preferredSlots[0];
        let confidence = 0.7;
        let reason = 'Dostępny slot';

        // Adjust confidence based on various factors
        if (bestSlot.score && bestSlot.score > 1.2) {
          confidence += 0.1;
          reason = 'Optymalny czas wykonania';
        }

        if (task.weight === 'CRITICAL') {
          confidence += 0.1;
          reason += ' (wysokiy priorytet)';
        }

        // Check if it's well before due date
        const timeToDue = task.dueDate!.getTime() - bestSlot.start.getTime();
        const daysToDue = timeToDue / (1000 * 60 * 60 * 24);

        if (daysToDue > 1) {
          confidence += 0.05;
          reason += ' z wyprzedzeniem';
        } else if (daysToDue < 0.5) {
          confidence -= 0.1;
          reason += ' (blisko terminu)';
        }

        suggestions.push({
          taskId: task.id,
          suggestedTime: bestSlot,
          confidence: Math.min(confidence, 0.95),
          reason,
        });
      } else {
        // No available slots - suggest extending deadline or reducing scope
        suggestions.push({
          taskId: task.id,
          suggestedTime: {
            start: task.dueDate!,
            end: addMinutes(task.dueDate!, estimatedDuration),
            available: false,
            score: 0,
          },
          confidence: 0.1,
          reason: 'Brak dostępnych slotów - rozważ przesunięcie terminu',
        });
      }
    }

    return suggestions;
  }

  // Helper method to estimate task duration
  private static getEstimatedTaskDuration(task: Task): number {
    // Basic estimation based on task weight
    switch (task.weight) {
      case 'LIGHT':
        return 30; // 30 minutes
      case 'NORMAL':
        return 60; // 1 hour
      case 'HEAVY':
        return 120; // 2 hours
      case 'CRITICAL':
        return 180; // 3 hours
      default:
        return 60;
    }
  }

  // Advanced conflict detection with family-wide analysis
  static async detectFamilyConflicts(
    familyId: string,
    startDate: Date,
    endDate: Date,
    excludeEventId?: string
  ): Promise<{
    hasConflicts: boolean;
    memberConflicts: {
      memberId: string;
      memberName: string;
      conflicts: ConflictDetection['conflicts'];
    }[];
    totalConflicts: number;
  }> {
    const family = await prisma.family.findUnique({
      where: { id: familyId },
      include: {
        members: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!family) {
      throw new Error('Family not found');
    }

    const memberConflicts = [];
    let totalConflicts = 0;

    for (const member of family.members) {
      const conflicts = await this.detectConflicts(member.id, startDate, endDate, excludeEventId);

      if (conflicts.hasConflicts) {
        const memberName = `${member.user.firstName || ''} ${member.user.lastName || ''}`.trim();

        memberConflicts.push({
          memberId: member.id,
          memberName: memberName || 'Nieznany',
          conflicts: conflicts.conflicts,
        });

        totalConflicts += conflicts.conflicts.length;
      }
    }

    return {
      hasConflicts: totalConflicts > 0,
      memberConflicts,
      totalConflicts,
    };
  }

  // Smart scheduling for recurring tasks
  static async scheduleRecurringTask(
    familyId: string,
    taskId: string,
    recurrenceRule: string,
    endDate: Date
  ): Promise<{
    scheduledSlots: TimeSlot[];
    conflicts: { date: Date; reason: string }[];
  }> {
    const task = await prisma.task.findUnique({
      where: { id: taskId },
      include: {
        assignments: {
          include: {
            member: true,
          },
        },
      },
    });

    if (!task || !task.assignments?.[0]) {
      throw new Error('Task or assignee not found');
    }

    // This would integrate with rrule library for proper recurring logic
    // For now, simplified implementation
    const scheduledSlots: TimeSlot[] = [];
    const conflicts: { date: Date; reason: string }[] = [];

    // Generate weekly occurrences (simplified)
    let currentDate = new Date(task.startDate || task.createdAt);
    const estimatedDuration = this.getEstimatedTaskDuration(task);

    while (currentDate <= endDate) {
      const suggestions = await this.suggestAlternativeTimes(
        task.assignments[0].memberId,
        estimatedDuration,
        currentDate
      );

      if (suggestions.length > 0) {
        scheduledSlots.push(suggestions[0]);
      } else {
        conflicts.push({
          date: new Date(currentDate),
          reason: 'Brak dostępnych slotów czasowych',
        });
      }

      // Move to next week (simplified)
      currentDate = addDays(currentDate, 7);
    }

    return {
      scheduledSlots,
      conflicts,
    };
  }
}
