'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface PrivacyPreferences {
  profileVisibility: 'public' | 'family' | 'private';
  showEmail: boolean;
  showActivity: boolean;
  showStats: boolean;
  allowInvitations: boolean;
  dataSharing: boolean;
  analyticsOptOut: boolean;
  marketingEmails: boolean;
}

const PRIVACY_PREFERENCES_KEY = ['user', 'preferences', 'privacy'] as const;

export function usePrivacyPreferences() {
  return useQuery({
    queryKey: PRIVACY_PREFERENCES_KEY,
    queryFn: async (): Promise<PrivacyPreferences> => {
      const response = await fetch('/api/user/preferences/privacy');
      if (!response.ok) {
        throw new Error('Nie udało się pobrać ustawień prywatności');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUpdatePrivacyPreferences() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (preferences: Partial<PrivacyPreferences>) => {
      const response = await fetch('/api/user/preferences/privacy', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      if (!response.ok) {
        throw new Error('Nie udało się zapisać preferencji');
      }

      return response.json();
    },
    onSuccess: updatedPreferences => {
      // Update the cache with new data
      queryClient.setQueryData(PRIVACY_PREFERENCES_KEY, updatedPreferences);
      toast.success('Ustawienia prywatności zostały zapisane');
    },
    onError: error => {
      console.error('Error saving privacy preferences:', error);
      toast.error('Nie udało się zapisać ustawień prywatności');
    },
  });
}
