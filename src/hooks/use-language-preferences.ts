'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface LanguagePreferences {
  preferredLanguage: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
}

const LANGUAGE_PREFERENCES_KEY = ['user', 'preferences', 'language'] as const;

export function useLanguagePreferences() {
  return useQuery({
    queryKey: LANGUAGE_PREFERENCES_KEY,
    queryFn: async (): Promise<LanguagePreferences> => {
      const response = await fetch('/api/user/preferences/language');
      if (!response.ok) {
        throw new Error('Nie udało się pobrać ustawień wyglądu');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUpdateLanguagePreferences() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (preferences: Partial<LanguagePreferences>) => {
      const response = await fetch('/api/user/preferences/language', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      if (!response.ok) {
        throw new Error('Nie udało się zapisać preferencji');
      }

      return response.json();
    },
    onSuccess: updatedPreferences => {
      // Update the cache with new data
      queryClient.setQueryData(LANGUAGE_PREFERENCES_KEY, updatedPreferences);
      toast.success('Ustawienia wyglądu zostały zapisane');
    },
    onError: error => {
      console.error('Error saving language preferences:', error);
      toast.error('Nie udało się zapisać ustawień');
    },
  });
}
