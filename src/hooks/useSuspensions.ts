import { useState, useEffect, useCallback } from 'react';

export interface SuspensionInfo {
  isSuspended: boolean;
  reason?: string;
  title?: string;
  endDate?: Date;
  suspensionType?: 'task' | 'member';
}

interface SuspensionWithDetails {
  id: string;
  familyId: string;
  title: string;
  description?: string | null;
  reason: string;
  startDate: string;
  endDate: string;
  status: string;
  createdById: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    user: {
      firstName: string | null;
      lastName: string | null;
    };
  };
  suspendedTasks: {
    task: {
      id: string;
      title: string;
      description?: string | null;
      points: number;
    };
  }[];
  suspendedMembers: {
    member: {
      id: string;
      user: {
        firstName: string | null;
        lastName: string | null;
      };
    };
  }[];
}

export function useSuspensions(familyId: string) {
  const [suspensions, setSuspensions] = useState<SuspensionWithDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSuspensions = useCallback(async () => {
    if (!familyId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/families/${familyId}/suspensions`);

      if (!response.ok) {
        throw new Error('Nie udało się pobrać zawieszeń');
      }

      const data = await response.json();
      setSuspensions(data);
    } catch (err: any) {
      console.error('Error fetching suspensions:', err);
      setError(err.message || 'Wystąpił błąd podczas pobierania zawieszeń');
      setSuspensions([]);
    } finally {
      setIsLoading(false);
    }
  }, [familyId]);

  useEffect(() => {
    fetchSuspensions();
  }, [fetchSuspensions]);

  // Get active suspensions
  const activeSuspensions = suspensions.filter(suspension => {
    const now = new Date();
    const startDate = new Date(suspension.startDate);
    const endDate = new Date(suspension.endDate);

    return suspension.status === 'ACTIVE' && startDate <= now && endDate >= now;
  });

  // Check if a specific task is suspended
  const getTaskSuspensionInfo = useCallback(
    (taskId: string): SuspensionInfo => {
      // Check task-specific suspensions
      for (const suspension of activeSuspensions) {
        const isSuspendedTask = suspension.suspendedTasks.some(st => st.task.id === taskId);
        if (isSuspendedTask) {
          return {
            isSuspended: true,
            reason: suspension.reason,
            title: suspension.title,
            endDate: new Date(suspension.endDate),
            suspensionType: 'task',
          };
        }
      }

      return { isSuspended: false };
    },
    [activeSuspensions]
  );

  // Check if a specific member is suspended
  const getMemberSuspensionInfo = useCallback(
    (memberId: string): SuspensionInfo => {
      // Check member-specific suspensions
      for (const suspension of activeSuspensions) {
        const isSuspendedMember = suspension.suspendedMembers.some(sm => sm.member.id === memberId);
        if (isSuspendedMember) {
          return {
            isSuspended: true,
            reason: suspension.reason,
            title: suspension.title,
            endDate: new Date(suspension.endDate),
            suspensionType: 'member',
          };
        }
      }

      return { isSuspended: false };
    },
    [activeSuspensions]
  );

  // Check if a task is suspended (either directly or via member suspension)
  const getTaskSuspensionStatus = useCallback(
    (taskId: string, assignedMemberId?: string): SuspensionInfo => {
      // First check direct task suspension
      const taskSuspension = getTaskSuspensionInfo(taskId);
      if (taskSuspension.isSuspended) {
        return taskSuspension;
      }

      // Then check member suspension if task is assigned
      if (assignedMemberId) {
        const memberSuspension = getMemberSuspensionInfo(assignedMemberId);
        if (memberSuspension.isSuspended) {
          return memberSuspension;
        }
      }

      return { isSuspended: false };
    },
    [getTaskSuspensionInfo, getMemberSuspensionInfo]
  );

  // Get suspension stats
  const suspensionStats = {
    total: suspensions.length,
    active: activeSuspensions.length,
    ended: suspensions.filter(s => s.status === 'ENDED').length,
    cancelled: suspensions.filter(s => s.status === 'CANCELLED').length,
    expired: suspensions.filter(s => {
      const now = new Date();
      const endDate = new Date(s.endDate);
      return s.status === 'ACTIVE' && endDate < now;
    }).length,
  };

  return {
    suspensions,
    activeSuspensions,
    isLoading,
    error,
    suspensionStats,
    getTaskSuspensionInfo,
    getMemberSuspensionInfo,
    getTaskSuspensionStatus,
    refetch: fetchSuspensions,
  };
}
