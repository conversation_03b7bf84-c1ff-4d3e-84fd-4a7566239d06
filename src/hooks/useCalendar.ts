'use client';

/**
 * useCalendar - hook do zarządzania danymi kalendarza
 *
 * UWAGA ARCHITEKTURALNA:
 * - <PERSON><PERSON><PERSON><PERSON> wyświ<PERSON><PERSON> zadania (Task), nie wydarzenia
 * - Wszystkie "CalendarTask" to zadania z różnymi formatami czasowymi
 * - Hook zachowuje kompatybilność wsteczną poprzez aliasy (events -> tasks)
 * - API endpoint /calendar zwraca zadania w formacie CalendarTask
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CalendarView } from '@prisma/client';
import {
  CalendarTask,
  CalendarFilters,
  CalendarSettings,
  CreateEventData,
  UpdateEventData,
} from '@/lib/validations/calendar';
import { getMonthBounds, getWeekBounds, getDayBounds } from '@/lib/utils/date.utils';

// API functions - pobieranie zadań do wyświetlenia w kalendarzu
async function fetchCalendarTasks(
  familyId: string,
  startDate: Date,
  endDate: Date,
  filters?: CalendarFilters
): Promise<{ tasks: CalendarTask[]; count: number }> {
  const params = new URLSearchParams({
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
  });

  if (filters?.memberId) params.append('memberId', filters.memberId);
  if (filters?.taskStatus) {
    filters.taskStatus.forEach(status => params.append('taskStatus', status));
  }
  if (filters?.taskCategory) {
    filters.taskCategory.forEach(category => params.append('taskCategory', category));
  }
  if (filters?.showCompleted) params.append('showCompleted', 'true');

  const response = await fetch(`/api/families/${familyId}/calendar?${params}`);
  if (!response.ok) {
    throw new Error('Failed to fetch calendar tasks');
  }
  return response.json();
}

async function fetchCalendarSettings(familyId: string): Promise<CalendarSettings> {
  const response = await fetch(`/api/families/${familyId}/calendar/settings`);
  if (!response.ok) {
    throw new Error('Failed to fetch calendar settings');
  }
  return response.json();
}

async function updateCalendarSettings(
  familyId: string,
  settings: Partial<CalendarSettings>
): Promise<CalendarSettings> {
  const response = await fetch(`/api/families/${familyId}/calendar/settings`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(settings),
  });
  if (!response.ok) {
    throw new Error('Failed to update calendar settings');
  }
  return response.json();
}

async function createCalendarTask(familyId: string, data: CreateEventData): Promise<CalendarTask> {
  const response = await fetch(`/api/families/${familyId}/calendar/events`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to create calendar task');
  }
  return response.json();
}

async function updateCalendarTask(
  familyId: string,
  taskId: string,
  data: UpdateEventData
): Promise<CalendarTask> {
  const response = await fetch(`/api/families/${familyId}/calendar/events/${taskId}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!response.ok) {
    throw new Error('Failed to update calendar task');
  }
  return response.json();
}

async function deleteCalendarTask(familyId: string, taskId: string): Promise<void> {
  const response = await fetch(`/api/families/${familyId}/calendar/events/${taskId}`, {
    method: 'DELETE',
  });
  if (!response.ok) {
    throw new Error('Failed to delete calendar task');
  }
}

// Main calendar hook
export function useCalendar(familyId: string, view: CalendarView, date: Date) {
  const [filters, setFilters] = useState<CalendarFilters>({
    startDate: new Date(),
    endDate: new Date(),
    showCompleted: false,
  });

  // Calculate date bounds based on view
  const dateBounds = useMemo(() => {
    switch (view) {
      case CalendarView.MONTH:
        return getMonthBounds(date);
      case CalendarView.WEEK:
        return getWeekBounds(date, 'MONDAY'); // TODO: Get from settings
      case CalendarView.DAY:
        return getDayBounds(date);
      case CalendarView.AGENDA:
        return getWeekBounds(date, 'MONDAY');
      default:
        return getMonthBounds(date);
    }
  }, [view, date]);

  // Merge filters with date bounds for query
  const queryFilters = useMemo(
    () => ({
      ...filters,
      startDate: dateBounds.start,
      endDate: dateBounds.end,
    }),
    [filters, dateBounds.start, dateBounds.end]
  );

  // Fetch tasks dla kalendarza
  const {
    data: tasksData,
    isLoading: isLoadingTasks,
    error: tasksError,
    refetch: refetchTasks,
  } = useQuery({
    queryKey: [
      'calendar-tasks',
      familyId,
      dateBounds.start.toISOString(),
      dateBounds.end.toISOString(),
      filters.memberId,
      filters.taskStatus,
      filters.taskCategory,
      filters.showCompleted,
    ],
    queryFn: () =>
      fetchCalendarTasks(familyId, queryFilters.startDate, queryFilters.endDate, queryFilters),
    enabled: !!familyId,
  });

  return {
    tasks: tasksData?.tasks || [],
    tasksCount: tasksData?.count || 0,
    isLoadingTasks,
    tasksError,
    filters: queryFilters,
    setFilters,
    refetchTasks,
  };
}

// Calendar tasks hook with mutations
export function useCalendarTasks(familyId: string, startDate: Date, endDate: Date) {
  const queryClient = useQueryClient();

  const {
    data: tasksData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['calendar-tasks', familyId, startDate.toISOString(), endDate.toISOString()],
    queryFn: () => fetchCalendarTasks(familyId, startDate, endDate),
    enabled: !!familyId,
  });

  return {
    tasks: tasksData?.tasks || [],
    count: tasksData?.count || 0,
    isLoading,
    error,
    refetch,
  };
}

// Calendar settings hook
export function useCalendarSettings(familyId: string) {
  const queryClient = useQueryClient();

  const {
    data: settings,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['calendar-settings', familyId],
    queryFn: () => fetchCalendarSettings(familyId),
    enabled: !!familyId,
  });

  const updateSettingsMutation = useMutation({
    mutationFn: (data: Partial<CalendarSettings>) => updateCalendarSettings(familyId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-settings', familyId] });
    },
  });

  return {
    settings,
    isLoading,
    error,
    refetch,
    updateSettings: updateSettingsMutation.mutate,
    isUpdatingSettings: updateSettingsMutation.isPending,
    updateSettingsError: updateSettingsMutation.error,
  };
}

// Task mutations hook dla kalendarza
export function useTaskMutations(familyId: string) {
  const queryClient = useQueryClient();

  const createTaskMutation = useMutation({
    mutationFn: (data: CreateEventData) => createCalendarTask(familyId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-tasks', familyId] });
    },
  });

  const updateTaskMutation = useMutation({
    mutationFn: ({ taskId, data }: { taskId: string; data: UpdateEventData }) =>
      updateCalendarTask(familyId, taskId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-tasks', familyId] });
    },
  });

  const deleteTaskMutation = useMutation({
    mutationFn: (taskId: string) => deleteCalendarTask(familyId, taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['calendar-tasks', familyId] });
    },
  });

  return {
    createTask: createTaskMutation.mutate,
    updateTask: updateTaskMutation.mutate,
    deleteTask: deleteTaskMutation.mutate,
    isCreating: createTaskMutation.isPending,
    isUpdating: updateTaskMutation.isPending,
    isDeleting: deleteTaskMutation.isPending,
    createError: createTaskMutation.error,
    updateError: updateTaskMutation.error,
    deleteError: deleteTaskMutation.error,
  };
}

/**
 * Główny hook zarządzający stanem kalendarza
 *
 * UWAGA:
 * - selectedTask to zadanie wyświetlane w kalendarzu
 * - tasks to lista zadań do wyświetlenia
 * - handleTaskClick/handleTaskDrop obsługują interakcje z zadaniami
 * - Zachowana kompatybilność wsteczna przez aliasy
 */
export function useCalendarState(familyId: string) {
  const [currentView, setCurrentView] = useState<CalendarView>(CalendarView.MONTH);
  const [currentDate, setCurrentDate] = useState(() => new Date());
  const [selectedTask, setSelectedTask] = useState<CalendarTask | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const { tasks, isLoadingTasks, filters, setFilters, refetchTasks } = useCalendar(
    familyId,
    currentView,
    currentDate
  );

  const { settings, updateSettings } = useCalendarSettings(familyId);

  const { createTask, updateTask, deleteTask, isCreating, isUpdating, isDeleting } =
    useTaskMutations(familyId);

  const handleTaskClick = useCallback((task: CalendarTask) => {
    setSelectedTask(task);
    setIsDetailsModalOpen(true);
  }, []);

  const handleTaskDrop = useCallback(
    (task: CalendarTask, newDate: Date) => {
      updateTask({
        taskId: task.id,
        data: {
          startDate: newDate,
          endDate: task.end
            ? new Date(newDate.getTime() + (task.end.getTime() - task.start.getTime()))
            : undefined,
        },
      });
    },
    [updateTask]
  );

  const handleSlotSelect = useCallback((startDate: Date, endDate?: Date) => {
    setCurrentDate(startDate);
    setIsCreateDialogOpen(true);
  }, []);

  const handleCreateTask = useCallback(
    (data: CreateEventData) => {
      return new Promise<void>((resolve, reject) => {
        createTask(data, {
          onSuccess: () => resolve(),
          onError: error => reject(error),
        });
      });
    },
    [createTask]
  );

  const handleUpdateTask = useCallback(
    (taskId: string, data: UpdateEventData) => {
      return new Promise<void>((resolve, reject) => {
        updateTask(
          { taskId, data },
          {
            onSuccess: () => resolve(),
            onError: error => reject(error),
          }
        );
      });
    },
    [updateTask]
  );

  const handleDeleteTask = useCallback(
    (taskId: string) => {
      return new Promise<void>((resolve, reject) => {
        deleteTask(taskId, {
          onSuccess: () => resolve(),
          onError: error => reject(error),
        });
      });
    },
    [deleteTask]
  );

  const closeModals = useCallback(() => {
    setSelectedTask(null);
    setIsDetailsModalOpen(false);
    setIsCreateDialogOpen(false);
  }, []);

  return {
    // State
    currentView,
    currentDate,
    selectedTask,
    isCreateDialogOpen,
    isDetailsModalOpen,

    // Data
    tasks,
    settings,
    filters,

    // Loading states
    isLoadingTasks,
    isCreating,
    isUpdating,
    isDeleting,

    // Actions
    setCurrentView,
    setCurrentDate,
    setFilters,
    handleTaskClick,
    handleTaskDrop,
    handleSlotSelect,
    handleCreateTask,
    handleUpdateTask,
    handleDeleteTask,
    updateSettings,
    refetchTasks,
    closeModals,

    // Modal controls
    setIsCreateDialogOpen,
    setIsDetailsModalOpen,
    setSelectedTask,

    // Kompatybilność wsteczna - aliasy dla starych nazw
    events: tasks, // alias dla tasks
    selectedEvent: selectedTask, // alias dla selectedTask
    isLoadingEvents: isLoadingTasks, // alias dla isLoadingTasks
    handleEventClick: handleTaskClick, // alias dla handleTaskClick
    handleEventDrop: handleTaskDrop, // alias dla handleTaskDrop
    refetchEvents: refetchTasks, // alias dla refetchTasks
    setSelectedEvent: setSelectedTask, // alias dla setSelectedTask
  };
}
