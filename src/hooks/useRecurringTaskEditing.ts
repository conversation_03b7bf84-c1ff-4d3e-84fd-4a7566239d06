/**
 * Hook do zarządzania edycją zadań cyklicznych
 * Obsługuje logikę edycji pojedynczego wystąpienia vs całej serii
 */

import { useState, useCallback } from 'react';
import { CalendarTask } from '@/lib/validations/calendar';
import { useTaskMutations } from './useTasks';
import { format } from 'date-fns';

interface UseRecurringTaskEditingOptions {
  familyId: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function useRecurringTaskEditing({
  familyId,
  onSuccess,
  onError,
}: UseRecurringTaskEditingOptions) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingTask, setEditingTask] = useState<CalendarTask | null>(null);
  const [loading, setLoading] = useState(false);

  const { createTask, updateTask } = useTaskMutations(familyId);

  const handleEditRecurringTask = useCallback((task: CalendarTask) => {
    if (!task.isRecurring) {
      // Dla zadań jednorazowych, otwórz normalny formularz edycji
      onEditDirectly?.(task);
      return;
    }

    // Dla zadań cyklicznych, pokaż dialog wyboru
    setEditingTask(task);
    setIsDialogOpen(true);
  }, []);

  const handleEditOccurrence = useCallback(
    async (task: CalendarTask) => {
      if (!task.originalTaskId) {
        onError?.('Błąd: nie można edytować wystąpienia - brak ID oryginalnego zadania');
        return;
      }

      setLoading(true);
      try {
        // Tworzymy nowe zadanie jednorazowe dla tego konkretnego wystąpienia
        const newTaskData = {
          title: task.title,
          description: task.description || '',
          startDate: task.start,
          endDate: task.end,
          allDay: task.allDay,
          color: task.color,
          frequency: 'ONCE' as const, // Wystąpienie jest zawsze jednorazowe
          recurrenceRule: undefined,
          // Kopiujemy przypisania z oryginalnego zadania
          assignments: task.assignments?.map(a => a.memberId) || [],
        };

        await createTask(newTaskData);

        // TODO: Dodaj do bazy informację o tym wystąpieniu jako "wyjątek" w serii
        // Możemy to zrobić przez dodanie pola excludedDates do Task model

        onSuccess?.();
        setIsDialogOpen(false);
        setEditingTask(null);
      } catch (error) {
        onError?.(error instanceof Error ? error.message : 'Błąd podczas edycji wystąpienia');
      } finally {
        setLoading(false);
      }
    },
    [createTask, onSuccess, onError]
  );

  const handleEditSeries = useCallback(
    async (originalTaskId: string) => {
      setLoading(true);
      try {
        // Pobierz pełne dane oryginalnego zadania z bazy danych
        // Pobierz pełne dane oryginalnego zadania z bazy danych
        console.log('🔄 useRecurringTaskEditing: Pobieranie oryginalnego zadania:', originalTaskId);

        const response = await fetch(`/api/families/${familyId}/tasks/${originalTaskId}`);
        if (!response.ok) {
          throw new Error('Nie udało się pobrać danych oryginalnego zadania');
        }

        const originalTask = await response.json();
        console.log(
          '✅ useRecurringTaskEditing: Pobrano zadanie z frequency:',
          originalTask.frequency
        );

        // Przekazujemy pełne dane oryginalnego zadania do komponentu nadrzędnego
        onEditDirectly?.(originalTask);

        setIsDialogOpen(false);
        setEditingTask(null);
      } catch (error) {
        console.error('❌ Błąd podczas pobierania danych oryginalnego zadania:', error);
        onError?.(error instanceof Error ? error.message : 'Błąd podczas edycji serii');
      } finally {
        setLoading(false);
      }
    },
    [familyId, onError]
  );

  const closeDialog = useCallback(() => {
    setIsDialogOpen(false);
    setEditingTask(null);
  }, []);

  // Callback który powinien być ustawiony przez komponent nadrzędny
  let onEditDirectly: ((task: CalendarTask) => void) | undefined;

  const setOnEditDirectly = useCallback((callback: (task: CalendarTask) => void) => {
    onEditDirectly = callback;
  }, []);

  return {
    // State
    isDialogOpen,
    editingTask,
    loading,

    // Actions
    handleEditRecurringTask,
    handleEditOccurrence,
    handleEditSeries,
    closeDialog,
    setOnEditDirectly,
  };
}

/**
 * Utility funkcje dla zadań cyklicznych
 */
export function isRecurringTaskOccurrence(task: CalendarTask): boolean {
  return !!(task.isRecurring && task.originalTaskId);
}

export function getRecurringTaskDisplayInfo(task: CalendarTask): {
  isOccurrence: boolean;
  seriesInfo?: string;
  occurrenceInfo?: string;
} {
  if (!task.isRecurring) {
    return { isOccurrence: false };
  }

  const isOccurrence = !!task.originalTaskId;

  if (isOccurrence) {
    return {
      isOccurrence: true,
      occurrenceInfo: `Wystąpienie z ${format(task.start, 'PPP')}`,
      seriesInfo: `Część serii cyklicznej`,
    };
  }

  return {
    isOccurrence: false,
    seriesInfo: `Zadanie cykliczne - ${task.frequency?.toLowerCase()}`,
  };
}

/**
 * Sprawdza czy zadanie można edytować jako serię
 */
export function canEditTaskSeries(task: CalendarTask): boolean {
  // Można edytować serię jeśli:
  // 1. To jest zadanie cykliczne
  // 2. Nie jest to zadanie zakończone/archiwalne
  // 3. Ma przypisane originalTaskId (jest wystąpieniem) lub jest zadaniem głównym

  return task.isRecurring && !isTaskCompleted(task);
}

/**
 * Sprawdza czy zadanie jest zakończone (pomocnicza funkcja)
 * TODO: Dodać proper check based on task status
 */
function isTaskCompleted(task: CalendarTask): boolean {
  // Placeholder - w rzeczywistej implementacji sprawdzalibyśmy status zadania
  return false;
}

/**
 * Generuje opcje edycji dla zadania cyklicznego
 */
export function getEditOptions(task: CalendarTask): {
  canEditOccurrence: boolean;
  canEditSeries: boolean;
  requiresDialog: boolean;
} {
  if (!task.isRecurring) {
    return {
      canEditOccurrence: false,
      canEditSeries: false,
      requiresDialog: false,
    };
  }

  const isOccurrence = isRecurringTaskOccurrence(task);
  const canEditSeries = canEditTaskSeries(task);

  return {
    canEditOccurrence: isOccurrence,
    canEditSeries,
    requiresDialog: isOccurrence && canEditSeries,
  };
}
