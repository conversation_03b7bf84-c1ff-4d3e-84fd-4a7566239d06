'use client';

import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { FamilyRole, FamilyMember } from '@prisma/client';
import { Permission, hasPermission, getRoleDisplayName } from '@/lib/auth';

export interface UserWithRole extends FamilyMember {
  user: {
    id: string;
    email: string;
    firstName: string | null;
    lastName: string | null;
  };
  family: {
    id: string;
    name: string;
    description: string | null;
    members: {
      id: string;
      user: {
        id: string;
        firstName: string | null;
        lastName: string | null;
        email: string;
      };
    }[];
    _count: {
      tasks: number;
      members: number;
    };
  };
}

export function useAuth() {
  const { user, isLoaded, isSignedIn } = useUser();

  return {
    user,
    isLoaded,
    isSignedIn,
    userId: user?.id,
    email: user?.emailAddresses[0]?.emailAddress,
    firstName: user?.firstName,
    lastName: user?.lastName,
  };
}

export function useFamilyRole(familyId: string | undefined) {
  const { userId } = useAuth();
  const [familyMember, setFamilyMember] = useState<UserWithRole | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!userId || !familyId) {
      setFamilyMember(null);
      setLoading(false);
      return;
    }

    const fetchFamilyRole = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/family/${familyId}/member`);

        if (!response.ok) {
          if (response.status === 404) {
            setFamilyMember(null);
            return;
          }
          throw new Error('Failed to fetch family role');
        }

        const data = await response.json();
        setFamilyMember(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setFamilyMember(null);
      } finally {
        setLoading(false);
      }
    };

    fetchFamilyRole();
  }, [userId, familyId]);

  return {
    familyMember,
    role: familyMember?.role,
    roleDisplayName: familyMember?.role ? getRoleDisplayName(familyMember.role) : null,
    isOwner: familyMember?.role === FamilyRole.OWNER,
    isParent: familyMember?.role === FamilyRole.PARENT,
    isGuardian: familyMember?.role === FamilyRole.GUARDIAN,
    isChild: familyMember?.role === FamilyRole.CHILD,
    isAdult: familyMember?.isAdult || false,
    points: familyMember?.points || 0,
    loading,
    error,
  };
}

export function useCanManageTasks(familyId: string | undefined) {
  const { role } = useFamilyRole(familyId);

  return {
    canCreateTasks: role ? hasPermission(role, 'MANAGE_TASKS') : false,
    canEditTasks: role ? hasPermission(role, 'MANAGE_TASKS') : false,
    canDeleteTasks: role ? hasPermission(role, 'MANAGE_TASKS') : false,
    canAssignTasks: role ? hasPermission(role, 'ASSIGN_TASKS') : false,
    canViewTasks: role ? hasPermission(role, 'VIEW_TASKS') : false,
    canCompleteTasks: role ? hasPermission(role, 'COMPLETE_TASKS') : false,
  };
}

export function useCanManageFamily(familyId: string | undefined) {
  const { role } = useFamilyRole(familyId);

  return {
    canManageFamily: role ? hasPermission(role, 'MANAGE_FAMILY') : false,
    canManageMembers: role ? hasPermission(role, 'MANAGE_MEMBERS') : false,
    canViewStatistics: role ? hasPermission(role, 'VIEW_STATISTICS') : false,
  };
}

export function usePermission(permission: Permission, familyId: string | undefined) {
  const { role } = useFamilyRole(familyId);

  return role ? hasPermission(role, permission) : false;
}

export function useUserFamilies() {
  const { userId } = useAuth();
  const [families, setFamilies] = useState<UserWithRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFamilies = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/user/families');

      if (!response.ok) {
        // Lepsze obsługiwanie różnych typów błędów
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.error || `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          throw new Error('Nie jesteś zalogowany');
        } else if (response.status === 404) {
          throw new Error('Użytkownik nie został znaleziony');
        } else if (response.status >= 500) {
          throw new Error('Wystąpił błąd serwera. Spróbuj ponownie później.');
        } else {
          throw new Error(errorMessage);
        }
      }

      const data = await response.json();
      setFamilies(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('Error fetching families:', err);

      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Wystąpił nieoczekiwany błąd podczas pobierania rodzin');
      }

      setFamilies([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!userId) {
      setFamilies([]);
      setLoading(false);
      setError(null);
      return;
    }

    fetchFamilies();
  }, [userId]);

  return {
    families,
    loading,
    error,
    refetch: fetchFamilies,
  };
}

export function useUserInvitations() {
  const { userId } = useAuth();
  const [invitations, setInvitations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchInvitations = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/user/invitations');

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        const errorMessage = errorData?.error || `HTTP ${response.status}: ${response.statusText}`;

        if (response.status === 401) {
          throw new Error('Nie jesteś zalogowany');
        } else if (response.status >= 500) {
          throw new Error('Wystąpił błąd serwera. Spróbuj ponownie później.');
        } else {
          throw new Error(errorMessage);
        }
      }

      const data = await response.json();
      setInvitations(Array.isArray(data) ? data : []);
    } catch (err) {
      console.error('Error fetching invitations:', err);

      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Wystąpił nieoczekiwany błąd podczas pobierania zaproszeń');
      }

      setInvitations([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!userId) {
      setInvitations([]);
      setLoading(false);
      setError(null);
      return;
    }

    fetchInvitations();
  }, [userId]);

  return {
    invitations,
    loading,
    error,
    refetch: fetchInvitations,
  };
}
