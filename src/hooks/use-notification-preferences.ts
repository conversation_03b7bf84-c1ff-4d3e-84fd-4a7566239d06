'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface NotificationPreferences {
  id: string;
  emailNotificationsEnabled: boolean;
  notifyTaskAssigned: boolean;
  notifyTaskCompleted: boolean;
  notifyTaskVerified: boolean;
  notifyTaskReminder: boolean;
  notifyFamilyInvitations: boolean;
  notifySuspiciousActivity: boolean;
}

const NOTIFICATION_PREFERENCES_KEY = ['user', 'preferences', 'notifications'] as const;

export function useNotificationPreferences() {
  return useQuery({
    queryKey: NOTIFICATION_PREFERENCES_KEY,
    queryFn: async (): Promise<NotificationPreferences> => {
      const response = await fetch('/api/user/preferences/notifications');
      if (!response.ok) {
        throw new Error('Nie udało się pobrać ustawień powiadomień');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUpdateNotificationPreferences() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (preferences: Partial<NotificationPreferences>) => {
      const response = await fetch('/api/user/preferences/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      if (!response.ok) {
        throw new Error('Nie udało się zapisać preferencji');
      }

      return response.json();
    },
    onSuccess: updatedPreferences => {
      // Update the cache with new data
      queryClient.setQueryData(NOTIFICATION_PREFERENCES_KEY, updatedPreferences);
      toast.success('Ustawienia powiadomień zostały zapisane');
    },
    onError: error => {
      console.error('Error saving notification preferences:', error);
      toast.error('Nie udało się zapisać ustawień powiadomień');
    },
  });
}
