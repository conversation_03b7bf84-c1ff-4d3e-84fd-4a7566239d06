import { toast as sonnerToast } from 'sonner';

interface Toast {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
}

export const useToast = () => {
  const toast = ({ title, description, variant = 'default' }: Toast) => {
    const message = description ? `${title} - ${description}` : title;

    switch (variant) {
      case 'destructive':
        sonnerToast.error(title, description ? { description } : undefined);
        break;
      case 'success':
        sonnerToast.success(title, description ? { description } : undefined);
        break;
      case 'default':
      default:
        sonnerToast(title, description ? { description } : undefined);
        break;
    }
  };

  return { toast };
};
