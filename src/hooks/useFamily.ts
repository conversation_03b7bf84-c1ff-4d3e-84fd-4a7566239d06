'use client';

import { useState, useEffect } from 'react';
import { Family, FamilyMember, User, Task, FamilyInvitation } from '@prisma/client';

type FamilyWithDetails = Family & {
  members: (FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  })[];
  tasks: Task[];
  invitations: FamilyInvitation[];
  _count: {
    tasks: number;
    members: number;
  };
};

type FamilyMemberWithDetails = FamilyMember & {
  user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  assignedTasks: Task[];
  _count: {
    completedTasks: number;
    assignedTasks: number;
  };
};

type FamilyInvitationWithDetails = FamilyInvitation & {
  family: Family & {
    members: (FamilyMember & {
      user: Pick<User, 'id' | 'firstName' | 'lastName'>;
    })[];
    _count: {
      members: number;
    };
  };
};

export function useFamily(familyId?: string) {
  const [family, setFamily] = useState<FamilyWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!familyId) {
      setFamily(null);
      setLoading(false);
      return;
    }

    const fetchFamily = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/families/${familyId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch family');
        }

        const familyData = await response.json();
        setFamily(familyData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setFamily(null);
      } finally {
        setLoading(false);
      }
    };

    fetchFamily();
  }, [familyId]);

  const refetch = () => {
    if (familyId) {
      const fetchFamily = async () => {
        try {
          const response = await fetch(`/api/families/${familyId}`);
          if (response.ok) {
            const familyData = await response.json();
            setFamily(familyData);
          }
        } catch (err) {
          console.error('Failed to refetch family:', err);
        }
      };
      fetchFamily();
    }
  };

  return {
    family,
    loading,
    error,
    refetch,
  };
}

export function useFamilyMembers(familyId: string) {
  const [members, setMembers] = useState<FamilyMemberWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMembers = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/families/${familyId}/members`);
        if (!response.ok) {
          throw new Error('Failed to fetch family members');
        }

        const membersData = await response.json();
        setMembers(membersData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setMembers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchMembers();
  }, [familyId]);

  const refetch = () => {
    const fetchMembers = async () => {
      try {
        const response = await fetch(`/api/families/${familyId}/members`);
        if (response.ok) {
          const membersData = await response.json();
          setMembers(membersData);
        }
      } catch (err) {
        console.error('Failed to refetch members:', err);
      }
    };
    fetchMembers();
  };

  return {
    members,
    loading,
    error,
    refetch,
  };
}

export function useFamilyInvitations(familyId?: string) {
  const [invitations, setInvitations] = useState<FamilyInvitationWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchInvitations = async () => {
      try {
        setLoading(true);
        setError(null);

        let url = '/api/families/invitations';
        if (familyId) {
          url = `/api/families/${familyId}/invitations`;
        }

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error('Failed to fetch invitations');
        }

        const invitationsData = await response.json();
        setInvitations(invitationsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setInvitations([]);
      } finally {
        setLoading(false);
      }
    };

    fetchInvitations();
  }, [familyId]);

  const refetch = () => {
    const fetchInvitations = async () => {
      try {
        let url = '/api/families/invitations';
        if (familyId) {
          url = `/api/families/${familyId}/invitations`;
        }

        const response = await fetch(url);
        if (response.ok) {
          const invitationsData = await response.json();
          setInvitations(invitationsData);
        }
      } catch (err) {
        console.error('Failed to refetch invitations:', err);
      }
    };
    fetchInvitations();
  };

  return {
    invitations,
    loading,
    error,
    refetch,
  };
}

export function useUserFamilies() {
  const [families, setFamilies] = useState<Family[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFamilies = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/families');
        if (!response.ok) {
          throw new Error('Failed to fetch families');
        }

        const familiesData = await response.json();
        setFamilies(familiesData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setFamilies([]);
      } finally {
        setLoading(false);
      }
    };

    fetchFamilies();
  }, []);

  const refetch = () => {
    const fetchFamilies = async () => {
      try {
        const response = await fetch('/api/families');
        if (response.ok) {
          const familiesData = await response.json();
          setFamilies(familiesData);
        }
      } catch (err) {
        console.error('Failed to refetch families:', err);
      }
    };
    fetchFamilies();
  };

  return {
    families,
    loading,
    error,
    refetch,
  };
}

// Hook for current family context
export function useCurrentFamily(familyId?: string) {
  const { family, loading: familyLoading, error: familyError, refetch } = useFamily(familyId);
  const [currentMember, setCurrentMember] = useState<FamilyMember | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!familyId) {
      setCurrentMember(null);
      setLoading(false);
      return;
    }

    const fetchCurrentMember = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/families/${familyId}/member`);
        if (!response.ok) {
          throw new Error('Failed to fetch current member');
        }

        const memberData = await response.json();
        setCurrentMember(memberData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setCurrentMember(null);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentMember();
  }, [familyId]);

  return {
    family,
    currentMember,
    loading: familyLoading || loading,
    error: familyError || error,
    refetch,
  };
}
