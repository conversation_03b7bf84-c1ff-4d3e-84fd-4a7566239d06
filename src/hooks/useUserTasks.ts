'use client';

import { useState, useEffect, useCallback } from 'react';
import { TaskFilters, TaskWithRelations } from './useTasks';

// Extended TaskWithRelations to include family information
export type UserTaskWithRelations = TaskWithRelations & {
  family: {
    id: string;
    name: string;
  };
};

// Hook for user tasks across all families
export function useUserTasks(filters?: TaskFilters) {
  const [tasks, setTasks] = useState<UserTaskWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const buildQueryString = useCallback((filters?: TaskFilters) => {
    if (!filters) return '';

    const params = new URLSearchParams();

    if (filters.status) {
      params.append('status', filters.status.join(','));
    }
    if (filters.assignedMemberIds && filters.assignedMemberIds.length > 0) {
      params.append('assignedMemberIds', filters.assignedMemberIds.join(','));
    }
    if (filters.dueDate?.from) {
      params.append('dueDateFrom', filters.dueDate.from.toISOString());
    }
    if (filters.dueDate?.to) {
      params.append('dueDateTo', filters.dueDate.to.toISOString());
    }
    if (filters.weight) {
      params.append('weight', filters.weight.join(','));
    }
    if (filters.frequency) {
      params.append('frequency', filters.frequency.join(','));
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    if (filters.completionStatus && filters.completionStatus.length > 0) {
      params.append('completionStatus', filters.completionStatus.join(','));
    }
    if (filters.pointsRange?.min !== undefined) {
      params.append('pointsMin', filters.pointsRange.min.toString());
    }
    if (filters.pointsRange?.max !== undefined) {
      params.append('pointsMax', filters.pointsRange.max.toString());
    }
    if (filters.includeCompleted) {
      params.append('includeCompleted', 'true');
    }

    return params.toString() ? `?${params.toString()}` : '';
  }, []);

  const fetchTasks = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const queryString = buildQueryString(filters);
      const response = await fetch(`/api/user/tasks${queryString}`);

      if (!response.ok) {
        throw new Error('Failed to fetch user tasks');
      }

      const tasksData = await response.json();

      // Deduplikacja zadań jako dodatkowe zabezpieczenie
      const uniqueTasks = tasksData.reduce(
        (acc: UserTaskWithRelations[], task: UserTaskWithRelations) => {
          if (!acc.find(t => t.id === task.id)) {
            acc.push(task);
          }
          return acc;
        },
        []
      );

      setTasks(uniqueTasks);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setTasks([]);
    } finally {
      setLoading(false);
    }
  }, [filters, buildQueryString]);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  const refetch = useCallback(() => {
    fetchTasks();
  }, [fetchTasks]);

  return {
    tasks,
    loading,
    error,
    refetch,
  };
}

// Hook for user task statistics
export function useUserTaskStats() {
  const [stats, setStats] = useState<{
    totalTasks: number;
    activeTasks: number;
    completedTasks: number;
    overdueTasks: number;
    pendingVerification: number;
    myAssignedTasks: number;
    familiesCount: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch all user tasks to calculate stats
      const response = await fetch('/api/user/tasks');

      if (!response.ok) {
        throw new Error('Failed to fetch user tasks for stats');
      }

      const tasks: UserTaskWithRelations[] = await response.json();
      const now = new Date();

      // Get unique families
      const uniqueFamilies = new Set(tasks.map(task => task.family.id));

      const activeTasks = tasks.filter(
        task => task.status === 'ACTIVE' && !task.completions.some(c => c.status === 'APPROVED')
      );

      const completedTasks = tasks.filter(task =>
        task.completions.some(c => c.status === 'APPROVED')
      );

      const overdueTasks = activeTasks.filter(task => task.dueDate && new Date(task.dueDate) < now);

      const pendingVerification = tasks.filter(task =>
        task.completions.some(c => c.status === 'PENDING')
      );

      // Get current user ID from one of the tasks (if any)
      let myAssignedTasks = 0;
      if (tasks.length > 0) {
        // We'll need to get current user's member IDs across all families
        // For now, we'll approximate this by counting tasks assigned to any member
        myAssignedTasks = activeTasks.filter(
          task => task.assignments && task.assignments.length > 0
        ).length;
      }

      setStats({
        totalTasks: tasks.length,
        activeTasks: activeTasks.length,
        completedTasks: completedTasks.length,
        overdueTasks: overdueTasks.length,
        pendingVerification: pendingVerification.length,
        myAssignedTasks,
        familiesCount: uniqueFamilies.size,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const refetch = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
}
