'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  Task,
  TaskStatus,
  TaskWeight,
  TaskFrequency,
  TaskAssignmentType,
  TaskAssignment,
  FamilyMember,
  User,
  TaskCompletion,
  CompletionStatus,
} from '@prisma/client';

// Types for task with relations
export type TaskWithRelations = Task & {
  assignedByUser: FamilyMember & {
    user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
  };
  completions: (TaskCompletion & {
    completedBy: FamilyMember & {
      user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
    };
    verifiedBy?: FamilyMember & {
      user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
    };
  })[];
  assignments: (TaskAssignment & {
    member: FamilyMember & {
      user: Pick<User, 'id' | 'firstName' | 'lastName' | 'email'>;
    };
  })[];
  _count?: {
    completions: number;
  };
};

export interface TaskFilters {
  status?: TaskStatus[];
  assignedMemberIds?: string[];
  dueDate?: {
    from?: Date;
    to?: Date;
  };
  weight?: TaskWeight[];
  frequency?: TaskFrequency[];
  search?: string;
  // Nowe filtry dla kategorii
  completionStatus?: ('pending' | 'approved' | 'rejected' | 'overdue' | 'due_soon')[];
  pointsRange?: {
    min?: number;
    max?: number;
  };
  includeCompleted?: boolean;
}

export interface CreateTaskData {
  title: string;
  description?: string;
  points: number;
  weight: TaskWeight;
  frequency: TaskFrequency;
  dueDate?: Date;
  assignedMemberIds?: string[];
  assignmentType?: TaskAssignmentType;
  // Calendar-specific fields
  allDay?: boolean;
  color?: string;
  recurrenceRule?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface UpdateTaskData {
  title?: string;
  description?: string;
  points?: number;
  weight?: TaskWeight;
  frequency?: TaskFrequency;
  status?: TaskStatus;
  dueDate?: Date;
  assignedMemberIds?: string[];
  assignmentType?: TaskAssignmentType;
  // Calendar-specific fields
  allDay?: boolean;
  color?: string;
  recurrenceRule?: string;
  startDate?: Date;
  endDate?: Date;
}

// Hook for family tasks
export function useTasks(familyId: string, filters?: TaskFilters) {
  const [tasks, setTasks] = useState<TaskWithRelations[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const buildQueryString = useCallback((filters?: TaskFilters) => {
    if (!filters) return '';

    const params = new URLSearchParams();

    if (filters.status) {
      params.append('status', filters.status.join(','));
    }
    if (filters.assignedMemberIds && filters.assignedMemberIds.length > 0) {
      params.append('assignedMemberIds', filters.assignedMemberIds.join(','));
    }
    if (filters.dueDate?.from) {
      params.append('dueDateFrom', filters.dueDate.from.toISOString());
    }
    if (filters.dueDate?.to) {
      params.append('dueDateTo', filters.dueDate.to.toISOString());
    }
    if (filters.weight) {
      params.append('weight', filters.weight.join(','));
    }
    if (filters.frequency) {
      params.append('frequency', filters.frequency.join(','));
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    if (filters.completionStatus && filters.completionStatus.length > 0) {
      params.append('completionStatus', filters.completionStatus.join(','));
    }
    if (filters.pointsRange?.min !== undefined) {
      params.append('pointsMin', filters.pointsRange.min.toString());
    }
    if (filters.pointsRange?.max !== undefined) {
      params.append('pointsMax', filters.pointsRange.max.toString());
    }
    if (filters.includeCompleted) {
      params.append('includeCompleted', 'true');
    }

    return params.toString() ? `?${params.toString()}` : '';
  }, []);

  const fetchTasks = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const queryString = buildQueryString(filters);
      const response = await fetch(`/api/families/${familyId}/tasks${queryString}`);

      if (!response.ok) {
        throw new Error('Failed to fetch tasks');
      }

      const tasksData = await response.json();
      setTasks(tasksData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setTasks([]);
    } finally {
      setLoading(false);
    }
  }, [familyId, filters, buildQueryString]);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  const refetch = useCallback(() => {
    fetchTasks();
  }, [fetchTasks]);

  // Local state update for optimistic updates
  const updateTaskLocally = useCallback(
    (taskId: string, updater: (task: TaskWithRelations) => TaskWithRelations) => {
      setTasks(prevTasks => prevTasks.map(task => (task.id === taskId ? updater(task) : task)));
    },
    []
  );

  return {
    tasks,
    loading,
    error,
    refetch,
    updateTaskLocally,
  };
}

// Hook for a single task
export function useTask(familyId: string, taskId: string) {
  const [task, setTask] = useState<TaskWithRelations | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTask = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/families/${familyId}/tasks/${taskId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch task');
      }

      const taskData = await response.json();
      setTask(taskData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setTask(null);
    } finally {
      setLoading(false);
    }
  }, [familyId, taskId]);

  useEffect(() => {
    fetchTask();
  }, [fetchTask]);

  const refetch = useCallback(() => {
    fetchTask();
  }, [fetchTask]);

  return {
    task,
    loading,
    error,
    refetch,
  };
}

// Hook for task mutations (create, update, delete, complete, verify)
export function useTaskMutations(familyId: string) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createTask = useCallback(
    async (data: CreateTaskData): Promise<TaskWithRelations> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create task');
        }

        const task = await response.json();
        return task;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const updateTask = useCallback(
    async (taskId: string, data: UpdateTaskData): Promise<TaskWithRelations> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks/${taskId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update task');
        }

        const task = await response.json();
        return task;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const deleteTask = useCallback(
    async (taskId: string): Promise<void> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks/${taskId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to delete task');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const completeTask = useCallback(
    async (taskId: string, notes?: string): Promise<TaskCompletion> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks/${taskId}/complete`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ notes }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to complete task');
        }

        const completion = await response.json();
        return completion;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const verifyTask = useCallback(
    async (
      taskId: string,
      approved: boolean,
      feedback?: string,
      completionId?: string
    ): Promise<TaskCompletion> => {
      setLoading(true);
      setError(null);

      try {
        const url = completionId
          ? `/api/families/${familyId}/tasks/${taskId}/verify?completionId=${completionId}`
          : `/api/families/${familyId}/tasks/${taskId}/verify`;

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ approved, feedback }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to verify task');
        }

        const completion = await response.json();
        return completion;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const assignTask = useCallback(
    async (taskId: string, memberId: string): Promise<TaskWithRelations> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks/${taskId}/assign`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ memberId }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to assign task');
        }

        const task = await response.json();
        return task;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const unassignTask = useCallback(
    async (taskId: string): Promise<TaskWithRelations> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks/${taskId}/assign`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to unassign task');
        }

        const task = await response.json();
        return task;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  const bulkTaskOperation = useCallback(
    async (
      taskIds: string[],
      operation: 'assign' | 'complete' | 'delete' | 'suspend' | 'activate',
      assignedMemberIds?: string[]
    ): Promise<any> => {
      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/families/${familyId}/tasks/bulk`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ taskIds, operation, assignedMemberIds }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to perform bulk operation');
        }

        const result = await response.json();
        return result;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [familyId]
  );

  return {
    loading,
    error,
    createTask,
    updateTask,
    deleteTask,
    completeTask,
    verifyTask,
    assignTask,
    unassignTask,
    bulkTaskOperation,
  };
}

// Hook for task completion tracking
export function useTaskCompletion(familyId: string, taskId: string) {
  const [completions, setCompletions] = useState<TaskCompletion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCompletions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // This would require an additional API endpoint for task completions
      // For now, we'll get completions through the task endpoint
      const response = await fetch(`/api/families/${familyId}/tasks/${taskId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch task completions');
      }

      const taskData = await response.json();
      setCompletions(taskData.completions || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setCompletions([]);
    } finally {
      setLoading(false);
    }
  }, [familyId, taskId]);

  useEffect(() => {
    fetchCompletions();
  }, [fetchCompletions]);

  const refetch = useCallback(() => {
    fetchCompletions();
  }, [fetchCompletions]);

  return {
    completions,
    loading,
    error,
    refetch,
  };
}

// Hook for member tasks (tasks assigned to a specific member)
export function useMemberTasks(familyId: string, memberId: string, status?: TaskStatus[]) {
  const filters: TaskFilters = {
    assignedMemberIds: [memberId],
    status,
  };

  return useTasks(familyId, filters);
}

// Hook for task statistics
export function useTaskStats(familyId: string) {
  const [stats, setStats] = useState<{
    totalTasks: number;
    activeTasks: number;
    completedTasks: number;
    overdueTasks: number;
    pendingVerification: number;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // We'll calculate stats from the tasks data
      const response = await fetch(`/api/families/${familyId}/tasks`);

      if (!response.ok) {
        throw new Error('Failed to fetch tasks for stats');
      }

      const tasks: TaskWithRelations[] = await response.json();
      const now = new Date();

      const activeTasks = tasks.filter(
        task =>
          task.status === TaskStatus.ACTIVE &&
          !task.completions.some(c => c.status === CompletionStatus.APPROVED)
      );

      const completedTasks = tasks.filter(task =>
        task.completions.some(c => c.status === CompletionStatus.APPROVED)
      );

      const overdueTasks = activeTasks.filter(task => task.dueDate && new Date(task.dueDate) < now);

      const pendingVerification = tasks.filter(task =>
        task.completions.some(c => c.status === CompletionStatus.PENDING)
      );

      setStats({
        totalTasks: tasks.length,
        activeTasks: activeTasks.length,
        completedTasks: completedTasks.length,
        overdueTasks: overdueTasks.length,
        pendingVerification: pendingVerification.length,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, [familyId]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const refetch = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
}
