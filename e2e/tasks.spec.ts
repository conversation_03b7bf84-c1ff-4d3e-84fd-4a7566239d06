import { test, expect } from '@playwright/test';

// Helper functions for authentication and setup
async function loginAsUser(page: any, email: string) {
  await page.goto('/auth/sign-in');
  await page.fill('[name="identifier"]', email);
  await page.fill('[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  await page.waitForURL(/\/dashboard/);
}

async function createTask(page: any, title: string, assignedTo?: string) {
  await page.click('text=Dodaj zadanie, button:has-text("dodaj"), [data-testid="add-task"]');
  await page.fill('[name="title"]', title);

  if (assignedTo) {
    await page.click('[name="assignedTo"]');
    await page.click(`text=${assignedTo}`);
  }

  await page.click('button[type="submit"]');
}

async function switchUserContext(page: any, email: string) {
  // Simulate switching user context (logout + login)
  await page.click('[data-testid="user-menu"], .user-menu');
  await page.click('text=Wyloguj, text=Logout');
  await loginAsUser(page, email);
}

test.describe('Task Management', () => {
  test.beforeEach(async ({ page }) => {
    // Setup: Login as parent user
    await loginAsUser(page, '<EMAIL>');
    await page.goto('/dashboard');
  });

  test('should display tasks dashboard correctly', async ({ page }) => {
    await expect(page.locator('h1, h2')).toContainText(/zadania|tasks|kokpit|dashboard/i);

    // Should show task sections
    await expect(page.locator('.task-list, [data-testid="task-list"]')).toBeVisible();

    // Should have add task button
    await expect(page.locator('button:has-text("dodaj"), [data-testid="add-task"]')).toBeVisible();
  });

  test('should create a new task successfully', async ({ page }) => {
    await page.click('text=Dodaj zadanie, button:has-text("dodaj"), [data-testid="add-task"]');

    // Fill task form
    await page.fill('[name="title"]', 'Pozmywaj naczynia');
    await page.fill('[name="description"]', 'Umyj wszystkie naczynia po obiedzie');

    // Set points
    await page.fill('[name="points"]', '5');

    // Select weight
    await page.click('[name="weight"]');
    await page.click('text=Normalne, text=NORMAL');

    // Select frequency
    await page.click('[name="frequency"]');
    await page.click('text=Jednorazowe, text=ONCE');

    // Assign to someone
    await page.click('[name="assignedTo"]');
    await page.click('option:first-child, [data-value]:first-child');

    await page.click('button[type="submit"]');

    // Should close dialog and show task in list
    await expect(page.locator('.task-card, [data-testid="task-card"]')).toContainText(
      'Pozmywaj naczynia'
    );
  });

  test('should validate task creation form', async ({ page }) => {
    await page.click('text=Dodaj zadanie, button:has-text("dodaj"), [data-testid="add-task"]');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation errors
    await expect(page.locator('.error, [role="alert"], .text-red-500')).toContainText(
      /wymagane|required/i
    );
  });

  test('should complete task as assigned user', async ({ page }) => {
    // First create a task
    await createTask(page, 'Test Task for Completion');

    // Find the task card and complete it
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Test Task for Completion' });
    await expect(taskCard).toBeVisible();

    // Click to complete (might be checkbox, button, or the card itself)
    await taskCard.click();

    // If there's a completion dialog
    const completionDialog = page
      .locator('[role="dialog"], .modal')
      .filter({ hasText: /wykonane|complete/i });
    if (await completionDialog.isVisible()) {
      await page.fill('[name="notes"]', 'Zadanie wykonane pomyślnie');
      await page.click('button:has-text("potwierdź"), button:has-text("wykonane")');
    }

    // Task should show as completed
    await expect(taskCard).toHaveClass(/completed|done/);
  });

  test('should show task details', async ({ page }) => {
    await createTask(page, 'Task with Details');

    // Click on task to view details
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task with Details' });
    await taskCard.click();

    // Should show task details (either in modal or expanded view)
    await expect(page.locator('text=Task with Details')).toBeVisible();
  });

  test('should edit existing task', async ({ page }) => {
    await createTask(page, 'Task to Edit');

    // Find task and open edit menu
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task to Edit' });
    await taskCard.hover();

    // Click edit button (might be in dropdown menu)
    await page.click('button:has-text("edytuj"), [data-testid="edit-task"], .edit-button');

    // Update task title
    await page.fill('[name="title"]', 'Updated Task Title');
    await page.click('button[type="submit"]');

    // Should show updated title
    await expect(page.locator('.task-card, [data-testid="task-card"]')).toContainText(
      'Updated Task Title'
    );
  });

  test('should delete task', async ({ page }) => {
    await createTask(page, 'Task to Delete');

    // Find task and open menu
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task to Delete' });
    await taskCard.hover();

    // Click delete button
    await page.click('button:has-text("usuń"), [data-testid="delete-task"], .delete-button');

    // Confirm deletion
    await page.click(
      'button:has-text("tak"), button:has-text("usuń"), button:has-text("potwierdź")'
    );

    // Task should be removed
    await expect(
      page.locator('.task-card, [data-testid="task-card"]').filter({ hasText: 'Task to Delete' })
    ).not.toBeVisible();
  });

  test('should filter tasks by status', async ({ page }) => {
    // Create tasks with different statuses
    await createTask(page, 'Active Task');
    await createTask(page, 'Completed Task');

    // Apply status filter
    await page.click('[data-testid="status-filter"], select[name="status"], .filter-button');
    await page.click('text=Aktywne, text=ACTIVE, option[value="ACTIVE"]');

    // Should show only active tasks
    await expect(page.locator('.task-card, [data-testid="task-card"]')).toContainText(
      'Active Task'
    );
  });

  test('should assign task to family member', async ({ page }) => {
    await createTask(page, 'Task to Assign');

    // Find task and open assignment menu
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task to Assign' });
    await taskCard.hover();

    await page.click('button:has-text("przypisz"), [data-testid="assign-task"]');

    // Select family member
    await page.click('[name="assignedTo"]');
    await page.click('option:first-child, [data-value]:first-child');
    await page.click('button[type="submit"]');

    // Should show assignment
    await expect(taskCard).toContainText(/przypisane|assigned/i);
  });

  test('should show task points and weight', async ({ page }) => {
    await page.click('text=Dodaj zadanie, button:has-text("dodaj"), [data-testid="add-task"]');

    await page.fill('[name="title"]', 'High Value Task');
    await page.fill('[name="points"]', '10');

    // Select heavy weight
    await page.click('[name="weight"]');
    await page.click('text=Ciężkie, text=HEAVY');

    await page.click('button[type="submit"]');

    // Should display points and weight
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'High Value Task' });
    await expect(taskCard).toContainText('10');
  });

  test('should handle recurring tasks', async ({ page }) => {
    await page.click('text=Dodaj zadanie, button:has-text("dodaj"), [data-testid="add-task"]');

    await page.fill('[name="title"]', 'Daily Chore');

    // Set as daily recurring
    await page.click('[name="frequency"]');
    await page.click('text=Codziennie, text=DAILY');

    await page.click('button[type="submit"]');

    // Should show frequency indicator
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Daily Chore' });
    await expect(taskCard).toBeVisible();
  });

  test('should show due dates', async ({ page }) => {
    await page.click('text=Dodaj zadanie, button:has-text("dodaj"), [data-testid="add-task"]');

    await page.fill('[name="title"]', 'Task with Due Date');

    // Set due date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dateString = tomorrow.toISOString().split('T')[0];

    await page.fill('[name="dueDate"]', dateString);

    await page.click('button[type="submit"]');

    // Should show due date
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task with Due Date' });
    await expect(taskCard).toContainText(/termin|due/i);
  });
});

test.describe('Task Verification Flow', () => {
  test('should allow parent to verify completed task', async ({ page }) => {
    // Login as parent and create task
    await loginAsUser(page, '<EMAIL>');
    await page.goto('/dashboard');
    await createTask(page, 'Task for Verification', 'Child User');

    // Switch to child context and complete task
    await switchUserContext(page, '<EMAIL>');

    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task for Verification' });
    await taskCard.click();

    // Complete task
    await page.fill('[name="notes"]', 'Task completed by child');
    await page.click('button:has-text("wykonane")');

    // Switch back to parent
    await switchUserContext(page, '<EMAIL>');

    // Go to verification section
    await page.click('text=Do weryfikacji, text=Pending, [data-testid="pending-tasks"]');

    // Verify task
    const pendingTask = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task for Verification' });
    await pendingTask.click();

    await page.click('button:has-text("zatwierdź"), button:has-text("approve")');

    // Should move to completed
    await expect(page.locator('.completed-tasks, [data-testid="completed-tasks"]')).toContainText(
      'Task for Verification'
    );
  });

  test('should allow parent to reject completed task', async ({ page }) => {
    // Similar setup as above
    await loginAsUser(page, '<EMAIL>');
    await page.goto('/dashboard');
    await createTask(page, 'Task for Rejection', 'Child User');

    // Switch to child and complete
    await switchUserContext(page, '<EMAIL>');
    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task for Rejection' });
    await taskCard.click();
    await page.click('button:has-text("wykonane")');

    // Switch back to parent and reject
    await switchUserContext(page, '<EMAIL>');
    await page.click('text=Do weryfikacji, [data-testid="pending-tasks"]');

    const pendingTask = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Task for Rejection' });
    await pendingTask.click();

    await page.click('button:has-text("odrzuć"), button:has-text("reject")');
    await page.fill('[name="feedback"]', 'Please redo this task');
    await page.click('button:has-text("potwierdź")');

    // Should return to active tasks
    await expect(page.locator('.active-tasks, [data-testid="active-tasks"]')).toContainText(
      'Task for Rejection'
    );
  });
});

test.describe('Task Mobile Experience', () => {
  test('should work on mobile viewport', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await loginAsUser(page, '<EMAIL>');
    await page.goto('/dashboard');

    // Should show mobile-optimized layout
    await expect(page.locator('.mobile-header, [data-testid="mobile-header"]')).toBeVisible();

    // Task cards should be mobile-friendly
    await expect(page.locator('.task-card, [data-testid="task-card"]')).toBeVisible();
  });

  test('should support swipe gestures on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await loginAsUser(page, '<EMAIL>');
    await page.goto('/dashboard');
    await createTask(page, 'Swipeable Task');

    const taskCard = page
      .locator('.task-card, [data-testid="task-card"]')
      .filter({ hasText: 'Swipeable Task' });

    // Simulate swipe gesture
    await taskCard.hover();
    await page.mouse.down();
    await page.mouse.move(100, 0);
    await page.mouse.up();

    // Should reveal action buttons or complete task
    await expect(page.locator('.swipe-actions, [data-testid="swipe-actions"]')).toBeVisible();
  });
});
