import { test, expect } from '@playwright/test';

// Helper functions
async function loginAsUser(page: any, email: string) {
  await page.goto('/auth/sign-in');
  await page.fill('[name="identifier"]', email);
  await page.fill('[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  await page.waitForURL(/\/dashboard/);
}

async function createFamily(page: any, familyName: string) {
  await page.click('text=Utwórz rodzinę, button:has-text("utwórz"), [data-testid="create-family"]');
  await page.fill('[name="familyName"]', familyName);
  await page.click('button[type="submit"]');
}

async function getInvitationToken(email: string): Promise<string> {
  // In a real test, this would query the database or API
  // For now, return a mock token
  return 'mock-invitation-token-123';
}

test.describe('Family Management', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsUser(page, '<EMAIL>');
  });

  test('should display family dashboard correctly', async ({ page }) => {
    await page.goto('/family');

    // Should show family information
    await expect(page.locator('h1, h2')).toContainText(/rodzina|family/i);

    // Should show family members list
    await expect(page.locator('.family-members, [data-testid="family-members"]')).toBeVisible();

    // Should have invite member button
    await expect(
      page.locator('button:has-text("zaproś"), [data-testid="invite-member"]')
    ).toBeVisible();
  });

  test('should create new family successfully', async ({ page }) => {
    await page.goto('/onboarding');

    await page.fill('[name="familyName"]', 'Rodzina Testowa');
    await page.click('button:has-text("utwórz")');

    // Should redirect to dashboard
    await expect(page).toHaveURL(/\/dashboard/);
    await expect(page.locator('h1, h2')).toContainText(/kokpit|dashboard/i);
  });

  test('should validate family creation form', async ({ page }) => {
    await page.goto('/onboarding');

    // Try to submit empty form
    await page.click('button[type="submit"]');

    // Should show validation error
    await expect(page.locator('.error, [role="alert"], .text-red-500')).toBeVisible();
  });

  test('should send invitation to new member', async ({ page }) => {
    await page.goto('/family');

    // Click invite member
    await page.click(
      'text=Zaproś członka, button:has-text("zaproś"), [data-testid="invite-member"]'
    );

    // Fill invitation form
    await page.fill('[name="email"]', '<EMAIL>');
    await page.selectOption('[name="role"]', 'CHILD');
    await page.fill('[name="firstName"]', 'New');
    await page.fill('[name="lastName"]', 'Member');

    await page.click('button[type="submit"]');

    // Should show pending invitation
    await expect(
      page.locator('.invitation-pending, [data-testid="pending-invitation"]')
    ).toContainText('<EMAIL>');
  });

  test('should validate invitation form', async ({ page }) => {
    await page.goto('/family');
    await page.click('text=Zaproś członka, [data-testid="invite-member"]');

    // Try to submit with invalid email
    await page.fill('[name="email"]', 'invalid-email');
    await page.click('button[type="submit"]');

    // Should show validation error
    await expect(page.locator('.error, [role="alert"], .text-red-500')).toContainText(
      /email|e-mail/i
    );
  });

  test('should handle invitation acceptance', async ({ browser }) => {
    const context = await browser.newContext();
    const parentPage = await context.newPage();

    await loginAsUser(parentPage, '<EMAIL>');
    await parentPage.goto('/family');

    // Send invitation
    await parentPage.click('text=Zaproś członka, [data-testid="invite-member"]');
    await parentPage.fill('[name="email"]', '<EMAIL>');
    await parentPage.selectOption('[name="role"]', 'CHILD');
    await parentPage.click('button[type="submit"]');

    await expect(
      parentPage.locator('.invitation-pending, [data-testid="pending-invitation"]')
    ).toContainText('<EMAIL>');

    // Simulate new user accepting invitation
    const memberContext = await browser.newContext();
    const memberPage = await memberContext.newPage();

    // Get invitation token (in real test, this would come from email or API)
    const invitationToken = await getInvitationToken('<EMAIL>');
    await memberPage.goto(`/invitations/${invitationToken}`);

    // Should show invitation details
    await expect(memberPage.locator('h1, h2')).toContainText(/zaproszenie|invitation/i);
    await expect(memberPage.locator('text=<EMAIL>')).toBeVisible();

    // Accept invitation
    await memberPage.click('text=Akceptuj zaproszenie, button:has-text("akceptuj")');

    // Should redirect to registration or dashboard
    await expect(memberPage).toHaveURL(/\/(auth\/sign-up|dashboard)/);

    // Verify member appears in family list
    await parentPage.reload();
    await expect(parentPage.locator('.family-member, [data-testid="family-member"]')).toContainText(
      '<EMAIL>'
    );

    await memberContext.close();
    await context.close();
  });

  test('should reject invitation', async ({ browser }) => {
    const context = await browser.newContext();
    const memberPage = await context.newPage();

    const invitationToken = await getInvitationToken('<EMAIL>');
    await memberPage.goto(`/invitations/${invitationToken}`);

    // Reject invitation
    await memberPage.click('text=Odrzuć zaproszenie, button:has-text("odrzuć")');

    // Should show rejection confirmation
    await expect(memberPage.locator('text=odrzucone, text=rejected')).toBeVisible();

    await context.close();
  });

  test('should display family member roles correctly', async ({ page }) => {
    await page.goto('/family');

    // Should show different role badges
    const members = page.locator('.family-member, [data-testid="family-member"]');
    await expect(members.first()).toBeVisible();

    // Should show role (PARENT, CHILD, etc.)
    await expect(page.locator('.role-badge, [data-testid="role-badge"]')).toBeVisible();
  });

  test('should change member role', async ({ page }) => {
    await page.goto('/family');

    // Find member and open role menu
    const member = page.locator('.family-member, [data-testid="family-member"]').first();
    await member.hover();

    await page.click('button:has-text("zmień rolę"), [data-testid="change-role"]');

    // Select new role
    await page.selectOption('[name="role"]', 'PARENT');
    await page.click('button:has-text("zapisz")');

    // Should update role display
    await expect(page.locator('.role-badge, [data-testid="role-badge"]')).toContainText(
      /parent|rodzic/i
    );
  });

  test('should remove family member', async ({ page }) => {
    await page.goto('/family');

    // Find member and open menu
    const member = page.locator('.family-member, [data-testid="family-member"]').last();
    await member.hover();

    await page.click('button:has-text("usuń"), [data-testid="remove-member"]');

    // Confirm removal
    await page.click(
      'button:has-text("tak"), button:has-text("usuń"), button:has-text("potwierdź")'
    );

    // Member should be removed
    await expect(member).not.toBeVisible();
  });

  test('should show family statistics', async ({ page }) => {
    await page.goto('/family');

    // Should show family stats
    await expect(page.locator('.family-stats, [data-testid="family-stats"]')).toBeVisible();

    // Should show member count
    await expect(page.locator('text=członków, text=members')).toBeVisible();

    // Should show completed tasks count
    await expect(page.locator('text=zadań, text=tasks')).toBeVisible();
  });

  test('should edit family settings', async ({ page }) => {
    await page.goto('/family');

    // Open settings
    await page.click('button:has-text("ustawienia"), [data-testid="family-settings"]');

    // Edit family name
    await page.fill('[name="familyName"]', 'Updated Family Name');

    // Save changes
    await page.click('button:has-text("zapisz")');

    // Should show updated name
    await expect(page.locator('h1, h2')).toContainText('Updated Family Name');
  });

  test('should handle family deletion', async ({ page }) => {
    await page.goto('/family');

    // Open settings
    await page.click('button:has-text("ustawienia"), [data-testid="family-settings"]');

    // Find delete option
    await page.click('button:has-text("usuń rodzinę"), [data-testid="delete-family"]');

    // Type confirmation
    await page.fill('[name="confirmation"]', 'USUŃ');
    await page.click('button:has-text("usuń")');

    // Should redirect to onboarding
    await expect(page).toHaveURL(/\/onboarding/);
  });
});

test.describe('Family Permissions', () => {
  test('should respect parent permissions', async ({ page }) => {
    await loginAsUser(page, '<EMAIL>');
    await page.goto('/family');

    // Parent should see all management options
    await expect(
      page.locator('button:has-text("zaproś"), [data-testid="invite-member"]')
    ).toBeVisible();
    await expect(
      page.locator('button:has-text("ustawienia"), [data-testid="family-settings"]')
    ).toBeVisible();
  });

  test('should restrict child permissions', async ({ page }) => {
    await loginAsUser(page, '<EMAIL>');
    await page.goto('/family');

    // Child should have limited options
    await expect(
      page.locator('button:has-text("zaproś"), [data-testid="invite-member"]')
    ).not.toBeVisible();

    // Should see family members but not management options
    await expect(page.locator('.family-members, [data-testid="family-members"]')).toBeVisible();
  });

  test('should prevent unauthorized family access', async ({ page }) => {
    await loginAsUser(page, '<EMAIL>');

    // Try to access family page
    await page.goto('/family/unauthorized-family-id');

    // Should redirect or show access denied
    await expect(page).toHaveURL(/\/(dashboard|access-denied)/);
  });
});

test.describe('Family Onboarding', () => {
  test('should guide new user through family creation', async ({ page }) => {
    // Simulate new user
    await page.goto('/onboarding');

    // Should show welcome message
    await expect(page.locator('h1, h2')).toContainText(/witaj|welcome/i);

    // Should show family creation form
    await expect(page.locator('[name="familyName"]')).toBeVisible();

    // Should have helpful hints
    await expect(page.locator('.hint, .help-text, [data-testid="help"]')).toBeVisible();
  });

  test('should allow joining existing family', async ({ page }) => {
    // Use invitation link
    const invitationToken = await getInvitationToken('<EMAIL>');
    await page.goto(`/invitations/${invitationToken}`);

    // Should show join family option
    await expect(page.locator('text=dołącz, text=join')).toBeVisible();

    // Should show family details
    await expect(page.locator('.family-info, [data-testid="family-info"]')).toBeVisible();
  });

  test('should handle invalid invitation links', async ({ page }) => {
    await page.goto('/invitations/invalid-token-123');

    // Should show error message
    await expect(page.locator('.error, [role="alert"], .text-red-500')).toContainText(
      /nieprawidłowe|invalid|wygasło|expired/i
    );

    // Should provide alternative actions
    await expect(page.locator('a[href="/onboarding"], button:has-text("utwórz")')).toBeVisible();
  });
});

test.describe('Family Mobile Experience', () => {
  test('should work on mobile viewport', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await loginAsUser(page, '<EMAIL>');
    await page.goto('/family');

    // Should show mobile-optimized layout
    await expect(page.locator('.mobile-layout, [data-testid="mobile-layout"]')).toBeVisible();

    // Members list should be mobile-friendly
    await expect(page.locator('.family-member, [data-testid="family-member"]')).toBeVisible();
  });

  test('should support mobile navigation', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });

    await loginAsUser(page, '<EMAIL>');
    await page.goto('/family');

    // Should have mobile navigation
    await expect(page.locator('.mobile-nav, [data-testid="mobile-nav"]')).toBeVisible();

    // Tab bar should be visible
    await expect(page.locator('.tab-bar, [data-testid="tab-bar"]')).toBeVisible();
  });
});
