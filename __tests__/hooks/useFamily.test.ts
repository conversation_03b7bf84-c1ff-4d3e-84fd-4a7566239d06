import { renderHook, waitFor, act } from '@testing-library/react';
import { useFamily, useFamilyMembers, useCurrentFamily } from '@/hooks/useFamily';

// Mock fetch API
global.fetch = jest.fn();

const mockFamilyData = {
  id: 'family-1',
  name: '<PERSON><PERSON><PERSON>',
  description: '<PERSON><PERSON><PERSON> rod<PERSON>',
  members: [
    {
      id: 'member-1',
      familyId: 'family-1',
      userId: 'user-1',
      role: 'OWNER',
      points: 100,
      user: {
        id: 'user-1',
        firstName: 'Jan',
        lastName: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
      },
    },
  ],
  tasks: [],
  invitations: [],
  _count: {
    tasks: 0,
    members: 1,
  },
};

const mockMembersData = [
  {
    id: 'member-1',
    familyId: 'family-1',
    userId: 'user-1',
    role: 'OWNER',
    points: 100,
    user: {
      id: 'user-1',
      firstName: 'Jan',
      lastName: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
    },
    assignedTasks: [],
    _count: {
      completedTasks: 5,
      assignedTasks: 10,
    },
  },
];

describe('useFamily', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockFamilyData,
    });
  });

  describe('Podstawowa funkcjonalność', () => {
    it('powinien zwrócić dane rodziny po pomyślnym pobraniu', async () => {
      const { result } = renderHook(() => useFamily('family-1'));

      // Początkowo loading powinien być true
      expect(result.current.loading).toBe(true);
      expect(result.current.family).toBe(null);
      expect(result.current.error).toBe(null);

      // Czekaj na zakończenie ładowania
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.family).toEqual(mockFamilyData);
      expect(result.current.error).toBe(null);
      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1');
    });

    it('powinien obsłużyć brak familyId', () => {
      const { result } = renderHook(() => useFamily());

      expect(result.current.loading).toBe(false);
      expect(result.current.family).toBe(null);
      expect(result.current.error).toBe(null);
      expect(global.fetch).not.toHaveBeenCalled();
    });

    it('powinien obsłużyć pusty familyId', () => {
      const { result } = renderHook(() => useFamily(''));

      expect(result.current.loading).toBe(false);
      expect(result.current.family).toBe(null);
      expect(result.current.error).toBe(null);
      expect(global.fetch).not.toHaveBeenCalled();
    });
  });

  describe('Obsługa błędów', () => {
    it('powinien obsłużyć błąd API', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 404,
      });

      const { result } = renderHook(() => useFamily('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.family).toBe(null);
      expect(result.current.error).toBe('Failed to fetch family');
    });

    it('powinien obsłużyć błąd sieci', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useFamily('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.family).toBe(null);
      expect(result.current.error).toBe('Network error');
    });

    it('powinien obsłużyć nieznany błąd', async () => {
      (global.fetch as jest.Mock).mockRejectedValue('Unknown error');

      const { result } = renderHook(() => useFamily('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.family).toBe(null);
      expect(result.current.error).toBe('Unknown error');
    });
  });

  describe('Refetch funkcjonalność', () => {
    it('powinien umożliwić ponowne pobranie danych', async () => {
      const { result } = renderHook(() => useFamily('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Zmień mock na nowe dane
      const updatedFamilyData = { ...mockFamilyData, name: 'Zaktualizowana Rodzina' };
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => updatedFamilyData,
      });

      // Wywołaj refetch
      act(() => {
        result.current.refetch();
      });

      await waitFor(() => {
        expect(result.current.family?.name).toBe('Zaktualizowana Rodzina');
      });

      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Zmiana familyId', () => {
    it('powinien pobrać nowe dane przy zmianie familyId', async () => {
      const { result, rerender } = renderHook(({ familyId }) => useFamily(familyId), {
        initialProps: { familyId: 'family-1' },
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.family?.id).toBe('family-1');

      // Zmień familyId
      const newFamilyData = { ...mockFamilyData, id: 'family-2', name: 'Inna Rodzina' };
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => newFamilyData,
      });

      rerender({ familyId: 'family-2' });

      await waitFor(() => {
        expect(result.current.family?.id).toBe('family-2');
      });

      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-2');
    });
  });
});

describe('useFamilyMembers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockMembersData,
    });
  });

  describe('Podstawowa funkcjonalność', () => {
    it('powinien zwrócić listę członków rodziny', async () => {
      const { result } = renderHook(() => useFamilyMembers('family-1'));

      expect(result.current.loading).toBe(true);
      expect(result.current.members).toEqual([]);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.members).toEqual(mockMembersData);
      expect(result.current.error).toBe(null);
      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/members');
    });

    it('powinien obsłużyć błąd API', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 403,
      });

      const { result } = renderHook(() => useFamilyMembers('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.members).toEqual([]);
      expect(result.current.error).toBe('Failed to fetch family members');
    });
  });

  describe('Refetch funkcjonalność', () => {
    it('powinien umożliwić ponowne pobranie członków', async () => {
      const { result } = renderHook(() => useFamilyMembers('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Wywołaj refetch
      act(() => {
        result.current.refetch();
      });

      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });
});

describe('useCurrentFamily', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Podstawowa funkcjonalność', () => {
    it('powinien zwrócić dane rodziny i aktualnego członka', async () => {
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockFamilyData,
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockMembersData[0],
        });

      const { result } = renderHook(() => useCurrentFamily('family-1'));

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.family).toEqual(mockFamilyData);
      expect(result.current.currentMember).toEqual(mockMembersData[0]);
      expect(result.current.error).toBe(null);

      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1');
      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/member');
    });

    it('powinien obsłużyć brak familyId', () => {
      const { result } = renderHook(() => useCurrentFamily());

      expect(result.current.loading).toBe(false);
      expect(result.current.family).toBe(null);
      expect(result.current.currentMember).toBe(null);
      expect(result.current.error).toBe(null);
    });
  });

  describe('Obsługa błędów', () => {
    it('powinien obsłużyć błąd przy pobieraniu członka', async () => {
      // Mock dla useFamily (sukces)
      (global.fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => mockFamilyData,
        })
        // Mock dla useCurrentFamily (błąd)
        .mockResolvedValueOnce({
          ok: false,
          status: 404,
        });

      const { result } = renderHook(() => useCurrentFamily('family-1'));

      // Czekaj aż oba wywołania się zakończą
      await waitFor(
        () => {
          expect(result.current.loading).toBe(false);
        },
        { timeout: 3000 }
      );

      expect(result.current.family).toEqual(mockFamilyData);
      expect(result.current.currentMember).toBe(null);
      expect(result.current.error).toBe('Failed to fetch current member');
    });
  });
});
