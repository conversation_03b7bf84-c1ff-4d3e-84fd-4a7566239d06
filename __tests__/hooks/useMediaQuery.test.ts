import { renderHook, act } from '@testing-library/react';
import { useMediaQuery } from '@/hooks/useMediaQuery';

// Mock window.matchMedia
const mockMatchMedia = jest.fn();

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

describe('useMediaQuery', () => {
  let mockMediaQueryList: {
    matches: boolean;
    addEventListener: jest.Mock;
    removeEventListener: jest.Mock;
  };

  beforeEach(() => {
    mockMediaQueryList = {
      matches: false,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQueryList);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('podstawowa funkcjonalność', () => {
    it('powinien zwrócić false gdy media query nie pasuje', () => {
      mockMediaQueryList.matches = false;

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

      expect(result.current).toBe(false);
      expect(mockMatchMedia).toHaveBeenCalledWith('(min-width: 768px)');
    });

    it('powinien zwrócić true gdy media query pasuje', () => {
      mockMediaQueryList.matches = true;

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

      expect(result.current).toBe(true);
    });
  });

  describe('nasłuchiwanie zmian', () => {
    it('powinien dodać event listener przy mount', () => {
      renderHook(() => useMediaQuery('(min-width: 768px)'));

      expect(mockMediaQueryList.addEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });

    it('powinien usunąć event listener przy unmount', () => {
      const { unmount } = renderHook(() => useMediaQuery('(min-width: 768px)'));

      unmount();

      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalledWith(
        'change',
        expect.any(Function)
      );
    });

    it('powinien aktualizować stan gdy media query się zmieni', () => {
      mockMediaQueryList.matches = false;
      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

      expect(result.current).toBe(false);

      // Symuluj zmianę media query
      act(() => {
        mockMediaQueryList.matches = true;
        const changeHandler = mockMediaQueryList.addEventListener.mock.calls[0][1];
        changeHandler({ matches: true });
      });

      expect(result.current).toBe(true);
    });
  });

  describe('różne media queries', () => {
    it('powinien działać z różnymi zapytaniami', () => {
      const queries = [
        '(min-width: 640px)',
        '(min-width: 768px)',
        '(min-width: 1024px)',
        '(prefers-color-scheme: dark)',
      ];

      queries.forEach(query => {
        mockMediaQueryList.matches = true;
        const { result } = renderHook(() => useMediaQuery(query));
        expect(result.current).toBe(true);
        expect(mockMatchMedia).toHaveBeenCalledWith(query);
      });
    });
  });

  describe('edge cases', () => {
    it('powinien obsłużyć brak wsparcia dla matchMedia', () => {
      // Usuń matchMedia
      delete (window as any).matchMedia;

      const { result } = renderHook(() => useMediaQuery('(min-width: 768px)'));

      expect(result.current).toBe(false);
    });
  });
});
