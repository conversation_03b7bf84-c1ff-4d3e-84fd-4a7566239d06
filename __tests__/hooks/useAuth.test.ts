import { renderHook, waitFor } from '@testing-library/react';
import { useAuth } from '@/hooks/useAuth';
import { useUser } from '@clerk/nextjs';

// Mock Clerk
jest.mock('@clerk/nextjs', () => ({
  useUser: jest.fn(),
}));

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

describe('useAuth', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('gdy użytkownik jest zalogowany', () => {
    it('powinien zwrócić dane użytkownika', () => {
      const mockUser = {
        id: 'user-1',
        firstName: 'Jan',
        lastName: '<PERSON><PERSON><PERSON>',
        emailAddresses: [{ emailAddress: '<EMAIL>' }],
      };

      mockUseUser.mockReturnValue({
        user: mockUser,
        isLoaded: true,
        isSignedIn: true,
      });

      const { result } = renderHook(() => useAuth());

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isLoaded).toBe(true);
      expect(result.current.isSignedIn).toBe(true);
    });
  });

  describe('gdy użytkownik nie jest zalogowany', () => {
    it('powinien zwrócić null dla użytkownika', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: true,
        isSignedIn: false,
      });

      const { result } = renderHook(() => useAuth());

      expect(result.current.user).toBeNull();
      expect(result.current.isLoaded).toBe(true);
      expect(result.current.isSignedIn).toBe(false);
    });
  });

  describe('gdy dane się ładują', () => {
    it('powinien zwrócić isLoaded jako false', () => {
      mockUseUser.mockReturnValue({
        user: null,
        isLoaded: false,
        isSignedIn: false,
      });

      const { result } = renderHook(() => useAuth());

      expect(result.current.user).toBeNull();
      expect(result.current.isLoaded).toBe(false);
      expect(result.current.isSignedIn).toBe(false);
    });
  });

  describe('obsługa błędów', () => {
    it('powinien obsłużyć błąd Clerk', () => {
      mockUseUser.mockImplementation(() => {
        throw new Error('Clerk error');
      });

      expect(() => renderHook(() => useAuth())).toThrow('Clerk error');
    });
  });
});
