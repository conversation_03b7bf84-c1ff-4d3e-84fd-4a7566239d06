import { renderHook, waitFor, act } from '@testing-library/react';
import { useTasks } from '@/hooks/useTasks';
import { TaskStatus, TaskWeight, TaskFrequency } from '@prisma/client';

// Mock fetch API
global.fetch = jest.fn();

const mockTasksData = [
  {
    id: 'task-1',
    title: '<PERSON>p<PERSON><PERSON><PERSON>e kuchni',
    description: 'Umyć naczynia i wyczyścić blaty',
    status: TaskStatus.ACTIVE,
    weight: TaskWeight.NORMAL,
    frequency: TaskFrequency.DAILY,
    dueDate: new Date('2025-01-15T10:00:00Z'),
    familyId: 'family-1',
    assignedToId: 'member-1',
    createdById: 'member-1',
    points: 10,
    createdAt: new Date('2025-01-01T10:00:00Z'),
    updatedAt: new Date('2025-01-01T10:00:00Z'),
    assignedTo: {
      id: 'member-1',
      user: {
        firstName: 'Jan',
        lastName: '<PERSON><PERSON><PERSON>',
      },
    },
    createdBy: {
      id: 'member-1',
      user: {
        firstName: 'Jan',
        lastName: '<PERSON><PERSON><PERSON>',
      },
    },
    completions: [],
    attachments: [],
    comments: [],
    _count: {
      completions: 0,
      comments: 0,
    },
  },
  {
    id: 'task-2',
    title: 'Odkurzanie salonu',
    description: 'Odkurzyć cały salon i dywan',
    status: TaskStatus.COMPLETED,
    weight: TaskWeight.HEAVY,
    frequency: TaskFrequency.WEEKLY,
    dueDate: new Date('2025-01-20T15:00:00Z'),
    familyId: 'family-1',
    assignedToId: 'member-2',
    createdById: 'member-1',
    points: 15,
    createdAt: new Date('2025-01-02T10:00:00Z'),
    updatedAt: new Date('2025-01-10T10:00:00Z'),
    assignedTo: {
      id: 'member-2',
      user: {
        firstName: 'Anna',
        lastName: 'Kowalska',
      },
    },
    createdBy: {
      id: 'member-1',
      user: {
        firstName: 'Jan',
        lastName: 'Kowalski',
      },
    },
    completions: [
      {
        id: 'completion-1',
        taskId: 'task-2',
        memberId: 'member-2',
        completedAt: new Date('2025-01-10T10:00:00Z'),
      },
    ],
    attachments: [],
    comments: [],
    _count: {
      completions: 1,
      comments: 0,
    },
  },
];

describe('useTasks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockTasksData,
    });
  });

  describe('Podstawowa funkcjonalność', () => {
    it('powinien zwrócić listę zadań po pomyślnym pobraniu', async () => {
      const { result } = renderHook(() => useTasks('family-1'));

      // Początkowo loading powinien być true
      expect(result.current.loading).toBe(true);
      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe(null);

      // Czekaj na zakończenie ładowania
      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual(mockTasksData);
      expect(result.current.error).toBe(null);
      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/tasks');
    });

    it('powinien obsłużyć brak familyId', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Invalid URL'));

      const { result } = renderHook(() => useTasks());

      // Hook próbuje ładować dane nawet bez familyId
      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe('Invalid URL');
    });

    it('powinien obsłużyć pusty familyId', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Invalid URL'));

      const { result } = renderHook(() => useTasks(''));

      expect(result.current.loading).toBe(true);

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe('Invalid URL');
    });
  });

  describe('Filtry zadań', () => {
    it('powinien wysłać filtry w query string', async () => {
      const filters = {
        status: [TaskStatus.ACTIVE],
        assignedMemberIds: ['member-1'],
        search: 'sprzątanie',
        includeCompleted: true, // Tylko true jest dodawane do query
      };

      const { result } = renderHook(() => useTasks('family-1', filters));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const expectedUrl =
        '/api/families/family-1/tasks?status=ACTIVE&assignedMemberIds=member-1&search=sprz%C4%85tanie&includeCompleted=true';
      expect(global.fetch).toHaveBeenCalledWith(expectedUrl);
    });

    it('powinien obsłużyć filtry z wieloma wartościami', async () => {
      const filters = {
        status: [TaskStatus.ACTIVE, TaskStatus.COMPLETED],
        weight: [TaskWeight.HEAVY, TaskWeight.NORMAL],
      };

      const { result } = renderHook(() => useTasks('family-1', filters));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const expectedUrl =
        '/api/families/family-1/tasks?status=ACTIVE%2CCOMPLETED&weight=HEAVY%2CNORMAL';
      expect(global.fetch).toHaveBeenCalledWith(expectedUrl);
    });

    it('powinien pominąć puste filtry', async () => {
      const filters = {
        status: [],
        assignedMemberIds: [],
        search: undefined,
      };

      const { result } = renderHook(() => useTasks('family-1', filters));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Puste tablice nadal generują parametry, ale z pustymi wartościami
      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/tasks?status=');
    });
  });

  describe('Obsługa błędów', () => {
    it('powinien obsłużyć błąd API', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 404,
      });

      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe('Failed to fetch tasks');
    });

    it('powinien obsłużyć błąd sieci', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe('Network error');
    });

    it('powinien obsłużyć nieznany błąd', async () => {
      (global.fetch as jest.Mock).mockRejectedValue('Unknown error');

      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe('Unknown error');
    });
  });

  describe('Refetch funkcjonalność', () => {
    it('powinien umożliwić ponowne pobranie danych', async () => {
      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Zmień mock na nowe dane
      const updatedTasksData = [{ ...mockTasksData[0], title: 'Zaktualizowane zadanie' }];
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => updatedTasksData,
      });

      // Wywołaj refetch
      act(() => {
        result.current.refetch();
      });

      await waitFor(() => {
        expect(result.current.tasks[0]?.title).toBe('Zaktualizowane zadanie');
      });

      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Lokalne aktualizacje zadań', () => {
    it('powinien umożliwić lokalną aktualizację zadania', async () => {
      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Aktualizuj zadanie lokalnie
      act(() => {
        result.current.updateTaskLocally('task-1', task => ({
          ...task,
          title: 'Lokalnie zaktualizowane zadanie',
        }));
      });

      expect(result.current.tasks[0]?.title).toBe('Lokalnie zaktualizowane zadanie');
      expect(result.current.tasks[1]?.title).toBe('Odkurzanie salonu'); // Inne zadanie bez zmian
    });

    it('powinien obsłużyć aktualizację nieistniejącego zadania', async () => {
      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const originalTasks = [...result.current.tasks];

      // Próbuj zaktualizować nieistniejące zadanie
      act(() => {
        result.current.updateTaskLocally('nonexistent-task', task => ({
          ...task,
          title: 'Nie powinno się zmienić',
        }));
      });

      // Zadania powinny pozostać bez zmian
      expect(result.current.tasks).toEqual(originalTasks);
    });
  });

  describe('Zmiana parametrów', () => {
    it('powinien pobrać nowe dane przy zmianie familyId', async () => {
      const { result, rerender } = renderHook(({ familyId }) => useTasks(familyId), {
        initialProps: { familyId: 'family-1' },
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual(mockTasksData);

      // Zmień familyId
      const newTasksData = [{ ...mockTasksData[0], id: 'task-3', familyId: 'family-2' }];
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => newTasksData,
      });

      rerender({ familyId: 'family-2' });

      await waitFor(() => {
        expect(result.current.tasks).toEqual(newTasksData);
      });

      expect(global.fetch).toHaveBeenCalledWith('/api/families/family-2/tasks');
    });

    it('powinien pobrać nowe dane przy zmianie filtrów', async () => {
      const { result, rerender } = renderHook(({ filters }) => useTasks('family-1', filters), {
        initialProps: { filters: {} },
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Zmień filtry
      const newFilters = { status: [TaskStatus.ACTIVE] };
      rerender({ filters: newFilters });

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/tasks?status=ACTIVE');
      });

      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Przypadki brzegowe', () => {
    it('powinien obsłużyć pustą odpowiedź API', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => [],
      });

      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe(null);
    });

    it('powinien obsłużyć nieprawidłową odpowiedź JSON', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      const { result } = renderHook(() => useTasks('family-1'));

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.tasks).toEqual([]);
      expect(result.current.error).toBe('Invalid JSON');
    });
  });
});
