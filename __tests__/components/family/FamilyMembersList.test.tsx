import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FamilyMembersList } from '@/components/family/FamilyMembersList';
import { FamilyRole } from '@prisma/client';

// Mock dla DeleteMemberDialog
jest.mock('@/components/family/DeleteMemberDialog', () => {
  return function MockDeleteMemberDialog({ member, onConfirm, onCancel }: any) {
    return (
      <div data-testid="delete-member-dialog">
        <button onClick={() => onConfirm(member)}>Potwierdź usunięcie</button>
        <button onClick={onCancel}>Anuluj</button>
      </div>
    );
  };
});

// Mock danych testowych
const mockMembers = [
  {
    id: 'member-1',
    familyId: 'family-1',
    userId: 'user-1',
    role: 'OWNER' as FamilyRole,
    points: 250,
    user: {
      id: 'user-1',
      firstName: 'Jan',
      lastName: '<PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
    },
    assignedTasks: [],
    _count: {
      completedTasks: 15,
      assignedTasks: 20,
    },
  },
  {
    id: 'member-2',
    familyId: 'family-1',
    userId: 'user-2',
    role: 'PARENT' as FamilyRole,
    points: 180,
    user: {
      id: 'user-2',
      firstName: 'Anna',
      lastName: 'Kowalska',
      email: '<EMAIL>',
    },
    assignedTasks: [],
    _count: {
      completedTasks: 12,
      assignedTasks: 15,
    },
  },
  {
    id: 'member-3',
    familyId: 'family-1',
    userId: 'user-3',
    role: 'CHILD' as FamilyRole,
    points: 95,
    user: {
      id: 'user-3',
      firstName: 'Piotr',
      lastName: 'Kowalski',
      email: '<EMAIL>',
    },
    assignedTasks: [],
    _count: {
      completedTasks: 8,
      assignedTasks: 10,
    },
  },
];

const mockCurrentMember = mockMembers[0]; // OWNER

describe('FamilyMembersList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Renderowanie podstawowe', () => {
    it('powinien renderować listę członków rodziny', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('Jan Kowalski')).toBeInTheDocument();
      expect(screen.getByText('Anna Kowalska')).toBeInTheDocument();
      expect(screen.getByText('Piotr Kowalski')).toBeInTheDocument();
    });

    it('powinien wyświetlić role członków', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('Właściciel')).toBeInTheDocument();
      expect(screen.getByText('Rodzic')).toBeInTheDocument();
      expect(screen.getByText('Dziecko')).toBeInTheDocument();
    });

    it('powinien wyświetlić punkty członków', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('250 pkt')).toBeInTheDocument();
      expect(screen.getByText('180 pkt')).toBeInTheDocument();
      expect(screen.getByText('95 pkt')).toBeInTheDocument();
    });

    it('powinien wyświetlić statystyki zadań', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('15/20')).toBeInTheDocument(); // completedTasks/assignedTasks
      expect(screen.getByText('12/15')).toBeInTheDocument();
      expect(screen.getByText('8/10')).toBeInTheDocument();
    });
  });

  describe('Sortowanie członków', () => {
    it('powinien sortować członków według hierarchii ról', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      const memberCards = screen.getAllByTestId(/member-card/);
      
      // Sprawdź kolejność: OWNER, PARENT, CHILD
      expect(memberCards[0]).toHaveTextContent('Jan Kowalski');
      expect(memberCards[1]).toHaveTextContent('Anna Kowalska');
      expect(memberCards[2]).toHaveTextContent('Piotr Kowalski');
    });

    it('powinien sortować według punktów w ramach tej samej roli', () => {
      const membersWithSameRole = [
        {
          ...mockMembers[1],
          points: 100,
        },
        {
          ...mockMembers[2],
          role: 'PARENT' as FamilyRole,
          points: 200,
        },
      ];

      render(
        <FamilyMembersList
          familyId="family-1"
          members={membersWithSameRole}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      const parentCards = screen.getAllByText(/Rodzic/);
      // Członek z większą liczbą punktów powinien być pierwszy
      expect(parentCards[0].closest('[data-testid*="member-card"]')).toHaveTextContent('200 pkt');
    });
  });

  describe('Menu akcji', () => {
    it('powinien wyświetlić menu akcji dla członków gdy canManageMembers jest true', async () => {
      const user = userEvent.setup();
      
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      const menuButtons = screen.getAllByRole('button', { name: /więcej opcji/i });
      await user.click(menuButtons[1]); // Kliknij na menu drugiego członka

      await waitFor(() => {
        expect(screen.getByText('Usuń z rodziny')).toBeInTheDocument();
      });
    });

    it('nie powinien wyświetlić menu akcji gdy canManageMembers jest false', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={false}
        />
      );

      const menuButtons = screen.queryAllByRole('button', { name: /więcej opcji/i });
      expect(menuButtons).toHaveLength(0);
    });

    it('nie powinien wyświetlić opcji usunięcia dla właściciela', async () => {
      const user = userEvent.setup();
      
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      const menuButtons = screen.getAllByRole('button', { name: /więcej opcji/i });
      await user.click(menuButtons[0]); // Kliknij na menu właściciela

      await waitFor(() => {
        expect(screen.queryByText('Usuń z rodziny')).not.toBeInTheDocument();
      });
    });
  });

  describe('Usuwanie członków', () => {
    it('powinien otworzyć dialog usuwania po kliknięciu "Usuń z rodziny"', async () => {
      const user = userEvent.setup();
      
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      const menuButtons = screen.getAllByRole('button', { name: /więcej opcji/i });
      await user.click(menuButtons[1]); // Menu drugiego członka

      await waitFor(() => {
        expect(screen.getByText('Usuń z rodziny')).toBeInTheDocument();
      });

      await user.click(screen.getByText('Usuń z rodziny'));

      expect(screen.getByTestId('delete-member-dialog')).toBeInTheDocument();
    });

    it('powinien zamknąć dialog po anulowaniu', async () => {
      const user = userEvent.setup();
      
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      // Otwórz dialog
      const menuButtons = screen.getAllByRole('button', { name: /więcej opcji/i });
      await user.click(menuButtons[1]);
      await user.click(screen.getByText('Usuń z rodziny'));

      // Anuluj
      await user.click(screen.getByText('Anuluj'));

      expect(screen.queryByTestId('delete-member-dialog')).not.toBeInTheDocument();
    });
  });

  describe('Avatary i inicjały', () => {
    it('powinien wyświetlić inicjały członków w avatarach', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('JK')).toBeInTheDocument(); // Jan Kowalski
      expect(screen.getByText('AK')).toBeInTheDocument(); // Anna Kowalska
      expect(screen.getByText('PK')).toBeInTheDocument(); // Piotr Kowalski
    });

    it('powinien obsłużyć brak imienia lub nazwiska', () => {
      const memberWithoutName = {
        ...mockMembers[0],
        user: {
          ...mockMembers[0].user,
          firstName: '',
          lastName: '',
        },
      };

      render(
        <FamilyMembersList
          familyId="family-1"
          members={[memberWithoutName]}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      // Powinien wyświetlić pierwszą literę emaila lub domyślny avatar
      expect(screen.getByText('J')).toBeInTheDocument(); // z <EMAIL>
    });
  });

  describe('Responsywność i dostępność', () => {
    it('powinien mieć odpowiednie aria-labels', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      const menuButtons = screen.getAllByRole('button', { name: /więcej opcji/i });
      expect(menuButtons[0]).toHaveAttribute('aria-label');
    });

    it('powinien obsługiwać nawigację klawiaturą', async () => {
      const user = userEvent.setup();
      
      render(
        <FamilyMembersList
          familyId="family-1"
          members={mockMembers}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      // Nawigacja do pierwszego menu
      await user.tab();
      const firstMenuButton = screen.getAllByRole('button', { name: /więcej opcji/i })[0];
      expect(firstMenuButton).toHaveFocus();
    });
  });

  describe('Przypadki brzegowe', () => {
    it('powinien obsłużyć pustą listę członków', () => {
      render(
        <FamilyMembersList
          familyId="family-1"
          members={[]}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('Brak członków rodziny')).toBeInTheDocument();
    });

    it('powinien obsłużyć członka bez statystyk zadań', () => {
      const memberWithoutStats = {
        ...mockMembers[0],
        _count: {
          completedTasks: 0,
          assignedTasks: 0,
        },
      };

      render(
        <FamilyMembersList
          familyId="family-1"
          members={[memberWithoutStats]}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText('0/0')).toBeInTheDocument();
    });

    it('powinien obsłużyć bardzo długie nazwy', () => {
      const memberWithLongName = {
        ...mockMembers[0],
        user: {
          ...mockMembers[0].user,
          firstName: 'Bardzo długie imię które może nie zmieścić się w karcie',
          lastName: 'Bardzo długie nazwisko które może nie zmieścić się w karcie',
        },
      };

      render(
        <FamilyMembersList
          familyId="family-1"
          members={[memberWithLongName]}
          currentMember={mockCurrentMember}
          canManageMembers={true}
        />
      );

      expect(screen.getByText(/Bardzo długie imię/)).toBeInTheDocument();
    });
  });
});
