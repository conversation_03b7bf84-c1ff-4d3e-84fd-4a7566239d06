import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import StatsDashboard from '@/components/stats/StatsDashboard';
import { StatsPeriod } from '@prisma/client';

// Mock dla komponentów wykresów
jest.mock('@/components/stats/CompletionRateChart', () => {
  return function MockCompletionRateChart({ familyId, period }: any) {
    return <div data-testid="completion-rate-chart">CompletionRateChart {familyId} {period}</div>;
  };
});

jest.mock('@/components/stats/PointsLeaderboard', () => {
  return function MockPointsLeaderboard({ familyId, period }: any) {
    return <div data-testid="points-leaderboard">PointsLeaderboard {familyId} {period}</div>;
  };
});

jest.mock('@/components/stats/ActivityTimeline', () => {
  return function MockActivityTimeline({ familyId, activities }: any) {
    return <div data-testid="activity-timeline">ActivityTimeline {familyId}</div>;
  };
});

// Mock dla fetch API
const mockStatsData = {
  summary: {
    monthlyCompletionRate: 85,
    weeklyCompletionRate: 92,
    thisWeek: {
      tasksCompleted: 15,
      totalPoints: 450,
      activeMembersCount: 3,
    },
    totalMembers: 4,
  },
  rankings: {
    points: [
      { memberId: 'member-1', memberName: 'Jan Kowalski', value: 200, rank: 1 },
      { memberId: 'member-2', memberName: 'Anna Kowalska', value: 150, rank: 2 },
    ],
    completion: [
      { memberId: 'member-1', memberName: 'Jan Kowalski', value: 95, rank: 1 },
      { memberId: 'member-2', memberName: 'Anna Kowalska', value: 88, rank: 2 },
    ],
  },
  recentActivity: [
    { id: 'activity-1', type: 'task_completed', memberName: 'Jan', taskTitle: 'Sprzątanie' },
  ],
  insights: {
    familyId: 'family-1',
    totalTasks: 25,
    completionRate: 85,
    totalPointsAwarded: 1200,
    mostActiveDay: 'Poniedziałek',
    topPerformer: {
      memberId: 'member-1',
      memberName: 'Jan Kowalski',
      points: 200,
    },
    trends: {
      tasksCreated: 'up' as const,
      completionRate: 'up' as const,
      memberActivity: 'stable' as const,
    },
  },
  trends: {
    completionRateChange: 5.2,
    isImproving: true,
  },
};

global.fetch = jest.fn();

describe('StatsDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockStatsData,
    });
  });

  describe('Renderowanie podstawowe', () => {
    it('powinien renderować tytuł dashboard', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Statystyki rodziny')).toBeInTheDocument();
      });
    });

    it('powinien renderować selektor okresu', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Okres')).toBeInTheDocument();
      });
    });

    it('powinien renderować przycisk odświeżania', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Odśwież')).toBeInTheDocument();
      });
    });
  });

  describe('Ładowanie danych', () => {
    it('powinien wyświetlić skeleton podczas ładowania', () => {
      (global.fetch as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 1000))
      );

      render(<StatsDashboard familyId="family-1" />);

      // Sprawdź czy są wyświetlane elementy ładowania
      expect(screen.getAllByTestId('skeleton')).toHaveLength(4);
    });

    it('powinien wywołać API z prawidłowymi parametrami', async () => {
      render(<StatsDashboard familyId="family-1" period="WEEKLY" />);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/stats?period=WEEKLY');
      });
    });
  });

  describe('Wyświetlanie statystyk', () => {
    it('powinien wyświetlić kluczowe metryki', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('92%')).toBeInTheDocument(); // weeklyCompletionRate
        expect(screen.getByText('15')).toBeInTheDocument(); // tasksCompleted
        expect(screen.getByText('450')).toBeInTheDocument(); // totalPoints
        expect(screen.getByText('3')).toBeInTheDocument(); // activeMembersCount
      });
    });

    it('powinien wyświetlić trend poprawy', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('5.1% od ostatniego miesiąca')).toBeInTheDocument();
      });
    });

    it('powinien wyświetlić ranking punktów', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Jan Kowalski')).toBeInTheDocument();
        expect(screen.getByText('200')).toBeInTheDocument();
        expect(screen.getByText('Anna Kowalska')).toBeInTheDocument();
        expect(screen.getByText('150')).toBeInTheDocument();
      });
    });

    it('powinien wyświetlić insights', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Najaktywniejszy dzień: Poniedziałek')).toBeInTheDocument();
        expect(screen.getByText('Najlepszy wykonawca: Jan Kowalski (200 pkt)')).toBeInTheDocument();
      });
    });
  });

  describe('Interakcje użytkownika', () => {
    it('powinien zmienić okres po wyborze z selecta', async () => {
      const user = userEvent.setup();
      
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Okres')).toBeInTheDocument();
      });

      // Kliknij na select
      const periodSelect = screen.getByRole('combobox');
      await user.click(periodSelect);

      // Wybierz MONTHLY
      await user.click(screen.getByText('Miesięcznie'));

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith('/api/families/family-1/stats?period=MONTHLY');
      });
    });

    it('powinien odświeżyć dane po kliknięciu przycisku odświeżania', async () => {
      const user = userEvent.setup();
      
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Odśwież')).toBeInTheDocument();
      });

      const refreshButton = screen.getByText('Odśwież');
      await user.click(refreshButton);

      // Sprawdź czy fetch został wywołany ponownie
      expect(global.fetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Obsługa błędów', () => {
    it('powinien wyświetlić komunikat błędu gdy API zwróci błąd', async () => {
      (global.fetch as jest.Mock).mockRejectedValue(new Error('API Error'));

      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Wystąpił błąd podczas ładowania statystyk')).toBeInTheDocument();
      });
    });

    it('powinien wyświetlić komunikat błędu gdy API zwróci status 404', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 404,
        json: async () => ({ error: 'Family not found' }),
      });

      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Wystąpił błąd podczas ładowania statystyk')).toBeInTheDocument();
      });
    });

    it('powinien umożliwić ponowną próbę po błędzie', async () => {
      const user = userEvent.setup();
      
      // Pierwszy wywołanie - błąd
      (global.fetch as jest.Mock)
        .mockRejectedValueOnce(new Error('API Error'))
        .mockResolvedValue({
          ok: true,
          json: async () => mockStatsData,
        });

      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Wystąpił błąd podczas ładowania statystyk')).toBeInTheDocument();
      });

      // Kliknij przycisk odświeżania
      const refreshButton = screen.getByText('Odśwież');
      await user.click(refreshButton);

      // Sprawdź czy dane zostały załadowane
      await waitFor(() => {
        expect(screen.getByText('92%')).toBeInTheDocument();
      });
    });
  });

  describe('Komponenty podrzędne', () => {
    it('powinien renderować wykres completion rate', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByTestId('completion-rate-chart')).toBeInTheDocument();
      });
    });

    it('powinien renderować leaderboard punktów', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByTestId('points-leaderboard')).toBeInTheDocument();
      });
    });

    it('powinien renderować timeline aktywności', async () => {
      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByTestId('activity-timeline')).toBeInTheDocument();
      });
    });
  });

  describe('Przypadki brzegowe', () => {
    it('powinien obsłużyć brak danych w odpowiedzi API', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => ({}),
      });

      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Brak danych do wyświetlenia')).toBeInTheDocument();
      });
    });

    it('powinien obsłużyć pusty ranking', async () => {
      const emptyRankingData = {
        ...mockStatsData,
        rankings: { points: [], completion: [] },
      };

      (global.fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: async () => emptyRankingData,
      });

      render(<StatsDashboard familyId="family-1" />);

      await waitFor(() => {
        expect(screen.getByText('Brak danych rankingowych')).toBeInTheDocument();
      });
    });
  });
});
