import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Badge } from '@/components/ui/badge';

describe('Badge', () => {
    describe('renderowanie podstawowe', () => {
        it('powinien renderować badge z tekstem', () => {
            render(<Badge>Test Badge</Badge>);
            
            const badge = screen.getByText('Test Badge');
            expect(badge).toBeDefined();
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<Badge>Test</Badge>);
            
            const badge = screen.getByText('Test');
            expect(badge).toHaveClass(
                'inline-flex',
                'items-center',
                'rounded-full',
                'border',
                'px-2.5',
                'py-0.5',
                'text-xs',
                'font-semibold',
                'transition-colors',
                'focus:outline-none',
                'focus:ring-2',
                'focus:ring-ring',
                'focus:ring-offset-2'
            );
        });

        it('powinien renderować jako div element', () => {
            render(<Badge>Test</Badge>);
            
            const badge = screen.getByText('Test');
            expect(badge.tagName).toBe('DIV');
        });
    });

    describe('warianty', () => {
        it('powinien renderować wariant default', () => {
            render(<Badge variant="default">Default</Badge>);
            
            const badge = screen.getByText('Default');
            expect(badge).toHaveClass(
                'border-transparent',
                'bg-primary',
                'text-primary-foreground',
                'hover:bg-primary/80'
            );
        });

        it('powinien renderować wariant secondary', () => {
            render(<Badge variant="secondary">Secondary</Badge>);
            
            const badge = screen.getByText('Secondary');
            expect(badge).toHaveClass(
                'border-transparent',
                'bg-secondary',
                'text-secondary-foreground',
                'hover:bg-secondary/80'
            );
        });

        it('powinien renderować wariant destructive', () => {
            render(<Badge variant="destructive">Destructive</Badge>);
            
            const badge = screen.getByText('Destructive');
            expect(badge).toHaveClass(
                'border-transparent',
                'bg-destructive',
                'text-destructive-foreground',
                'hover:bg-destructive/80'
            );
        });

        it('powinien renderować wariant outline', () => {
            render(<Badge variant="outline">Outline</Badge>);
            
            const badge = screen.getByText('Outline');
            expect(badge).toHaveClass('text-foreground');
            expect(badge).not.toHaveClass('border-transparent');
        });

        it('powinien używać domyślnego wariantu gdy nie określono', () => {
            render(<Badge>No Variant</Badge>);
            
            const badge = screen.getByText('No Variant');
            expect(badge).toHaveClass(
                'border-transparent',
                'bg-primary',
                'text-primary-foreground'
            );
        });
    });

    describe('niestandardowe klasy CSS', () => {
        it('powinien dodać niestandardowe klasy', () => {
            render(<Badge className="custom-badge">Custom</Badge>);
            
            const badge = screen.getByText('Custom');
            expect(badge).toHaveClass('custom-badge');
        });

        it('powinien zachować domyślne klasy przy dodawaniu niestandardowych', () => {
            render(<Badge className="custom-badge">Test</Badge>);
            
            const badge = screen.getByText('Test');
            expect(badge).toHaveClass('inline-flex', 'items-center', 'custom-badge');
        });

        it('powinien obsłużyć kombinację wariantu i niestandardowych klas', () => {
            render(<Badge variant="secondary" className="custom-secondary">Test</Badge>);
            
            const badge = screen.getByText('Test');
            expect(badge).toHaveClass('bg-secondary', 'custom-secondary');
        });
    });

    describe('zawartość', () => {
        it('powinien renderować tekst', () => {
            render(<Badge>Simple Text</Badge>);
            
            expect(screen.getByText('Simple Text')).toBeDefined();
        });

        it('powinien renderować liczby', () => {
            render(<Badge>{42}</Badge>);
            
            expect(screen.getByText('42')).toBeDefined();
        });

        it('powinien renderować elementy JSX', () => {
            render(
                <Badge>
                    <span>Icon</span>
                    <span>Text</span>
                </Badge>
            );
            
            expect(screen.getByText('Icon')).toBeDefined();
            expect(screen.getByText('Text')).toBeDefined();
        });

        it('powinien obsłużyć pusty badge', () => {
            render(<Badge></Badge>);
            
            const badge = document.querySelector('.inline-flex');
            expect(badge).toBeDefined();
        });
    });

    describe('interakcje', () => {
        it('powinien obsłużyć kliknięcie', async () => {
            const handleClick = jest.fn();
            const user = userEvent.setup();
            
            render(<Badge onClick={handleClick}>Clickable</Badge>);
            
            const badge = screen.getByText('Clickable');
            await user.click(badge);
            
            expect(handleClick).toHaveBeenCalledTimes(1);
        });

        it('powinien obsłużyć hover', async () => {
            const user = userEvent.setup();
            
            render(<Badge variant="default">Hoverable</Badge>);
            
            const badge = screen.getByText('Hoverable');
            await user.hover(badge);
            
            expect(badge).toHaveClass('hover:bg-primary/80');
        });

        it('powinien obsłużyć focus', async () => {
            const user = userEvent.setup();
            
            render(<Badge tabIndex={0}>Focusable</Badge>);
            
            const badge = screen.getByText('Focusable');
            await user.tab();
            
            expect(badge).toHaveFocus();
        });
    });

    describe('atrybuty HTML', () => {
        it('powinien obsłużyć atrybut id', () => {
            render(<Badge id="test-badge">Test</Badge>);
            
            const badge = screen.getByText('Test');
            expect(badge.getAttribute('id')).toBe('test-badge');
        });

        it('powinien obsłużyć atrybut data-testid', () => {
            render(<Badge data-testid="badge-component">Test</Badge>);
            
            const badge = screen.getByTestId('badge-component');
            expect(badge).toBeDefined();
        });

        it('powinien obsłużyć atrybut role', () => {
            render(<Badge role="status">Status Badge</Badge>);
            
            const badge = screen.getByRole('status');
            expect(badge).toBeDefined();
        });

        it('powinien obsłużyć atrybuty aria', () => {
            render(<Badge aria-label="Custom label">Test</Badge>);
            
            const badge = screen.getByLabelText('Custom label');
            expect(badge).toBeDefined();
        });

        it('powinien obsłużyć atrybut title', () => {
            render(<Badge title="Tooltip text">Test</Badge>);
            
            const badge = screen.getByText('Test');
            expect(badge.getAttribute('title')).toBe('Tooltip text');
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie klasy focus', () => {
            render(<Badge>Accessible</Badge>);
            
            const badge = screen.getByText('Accessible');
            expect(badge).toHaveClass(
                'focus:outline-none',
                'focus:ring-2',
                'focus:ring-ring',
                'focus:ring-offset-2'
            );
        });

        it('powinien być dostępny przez klawiaturę gdy ma tabIndex', async () => {
            const user = userEvent.setup();
            
            render(<Badge tabIndex={0}>Keyboard Accessible</Badge>);
            
            const badge = screen.getByText('Keyboard Accessible');
            
            await user.tab();
            expect(badge).toHaveFocus();
        });

        it('powinien obsłużyć screen reader text', () => {
            render(
                <Badge>
                    <span className="sr-only">Status:</span>
                    Active
                </Badge>
            );
            
            expect(screen.getByText('Status:')).toBeDefined();
            expect(screen.getByText('Active')).toBeDefined();
        });
    });

    describe('przypadki brzegowe', () => {
        it('powinien obsłużyć bardzo długi tekst', () => {
            const longText = 'To jest bardzo długi tekst dla badge który może się nie zmieścić';
            render(<Badge>{longText}</Badge>);
            
            expect(screen.getByText(longText)).toBeDefined();
        });

        it('powinien obsłużyć znaki specjalne', () => {
            render(<Badge>!@#$%^&*()</Badge>);
            
            expect(screen.getByText('!@#$%^&*()')).toBeDefined();
        });

        it('powinien obsłużyć tekst z emoji', () => {
            render(<Badge>🎉 Success!</Badge>);
            
            expect(screen.getByText('🎉 Success!')).toBeDefined();
        });

        it('powinien obsłużyć wszystkie propsy HTML div', () => {
            render(
                <Badge
                    style={{ backgroundColor: 'red' }}
                    onMouseEnter={() => {}}
                    onMouseLeave={() => {}}
                >
                    Test
                </Badge>
            );
            
            const badge = screen.getByText('Test');
            expect(badge.style.backgroundColor).toBe('red');
        });
    });

    describe('kombinacje wariantów z zawartością', () => {
        it('powinien renderować badge z ikoną i tekstem', () => {
            render(
                <Badge variant="secondary">
                    <svg className="w-3 h-3 mr-1" data-testid="icon">
                        <circle cx="50" cy="50" r="40" />
                    </svg>
                    With Icon
                </Badge>
            );
            
            expect(screen.getByTestId('icon')).toBeDefined();
            expect(screen.getByText('With Icon')).toBeDefined();
        });

        it('powinien renderować badge z licznikiem', () => {
            render(
                <Badge variant="destructive">
                    Errors: <strong>5</strong>
                </Badge>
            );
            
            expect(screen.getByText('Errors:')).toBeDefined();
            expect(screen.getByText('5')).toBeDefined();
        });
    });
});
