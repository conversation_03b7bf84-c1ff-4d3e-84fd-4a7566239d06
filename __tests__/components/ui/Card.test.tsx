import { render, screen } from '@testing-library/react';
import { 
    Card, 
    CardHeader, 
    CardTitle, 
    CardDescription, 
    CardContent, 
    CardFooter, 
    CardAction 
} from '@/components/ui/card';

describe('Card', () => {
    describe('renderowanie podstawowe', () => {
        it('powinien renderować kartę z zawartością', () => {
            render(
                <Card>
                    <div>Zawartość karty</div>
                </Card>
            );
            
            const card = screen.getByText('Zawartość karty').closest('[data-slot="card"]');
            expect(card).toBeDefined();
            expect(screen.getByText('Zawartość karty')).toBeDefined();
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<Card>Test</Card>);
            
            const card = screen.getByText('Test').closest('[data-slot="card"]');
            expect(card).toHaveClass(
                'bg-card',
                'text-card-foreground',
                'flex',
                'flex-col',
                'gap-6',
                'rounded-xl',
                'border',
                'py-6',
                'shadow-sm'
            );
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<Card className="custom-class">Test</Card>);
            
            const card = screen.getByText('Test').closest('[data-slot="card"]');
            expect(card).toHaveClass('custom-class');
        });
    });

    describe('CardHeader', () => {
        it('powinien renderować nagłówek karty', () => {
            render(
                <Card>
                    <CardHeader>
                        <div>Nagłówek</div>
                    </CardHeader>
                </Card>
            );
            
            const header = screen.getByText('Nagłówek').closest('[data-slot="card-header"]');
            expect(header).toBeDefined();
            expect(screen.getByText('Nagłówek')).toBeDefined();
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(
                <CardHeader>
                    <div>Test</div>
                </CardHeader>
            );
            
            const header = screen.getByText('Test').closest('[data-slot="card-header"]');
            expect(header).toHaveClass(
                '@container/card-header',
                'grid',
                'auto-rows-min',
                'grid-rows-[auto_auto]',
                'items-start',
                'gap-1.5',
                'px-6'
            );
        });
    });

    describe('CardTitle', () => {
        it('powinien renderować tytuł karty', () => {
            render(<CardTitle>Tytuł karty</CardTitle>);
            
            const title = screen.getByText('Tytuł karty');
            expect(title).toBeDefined();
            expect(title.getAttribute('data-slot')).toBe('card-title');
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<CardTitle>Test</CardTitle>);
            
            const title = screen.getByText('Test');
            expect(title).toHaveClass('leading-none', 'font-semibold');
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<CardTitle className="custom-title">Test</CardTitle>);
            
            const title = screen.getByText('Test');
            expect(title).toHaveClass('custom-title');
        });
    });

    describe('CardDescription', () => {
        it('powinien renderować opis karty', () => {
            render(<CardDescription>Opis karty</CardDescription>);
            
            const description = screen.getByText('Opis karty');
            expect(description).toBeDefined();
            expect(description.getAttribute('data-slot')).toBe('card-description');
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<CardDescription>Test</CardDescription>);
            
            const description = screen.getByText('Test');
            expect(description).toHaveClass('text-muted-foreground', 'text-sm');
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<CardDescription className="custom-desc">Test</CardDescription>);
            
            const description = screen.getByText('Test');
            expect(description).toHaveClass('custom-desc');
        });
    });

    describe('CardContent', () => {
        it('powinien renderować zawartość karty', () => {
            render(<CardContent>Zawartość</CardContent>);
            
            const content = screen.getByText('Zawartość');
            expect(content).toBeDefined();
            expect(content.getAttribute('data-slot')).toBe('card-content');
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<CardContent>Test</CardContent>);
            
            const content = screen.getByText('Test');
            expect(content).toHaveClass('px-6');
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<CardContent className="custom-content">Test</CardContent>);
            
            const content = screen.getByText('Test');
            expect(content).toHaveClass('custom-content');
        });
    });

    describe('CardFooter', () => {
        it('powinien renderować stopkę karty', () => {
            render(<CardFooter>Stopka</CardFooter>);
            
            const footer = screen.getByText('Stopka');
            expect(footer).toBeDefined();
            expect(footer.getAttribute('data-slot')).toBe('card-footer');
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<CardFooter>Test</CardFooter>);
            
            const footer = screen.getByText('Test');
            expect(footer).toHaveClass('flex', 'items-center', 'px-6');
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<CardFooter className="custom-footer">Test</CardFooter>);
            
            const footer = screen.getByText('Test');
            expect(footer).toHaveClass('custom-footer');
        });
    });

    describe('CardAction', () => {
        it('powinien renderować akcję karty', () => {
            render(<CardAction>Akcja</CardAction>);
            
            const action = screen.getByText('Akcja');
            expect(action).toBeDefined();
            expect(action.getAttribute('data-slot')).toBe('card-action');
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<CardAction>Test</CardAction>);
            
            const action = screen.getByText('Test');
            expect(action).toHaveClass(
                'col-start-2',
                'row-span-2',
                'row-start-1',
                'self-start',
                'justify-self-end'
            );
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<CardAction className="custom-action">Test</CardAction>);
            
            const action = screen.getByText('Test');
            expect(action).toHaveClass('custom-action');
        });
    });

    describe('kompozycja komponentów', () => {
        it('powinien renderować kompletną kartę z wszystkimi elementami', () => {
            render(
                <Card>
                    <CardHeader>
                        <CardTitle>Tytuł</CardTitle>
                        <CardDescription>Opis</CardDescription>
                        <CardAction>Akcja</CardAction>
                    </CardHeader>
                    <CardContent>
                        <p>Zawartość karty</p>
                    </CardContent>
                    <CardFooter>
                        <button>Przycisk</button>
                    </CardFooter>
                </Card>
            );
            
            expect(screen.getByText('Tytuł')).toBeDefined();
            expect(screen.getByText('Opis')).toBeDefined();
            expect(screen.getByText('Akcja')).toBeDefined();
            expect(screen.getByText('Zawartość karty')).toBeDefined();
            expect(screen.getByText('Przycisk')).toBeDefined();
        });

        it('powinien obsłużyć kartę tylko z tytułem i zawartością', () => {
            render(
                <Card>
                    <CardHeader>
                        <CardTitle>Prosty tytuł</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <p>Prosta zawartość</p>
                    </CardContent>
                </Card>
            );
            
            expect(screen.getByText('Prosty tytuł')).toBeDefined();
            expect(screen.getByText('Prosta zawartość')).toBeDefined();
        });

        it('powinien obsłużyć kartę bez nagłówka', () => {
            render(
                <Card>
                    <CardContent>
                        <p>Zawartość bez nagłówka</p>
                    </CardContent>
                </Card>
            );
            
            expect(screen.getByText('Zawartość bez nagłówka')).toBeDefined();
        });
    });

    describe('przypadki brzegowe', () => {
        it('powinien obsłużyć pustą kartę', () => {
            render(<Card></Card>);
            
            const card = document.querySelector('[data-slot="card"]');
            expect(card).toBeDefined();
        });

        it('powinien obsłużyć zagnieżdżone elementy', () => {
            render(
                <Card>
                    <CardContent>
                        <div>
                            <span>Zagnieżdżony</span>
                            <strong>element</strong>
                        </div>
                    </CardContent>
                </Card>
            );
            
            expect(screen.getByText('Zagnieżdżony')).toBeDefined();
            expect(screen.getByText('element')).toBeDefined();
        });

        it('powinien przekazać wszystkie propsy HTML', () => {
            render(
                <Card id="test-card" role="region" aria-label="Test card">
                    Test
                </Card>
            );
            
            const card = screen.getByText('Test').closest('[data-slot="card"]');
            expect(card?.getAttribute('id')).toBe('test-card');
            expect(card?.getAttribute('role')).toBe('region');
            expect(card?.getAttribute('aria-label')).toBe('Test card');
        });
    });
});
