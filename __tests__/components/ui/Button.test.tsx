import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '@/components/ui/button';

describe('Button', () => {
    describe('renderowanie podstawowe', () => {
        it('powinien renderować przycisk z tekstem', () => {
            render(<Button>Kliknij mnie</Button>);

            const button = screen.getByRole('button');
            expect(button).toBeDefined();
            expect(screen.getByText('Kliknij mnie')).toBeDefined();
        });

        it('powinien nie mieć typu gdy nie jest określony', () => {
            render(<Button>Test</Button>);

            const button = screen.getByRole('button');
            expect(button.getAttribute('type')).toBeNull();
        });

        it('powinien mieć atrybut data-slot', () => {
            render(<Button>Test</Button>);

            const button = screen.getByRole('button');
            expect(button.getAttribute('data-slot')).toBe('button');
        });
    });

    describe('warianty', () => {
        it('powinien renderować wariant default', () => {
            render(<Button variant="default">Default</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('bg-primary', 'text-primary-foreground');
        });

        it('powinien renderować wariant destructive', () => {
            render(<Button variant="destructive">Destructive</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('bg-destructive', 'text-white');
        });

        it('powinien renderować wariant outline', () => {
            render(<Button variant="outline">Outline</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('border', 'bg-background');
        });

        it('powinien renderować wariant secondary', () => {
            render(<Button variant="secondary">Secondary</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('bg-secondary', 'text-secondary-foreground');
        });

        it('powinien renderować wariant ghost', () => {
            render(<Button variant="ghost">Ghost</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('hover:bg-accent');
        });

        it('powinien renderować wariant link', () => {
            render(<Button variant="link">Link</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('text-primary', 'underline-offset-4');
        });
    });

    describe('rozmiary', () => {
        it('powinien renderować domyślny rozmiar', () => {
            render(<Button size="default">Default Size</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('h-9', 'px-4', 'py-2');
        });

        it('powinien renderować rozmiar small', () => {
            render(<Button size="sm">Small</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('h-8', 'px-3');
        });

        it('powinien renderować rozmiar large', () => {
            render(<Button size="lg">Large</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('h-10', 'px-6');
        });

        it('powinien renderować rozmiar icon', () => {
            render(<Button size="icon">🔥</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('size-9');
        });
    });

    describe('stany', () => {
        it('powinien obsłużyć stan disabled', () => {
            render(<Button disabled>Disabled</Button>);

            const button = screen.getByRole('button');
            expect(button).toBeDisabled();
            expect(button).toHaveClass('disabled:pointer-events-none', 'disabled:opacity-50');
        });

        it('powinien obsłużyć niestandardowy typ', () => {
            render(<Button type="submit">Submit</Button>);

            const button = screen.getByRole('button');
            expect(button.getAttribute('type')).toBe('submit');
        });
    });

    describe('interakcje', () => {
        it('powinien obsłużyć kliknięcie', async () => {
            const handleClick = jest.fn();
            const user = userEvent.setup();

            render(<Button onClick={handleClick}>Kliknij</Button>);

            const button = screen.getByRole('button');
            await user.click(button);

            expect(handleClick).toHaveBeenCalledTimes(1);
        });

        it('nie powinien wywołać onClick gdy disabled', async () => {
            const handleClick = jest.fn();
            const user = userEvent.setup();

            render(<Button onClick={handleClick} disabled>Disabled</Button>);

            const button = screen.getByRole('button');
            await user.click(button);

            expect(handleClick).not.toHaveBeenCalled();
        });

        it('powinien obsłużyć focus i blur', async () => {
            const user = userEvent.setup();

            render(<Button>Focus Test</Button>);

            const button = screen.getByRole('button');

            await user.tab();
            expect(button).toHaveFocus();

            await user.tab();
            expect(button).not.toHaveFocus();
        });
    });

    describe('niestandardowe klasy CSS', () => {
        it('powinien dodać niestandardowe klasy', () => {
            render(<Button className="custom-class">Custom</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('custom-class');
        });

        it('powinien zachować domyślne klasy przy dodawaniu niestandardowych', () => {
            render(<Button className="custom-class">Test</Button>);

            const button = screen.getByRole('button');
            expect(button).toHaveClass('inline-flex', 'items-center', 'custom-class');
        });
    });

    describe('asChild prop', () => {
        it('powinien renderować jako Slot gdy asChild=true', () => {
            render(
                <Button asChild>
                    <a href="/test">Link Button</a>
                </Button>
            );

            const link = screen.getByRole('link');
            expect(link).toBeDefined();
            expect(link.getAttribute('href')).toBe('/test');
            expect(link).toHaveClass('inline-flex', 'items-center');
        });

        it('powinien renderować jako button gdy asChild=false', () => {
            render(<Button asChild={false}>Normal Button</Button>);

            const button = screen.getByRole('button');
            expect(button).toBeDefined();
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie atrybuty ARIA', () => {
            render(<Button aria-label="Custom label">Button</Button>);

            const button = screen.getByRole('button');
            expect(button.getAttribute('aria-label')).toBe('Custom label');
        });

        it('powinien być dostępny przez klawiaturę', async () => {
            const handleClick = jest.fn();
            const user = userEvent.setup();

            render(<Button onClick={handleClick}>Keyboard Test</Button>);

            const button = screen.getByRole('button');

            await user.tab();
            expect(button).toHaveFocus();

            await user.keyboard('{Enter}');
            expect(handleClick).toHaveBeenCalledTimes(1);

            await user.keyboard(' ');
            expect(handleClick).toHaveBeenCalledTimes(2);
        });
    });

    describe('przypadki brzegowe', () => {
        it('powinien obsłużyć pusty children', () => {
            render(<Button></Button>);

            const button = screen.getByRole('button');
            expect(button).toBeDefined();
        });

        it('powinien obsłużyć children jako number', () => {
            render(<Button>{42}</Button>);

            const button = screen.getByRole('button');
            expect(screen.getByText('42')).toBeDefined();
        });

        it('powinien obsłużyć złożone children', () => {
            render(
                <Button>
                    <span>Icon</span>
                    <span>Text</span>
                </Button>
            );

            const button = screen.getByRole('button');
            expect(screen.getByText('Icon')).toBeDefined();
            expect(screen.getByText('Text')).toBeDefined();
        });
    });
});
