import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Input } from '@/components/ui/input';

describe('Input', () => {
    describe('renderowanie podstawowe', () => {
        it('powinien renderować pole input', () => {
            render(<Input />);

            const input = screen.getByRole('textbox');
            expect(input).toBeDefined();
            expect(input.getAttribute('data-slot')).toBe('input');
        });

        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<Input />);

            const input = screen.getByRole('textbox');
            expect(input).toHaveClass(
                'flex',
                'h-9',
                'w-full',
                'min-w-0',
                'rounded-md',
                'border',
                'bg-transparent',
                'px-3',
                'py-1',
                'text-base',
                'shadow-xs',
                'transition-[color,box-shadow]',
                'outline-none'
            );
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(<Input className="custom-input" />);

            const input = screen.getByRole('textbox');
            expect(input).toHaveClass('custom-input');
        });
    });

    describe('typy input', () => {
        it('powinien nie mieć typu gdy nie jest określony', () => {
            render(<Input />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('type')).toBeNull();
        });

        it('powinien renderować typ email', () => {
            render(<Input type="email" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('type')).toBe('email');
        });

        it('powinien renderować typ password', () => {
            render(<Input type="password" />);

            const input = document.querySelector('input[type="password"]');
            expect(input).toBeDefined();
        });

        it('powinien renderować typ number', () => {
            render(<Input type="number" />);

            const input = screen.getByRole('spinbutton');
            expect(input.getAttribute('type')).toBe('number');
        });

        it('powinien renderować typ tel', () => {
            render(<Input type="tel" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('type')).toBe('tel');
        });

        it('powinien renderować typ url', () => {
            render(<Input type="url" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('type')).toBe('url');
        });

        it('powinien renderować typ search', () => {
            render(<Input type="search" />);

            const input = screen.getByRole('searchbox');
            expect(input.getAttribute('type')).toBe('search');
        });
    });

    describe('placeholder', () => {
        it('powinien wyświetlić placeholder', () => {
            render(<Input placeholder="Wpisz tekst..." />);

            const input = screen.getByPlaceholderText('Wpisz tekst...');
            expect(input).toBeDefined();
        });

        it('powinien mieć klasy CSS dla placeholder', () => {
            render(<Input placeholder="Test" />);

            const input = screen.getByPlaceholderText('Test');
            expect(input).toHaveClass('placeholder:text-muted-foreground');
        });
    });

    describe('stan disabled', () => {
        it('powinien obsłużyć stan disabled', () => {
            render(<Input disabled />);

            const input = screen.getByRole('textbox');
            expect(input).toBeDisabled();
            expect(input).toHaveClass(
                'disabled:pointer-events-none',
                'disabled:cursor-not-allowed',
                'disabled:opacity-50'
            );
        });

        it('nie powinien przyjmować wartości gdy disabled', async () => {
            const user = userEvent.setup();
            render(<Input disabled />);

            const input = screen.getByRole('textbox');
            await user.type(input, 'test');

            expect(input).toHaveValue('');
        });
    });

    describe('wartości i interakcje', () => {
        it('powinien obsłużyć wartość początkową', () => {
            render(<Input defaultValue="Początkowa wartość" />);

            const input = screen.getByDisplayValue('Początkowa wartość');
            expect(input).toBeDefined();
        });

        it('powinien obsłużyć kontrolowaną wartość', () => {
            render(<Input value="Kontrolowana wartość" onChange={() => { }} />);

            const input = screen.getByDisplayValue('Kontrolowana wartość');
            expect(input).toBeDefined();
        });

        it('powinien wywołać onChange przy wpisywaniu', async () => {
            const handleChange = jest.fn();
            const user = userEvent.setup();

            render(<Input onChange={handleChange} />);

            const input = screen.getByRole('textbox');
            await user.type(input, 'test');

            expect(handleChange).toHaveBeenCalled();
        });

        it('powinien obsłużyć focus i blur', async () => {
            const handleFocus = jest.fn();
            const handleBlur = jest.fn();
            const user = userEvent.setup();

            render(<Input onFocus={handleFocus} onBlur={handleBlur} />);

            const input = screen.getByRole('textbox');

            await user.click(input);
            expect(handleFocus).toHaveBeenCalledTimes(1);

            await user.tab();
            expect(handleBlur).toHaveBeenCalledTimes(1);
        });
    });

    describe('stany focus i validation', () => {
        it('powinien mieć klasy CSS dla focus', () => {
            render(<Input />);

            const input = screen.getByRole('textbox');
            expect(input).toHaveClass(
                'focus-visible:border-ring',
                'focus-visible:ring-ring/50',
                'focus-visible:ring-[3px]'
            );
        });

        it('powinien mieć klasy CSS dla aria-invalid', () => {
            render(<Input aria-invalid="true" />);

            const input = screen.getByRole('textbox');
            expect(input).toHaveClass(
                'aria-invalid:ring-destructive/20',
                'aria-invalid:border-destructive'
            );
        });

        it('powinien obsłużyć atrybut required', () => {
            render(<Input required />);

            const input = screen.getByRole('textbox');
            expect(input).toBeRequired();
        });
    });

    describe('atrybuty HTML', () => {
        it('powinien obsłużyć atrybut name', () => {
            render(<Input name="username" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('name')).toBe('username');
        });

        it('powinien obsłużyć atrybut id', () => {
            render(<Input id="email-input" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('id')).toBe('email-input');
        });

        it('powinien obsłużyć atrybut maxLength', () => {
            render(<Input maxLength={10} />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('maxLength')).toBe('10');
        });

        it('powinien obsłużyć atrybut minLength', () => {
            render(<Input minLength={3} />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('minLength')).toBe('3');
        });

        it('powinien obsłużyć atrybut pattern', () => {
            render(<Input pattern="[0-9]*" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('pattern')).toBe('[0-9]*');
        });
    });

    describe('dostępność', () => {
        it('powinien obsłużyć aria-label', () => {
            render(<Input aria-label="Nazwa użytkownika" />);

            const input = screen.getByLabelText('Nazwa użytkownika');
            expect(input).toBeDefined();
        });

        it('powinien obsłużyć aria-describedby', () => {
            render(<Input aria-describedby="help-text" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('aria-describedby')).toBe('help-text');
        });

        it('powinien być dostępny przez klawiaturę', async () => {
            const user = userEvent.setup();
            render(<Input />);

            const input = screen.getByRole('textbox');

            await user.tab();
            expect(input).toHaveFocus();
        });
    });

    describe('typ file', () => {
        it('powinien obsłużyć typ file', () => {
            render(<Input type="file" />);

            const input = document.querySelector('input[type="file"]');
            expect(input).toBeDefined();
            expect(input).toHaveClass(
                'file:inline-flex',
                'file:h-7',
                'file:border-0',
                'file:bg-transparent',
                'file:text-sm',
                'file:font-medium'
            );
        });

        it('powinien obsłużyć atrybut accept dla plików', () => {
            render(<Input type="file" accept=".jpg,.png" />);

            const input = document.querySelector('input[type="file"]');
            expect(input?.getAttribute('accept')).toBe('.jpg,.png');
        });

        it('powinien obsłużyć atrybut multiple dla plików', () => {
            render(<Input type="file" multiple />);

            const input = document.querySelector('input[type="file"]');
            expect(input?.hasAttribute('multiple')).toBe(true);
        });
    });

    describe('przypadki brzegowe', () => {
        it('powinien obsłużyć wszystkie propsy HTML', () => {
            render(
                <Input
                    autoComplete="email"
                    autoFocus
                    readOnly
                    tabIndex={1}
                />
            );

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('autoComplete')).toBe('email');
            expect(input).toHaveFocus();
            expect(input).toHaveAttribute('readOnly');
            expect(input.getAttribute('tabIndex')).toBe('1');
        });

        it('powinien obsłużyć data atrybuty', () => {
            render(<Input data-testid="test-input" data-custom="value" />);

            const input = screen.getByRole('textbox');
            expect(input.getAttribute('data-testid')).toBe('test-input');
            expect(input.getAttribute('data-custom')).toBe('value');
        });
    });
});
