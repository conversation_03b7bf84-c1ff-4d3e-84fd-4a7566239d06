import { render, screen } from '@testing-library/react';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
    SelectLabel,
    SelectSeparator,
    SelectGroup
} from '@/components/ui/select';

// Mock hasPointerCapture and scrollIntoView for jsdom compatibility
Object.defineProperty(Element.prototype, 'hasPointerCapture', {
    value: jest.fn(() => false),
    writable: true,
});

Object.defineProperty(Element.prototype, 'scrollIntoView', {
    value: jest.fn(),
    writable: true,
});

describe('Select', () => {
    const BasicSelect = ({ onValueChange = () => { }, defaultValue = undefined, disabled = false }) => (
        <Select onValueChange={onValueChange} defaultValue={defaultValue} disabled={disabled}>
            <SelectTrigger>
                <SelectValue placeholder="Wybierz opcję" />
            </SelectTrigger>
            <SelectContent>
                <SelectItem value="option1">Opcja 1</SelectItem>
                <SelectItem value="option2">Opcja 2</SelectItem>
                <SelectItem value="option3">Opcja 3</SelectItem>
            </SelectContent>
        </Select>
    );

    describe('renderowanie podstawowe', () => {
        it('powinien renderować trigger z placeholder', () => {
            render(<BasicSelect />);

            const trigger = screen.getByRole('combobox');
            expect(trigger).toBeDefined();
            expect(screen.getByText('Wybierz opcję')).toBeDefined();
        });

        it('powinien mieć odpowiednie klasy CSS na trigger', () => {
            render(<BasicSelect />);

            const trigger = screen.getByRole('combobox');
            expect(trigger).toHaveClass(
                'flex',
                'h-10',
                'w-full',
                'items-center',
                'justify-between',
                'rounded-md',
                'border',
                'border-input',
                'bg-background',
                'px-3',
                'py-2',
                'text-sm'
            );
        });

        it('powinien wyświetlić ikonę chevron', () => {
            render(<BasicSelect />);

            const chevronIcon = document.querySelector('.lucide-chevron-down');
            expect(chevronIcon).toBeDefined();
        });
    });

    describe('focus i klawiatura', () => {
        it('powinien być focusowalny', () => {
            render(<BasicSelect />);

            const trigger = screen.getByRole('combobox');
            trigger.focus();
            expect(trigger).toHaveFocus();
        });

        it('powinien mieć odpowiednie klasy CSS dla focus', () => {
            render(<BasicSelect />);

            const trigger = screen.getByRole('combobox');
            expect(trigger).toHaveClass(
                'focus:outline-none',
                'focus:ring-2',
                'focus:ring-ring',
                'focus:ring-offset-2'
            );
        });
    });

    describe('wartości', () => {
        it('powinien obsłużyć wartość domyślną', () => {
            render(<BasicSelect defaultValue="option2" />);

            expect(screen.getByText('Opcja 2')).toBeDefined();
        });

        it('powinien renderować kontrolowany Select', () => {
            render(
                <Select value="option1" onValueChange={() => { }}>
                    <SelectTrigger>
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="option1">Opcja 1</SelectItem>
                    </SelectContent>
                </Select>
            );

            expect(screen.getByText('Opcja 1')).toBeDefined();
        });
    });

    describe('stan disabled', () => {
        it('powinien obsłużyć stan disabled', () => {
            render(<BasicSelect disabled />);

            const trigger = screen.getByRole('combobox');
            expect(trigger).toBeDisabled();
            expect(trigger).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50');
        });

    });

    describe('komponenty Select', () => {
        it('powinien renderować SelectTrigger z niestandardowymi klasami', () => {
            render(
                <Select>
                    <SelectTrigger className="custom-trigger">
                        <SelectValue placeholder="Test" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="test">Test</SelectItem>
                    </SelectContent>
                </Select>
            );

            const trigger = screen.getByRole('combobox');
            expect(trigger).toHaveClass('custom-trigger');
        });

        it('powinien renderować SelectItem z odpowiednimi klasami', () => {
            render(
                <Select>
                    <SelectTrigger>
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="test" className="custom-item">Test Item</SelectItem>
                    </SelectContent>
                </Select>
            );

            // SelectItem renderuje się tylko w otwartym dropdown, więc testujemy strukturę
            const trigger = screen.getByRole('combobox');
            expect(trigger).toBeDefined();
        });

        it('powinien renderować SelectLabel w grupie', () => {
            render(
                <Select>
                    <SelectTrigger>
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectGroup>
                            <SelectLabel>Test Label</SelectLabel>
                            <SelectItem value="test">Test Item</SelectItem>
                        </SelectGroup>
                    </SelectContent>
                </Select>
            );

            // SelectLabel renderuje się tylko w otwartym dropdown, więc testujemy strukturę
            const trigger = screen.getByRole('combobox');
            expect(trigger).toBeDefined();
        });

        it('powinien renderować SelectSeparator', () => {
            render(
                <div>
                    <SelectSeparator />
                </div>
            );

            const separator = document.querySelector('[role="separator"]');
            expect(separator).toBeDefined();
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie atrybuty ARIA', () => {
            render(<BasicSelect />);

            const trigger = screen.getByRole('combobox');
            expect(trigger.getAttribute('aria-expanded')).toBe('false');
            expect(trigger.getAttribute('role')).toBe('combobox');
        });

        it('powinien mieć odpowiednie atrybuty dla disabled', () => {
            render(<BasicSelect disabled />);

            const trigger = screen.getByRole('combobox');
            expect(trigger).toBeDisabled();
        });
    });

    describe('przypadki brzegowe', () => {
        it('powinien obsłużyć Select bez wartości', () => {
            render(
                <Select>
                    <SelectTrigger>
                        <SelectValue placeholder="Brak opcji" />
                    </SelectTrigger>
                    <SelectContent>
                        {/* Pusta lista */}
                    </SelectContent>
                </Select>
            );

            const trigger = screen.getByRole('combobox');
            expect(trigger).toBeDefined();
            expect(screen.getByText('Brak opcji')).toBeDefined();
        });

        it('powinien obsłużyć wszystkie propsy HTML', () => {
            render(
                <Select>
                    <SelectTrigger id="test-select" data-testid="select-trigger">
                        <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="test">Test</SelectItem>
                    </SelectContent>
                </Select>
            );

            const trigger = screen.getByRole('combobox');
            expect(trigger.getAttribute('id')).toBe('test-select');
            expect(trigger.getAttribute('data-testid')).toBe('select-trigger');
        });
    });
});
