import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogClose,
    DialogOverlay,
    DialogPortal
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';

// Mock hasPointerCapture and scrollIntoView for jsdom compatibility
Object.defineProperty(Element.prototype, 'hasPointerCapture', {
    value: jest.fn(() => false),
    writable: true,
});

Object.defineProperty(Element.prototype, 'scrollIntoView', {
    value: jest.fn(),
    writable: true,
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

describe('Dialog', () => {
    const BasicDialog = ({ open = false, onOpenChange = () => { } }) => (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogTrigger asChild>
                <Button>Otwórz Dialog</Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Tytuł Dialogu</DialogTitle>
                    <DialogDescription>Opis dialogu</DialogDescription>
                </DialogHeader>
                <div>Zawartość dialogu</div>
                <DialogFooter>
                    <DialogClose asChild>
                        <Button variant="outline">Anuluj</Button>
                    </DialogClose>
                    <Button>Potwierdź</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );

    describe('renderowanie podstawowe', () => {
        it('powinien renderować trigger', () => {
            render(<BasicDialog />);

            const trigger = screen.getByRole('button', { name: 'Otwórz Dialog' });
            expect(trigger).toBeDefined();
        });

        it('powinien mieć odpowiednie data-slot na komponencie Dialog', () => {
            render(
                <Dialog>
                    <div>Test</div>
                </Dialog>
            );

            const dialog = document.querySelector('[data-slot="dialog"]');
            expect(dialog).toBeDefined();
        });

        it('powinien renderować DialogTrigger z data-slot', () => {
            render(
                <Dialog>
                    <DialogTrigger asChild>
                        <Button>Test Trigger</Button>
                    </DialogTrigger>
                </Dialog>
            );

            const trigger = screen.getByRole('button');
            expect(trigger.getAttribute('data-slot')).toBe('dialog-trigger');
        });
    });

    describe('stan otwarty', () => {
        it('powinien wyświetlić zawartość gdy open=true', () => {
            render(<BasicDialog open={true} />);

            expect(screen.getByText('Tytuł Dialogu')).toBeDefined();
            expect(screen.getByText('Opis dialogu')).toBeDefined();
            expect(screen.getByText('Zawartość dialogu')).toBeDefined();
        });

        it('nie powinien wyświetlić zawartości gdy open=false', () => {
            render(<BasicDialog open={false} />);

            expect(screen.queryByText('Tytuł Dialogu')).toBeNull();
            expect(screen.queryByText('Opis dialogu')).toBeNull();
        });

        it('powinien wywołać onOpenChange przy zamykaniu', async () => {
            const handleOpenChange = jest.fn();
            const user = userEvent.setup();

            render(<BasicDialog open={true} onOpenChange={handleOpenChange} />);

            const closeButton = screen.getByRole('button', { name: 'Anuluj' });
            await user.click(closeButton);

            expect(handleOpenChange).toHaveBeenCalledWith(false);
        });
    });

    describe('DialogContent', () => {
        it('powinien mieć odpowiednie klasy CSS', () => {
            render(<BasicDialog open={true} />);

            const content = document.querySelector('[data-slot="dialog-content"]');
            expect(content).toHaveClass(
                'bg-background',
                'fixed',
                'top-[50%]',
                'left-[50%]',
                'z-50',
                'grid',
                'w-full',
                'translate-x-[-50%]',
                'translate-y-[-50%]',
                'gap-4',
                'rounded-lg',
                'border',
                'p-6',
                'shadow-lg'
            );
        });

        it('powinien renderować przycisk zamknięcia domyślnie', () => {
            render(<BasicDialog open={true} />);

            const closeButton = document.querySelector('[data-slot="dialog-close"]');
            expect(closeButton).toBeDefined();

            const closeIcon = document.querySelector('.lucide-x');
            expect(closeIcon).toBeDefined();
        });

        it('powinien ukryć przycisk zamknięcia gdy showCloseButton=false', () => {
            render(
                <Dialog open={true}>
                    <DialogContent showCloseButton={false}>
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                        <div>Test</div>
                    </DialogContent>
                </Dialog>
            );

            const closeButton = document.querySelector('[data-slot="dialog-close"]');
            expect(closeButton).toBeNull();
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(
                <Dialog open={true}>
                    <DialogContent className="custom-content">
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                        <div>Test</div>
                    </DialogContent>
                </Dialog>
            );

            const content = document.querySelector('[data-slot="dialog-content"]');
            expect(content).toHaveClass('custom-content');
        });
    });

    describe('DialogHeader', () => {
        it('powinien renderować header z odpowiednimi klasami', () => {
            render(
                <DialogHeader>
                    <div>Header content</div>
                </DialogHeader>
            );

            const header = document.querySelector('[data-slot="dialog-header"]');
            expect(header).toBeDefined();
            expect(header).toHaveClass(
                'flex',
                'flex-col',
                'gap-2',
                'text-center',
                'sm:text-left'
            );
            expect(screen.getByText('Header content')).toBeDefined();
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(
                <DialogHeader className="custom-header">
                    <div>Test</div>
                </DialogHeader>
            );

            const header = document.querySelector('[data-slot="dialog-header"]');
            expect(header).toHaveClass('custom-header');
        });
    });

    describe('DialogTitle', () => {
        it('powinien renderować tytuł z odpowiednimi klasami', () => {
            render(
                <Dialog open={true}>
                    <DialogContent>
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                    </DialogContent>
                </Dialog>
            );

            const title = document.querySelector('[data-slot="dialog-title"]');
            expect(title).toBeDefined();
            expect(title).toHaveClass(
                'text-lg',
                'leading-none',
                'font-semibold'
            );
            expect(screen.getByText('Test Title')).toBeDefined();
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(
                <Dialog open={true}>
                    <DialogContent>
                        <DialogTitle className="custom-title">Test</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                    </DialogContent>
                </Dialog>
            );

            const title = document.querySelector('[data-slot="dialog-title"]');
            expect(title).toHaveClass('custom-title');
        });
    });

    describe('DialogDescription', () => {
        it('powinien renderować opis z odpowiednimi klasami', () => {
            render(
                <Dialog open={true}>
                    <DialogContent>
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                    </DialogContent>
                </Dialog>
            );

            const description = document.querySelector('[data-slot="dialog-description"]');
            expect(description).toBeDefined();
            expect(description).toHaveClass(
                'text-muted-foreground',
                'text-sm'
            );
            expect(screen.getByText('Test Description')).toBeDefined();
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(
                <Dialog open={true}>
                    <DialogContent>
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription className="custom-desc">Test</DialogDescription>
                    </DialogContent>
                </Dialog>
            );

            const description = document.querySelector('[data-slot="dialog-description"]');
            expect(description).toHaveClass('custom-desc');
        });
    });

    describe('DialogFooter', () => {
        it('powinien renderować footer z odpowiednimi klasami', () => {
            render(
                <DialogFooter>
                    <div>Footer content</div>
                </DialogFooter>
            );

            const footer = document.querySelector('[data-slot="dialog-footer"]');
            expect(footer).toBeDefined();
            expect(footer).toHaveClass(
                'flex',
                'flex-col-reverse',
                'gap-2',
                'sm:flex-row',
                'sm:justify-end'
            );
            expect(screen.getByText('Footer content')).toBeDefined();
        });

        it('powinien dodać niestandardowe klasy CSS', () => {
            render(
                <DialogFooter className="custom-footer">
                    <div>Test</div>
                </DialogFooter>
            );

            const footer = document.querySelector('[data-slot="dialog-footer"]');
            expect(footer).toHaveClass('custom-footer');
        });
    });

    describe('DialogOverlay', () => {
        it('powinien renderować overlay z odpowiednimi klasami', () => {
            render(
                <Dialog open={true}>
                    <DialogContent>
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                        <div>Test</div>
                    </DialogContent>
                </Dialog>
            );

            const overlay = document.querySelector('[data-slot="dialog-overlay"]');
            expect(overlay).toBeDefined();
            expect(overlay).toHaveClass(
                'fixed',
                'inset-0',
                'z-50',
                'bg-black/50'
            );
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie atrybuty ARIA', () => {
            render(<BasicDialog open={true} />);

            const dialog = document.querySelector('[role="dialog"]');
            expect(dialog).toBeDefined();
        });

        it('powinien mieć screen reader text dla przycisku zamknięcia', () => {
            render(<BasicDialog open={true} />);

            expect(screen.getByText('Close')).toBeDefined();
        });
    });

    describe('przypadki brzegowe', () => {
        it('powinien obsłużyć Dialog bez trigger', () => {
            render(
                <Dialog open={true}>
                    <DialogContent>
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                        <div>Test bez trigger</div>
                    </DialogContent>
                </Dialog>
            );

            expect(screen.getByText('Test bez trigger')).toBeDefined();
        });

        it('powinien obsłużyć wszystkie propsy HTML', () => {
            render(
                <Dialog open={true}>
                    <DialogContent id="test-dialog" data-testid="dialog-content">
                        <DialogTitle>Test Title</DialogTitle>
                        <DialogDescription>Test Description</DialogDescription>
                        <div>Test</div>
                    </DialogContent>
                </Dialog>
            );

            const content = document.querySelector('[data-slot="dialog-content"]');
            expect(content?.getAttribute('id')).toBe('test-dialog');
            expect(content?.getAttribute('data-testid')).toBe('dialog-content');
        });
    });
});
