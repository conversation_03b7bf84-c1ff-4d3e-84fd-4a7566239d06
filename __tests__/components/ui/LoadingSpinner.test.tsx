import { render, screen } from '@testing-library/react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

describe('LoadingSpinner', () => {
    describe('renderowanie podstawowe', () => {
        it('powinien renderować spinner z domyślnymi właściwościami', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toBeInTheDocument();
            expect(spinner).toHaveAttribute('aria-label', 'Loading');
            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });
    });

    describe('rozmiary', () => {
        it('powinien renderować mały spinner', () => {
            render(<LoadingSpinner size="sm" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('h-4', 'w-4');
        });

        it('powinien renderować średni spinner (domyślny)', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('h-6', 'w-6');
        });

        it('powinien renderować duży spinner', () => {
            render(<LoadingSpinner size="lg" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('h-8', 'w-8');
        });
    });

    describe('warianty kolorów', () => {
        it('powinien renderować spinner z domyślnym kolorem', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('border-purple-600', 'border-t-transparent');
        });

        it('powinien renderować spinner z białym kolorem', () => {
            render(<LoadingSpinner color="white" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('border-white', 'border-t-transparent');
        });

        it('powinien renderować spinner z szarym kolorem', () => {
            render(<LoadingSpinner color="gray" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('border-gray-600', 'border-t-transparent');
        });
    });

    describe('niestandardowe klasy CSS', () => {
        it('powinien dodać niestandardowe klasy CSS', () => {
            const customClass = 'my-custom-class';
            render(<LoadingSpinner className={customClass} />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass(customClass);
        });

        it('powinien zachować domyślne klasy przy dodawaniu niestandardowych', () => {
            render(<LoadingSpinner className="custom-class" />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('animate-spin', 'rounded-full', 'border-2', 'custom-class');
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie atrybuty ARIA', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveAttribute('role', 'status');
            expect(spinner).toHaveAttribute('aria-label', 'Loading');
        });

        it('powinien mieć ukryty tekst dla czytników ekranu', () => {
            render(<LoadingSpinner />);

            const hiddenText = screen.getByText('Loading...');
            expect(hiddenText).toHaveClass('sr-only');
        });
    });

    describe('animacja', () => {
        it('powinien mieć klasę animacji', () => {
            render(<LoadingSpinner />);

            const spinner = screen.getByRole('status');
            expect(spinner).toHaveClass('animate-spin');
        });
    });
});
