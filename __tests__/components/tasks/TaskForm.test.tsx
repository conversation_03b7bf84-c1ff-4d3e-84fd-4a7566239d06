import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TaskForm from '@/components/tasks/TaskForm';
import { TaskWeight, TaskFrequency } from '@prisma/client';

// Mock hooks
jest.mock('@/hooks/useTasks', () => ({
    useTaskMutations: jest.fn(() => ({
        createTask: jest.fn(),
        updateTask: jest.fn(),
        loading: false,
        error: null,
    })),
}));

jest.mock('@/hooks/use-toast', () => ({
    useToast: jest.fn(() => ({
        toast: jest.fn(),
    })),
}));

// Mock TaskForm components
jest.mock('@/components/tasks/TaskForm/components/BasicInfoSection', () => ({
    BasicInfoSection: ({ formData, onFieldChange }: any) => (
        <div data-testid="basic-info-section">
            <input
                data-testid="title-input"
                value={formData.title}
                onChange={(e) => onFieldChange('title', e.target.value)}
                placeholder="Tytuł zadania"
            />
            <textarea
                data-testid="description-input"
                value={formData.description}
                onChange={(e) => onFieldChange('description', e.target.value)}
                placeholder="Opis zadania"
            />
        </div>
    ),
}));

jest.mock('@/components/tasks/TaskForm/components/PropertiesSection', () => ({
    PropertiesSection: ({ formData, onFieldChange }: any) => (
        <div data-testid="properties-section">
            <input
                data-testid="points-input"
                type="number"
                value={formData.points}
                onChange={(e) => onFieldChange('points', parseInt(e.target.value))}
                min="1"
                max="10"
            />
            <select
                data-testid="weight-select"
                value={formData.weight}
                onChange={(e) => onFieldChange('weight', e.target.value)}
            >
                <option value={TaskWeight.LIGHT}>Lekkie</option>
                <option value={TaskWeight.NORMAL}>Normalne</option>
                <option value={TaskWeight.HEAVY}>Ciężkie</option>
                <option value={TaskWeight.CRITICAL}>Krytyczne</option>
            </select>
            <select
                data-testid="frequency-select"
                value={formData.frequency}
                onChange={(e) => onFieldChange('frequency', e.target.value)}
            >
                <option value={TaskFrequency.ONCE}>Jednorazowe</option>
                <option value={TaskFrequency.DAILY}>Codziennie</option>
                <option value={TaskFrequency.WEEKLY}>Tygodniowo</option>
                <option value={TaskFrequency.MONTHLY}>Miesięcznie</option>
                <option value={TaskFrequency.CUSTOM}>Niestandardowe</option>
            </select>
        </div>
    ),
}));

jest.mock('@/components/tasks/TaskForm/components/DateTimeSection', () => ({
    DateTimeSection: ({ formData, onFieldChange }: any) => (
        <div data-testid="datetime-section">
            <input
                data-testid="start-date-input"
                type="date"
                value={formData.startDateTime}
                onChange={(e) => onFieldChange('startDateTime', e.target.value)}
            />
            <input
                data-testid="all-day-checkbox"
                type="checkbox"
                checked={formData.allDay}
                onChange={(e) => onFieldChange('allDay', e.target.checked)}
            />
        </div>
    ),
}));

jest.mock('@/components/tasks/TaskForm/components/RecurrenceSection', () => ({
    RecurrenceSection: ({ formData, onFieldChange }: any) => (
        <div data-testid="recurrence-section">
            <input
                data-testid="recurrence-rule-input"
                value={formData.recurrenceRule}
                onChange={(e) => onFieldChange('recurrenceRule', e.target.value)}
                placeholder="Reguła powtarzania"
            />
        </div>
    ),
}));

jest.mock('@/components/tasks/TaskForm/components/AssignmentSection', () => ({
    AssignmentSection: ({ formData, familyMembers, onFieldChange }: any) => (
        <div data-testid="assignment-section">
            <select
                data-testid="assigned-members-select"
                multiple
                value={formData.assignedMemberIds}
                onChange={(e) => {
                    const values = Array.from(e.target.selectedOptions, (option: any) => option.value);
                    onFieldChange('assignedMemberIds', values);
                }}
            >
                {familyMembers.map((member: any) => (
                    <option key={member.id} value={member.id}>
                        {member.user.firstName} {member.user.lastName}
                    </option>
                ))}
            </select>
        </div>
    ),
}));

jest.mock('@/components/tasks/TaskForm/components/TaskPreview', () => ({
    TaskPreview: ({ formData }: any) => (
        <div data-testid="task-preview">
            Podgląd: {formData.title}
        </div>
    ),
}));

jest.mock('@/components/tasks/TaskForm/components/FormActions', () => ({
    FormActions: ({ onClose, loading, isTitleEmpty, mode }: any) => (
        <div data-testid="form-actions">
            <button type="button" onClick={onClose} data-testid="cancel-button">
                Anuluj
            </button>
            <button
                type="submit"
                disabled={loading || isTitleEmpty}
                data-testid="submit-button"
            >
                {mode === 'create' ? 'Utwórz zadanie' : 'Zapisz zmiany'}
            </button>
        </div>
    ),
}));

describe('TaskForm', () => {
    const mockFamilyMembers = [
        {
            id: 'member-1',
            familyId: 'family-1',
            userId: 'user-1',
            role: 'PARENT' as const,
            isAdult: true,
            points: 100,
            joinedAt: new Date(),
            lastActive: new Date(),
            user: {
                id: 'user-1',
                firstName: 'Jan',
                lastName: 'Kowalski',
                email: '<EMAIL>',
            },
        },
        {
            id: 'member-2',
            familyId: 'family-1',
            userId: 'user-2',
            role: 'CHILD' as const,
            isAdult: false,
            points: 50,
            joinedAt: new Date(),
            lastActive: new Date(),
            user: {
                id: 'user-2',
                firstName: 'Anna',
                lastName: 'Kowalska',
                email: '<EMAIL>',
            },
        },
    ];

    const defaultProps = {
        familyId: 'family-1',
        familyMembers: mockFamilyMembers,
        onClose: jest.fn(),
        onSuccess: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('renderowanie podstawowe', () => {
        it('powinien renderować formularz w trybie tworzenia', () => {
            render(<TaskForm {...defaultProps} mode="create" />);

            expect(screen.getByTestId('basic-info-section')).toBeInTheDocument();
            expect(screen.getByTestId('properties-section')).toBeInTheDocument();
            expect(screen.getByTestId('datetime-section')).toBeInTheDocument();
            expect(screen.getByTestId('assignment-section')).toBeInTheDocument();
            expect(screen.getByTestId('task-preview')).toBeInTheDocument();
            expect(screen.getByTestId('form-actions')).toBeInTheDocument();
        });

        it('powinien renderować formularz w trybie edycji', () => {
            const mockTask = {
                id: 'task-1',
                title: 'Testowe zadanie',
                description: 'Opis zadania',
                points: 5,
                weight: TaskWeight.NORMAL,
                frequency: TaskFrequency.ONCE,
                startDateTime: new Date(),
                allDay: true,
                assignments: [],
            };

            render(<TaskForm {...defaultProps} mode="edit" task={mockTask} />);

            expect(screen.getByTestId('basic-info-section')).toBeInTheDocument();
            expect(screen.getByDisplayValue('Testowe zadanie')).toBeInTheDocument();
        });

        it('powinien renderować formularz mobilny', () => {
            render(<TaskForm {...defaultProps} isMobile={true} />);

            const mobileContainer = screen.getByTestId('basic-info-section').closest('.fixed');
            expect(mobileContainer).toHaveClass('fixed', 'inset-0', 'bg-white', 'z-50');
        });
    });

    describe('pola formularza', () => {
        it('powinien umożliwić wprowadzenie tytułu zadania', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const titleInput = screen.getByTestId('title-input');
            await user.type(titleInput, 'Nowe zadanie');

            expect(titleInput).toHaveValue('Nowe zadanie');
        });

        it('powinien umożliwić wprowadzenie opisu zadania', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const descriptionInput = screen.getByTestId('description-input');
            await user.type(descriptionInput, 'Szczegółowy opis zadania');

            expect(descriptionInput).toHaveValue('Szczegółowy opis zadania');
        });

        it('powinien umożliwić zmianę punktów zadania', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const pointsInput = screen.getByTestId('points-input');

            // Sprawdź domyślną wartość
            expect(pointsInput).toHaveValue(3);

            // Zmień na prawidłową wartość (1-10) - użyj fireEvent zamiast userEvent
            fireEvent.change(pointsInput, { target: { value: '7' } });

            expect(pointsInput).toHaveValue(7);
        });

        it('powinien umożliwić zmianę wagi zadania', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const weightSelect = screen.getByTestId('weight-select');
            await user.selectOptions(weightSelect, TaskWeight.HEAVY);

            expect(weightSelect).toHaveValue(TaskWeight.HEAVY);
        });

        it('powinien umożliwić zmianę częstotliwości zadania', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const frequencySelect = screen.getByTestId('frequency-select');
            await user.selectOptions(frequencySelect, TaskFrequency.DAILY);

            expect(frequencySelect).toHaveValue(TaskFrequency.DAILY);
        });
    });

    describe('sekcja powtarzania', () => {
        it('powinien pokazać sekcję powtarzania dla częstotliwości niestandardowej', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const frequencySelect = screen.getByTestId('frequency-select');
            await user.selectOptions(frequencySelect, TaskFrequency.CUSTOM);

            expect(screen.getByTestId('recurrence-section')).toBeInTheDocument();
        });

        it('nie powinien pokazać sekcji powtarzania dla innych częstotliwości', () => {
            render(<TaskForm {...defaultProps} />);

            expect(screen.queryByTestId('recurrence-section')).not.toBeInTheDocument();
        });
    });

    describe('przypisywanie członków', () => {
        it('powinien umożliwić przypisanie członków rodziny', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const membersSelect = screen.getByTestId('assigned-members-select');
            await user.selectOptions(membersSelect, ['member-1', 'member-2']);

            const selectedOptions = Array.from(membersSelect.selectedOptions, option => option.value);
            expect(selectedOptions).toEqual(['member-1', 'member-2']);
        });
    });

    describe('akcje formularza', () => {
        it('powinien wywołać onClose po kliknięciu anuluj', async () => {
            const user = userEvent.setup();
            const onClose = jest.fn();
            render(<TaskForm {...defaultProps} onClose={onClose} />);

            const cancelButton = screen.getByTestId('cancel-button');
            await user.click(cancelButton);

            expect(onClose).toHaveBeenCalledTimes(1);
        });

        it('powinien wyłączyć przycisk submit gdy tytuł jest pusty', () => {
            render(<TaskForm {...defaultProps} />);

            const submitButton = screen.getByTestId('submit-button');
            expect(submitButton).toBeDisabled();
        });

        it('powinien włączyć przycisk submit gdy tytuł jest wypełniony', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const titleInput = screen.getByTestId('title-input');
            await user.type(titleInput, 'Zadanie z tytułem');

            const submitButton = screen.getByTestId('submit-button');
            expect(submitButton).not.toBeDisabled();
        });
    });

    describe('walidacja', () => {
        it('powinien pokazać błąd gdy brak godziny rozpoczęcia dla zadań nie całodniowych', async () => {
            const user = userEvent.setup();
            const { useToast } = require('@/hooks/use-toast');
            const mockToast = jest.fn();
            useToast.mockReturnValue({ toast: mockToast });

            render(<TaskForm {...defaultProps} />);

            // Ustaw zadanie jako nie całodniowe
            const allDayCheckbox = screen.getByTestId('all-day-checkbox');
            await user.click(allDayCheckbox);

            // Wypełnij tytuł i wyślij formularz
            const titleInput = screen.getByTestId('title-input');
            await user.type(titleInput, 'Zadanie testowe');

            const form = screen.getByTestId('basic-info-section').closest('form');
            fireEvent.submit(form!);

            await waitFor(() => {
                expect(mockToast).toHaveBeenCalledWith({
                    title: 'Błąd walidacji',
                    description: 'Godzina rozpoczęcia jest wymagana dla wydarzeń o określonym czasie.',
                    variant: 'destructive',
                });
            });
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie etykiety dla pól formularza', () => {
            render(<TaskForm {...defaultProps} />);

            expect(screen.getByPlaceholderText('Tytuł zadania')).toBeInTheDocument();
            expect(screen.getByPlaceholderText('Opis zadania')).toBeInTheDocument();
        });

        it('powinien umożliwić nawigację klawiaturą', async () => {
            const user = userEvent.setup();
            render(<TaskForm {...defaultProps} />);

            const titleInput = screen.getByTestId('title-input');
            await user.tab();
            expect(titleInput).toHaveFocus();
        });
    });
});
