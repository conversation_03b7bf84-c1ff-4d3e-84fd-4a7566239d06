import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskFilters } from '@/components/tasks/TaskFilters';
import { TaskStatus, TaskWeight, TaskFrequency } from '@prisma/client';

// Mock danych testowych
const mockFamilyMembers = [
    {
        id: 'member-1',
        familyId: 'family-1',
        userId: 'user-1',
        role: 'PARENT' as const,
        points: 100,
        user: {
            id: 'user-1',
            firstName: 'Jan',
            lastName: '<PERSON>walski',
            email: '<EMAIL>',
        },
    },
    {
        id: 'member-2',
        familyId: 'family-1',
        userId: 'user-2',
        role: 'CHILD' as const,
        points: 50,
        user: {
            id: 'user-2',
            firstName: 'Anna',
            lastName: 'Kowalska',
            email: '<EMAIL>',
        },
    },
];

const defaultFilters = {
    includeCompleted: false,
};

const mockOnFiltersChange = jest.fn();

describe('TaskFilters', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Renderowanie podstawowe', () => {
        it('powinien renderować pole wyszukiwania', () => {
            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            expect(screen.getByPlaceholderText('Szukaj zadań...')).toBeInTheDocument();
        });

        it('powinien renderować checkbox "Pokaż ukończone"', () => {
            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            expect(screen.getByLabelText('Pokaż ukończone')).toBeInTheDocument();
        });

        it('powinien renderować przycisk "Wyczyść" gdy są aktywne filtry', () => {
            const filtersWithSearch = { ...defaultFilters, search: 'test' };

            render(
                <TaskFilters
                    filters={filtersWithSearch}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            expect(screen.getByText('Wyczyść')).toBeInTheDocument();
        });
    });

    describe('Funkcjonalność wyszukiwania', () => {
        it('powinien wywołać onFiltersChange przy wpisywaniu w pole wyszukiwania', async () => {
            const user = userEvent.setup();

            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const searchInput = screen.getByPlaceholderText('Szukaj zadań...');
            await user.type(searchInput, 'test');

            // userEvent.type wywołuje onChange dla każdej litery, sprawdzamy ostatnie wywołanie
            expect(mockOnFiltersChange).toHaveBeenLastCalledWith({
                ...defaultFilters,
                search: 'test',
            });
        });

        it('powinien usunąć filtr wyszukiwania gdy pole jest puste', async () => {
            const user = userEvent.setup();
            const filtersWithSearch = { ...defaultFilters, search: 'test' };

            render(
                <TaskFilters
                    filters={filtersWithSearch}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const searchInput = screen.getByDisplayValue('test');
            await user.clear(searchInput);

            expect(mockOnFiltersChange).toHaveBeenCalledWith({
                ...defaultFilters,
                search: undefined,
            });
        });
    });

    describe('Checkbox "Pokaż ukończone"', () => {
        it('powinien wywołać onFiltersChange przy zmianie stanu checkbox', async () => {
            const user = userEvent.setup();

            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const checkbox = screen.getByLabelText('Pokaż ukończone');
            await user.click(checkbox);

            expect(mockOnFiltersChange).toHaveBeenCalledWith({
                ...defaultFilters,
                includeCompleted: true,
            });
        });

        it('powinien być zaznaczony gdy includeCompleted jest true', () => {
            const filtersWithCompleted = { ...defaultFilters, includeCompleted: true };

            render(
                <TaskFilters
                    filters={filtersWithCompleted}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const checkbox = screen.getByLabelText('Pokaż ukończone');
            expect(checkbox).toBeChecked();
        });
    });

    describe('Przycisk "Wyczyść"', () => {
        it('powinien wyczyścić wszystkie filtry po kliknięciu', async () => {
            const user = userEvent.setup();
            const filtersWithMultiple = {
                search: 'test',
                status: [TaskStatus.ACTIVE],
                includeCompleted: true,
            };

            render(
                <TaskFilters
                    filters={filtersWithMultiple}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const clearButton = screen.getByText('Wyczyść');
            await user.click(clearButton);

            expect(mockOnFiltersChange).toHaveBeenCalledWith({
                includeCompleted: false,
            });
        });

        it('powinien pokazać liczbę aktywnych filtrów', () => {
            const filtersWithMultiple = {
                search: 'test',
                status: [TaskStatus.ACTIVE],
                weight: [TaskWeight.HIGH],
                includeCompleted: false,
            };

            render(
                <TaskFilters
                    filters={filtersWithMultiple}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            // Powinien pokazać badge z liczbą aktywnych filtrów (3: search, status, weight)
            expect(screen.getByText('3')).toBeInTheDocument();
        });
    });

    describe('Filtry statusu', () => {
        it('powinien otworzyć popover z opcjami statusu po kliknięciu', async () => {
            const user = userEvent.setup();

            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            // Znajdź przycisk Status
            const statusButton = screen.getByText('Status');
            await user.click(statusButton);

            await waitFor(() => {
                expect(screen.getByText('Aktywne')).toBeInTheDocument();
                expect(screen.getByText('Ukończone')).toBeInTheDocument();
                expect(screen.getByText('Przeterminowane')).toBeInTheDocument();
            });
        });
    });

    describe('Filtry członków', () => {
        it('powinien wyświetlić członków rodziny w filtrze', async () => {
            const user = userEvent.setup();

            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const membersButton = screen.getByText('Członkowie');
            await user.click(membersButton);

            await waitFor(() => {
                expect(screen.getByText('Jan Kowalski')).toBeInTheDocument();
                expect(screen.getByText('Anna Kowalska')).toBeInTheDocument();
            });
        });
    });

    describe('Responsywność i dostępność', () => {
        it('powinien mieć odpowiednie aria-labels', () => {
            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const searchInput = screen.getByPlaceholderText('Szukaj zadań...');
            expect(searchInput).toHaveAttribute('type', 'text');
        });

        it('powinien obsługiwać nawigację klawiaturą', async () => {
            const user = userEvent.setup();

            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            const searchInput = screen.getByPlaceholderText('Szukaj zadań...');

            // Focus na pole wyszukiwania
            await user.tab();
            expect(searchInput).toHaveFocus();
        });
    });

    describe('Przypadki brzegowe', () => {
        it('powinien obsłużyć pustą listę członków rodziny', () => {
            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={[]}
                />
            );

            expect(screen.getByPlaceholderText('Szukaj zadań...')).toBeInTheDocument();
        });

        it('powinien obsłużyć undefined filtry', () => {
            render(
                <TaskFilters
                    filters={{}}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={mockFamilyMembers}
                />
            );

            expect(screen.getByPlaceholderText('Szukaj zadań...')).toBeInTheDocument();
        });

        it('powinien obsłużyć bardzo długie nazwy członków', () => {
            const membersWithLongNames = [
                {
                    ...mockFamilyMembers[0],
                    user: {
                        ...mockFamilyMembers[0].user,
                        firstName: 'Bardzo długie imię które może nie zmieścić się',
                        lastName: 'Bardzo długie nazwisko które może nie zmieścić się',
                    },
                },
            ];

            render(
                <TaskFilters
                    filters={defaultFilters}
                    onFiltersChange={mockOnFiltersChange}
                    familyMembers={membersWithLongNames}
                />
            );

            expect(screen.getByPlaceholderText('Szukaj zadań...')).toBeInTheDocument();
        });
    });
});
