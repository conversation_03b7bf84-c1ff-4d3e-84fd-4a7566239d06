import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskList } from '@/components/tasks/TaskList';
import { TaskStatus, TaskWeight, TaskFrequency, CompletionStatus } from '@prisma/client';

// Mock hooks
jest.mock('@/hooks/useSuspensions', () => ({
    useSuspensions: jest.fn(() => ({
        getTaskSuspensionStatus: jest.fn(() => ({
            isSuspended: false,
            suspensionReason: null,
            suspendedUntil: null,
        })),
    })),
}));

// Mock components
jest.mock('@/components/tasks/TaskCard', () => ({
    TaskCard: ({ task, onToggleSelection, isSelected, bulkSelectMode }: any) => (
        <div data-testid={`task-card-${task.id}`} className="task-card">
            <h3>{task.title}</h3>
            <p>{task.description}</p>
            <span data-testid={`task-points-${task.id}`}>{task.points} pkt</span>
            <span data-testid={`task-status-${task.id}`}>{task.status}</span>
            {bulkSelectMode && (
                <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={onToggleSelection}
                    data-testid={`task-checkbox-${task.id}`}
                />
            )}
        </div>
    ),
}));

jest.mock('@/components/tasks/BulkTaskActions', () => ({
    BulkTaskActions: ({ selectedCompletions, onClearSelection }: any) => (
        <div data-testid="bulk-actions">
            <span>Zaznaczone: {selectedCompletions.length}</span>
            <button onClick={onClearSelection} data-testid="clear-selection">
                Wyczyść zaznaczenie
            </button>
        </div>
    ),
}));

describe('TaskList', () => {
    const mockCurrentMember = {
        id: 'member-1',
        familyId: 'family-1',
        userId: 'user-1',
        role: 'PARENT' as const,
        isAdult: true,
        points: 100,
        joinedAt: new Date(),
        lastActive: new Date(),
    };

    const mockFamilyMembers = [
        {
            id: 'member-1',
            familyId: 'family-1',
            userId: 'user-1',
            role: 'PARENT' as const,
            isAdult: true,
            points: 100,
            joinedAt: new Date(),
            lastActive: new Date(),
            user: {
                id: 'user-1',
                firstName: 'Jan',
                lastName: 'Kowalski',
                email: '<EMAIL>',
            },
        },
        {
            id: 'member-2',
            familyId: 'family-1',
            userId: 'user-2',
            role: 'CHILD' as const,
            isAdult: false,
            points: 50,
            joinedAt: new Date(),
            lastActive: new Date(),
            user: {
                id: 'user-2',
                firstName: 'Anna',
                lastName: 'Kowalska',
                email: '<EMAIL>',
            },
        },
    ];

    const mockTasks = [
        {
            id: 'task-1',
            title: 'Zadanie aktywne',
            description: 'Opis zadania aktywnego',
            points: 5,
            weight: TaskWeight.NORMAL,
            frequency: TaskFrequency.ONCE,
            status: TaskStatus.ACTIVE,
            dueDate: new Date(Date.now() + 86400000), // jutro
            createdAt: new Date(),
            updatedAt: new Date(),
            familyId: 'family-1',
            createdById: 'user-1',
            assignments: [
                {
                    id: 'assignment-1',
                    taskId: 'task-1',
                    memberId: 'member-1',
                    assignedAt: new Date(),
                    member: mockFamilyMembers[0],
                },
            ],
            completions: [],
            suspensions: [],
        },
        {
            id: 'task-2',
            title: 'Zadanie ukończone',
            description: 'Opis zadania ukończonego',
            points: 3,
            weight: TaskWeight.LIGHT,
            frequency: TaskFrequency.ONCE,
            status: TaskStatus.ACTIVE,
            dueDate: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
            familyId: 'family-1',
            createdById: 'user-1',
            assignments: [
                {
                    id: 'assignment-2',
                    taskId: 'task-2',
                    memberId: 'member-2',
                    assignedAt: new Date(),
                    member: mockFamilyMembers[1],
                },
            ],
            completions: [
                {
                    id: 'completion-1',
                    taskId: 'task-2',
                    memberId: 'member-2',
                    status: CompletionStatus.APPROVED,
                    completedAt: new Date(),
                    verifiedAt: new Date(),
                    verifiedById: 'user-1',
                },
            ],
            suspensions: [],
        },
        {
            id: 'task-3',
            title: 'Zadanie przeterminowane',
            description: 'Opis zadania przeterminowanego',
            points: 7,
            weight: TaskWeight.HEAVY,
            frequency: TaskFrequency.ONCE,
            status: TaskStatus.ACTIVE,
            dueDate: new Date(Date.now() - 86400000), // wczoraj
            createdAt: new Date(),
            updatedAt: new Date(),
            familyId: 'family-1',
            createdById: 'user-1',
            assignments: [
                {
                    id: 'assignment-3',
                    taskId: 'task-3',
                    memberId: 'member-1',
                    assignedAt: new Date(),
                    member: mockFamilyMembers[0],
                },
            ],
            completions: [],
            suspensions: [],
        },
        {
            id: 'task-4',
            title: 'Zadanie do weryfikacji',
            description: 'Opis zadania do weryfikacji',
            points: 4,
            weight: TaskWeight.NORMAL,
            frequency: TaskFrequency.ONCE,
            status: TaskStatus.ACTIVE,
            dueDate: new Date(),
            createdAt: new Date(),
            updatedAt: new Date(),
            familyId: 'family-1',
            createdById: 'user-1',
            assignments: [
                {
                    id: 'assignment-4',
                    taskId: 'task-4',
                    memberId: 'member-2',
                    assignedAt: new Date(),
                    member: mockFamilyMembers[1],
                },
            ],
            completions: [
                {
                    id: 'completion-2',
                    taskId: 'task-4',
                    memberId: 'member-2',
                    status: CompletionStatus.PENDING,
                    completedAt: new Date(),
                },
            ],
            suspensions: [],
        },
    ];

    const defaultProps = {
        familyId: 'family-1',
        tasks: mockTasks,
        currentMember: mockCurrentMember,
        familyMembers: mockFamilyMembers,
        canManageTasks: true,
        canAssignTasks: true,
        canCompleteTasks: true,
        onTaskUpdate: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('renderowanie podstawowe', () => {
        it('powinien renderować listę zadań', () => {
            render(<TaskList {...defaultProps} />);

            expect(screen.getByText('Zadania rodziny na dziś')).toBeInTheDocument();
            // TaskList renderuje zadania w dwóch miejscach (główny widok + siatka)
            // Zadania ukończone są domyślnie filtrowane
            expect(screen.getAllByTestId('task-card-task-1')).toHaveLength(2);
            expect(screen.getAllByTestId('task-card-task-3')).toHaveLength(2);
            expect(screen.getAllByTestId('task-card-task-4')).toHaveLength(2);
            // Zadanie ukończone nie jest widoczne domyślnie
            expect(screen.queryByTestId('task-card-task-2')).not.toBeInTheDocument();
        });

        it('powinien pokazać statystyki zadań', () => {
            render(<TaskList {...defaultProps} />);

            // Sprawdź czy są wyświetlane statystyki
            expect(screen.getAllByText(/ukończone/)).toHaveLength(3); // w różnych miejscach
        });

        it('powinien pokazać komunikat gdy brak zadań', () => {
            render(<TaskList {...defaultProps} tasks={[]} />);

            expect(screen.getAllByText('Brak zadań')).toHaveLength(2); // w głównym widoku i siatce
            expect(screen.getAllByText('Nie ma jeszcze żadnych zadań w tej rodzinie.')).toHaveLength(2);
        });
    });

    describe('filtrowanie zadań', () => {
        it('powinien pokazać zadania w głównym widoku', () => {
            render(<TaskList {...defaultProps} />);

            // Sprawdź czy zadania są renderowane (uwzględniając duplikaty)
            // 3 aktywne zadania x 2 miejsca = 6 zadań (ukończone są filtrowane)
            expect(screen.getAllByTestId(/task-card-task-/)).toHaveLength(6);
        });
    });

    describe('podstawowe funkcjonalności', () => {
        it('powinien renderować zadania w odpowiedniej kolejności', () => {
            render(<TaskList {...defaultProps} />);

            // Sprawdź czy zadania są renderowane
            const taskCards = screen.getAllByTestId(/task-card-task-/);
            expect(taskCards.length).toBeGreaterThan(0);
        });
    });

    describe('przełączniki widoku', () => {
        it('powinien przełączać widok ukończonych zadań', async () => {
            const user = userEvent.setup();
            render(<TaskList {...defaultProps} />);

            const showCompletedButton = screen.getByText('Pokaż ukończone');
            await user.click(showCompletedButton);

            expect(screen.getByText('Ukryj ukończone')).toBeInTheDocument();
        });

        it('powinien przełączać widok zawieszonych zadań', async () => {
            const user = userEvent.setup();
            render(<TaskList {...defaultProps} />);

            const showSuspendedButton = screen.getByText('Pokaż zawieszone');
            await user.click(showSuspendedButton);

            expect(screen.getByText('Ukryj zawieszone')).toBeInTheDocument();
        });
    });

    describe('uprawnienia', () => {
        it('nie powinien pokazać przycisku tworzenia zadania gdy brak uprawnień', () => {
            render(
                <TaskList
                    {...defaultProps}
                    tasks={[]}
                    canManageTasks={false}
                />
            );

            expect(screen.queryByText('Utwórz pierwsze zadanie')).not.toBeInTheDocument();
        });
    });

    describe('dostępność', () => {
        it('powinien mieć odpowiednie nagłówki', () => {
            render(<TaskList {...defaultProps} />);

            expect(screen.getByText('Zadania rodziny na dziś')).toBeInTheDocument();
        });
    });
});
