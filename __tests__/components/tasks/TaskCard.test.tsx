import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { TaskCard } from '@/components/tasks/TaskCard';
import { mockTask, mockMember } from '@/lib/test-utils/mocks';
import { TaskStatus, CompletionStatus, TaskWeight, TaskFrequency } from '@prisma/client';
import { TaskWithRelations } from '@/hooks/useTasks';

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

// Mock alert and confirm
Object.defineProperty(window, 'alert', {
    value: jest.fn(),
    writable: true,
});
Object.defineProperty(window, 'confirm', {
    value: jest.fn(),
    writable: true,
});
const mockAlert = window.alert as jest.Mock;
const mockConfirm = window.confirm as jest.Mock;

describe('TaskCard', () => {
    const mockTaskWithRelations: TaskWithRelations = {
        ...mockTask,
        assignedTo: {
            ...mockMember,
            user: {
                id: 'user-1',
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
            },
        },
        assignedByUser: {
            ...mockMember,
            user: {
                id: 'user-2',
                firstName: 'Jane',
                lastName: 'Smith',
                email: '<EMAIL>',
            },
        },
        completions: [],
    };

    const defaultProps = {
        task: mockTaskWithRelations,
        currentMember: mockMember,
        canManageTasks: true,
        canAssignTasks: true,
        canCompleteTasks: true,
        onTaskUpdate: jest.fn(),
    };

    beforeEach(() => {
        jest.clearAllMocks();
        mockFetch.mockClear();
    });

    describe('rendering', () => {
        it('should render task information correctly', () => {
            render(<TaskCard {...defaultProps} />);

            expect(screen.getByText(mockTaskWithRelations.title)).toBeInTheDocument();
            expect(screen.getByText('Przypisane do:')).toBeInTheDocument();
            expect(screen.getByText('Test User')).toBeInTheDocument();
        });

        it('should show task assigned to current user', () => {
            const taskAssignedToCurrentUser = {
                ...mockTaskWithRelations,
                assignments: [{ memberId: mockMember.id, member: mockMember }],
                assignedTo: {
                    ...mockMember,
                    user: {
                        id: mockMember.userId,
                        firstName: 'Current',
                        lastName: 'User',
                        email: '<EMAIL>',
                    },
                },
            };

            render(<TaskCard {...defaultProps} task={taskAssignedToCurrentUser} />);

            expect(screen.getByText('Test User')).toBeInTheDocument();
            expect(screen.getByText('(Ty)')).toBeInTheDocument();
        });

        it('should show unassigned task', () => {
            const unassignedTask = {
                ...mockTaskWithRelations,
                assignedTo: null,
                assignments: [],
            };

            render(<TaskCard {...defaultProps} task={unassignedTask} />);

            expect(screen.getByText('Nieprzypisane')).toBeInTheDocument();
        });

        it('should display points when available', () => {
            const taskWithPoints = {
                ...mockTaskWithRelations,
                points: 5,
            };

            render(<TaskCard {...defaultProps} task={taskWithPoints} />);

            expect(screen.getByText('5')).toBeInTheDocument();
        });

        it('should show due date for active tasks', () => {
            const taskWithDueDate = {
                ...mockTaskWithRelations,
                dueDate: new Date('2024-06-30'),
            };

            render(<TaskCard {...defaultProps} task={taskWithDueDate} />);

            expect(screen.getByText(/Termin:/)).toBeInTheDocument();
        });
    });

    describe('task status display', () => {
        it('should show completed status', () => {
            const completedTask = {
                ...mockTaskWithRelations,
                completions: [
                    {
                        id: 'completion-1',
                        taskId: mockTaskWithRelations.id,
                        completedById: 'member-1',
                        status: CompletionStatus.APPROVED,
                        pointsAwarded: 5,
                        completedAt: new Date('2024-06-25'),
                        verifiedAt: new Date('2024-06-25'),
                        verifiedById: 'member-2',
                        notes: null,
                        completedBy: mockMember,
                        verifiedBy: mockMember,
                    },
                ],
            };

            render(<TaskCard {...defaultProps} task={completedTask} />);

            // Sprawdź czy ikona CheckCircle jest renderowana (ma klasę success-checkmark)
            const checkIcon = document.querySelector('.success-checkmark');
            expect(checkIcon).toBeInTheDocument();
            expect(screen.getByText('Ukończone')).toBeInTheDocument();
        });

        it('should show pending status', () => {
            const pendingTask = {
                ...mockTaskWithRelations,
                completions: [
                    {
                        id: 'completion-1',
                        taskId: mockTaskWithRelations.id,
                        completedById: 'member-1',
                        status: CompletionStatus.PENDING,
                        pointsAwarded: 0,
                        completedAt: new Date('2024-06-25'),
                        verifiedAt: null,
                        verifiedById: null,
                        notes: null,
                        completedBy: mockMember,
                        verifiedBy: null,
                    },
                ],
            };

            render(<TaskCard {...defaultProps} task={pendingTask} />);

            // Clock icon for pending status
            expect(screen.getByText(/Ukończone/)).toBeInTheDocument();
        });

        it('should show overdue status', () => {
            jest.useFakeTimers();
            jest.setSystemTime(new Date('2024-07-01'));

            const overdueTask = {
                ...mockTaskWithRelations,
                dueDate: new Date('2024-06-30'),
                status: TaskStatus.ACTIVE,
            };

            render(<TaskCard {...defaultProps} task={overdueTask} />);

            expect(screen.getByText(/Termin:/)).toHaveClass('text-red-600');

            jest.useRealTimers();
        });

        it('should show suspended status', () => {
            const suspensionInfo = {
                isSuspended: true,
                reason: 'Test suspension',
                endDate: new Date('2024-07-15'),
            };

            render(<TaskCard {...defaultProps} suspensionInfo={suspensionInfo} />);

            expect(screen.getByText('Zawieszone')).toBeInTheDocument();
        });
    });

    describe('interactions', () => {
        it('should render task card with proper structure', () => {
            render(<TaskCard {...defaultProps} />);

            // Sprawdź czy karta zadania jest renderowana
            const taskCard = screen.getByText(mockTaskWithRelations.title).closest('[data-slot="card"]');
            expect(taskCard).toBeInTheDocument();

            // Sprawdź czy zawiera podstawowe informacje
            expect(screen.getByText('Test User')).toBeInTheDocument();
            expect(screen.getByText('5')).toBeInTheDocument(); // punkty
        });

        it('should show task completion status correctly', () => {
            const completedTask = {
                ...mockTaskWithRelations,
                completions: [
                    {
                        id: 'completion-1',
                        taskId: mockTaskWithRelations.id,
                        completedById: 'member-1',
                        status: CompletionStatus.APPROVED,
                        pointsAwarded: 5,
                        completedAt: new Date('2024-06-25'),
                        verifiedAt: new Date('2024-06-25'),
                        verifiedById: 'member-2',
                        notes: null,
                        completedBy: mockMember,
                        verifiedBy: mockMember,
                    },
                ],
            };

            render(<TaskCard {...defaultProps} task={completedTask} />);

            // Sprawdź czy ikona CheckCircle jest renderowana
            const checkIcon = document.querySelector('.success-checkmark');
            expect(checkIcon).toBeInTheDocument();
        });

        it('should handle task verification approval', async () => {
            const user = userEvent.setup();
            mockFetch.mockResolvedValueOnce({
                ok: true,
                json: async () => ({}),
            } as Response);

            const pendingTask = {
                ...mockTaskWithRelations,
                completions: [
                    {
                        id: 'completion-1',
                        taskId: mockTaskWithRelations.id,
                        completedById: 'member-1',
                        status: CompletionStatus.PENDING,
                        pointsAwarded: 0,
                        completedAt: new Date('2024-06-25'),
                        verifiedAt: null,
                        verifiedById: null,
                        notes: null,
                        completedBy: mockMember,
                        verifiedBy: null,
                    },
                ],
            };

            render(<TaskCard {...defaultProps} task={pendingTask} />);

            // Find and click the more actions button (dropdown menu trigger)
            const moreButton = screen.getByRole('button', { expanded: false });
            await user.click(moreButton);

            // Click approve button
            const approveButton = screen.getByText('Zatwierdź');
            await user.click(approveButton);

            await waitFor(() => {
                expect(mockFetch).toHaveBeenCalledWith(
                    `/api/families/${mockTaskWithRelations.familyId}/tasks/${mockTaskWithRelations.id}/verify?completionId=completion-1`,
                    expect.objectContaining({
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ approved: true }),
                    })
                );
            });
        });

        it('should show dropdown menu button when user can manage tasks', () => {
            render(<TaskCard {...defaultProps} canManageTasks={true} />);

            // Przycisk menu powinien być widoczny
            const moreButton = screen.getByRole('button', { expanded: false });
            expect(moreButton).toBeInTheDocument();
        });

        it('should not show dropdown menu when user cannot manage tasks', async () => {
            render(<TaskCard {...defaultProps} canManageTasks={false} />);

            // Przycisk menu nie powinien być widoczny gdy użytkownik nie może zarządzać zadaniami
            const moreButton = screen.queryByRole('button', { expanded: false });
            expect(moreButton).not.toBeInTheDocument();
        });
    });

    describe('permissions', () => {
        it('should not show actions when user cannot manage tasks', () => {
            render(
                <TaskCard
                    {...defaultProps}
                    canManageTasks={false}
                    canAssignTasks={false}
                    canCompleteTasks={false}
                />
            );

            expect(screen.queryByRole('button', { name: /more/i })).not.toBeInTheDocument();
        });

        it('should not allow completion when user cannot complete tasks', () => {
            const taskForCurrentUser = {
                ...mockTaskWithRelations,
                assignments: [{ memberId: mockMember.id, member: mockMember }],
            };

            render(<TaskCard {...defaultProps} task={taskForCurrentUser} canCompleteTasks={false} />);

            // Task card should not be clickable
            const taskCard = screen.getByText(mockTaskWithRelations.title).closest('[role="button"]');
            expect(taskCard).not.toBeInTheDocument();
        });

        it('should not allow completion when task is suspended', () => {
            const taskForCurrentUser = {
                ...mockTaskWithRelations,
                assignments: [{ memberId: mockMember.id, member: mockMember }],
            };

            const suspensionInfo = {
                isSuspended: true,
                reason: 'Test suspension',
            };

            render(
                <TaskCard {...defaultProps} task={taskForCurrentUser} suspensionInfo={suspensionInfo} />
            );

            // Task should not be clickable when suspended
            const taskCard = screen.getByText(mockTaskWithRelations.title).closest('[role="button"]');
            expect(taskCard).not.toBeInTheDocument();
        });
    });

    describe('member display names', () => {
        it('should display full name when available', () => {
            render(<TaskCard {...defaultProps} />);

            expect(screen.getByText('Test User')).toBeInTheDocument();
        });

        it('should display email username when name not available', () => {
            const taskWithEmailOnly = {
                ...mockTaskWithRelations,
                assignedTo: {
                    ...mockMember,
                    user: {
                        id: 'user-1',
                        firstName: '',
                        lastName: '',
                        email: '<EMAIL>',
                    },
                },
            };

            render(<TaskCard {...defaultProps} task={taskWithEmailOnly} />);

            expect(screen.getByText('Test User')).toBeInTheDocument();
        });
    });
});
