import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeToggle } from '@/components/theme/ThemeToggle';

// Mock localStorage
const mockLocalStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

// Mock getBoundingClientRect
Element.prototype.getBoundingClientRect = jest.fn(() => ({
    width: 120,
    height: 40,
    top: 0,
    left: 0,
    bottom: 40,
    right: 120,
    x: 0,
    y: 0,
    toJSON: jest.fn(),
}));

describe('ThemeToggle', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        mockLocalStorage.getItem.mockReturnValue(null);

        // Mock document.documentElement
        Object.defineProperty(document, 'documentElement', {
            value: {
                classList: {
                    toggle: jest.fn(),
                },
            },
            writable: true,
        });
    });

    describe('renderowanie podstawowe', () => {
        it('powinien renderować przycisk przełączania motywu', async () => {
            render(<ThemeToggle />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });
        });

        it('powinien pokazać ukryty tekst dla czytników ekranu', async () => {
            render(<ThemeToggle />);

            await waitFor(() => {
                const hiddenText = screen.getByText('Przełącz motyw');
                expect(hiddenText).toHaveClass('sr-only');
            });
        });

        it('powinien pokazać ikonę monitora dla motywu systemowego (domyślnie)', async () => {
            render(<ThemeToggle />);

            await waitFor(() => {
                const monitorIcon = document.querySelector('.lucide-monitor');
                expect(monitorIcon).toBeInTheDocument();
            });
        });

        it('powinien pokazać ikonę słońca dla jasnego motywu', async () => {
            mockLocalStorage.getItem.mockReturnValue('light');

            render(<ThemeToggle />);

            await waitFor(() => {
                const sunIcon = document.querySelector('.lucide-sun');
                expect(sunIcon).toBeInTheDocument();
            });
        });

        it('powinien pokazać ikonę księżyca dla ciemnego motywu', async () => {
            mockLocalStorage.getItem.mockReturnValue('dark');

            render(<ThemeToggle />);

            await waitFor(() => {
                const moonIcon = document.querySelector('.lucide-moon');
                expect(moonIcon).toBeInTheDocument();
            });
        });
    });

    describe('interakcje - wariant button', () => {
        it('powinien przełączyć motyw po kliknięciu (light -> dark)', async () => {
            const user = userEvent.setup();
            mockLocalStorage.getItem.mockReturnValue('light');

            render(<ThemeToggle variant="button" />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });

            const button = screen.getByRole('button');
            await user.click(button);

            expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme', 'dark');
        });

        it('powinien przełączyć motyw po kliknięciu (dark -> system)', async () => {
            const user = userEvent.setup();
            mockLocalStorage.getItem.mockReturnValue('dark');

            render(<ThemeToggle variant="button" />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });

            const button = screen.getByRole('button');
            await user.click(button);

            expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme', 'system');
        });

        it('powinien przełączyć motyw po kliknięciu (system -> light)', async () => {
            const user = userEvent.setup();
            mockLocalStorage.getItem.mockReturnValue('system');

            render(<ThemeToggle variant="button" />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });

            const button = screen.getByRole('button');
            await user.click(button);

            expect(mockLocalStorage.setItem).toHaveBeenCalledWith('theme', 'light');
        });
    });

    describe('dostępność', () => {
        it('powinien mieć ukryty tekst dla czytników ekranu', async () => {
            render(<ThemeToggle />);

            await waitFor(() => {
                const hiddenText = screen.getByText('Przełącz motyw');
                expect(hiddenText).toHaveClass('sr-only');
            });
        });

        it('powinien być dostępny przez klawiaturę', async () => {
            const user = userEvent.setup();
            render(<ThemeToggle variant="button" />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });

            const button = screen.getByRole('button');

            // Sprawdź czy można nawigować do przycisku
            await user.tab();
            expect(button).toHaveFocus();

            // Sprawdź czy można aktywować przycisk klawiszem Enter
            await user.keyboard('{Enter}');

            // Sprawdź czy localStorage został wywołany
            expect(mockLocalStorage.setItem).toHaveBeenCalled();
        });
    });

    describe('warianty komponentu', () => {
        it('powinien renderować wariant dropdown (domyślny)', async () => {
            render(<ThemeToggle />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });
        });

        it('powinien renderować wariant button', async () => {
            render(<ThemeToggle variant="button" />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toBeInTheDocument();
            });
        });

        it('powinien obsłużyć niestandardowe klasy CSS', async () => {
            render(<ThemeToggle className="custom-class" />);

            await waitFor(() => {
                const button = screen.getByRole('button');
                expect(button).toHaveClass('custom-class');
            });
        });
    });
});
