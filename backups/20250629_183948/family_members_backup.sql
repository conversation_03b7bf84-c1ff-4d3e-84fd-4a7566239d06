PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS "family_members" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "familyId" TEXT NOT NULL,
    "role" TEXT NOT NULL DEFAULT 'CHILD',
    "isAdult" BOOLEAN NOT NULL DEFAULT false,
    "points" INTEGER NOT NULL DEFAULT 0,
    "joinedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP, "lastActive" DATETIME,
    CONSTRAINT "family_members_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "family_members_familyId_fkey" FOREIGN KEY ("familyId") REFERENCES "families" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO family_members VALUES('cmcfkbf0z000gvdsjzwgtsumw','cmcfh57e90000vd2mmlhklmta','cmcfkbf0z000evdsj9hkwkicb','OWNER',1,150,1751074115796,NULL);
INSERT INTO family_members VALUES('cmcfkbf0z000hvdsjlhtprzew','cmcfkbez2000avdsj2apsdm6r','cmcfkbf0z000evdsj9hkwkicb','PARENT',1,120,1751074115796,NULL);
INSERT INTO family_members VALUES('cmcfkbf0z000ivdsjrtdvfhht','cmcfkbez2000bvdsji1psg5mq','cmcfkbf0z000evdsj9hkwkicb','PARENT',0,85,1751074115796,1751099757876);
INSERT INTO family_members VALUES('cmcfkbf11000mvdsjfsgt7zpq','cmcfkbez10000vdsj6x21ur2x','cmcfkbf11000kvdsj8pgjyxc9','OWNER',1,200,1751074115797,NULL);
INSERT INTO family_members VALUES('cmcfkbf11000nvdsj5bmc0dkr','cmcfkbez10001vdsju2vs2ftu','cmcfkbf11000kvdsj8pgjyxc9','PARENT',1,180,1751074115797,NULL);
INSERT INTO family_members VALUES('cmcfkbf11000ovdsjwh1d89fi','cmcfkbez10002vdsjb48k3d0q','cmcfkbf11000kvdsj8pgjyxc9','CHILD',0,95,1751074115797,NULL);
INSERT INTO family_members VALUES('cmcfkbf11000rvdsjt7bezxzn','cmcfkbez10009vdsj64f3d1jb','cmcfkbf11000pvdsjnhe9nqjl','OWNER',1,170,1751074115798,NULL);
INSERT INTO family_members VALUES('cmcfkbf11000svdsjkyvf27qk','cmcfkbez10006vdsj2kcpk05u','cmcfkbf11000pvdsjnhe9nqjl','PARENT',1,160,1751074115798,NULL);
INSERT INTO family_members VALUES('cmcfkbf11000tvdsj6zuzmlt6','cmcfkbez7000dvdsj18cj541k','cmcfkbf11000pvdsjnhe9nqjl','GUARDIAN',1,110,1751074115798,NULL);
INSERT INTO family_members VALUES('cmcfkbf11000uvdsjce738j1t','cmcfkbez10005vdsjeiwpk7g5','cmcfkbf11000pvdsjnhe9nqjl','CHILD',0,80,1751074115798,NULL);
INSERT INTO family_members VALUES('cmcfkbf12000xvdsjitk7bw78','cmcfkbez10008vdsjxvm64gvg','cmcfkbf12000vvdsj4a15wqvp','OWNER',1,190,1751074115799,NULL);
INSERT INTO family_members VALUES('cmcfkbf12000yvdsjx750n4wg','cmcfkbez10007vdsjw3mwnfas','cmcfkbf12000vvdsj4a15wqvp','PARENT',1,175,1751074115799,NULL);
INSERT INTO family_members VALUES('cmcfkbf12000zvdsjhv8802s4','cmcfkbez10004vdsjhwi9cgh8','cmcfkbf12000vvdsj4a15wqvp','CHILD',0,100,1751074115799,NULL);
INSERT INTO family_members VALUES('cmcfkbf120010vdsj0kclbflm','cmcfkbez10003vdsjvrydf3zc','cmcfkbf12000vvdsj4a15wqvp','CHILD',0,60,1751074115799,NULL);
COMMIT;
