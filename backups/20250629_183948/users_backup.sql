PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS "users" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "clerkId" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "preferredLanguage" TEXT NOT NULL DEFAULT 'pl',
    "timezone" TEXT NOT NULL DEFAULT 'Europe/Warsaw',
    "dateFormat" TEXT NOT NULL DEFAULT 'dd.MM.yyyy',
    "timeFormat" TEXT NOT NULL DEFAULT '24h',
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO users VALUES('cmcffcuoz0000vdldrmm6n469','demo_jan_kowalski','6baran6+jan_kowals<PERSON>@gmail.com','Jan','<PERSON><PERSON><PERSON>','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751065784675,1751157295566);
INSERT INTO users VALUES('cmcfh57e90000vd2mmlhklmta','user_2yCiLbyw0MyLagtnJQQoFjO6eVt','<EMAIL>',NULL,NULL,'pl','Europe/Warsaw','dd.MM.yyyy','24h',1751068787122,1751068787122);
INSERT INTO users VALUES('cmcfkbez10000vdsj6x21ur2x','sample_piotr_nowak','<EMAIL>','Piotr','Nowak','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez2000avdsj2apsdm6r','sample_anna_kowalska','<EMAIL>','Anna','Kowalska','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115726,1751157295566);
INSERT INTO users VALUES('cmcfkbez2000bvdsji1psg5mq','sample_michal_kowalski','<EMAIL>','Michał','Kowalski','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115727,1751159729933);
INSERT INTO users VALUES('cmcfkbez2000cvdsj7gmizzuy','sample_zosia_kowalska','<EMAIL>','Zosia','Kowalska','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115727,1751157295566);
INSERT INTO users VALUES('cmcfkbez7000dvdsj18cj541k','sample_karolina_wisniewska','<EMAIL>','Karolina','Wiśniewska','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115731,1751157295566);
INSERT INTO users VALUES('cmcfkbez10006vdsj2kcpk05u','sample_magdalena_wisniewska','<EMAIL>','Magdalena','Wiśniewska','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10003vdsjvrydf3zc','sample_kamil_dudek','<EMAIL>','Kamil','Dudek','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10001vdsju2vs2ftu','sample_katarzyna_nowak','<EMAIL>','Katarzyna','Nowak','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10005vdsjeiwpk7g5','sample_mateusz_wisniewski','<EMAIL>','Mateusz','Wiśniewski','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10004vdsjhwi9cgh8','sample_patrycja_dudek','<EMAIL>','Patrycja','Dudek','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10009vdsj64f3d1jb','sample_tomasz_wisniewski','<EMAIL>','Tomasz','Wiśniewski','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10007vdsjw3mwnfas','sample_ewa_dudek','<EMAIL>','Ewa','Dudek','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10008vdsjxvm64gvg','sample_andrzej_dudek','<EMAIL>','Andrzej','Dudek','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
INSERT INTO users VALUES('cmcfkbez10002vdsjb48k3d0q','sample_jakub_nowak','<EMAIL>','Jakub','Nowak','pl','Europe/Warsaw','dd.MM.yyyy','24h',1751074115725,1751157295566);
COMMIT;
