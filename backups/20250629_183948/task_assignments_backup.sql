PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS "task_assignments" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "taskId" TEXT NOT NULL,
    "memberId" TEXT NOT NULL,
    "assignedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "assignedBy" TEXT NOT NULL,
    CONSTRAINT "task_assignments_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "tasks" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "task_assignments_memberId_fkey" FOREIGN KEY ("memberId") REFERENCES "family_members" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO task_assignments VALUES('cmchocfbl0000vdy57lkhamvg','cmcgz97fr0001vdztk3ybfu2e','cmcfkbf0z000gvdsjzwgtsumw',1751201813649,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchocfbm0001vdy52fhbufgc','cmcgz97fr0001vdztk3ybfu2e','cmcfkbf0z000hvdsjlhtprzew',1751201813649,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchocfbm0002vdy54vu4h7uf','cmcgz97fr0001vdztk3ybfu2e','cmcfkbf0z000ivdsjrtdvfhht',1751201813649,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchogj7u0003vdy5v82ffpgu','cmcguxlwq0001vdf6k9n0lhjp','cmcfkbf0z000gvdsjzwgtsumw',1751202005322,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchogj7u0004vdy5cwyg18t3','cmcguxlwq0001vdf6k9n0lhjp','cmcfkbf0z000hvdsjlhtprzew',1751202005322,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchogj7u0005vdy5rhgkkml9','cmcguxlwq0001vdf6k9n0lhjp','cmcfkbf0z000ivdsjrtdvfhht',1751202005322,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchop2tw0008vdy5n3a4pmfb','cmchop2tr0007vdy5nqjq2501','cmcfkbf0z000hvdsjlhtprzew',1751202403987,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchop2tw0009vdy5a0kzbcnf','cmchop2tr0007vdy5nqjq2501','cmcfkbf0z000ivdsjrtdvfhht',1751202403987,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchs5ech0000vdi5bvzqvcrc','cmcfkbf1o002mvdsjdxhrt5lf','cmcfkbf0z000ivdsjrtdvfhht',1751208204256,'cmcfkbf0z000gvdsjzwgtsumw');
INSERT INTO task_assignments VALUES('cmchs5k0z0001vdi5bcoc8ps0','cmcfkbf1x003gvdsj8joivfxa','cmcfkbf0z000ivdsjrtdvfhht',1751208211618,'cmcfkbf0z000gvdsjzwgtsumw');
COMMIT;
