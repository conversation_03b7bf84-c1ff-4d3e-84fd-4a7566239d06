PRAGMA foreign_keys=OFF;
BEGIN TRANSACTION;
CREATE TABLE IF NOT EXISTS "families" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO families VALUES('cmcfkbf0z000evdsj9hkwkicb','<PERSON><PERSON><PERSON>wal<PERSON>','<PERSON><PERSON><PERSON> kochana rodzina, która organizuje życie codzienne przez wspólne zadania i obowiązki. :)',1751074115796,1751076632046);
INSERT INTO families VALUES('cmcfkbf11000kvdsj8pgjyxc9','Rodzina Nowaków','Aktywna rodzina, która lubi spędzać czas razem i wspólnie planować obowiązki domowe.',1751074115797,1751074115797);
INSERT INTO families VALUES('cmcfkbf11000pvdsjnhe9nqjl','Rodzin<PERSON> Wiśniewskich','Du<PERSON>a, wielopokoleniowa rodzina, gdzie każdy ma swoje obowiązki dostosowane do wieku.',1751074115798,1751074115798);
INSERT INTO families VALUES('cmcfkbf12000vvdsj4a15wqvp','Rodzina Dudków','Zorganizowana rodzina, która stawia na współpracę i wzajemną pomoc w codziennych obowiązkach.',1751074115799,1751074115799);
COMMIT;
